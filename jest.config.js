/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
    roots: ['<rootDir>/js'],
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    moduleNameMapper: {
        'react-components/(.*)': '<rootDir>/js/react-components/$1',
        'common/(.*)': '<rootDir>/js/common/$1',
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
            '<rootDir>/__mocks__/fileMock.js',
        'images/(.*)': '<rootDir>/images/$1',
        //TODO: add other aliases if needed
    },
    transform: {
        '^.+\\.(ts|tsx)$': 'ts-jest',
        '^.+\\.js$': 'babel-jest',
        '.+\\.(css|styl|less|sass|scss)$': 'jest-css-modules-transform',
    },
    transformIgnorePatterns: ['node_modules/(?!(module-that-needs-to-be-transformed)/)'],
};
