/**
 * @see https://prettier.io/docs/configuration
 * @type {import("prettier").Config}
 */
const config = {
    trailingComma: 'es5',
    tabWidth: 4,
    useTabs: false,
    semi: true,
    singleQuote: true,
    printWidth: 120,
    overrides: [
        {
            files: ['*.jsx', '*.tsx'],
            options: {
                bracketSameLine: false,
            },
        },
    ],
};

export default config;
