@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

reset-password-component {
    width: 0;
}

.error-text-message {
    margin-bottom: 8px;
    font-size: $secondary-font-size;
    color: $red;
    opacity: 1;
}

.success-text-message {
    padding-top: 48px;
    color: $main-green;
    text-align: center;
    opacity: 1;
}

.hide-text-message {
    margin-bottom: 8px;
    opacity: 0;
}

.forgot-password-block {
    color: $main-black;
    text-decoration: underline;
    cursor: pointer;
}

.forgot-password-block-wrapper {
    margin-top: 8px;
    cursor: default;
}

.input-email-margin-bottom {
    width: 100%;
    height: 42px;
    padding: 12px;
    text-decoration: none;
    background-color: $main-white;
    border: 1px solid $border-grey;
    border-radius: 8px;
    outline: none;
    transition: all 0.2s ease;

    &:hover {
        border-color: $dark-grey;
    }

    &:focus {
        background-color: $pale-grey;
        border-color: $semi-black;

        &:hover {
            border-color: $semi-black;
        }
    }
}

.reset-password-title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: 500;
    color: $main-black;

    &-wrapper {
        text-align: center;
    }
}

.showResetPasswordForm {
    transition: all 1s;
    transform: translateX(100%) !important;
}

.hideLoginModal {
    transition: all 1s;
    transform: translateX(-110%) !important;
}

.resetPasswordForm {
    position: relative;
    min-width: 586px;
    transition: all 1s;
    transform: translateX(-100%);
}

.reset-form-wrapper {
    display: flex;
    flex-direction: column;
    gap: 75px;
    padding: var(--main-gap);
}

.back-arrow-icon {
    position: absolute;
    top: var(--main-gap);
    left: var(--main-gap);
    display: inline-block;
    margin: 0;
    cursor: pointer;
    transition: all 0.3s ease-in;
    @include control-icon-size;
    @include icon-mask('/images/redesign/svg-icons/arrow-left.svg', $main-black);

    &:hover,
    &:focus {
        background-color: $secondary-black;
    }
}

.error {
    border-color: $red !important;
}

.arrow-modal {
    position: absolute;
    right: 160px;
    bottom: 40px;
    width: 70px;
    height: 25px;
    background: url('/external/assets/img/svg/im_line_for_plane.svg');
    background-size: cover;
}

.plan-modal {
    position: absolute;
    bottom: 60px;
    left: 140px;
    width: 37px;
    height: 23px;
    background: url(/external/assets/img/svg/im_plane.svg);
    background-size: cover;
    -moz-transform: rotate(15deg);
    -ms-transform: rotate(15deg);
    -webkit-transform: rotate(15deg);
    -o-transform: rotate(15deg);
    transform: rotate(15deg);
}

.successResetMessage {
    padding: var(--main-gap);
    margin: auto;
    background-color: $main-white;
}

.success-reset-block-title {
    font-family: $main-font-family;
    font-size: $heading-font-size;
    font-weight: 400;
}

.modal-button-wrapper {
    margin: 0;
    text-align: center;
}

@media (max-width: 620px) {
    .resetPasswordForm {
        min-width: 320px;
    }
}

@media (max-width: 479px) {
    .resetPasswordForm {
        padding: 0 20px;
    }
}
