@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';
@import 'mixins';
@import 'variables';

.block-vacancies {
    position: relative;
    margin-top: var(--main-gap);

    #scrollup {
        display: none;
    }

    .container-fluid {
        padding: 0;
    }

    .row {
        @include disabled-margins-for-row();

        & > .col-lg-12 {
            &.return-on-report-page {
                padding-left: 15px;
            }
        }

        .buttons-wrapper {
            display: flex;
            gap: 20px;
        }
    }

    .return-on-report-page {
        margin: 38px 0 33px;

        & > a {
            @include return-on-report-page-btn();

            &:hover {
                .arrow {
                    background-position: -16px 0;
                }
            }
        }

        .arrow {
            position: absolute;
            top: 8px;
            left: 44px;
            width: 18px;
            height: 13px;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAANCAYAAADISGwcAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkJEMkE2MjA4NzA3MjExRTg4NTBCQkJFQUIzNDE0RjJDIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkJEMkE2MjA5NzA3MjExRTg4NTBCQkJFQUIzNDE0RjJDIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QkQyQTYyMDY3MDcyMTFFODg1MEJCQkVBQjM0MTRGMkMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QkQyQTYyMDc3MDcyMTFFODg1MEJCQkVBQjM0MTRGMkMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6Aa14vAAAA9klEQVR42qSUPQoCMRBGk2UXtPMuuYeVjSiWgjbewhOIiqU/iGglVp4htYgXsPACoiB+AyOEJUpmDDyyZJk3k81mrHPOREYdbMAMDMyP4b3/9irJkUXWmmAHKqDPs3QkO8oFtMASFOAJOuAuTC5y5KXAORdFgQ2wVyQXOT5foAcWfyZXOSx+QgocB2s3cEpMesFP2OXkKgcV8ODzUg0UYDGpHTlfkbD6K+0sMf7Ms9pBBUz4ecTnVwNDcBRsRO3IAgFdlxeoggM3EiMsQuwI+8AqEBTcSKRFiB1ZRNDma0SCraITihyxVrzmO0zda6rohCLHW4ABAN9kT2Kf8NOYAAAAAElFTkSuQmCC);
            background-repeat: no-repeat;
            background-position: 1px 0;
        }
    }

    .search {
        background-color: $theme-white;
        border-radius: 5px;

        .chosenStatus {
            width: 95%;
            padding: 3px 16px;
            margin: 5px 0;

            border-radius: 5px;

            & > i {
                margin-top: 4px;

                &:hover {
                    cursor: pointer;
                }
            }
        }

        .input-group-wrapper {
            padding: 0 15px;

            .input-group {
                width: 100%;
                margin-top: 15px;
                margin-bottom: 20px;

                border: 1px solid #999;
                border-radius: 10px;

                input {
                    padding-left: 0;
                    font-size: 14px;
                    font-weight: normal;

                    letter-spacing: 0.6px;

                    border-bottom: none;
                }

                .input-group-addon {
                    @include input-group-addon-styling();

                    border-bottom: none;
                    border-radius: 10px;

                    .info-icon {
                        display: block;
                        -webkit-align-self: center;
                        align-self: center;
                        width: 14px;
                        height: 14px;
                        background: transparent url(/images/sprite/icons/info.svg) no-repeat;
                        background-size: cover;
                        -ms-grid-row-align: center;
                    }

                    .tooltip-outer {
                        margin-top: 100px;
                    }

                    .grey {
                        color: $theme-light-grey;
                    }

                    &:hover {
                        color: $theme-link-hover-color;
                        cursor: pointer;
                    }
                }
            }
        }

        input {
            @include input-styling();
        }

        .select {
            select {
                @include select-styling();

                color: $theme-grey;

                option {
                    color: $theme-dark;
                }

                .disable {
                    color: #b8b8b8;
                }

                &.margin-top {
                    margin-top: 15px;
                }

                .greyColor {
                    color: #999;
                }
            }
        }

        .close-search {
            padding-top: 10px;

            color: $theme-dark;

            i {
                margin-top: 3px;
            }
        }

        a.search {
            @include button-green-styling();

            margin: 8px 15px;
        }

        .set-default {
            margin-top: 20px;
        }

        .advancedSearch {
            .select2-container {
                width: 100%;
            }

            a {
                &:not(.select2-choice) {
                    @include link-styling();
                }
            }

            .margin-top {
                margin-top: 15px;
            }
        }
    }

    .scopeVacancies {
        padding-left: 0;
        margin: 15px 0 0 0;

        span {
            padding: 2px 6px;

            color: #fff;
            background-color: #0aa5df;
        }
    }

    .line-height-21 {
        line-height: 21px;
        word-break: break-all;
    }

    .criterionSearchCandidate {
        padding: 0;
        margin: 10px 0 10px 0;
    }

    .empty-search {
        display: flex;
        flex-direction: column;
        padding: 20px 22px 18px;
        margin-top: 0;
        margin-bottom: auto;
        font-family: $main-font-family;
        color: #1c1f23;
        background-color: $main-white;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

        .count_empty {
            margin-bottom: 18px;

            .bold {
                font-weight: 500;
            }
        }

        .action {
            .use-advanced-search {
                @include link-green;
            }
        }
    }

    .count {
        padding: 0 4px;

        margin: 15px 0;
        font-weight: 500;

        text-transform: capitalize;
        background-color: $theme-subtitle-background;

        border-radius: 5px;

        span {
            font-weight: 600;
        }
    }

    .vacancy-table {
        & > .row {
            padding: 0;
        }

        .col-lg-12 {
            padding: 0;

            table {
                background-color: $theme-white;

                thead {
                    tr {
                        th {
                            height: 37px;
                            padding: 10px;
                            overflow: hidden;
                            font-size: 14px;
                            font-weight: normal;
                            line-height: 14px;
                            color: $semi-black;
                            text-align: left;
                            vertical-align: middle;
                            background-color: $background-grey;

                            &.dateFinish {
                                white-space: nowrap;
                            }
                        }
                    }
                }

                tbody {
                    tr {
                        position: relative;
                        height: 56px;
                        border-left: 2px solid transparent;

                        &:hover {
                            cursor: pointer;
                            background-color: rgba(0, 181, 73, 0.02);
                            border-left: 2px solid #00b549;
                        }

                        &.table-space {
                            height: 8px;
                            box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.12);
                        }

                        &:last-child {
                            td {
                                border-bottom: none;
                            }
                        }

                        td {
                            padding: 6px 10px;
                            vertical-align: middle;
                            border: none;
                            border-right: 1px solid $border-grey;
                            border-bottom: 1px solid $border-grey;

                            &.client-name {
                                overflow: hidden;
                            }

                            .select-ops.custom-new > div.list-ops .list-item-ops {
                                padding: 3px;
                            }
                        }
                    }
                }

                .client-name {
                    .image-link {
                        width: 100%;

                        img {
                            max-width: 70px;
                        }
                    }
                }

                .client-link-wrapper {
                    font-size: 0;

                    &__name {
                        display: inline-block;
                        font-size: 14px;
                    }
                }

                .location {
                    white-space: nowrap;
                }

                .priority {
                    white-space: nowrap;
                }

                .status {
                    .open {
                        color: #202021;
                        background-color: #cae5af;
                    }

                    .inwork {
                        color: #202021;
                        background-color: #ffe699;
                    }

                    .otherColor {
                        color: #202021;
                        background-color: $theme-light-grey-hover;
                    }
                }
            }
        }

        .header-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .vacancy-name-link {
            font-weight: normal;
            color: $main-black;
            text-decoration: none;

            &:hover {
                color: $main-black;
                text-decoration: none;
            }
        }

        .link-green {
            @include link-green;
        }

        .responsibles {
            display: flex;
            gap: 8px;
            align-items: center;
            color: $semi-black;
        }

        .multiple-resp-popover {
            padding: 0;
            border: 1px solid $border-grey;
            border-radius: 4px;
            box-shadow: $main-box-shadow;

            .responsible-person-component {
                display: block;
                width: 36px;
            }

            .popover-content {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                align-items: center;
                min-width: 36px;
                max-width: 300px;
                max-height: 187px;
                padding: 8px;
                overflow: auto;
            }

            .arrow {
                display: none;
            }

            &__item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                &_bold-name {
                    font-weight: 600;
                }
            }
        }

        .pagination-block {
            display: none;
        }
    }

    &.reports {
        margin-top: 0;

        .row {
            padding-left: 0;
        }

        .col-lg-12 {
            padding: var(--main-gap);
        }

        .vacancy-table {
            overflow: auto;
            @include border-large;
        }

        table {
            padding: 0;
            margin: 0;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;

            thead tr th {
                border-bottom: 1px solid $border-grey;
            }

            thead {
                th:first-child {
                    border-top-left-radius: 12px;
                }

                th:last-child {
                    border-top-right-radius: 12px;
                }
            }

            tbody {
                tr:nth-last-child(3),
                tr:nth-last-child(2),
                tr:nth-last-child(1) {
                    .custom-new {
                        top: -235px;
                    }
                }
            }
        }

        .one-row-table {
            tbody {
                tr {
                    .custom-new {
                        top: 32px !important;
                    }
                }
            }
        }

        .select-all-checkbox {
            text-align: center !important;
            border-right: 1px solid transparent;
        }

        .vacancies-pagination {
            & > div {
                border-top: 1px solid $border-grey;
            }
        }
    }

    .select2-container .select2-choice abbr {
        &.select2-search-choice-close {
            top: 8px !important;
            right: 37px !important;
        }

        &.select2-search-choice-edit-origin {
            display: none;
        }
    }
}

@media (max-width: $screen-sm) {
    .no-padding-sm {
        padding: 0;
    }
}

@media (max-width: $screen-xs) {
    .block-vacancies .search {
        margin-left: -8px;
    }
}

@media (max-width: 1375px) {
    .block-vacancies .vacancy-table .col-lg-12 table thead tr th {
        height: 48px !important;
    }
}

@media (max-width: 992px) {
    .clientName {
        min-width: 110px !important;
    }
}

@media (max-width: 575px) {
    .block-vacancies .criterionSearchCandidate {
        margin: 0 0 20px 10px;
    }

    .block-vacancies .vacancy-table .col-lg-12 table {
        display: block;
        overflow-x: auto;
    }

    .flex-align-center {
        flex-wrap: wrap;
    }

    .block-vacancies .vacancy-table .col-lg-12 table .clientName {
        min-width: 90px;
    }

    .block-vacancies .vacancy-table .col-lg-12 table .openingDate {
        min-width: 130px;
    }
}

.show-empty-search {
    display: block;
}

.activeSort {
    color: #40a349;

    & svg path {
        fill: #40a349 !important;
    }
}

.disable {
    display: none;
    color: #999999;
}

.pencilHover {
    svg {
        fill: #202021;
    }

    svg:hover {
        fill: #019b3f;
    }
}

.changeInterviewStep {
    label {
        left: -10px !important;
    }
}
