@import 'js/common/styles/mixins';

.excel-popup-wrapper {
    display: flex;
    justify-content: space-evenly;

    .export-history {
        display: flex;
        flex-direction: column;
        align-items: center;

        .export-image img {
            height: 70px;
        }

        a {
            @include link-green;
        }
    }

    .export {
        &-type {
            display: flex;
            flex-direction: column;
            gap: 12px;

            &-item {
                display: flex;
                gap: 8px;

                span {
                    cursor: pointer;
                }
            }
        }

        .exporting {
            a {
                color: $main-green;
                transition: all 0.3s ease-in;

                &:hover {
                    color: $yellow;
                }
            }
        }
    }

    .radio-block {
        display: flex;
        gap: 5px;
    }
}

.excel-history {
    @include flex(column, flex-start, center);
    position: relative;

    width: 700px;
    padding: 20px 30px 20px 35px;
    margin: -1px 0 -1px -50px;
    background-color: #e3f2dd;

    border-radius: 9px;

    .exporting {
        @include flex(row, flex-start, center);

        width: 60%;
        padding: 15px 22px;
        background-color: #eff7ee;

        border-radius: 10px;
    }

    .intro {
        margin: 0 auto 25px;

        text-align: center;

        h2 {
            margin: 0 0 5px 0;
            font-size: 26px;
        }

        i.fa-times {
            position: absolute;
            top: 10px;
            right: 10px;

            cursor: pointer;
        }
    }

    .export {
        display: flex;

        align-items: center;
        justify-content: space-evenly;

        .export-image {
            width: 20%;
            height: 100%;
            padding: 0 37px 0 0;

            text-align: center;
        }

        .export-type {
            width: 60%;
            padding: 15px 22px;
            background-color: #eff7ee;

            border-radius: 10px;

            h5 {
                margin: 0 0 20px 0;
            }

            .type {
                display: flex;
                width: 100%;

                input {
                    display: inline-block;
                    margin-right: 9px;
                }

                label {
                    display: inline-block;
                }
            }
        }

        .export-history {
            width: 20%;
            height: 100%;
            padding-left: 4%;

            text-align: center;

            & a {
                font-size: 15px;

                text-decoration: underline;
            }
        }

        &.align-start {
            justify-content: flex-start;
        }
    }

    .buttons-wrap {
        display: flex;
        flex-direction: column;

        align-items: center;
        margin-top: 20px;

        .btn-primary-new {
            line-height: 14px;
            @include btn-primary-new();
        }
    }
}
