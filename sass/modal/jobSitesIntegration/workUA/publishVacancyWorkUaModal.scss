.publish-work-popup,
.publish-rabota-popup {
    .item-title {
        gap: 4px;
    }

    .modal-dialog {
        width: 100%;
        max-width: 704px;

        .publish-vacancy-work,
        .publish-vacancy-rabota {
            .modal-body {
                .row_wrapper {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    justify-content: space-between;

                    .item {
                        display: inline-flex;
                        flex-direction: column;
                        width: 48%;

                        img {
                            width: 18px;
                            margin-left: 10px;
                            cursor: pointer;
                            filter: $filter-semi-black;
                        }

                        .custom-select-new-wrapper {
                            display: flex;

                            select-single {
                                width: 100%;
                            }
                        }
                    }

                    .currency {
                        &__group {
                            display: flex;
                            gap: 5px;
                            align-items: center;
                            height: 42px;
                            padding: 0 12px;
                            background-color: $main-white;
                            @include border-small;

                            &-input {
                                width: 100%;
                                padding: 0;
                                border: none;
                            }

                            &-divider {
                                height: 50%;
                                margin: 0 5px;
                                border-left: 1px solid $border-grey;
                            }
                        }

                        &__checkbox {
                            display: flex;
                            flex-direction: row;
                            gap: 8px;
                            align-items: center;
                        }
                    }

                    input-component input::-webkit-outer-spin-button,
                    input::-webkit-inner-spin-button {
                        margin: 0;
                        -webkit-appearance: none;
                    }
                }

                .error-text {
                    font-size: 12px;
                    color: $red;
                    opacity: 1 !important;
                }

                .error {
                    .cke_chrome {
                        border: 1px solid $red !important;
                    }
                }
            }
        }
    }
}
