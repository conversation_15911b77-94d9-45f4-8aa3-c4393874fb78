@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.vacancy-pipeline {
    display: flex;
    flex-direction: column;
    width: calc(100vw - var(--sidebar-width) - 30px);
    /*
    *min-height: calc(100vh - 64px - 44px - 104px - 64px - 24px - 24px);
    */
    max-height: calc(100vh - 90px);
    padding: 0;
    margin: var(--main-gap) 0 var(--main-gap) var(--main-gap);
    background: $main-white;
    border: 1px solid $border-grey;
    border-radius: 12px;

    .wrapper-mass {
        display: flex;
        gap: 16px;
        align-items: center;
        justify-content: flex-start;
        padding: 16px;
        border-bottom: 1px solid $border-grey;

        &_empty-candidates {
            border-bottom: none;
        }

        &__system-scope {
            flex-shrink: 0;
            margin-right: 0;
            margin-left: auto;
        }

        button-component {
            button {
                font-size: $secondary-font-size;
                letter-spacing: 0.28px;
            }
        }

        &__text {
            font-size: 12px;
            color: #828282;
            letter-spacing: 0.004em;
        }

        &__actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;

            button-with-icon button {
                font-size: $secondary-font-size;
                letter-spacing: 0.28px;
            }
        }
    }

    .flex-row {
        display: flex;
        flex-direction: column;
        margin: 8px 0;
        overflow: auto;
        overflow-y: hidden;
        transition: 0.3s all;
        scroll-behavior: smooth;

        &:last-child {
            margin-bottom: 8px;
        }

        &::-webkit-scrollbar {
            height: $normal-scrollbar-size;
        }

        &::-webkit-scrollbar-thumb {
            background-color: $dark-grey;
            border-radius: 5px;
        }
    }

    .titles-list {
        display: flex;
        gap: 16px;
        padding: 0 14px;
    }

    .columns-wrapper {
        display: flex;
        min-height: calc(100vh - var(--header-height) - 44px - 104px - var(--header-height) - 24px - 24px - 95px);
    }

    .wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 270px;
        max-width: 270px;
        height: auto;
        margin-right: 16px;
        margin-bottom: 8px;
        background: $main-white;

        &_empty {
            min-width: 180px;
            max-width: 180px;
        }

        &__title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-width: 270px;
            font-size: $main-font-size;
            font-weight: 500;
            line-height: 26px;
            color: $main-black;
            letter-spacing: 0.36px;
            word-break: break-word;

            &_empty {
                min-width: 180px;
            }

            &-text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            &.approved {
                color: $main-green;
            }

            &.refuse {
                color: $red;
            }

            &.selectable {
                padding-right: 58px;
            }

            .contact-icon-position {
                display: flex;

                img {
                    width: 16px;
                    margin-left: 6px;
                    filter: $filter-semi-black;
                }

                .tooltip-white-hint {
                    top: 37px;
                    left: 30px;
                    width: 240px;
                }

                &:hover {
                    .hint {
                        opacity: 1;
                    }
                }
            }

            &.customBorderTop:after {
                position: absolute;
                top: 40px;
                left: -2px;
                width: 279px;
                height: 1px;
                content: '';
                background: $border-grey;
            }
        }

        &__items {
            position: relative;
            z-index: 0.5;
            display: flex;
            gap: 12px;
            justify-content: space-between;
            width: 270px;
            padding: 10px;
            cursor: default;
            border: 1px solid $border-grey;
            border-radius: 8px;
            box-shadow: 0 4px 16px 0 rgba(33, 33, 33, 0.02);

            &__person {
                display: flex;
                flex-direction: row;
                gap: 12px;
                align-items: flex-start;
                justify-content: start;

                &__img {
                    min-width: 38px;
                    height: 38px;
                    border-radius: 50%;

                    .responsible-wrapper {
                        width: 100%;
                        height: 100%;
                        margin-top: 0;
                        overflow: hidden;

                        img {
                            width: 38px;
                        }
                    }
                }

                &__info {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    overflow: hidden;

                    &-name {
                        width: max-content;
                        font-size: $secondary-font-size;
                        font-weight: 600;
                        color: $main-black;
                        letter-spacing: 0.28px;

                        &:hover {
                            color: $yellow;
                        }
                    }

                    &-position {
                        display: -webkit-box;
                        max-height: 32px;
                        overflow: hidden;
                        overflow: hidden;
                        font-size: $small-font-size;
                        line-height: 16px;
                        color: $semi-black;
                        text-overflow: ellipsis;
                        text-overflow: ellipsis;
                        letter-spacing: 0.24px;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                }

                &__checkbox {
                    margin-left: auto;
                }

                &__inner {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                }
            }

            &__info {
                display: flex;
                flex-direction: column;
                gap: 4px;

                &-date {
                    display: flex;
                    align-items: center;

                    span {
                        font-size: $small-font-size;
                        line-height: normal;
                        color: $main-black;
                        letter-spacing: 0.24px;

                        img {
                            margin-right: 0;
                        }
                    }
                }

                .edit-date-pencil {
                    width: 14px;
                    height: 14px;
                    margin-left: 4px;
                    filter: $filter-semi-black;
                }

                &-responsible {
                    display: flex;
                    align-items: center;
                    width: fit-content;
                    font-size: $small-font-size;
                    line-height: 16px;
                    color: $main-black;
                    letter-spacing: 0.24px;

                    span span:first-child {
                        cursor: default;
                    }
                }

                &-days {
                    display: flex;
                    align-items: center;
                    width: fit-content;
                    font-size: $small-font-size;
                    line-height: 16px;
                    color: $main-black;
                    letter-spacing: 0.24px;
                }

                &-archive {
                    bottom: 10px;
                    display: flex;
                    gap: 4px;
                    align-items: center;
                    justify-content: space-between;
                    max-width: 80%;
                    min-height: 30px;
                    font-size: $small-font-size;
                    line-height: 16px;
                    color: $main-black;

                    span {
                        width: 120px;
                    }
                }

                img {
                    width: 18px;
                    margin-right: 8px;
                    filter: $filter-semi-black;
                }
            }

            &__footer {
                display: flex;
                flex-direction: column;
                gap: 8px;
                align-items: flex-end;

                &_archived {
                    padding-bottom: 10px;
                }

                &-comment {
                    margin-right: 1px;
                    cursor: pointer;

                    img {
                        @include control-icon-size;
                        filter: $filter-semi-black;

                        &.active {
                            filter: $filter-main-green;
                        }
                    }
                }

                &-score {
                    display: flex;
                    align-items: end;
                    margin-right: 1px;

                    span {
                        margin-right: 4px;
                        font-size: $secondary-font-size;
                        color: $main-green;
                        letter-spacing: 0.28px;
                    }

                    img {
                        @include control-icon-size;
                        filter: $filter-main-green;
                    }

                    .empty {
                        filter: $filter-semi-black;
                    }
                }

                &-advice-score {
                    width: fit-content;
                    margin-bottom: 10px;
                    margin-left: 8px;
                    font-size: $secondary-font-size;
                    line-height: 19px;
                    color: $secondary-black;
                    letter-spacing: 0.24px;
                }

                &-archived {
                    position: absolute;
                    right: 10px;

                    button-component button {
                        font-size: $secondary-font-size;
                    }
                }

                &-remove {
                    @include control-icon-size;
                    margin-right: 1px;
                    margin-bottom: 0;
                    margin-left: auto;
                    pointer-events: none;
                    filter: $filter-semi-black;
                    opacity: 0;
                    transition: 0.3s all;
                }
            }

            &.selected {
                color: #3c763d;
                background-color: #dff0d8;
            }

            &.my {
                border: 1px solid $main-green;
            }

            &:hover {
                .hint {
                    top: -55px;
                    left: 15px;
                    width: max-content;
                    max-width: 250px;
                    opacity: 1;
                }

                .wrapper__items__footer-remove {
                    pointer-events: inherit;
                    opacity: 1;
                    transition: opacity 0.5s ease 0.2s;
                }
            }
        }

        .left-block {
            display: flex;
            flex-direction: column;
            gap: 10px;
            min-width: 0;
            overflow: hidden;
        }

        @keyframes rotate {
            0% {
                opacity: 0;
                transition: 0.5s all;
            }

            100% {
                opacity: 1;
                transition: 0.5s all;
            }
        }

        &:first-child {
            margin-left: 16px;
        }

        &:last-child {
            margin-right: 16px;
        }

        &.disabled {
            opacity: 0.5;
        }

        &::-webkit-scrollbar {
            width: 5px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: $dark-grey;
            border-radius: 5px;
        }

        .hint {
            position: absolute;
            top: 37px;
            left: 30px;
            z-index: 2147483647;
            width: 240px;
            padding: 12px !important;
            font-size: $secondary-font-size;
            font-weight: 100;
            line-height: 18px;
            color: $main-white;
            word-break: break-word;
            pointer-events: none;
            background-color: $grey-blue;
            border-radius: 8px;
            box-shadow: $border-box-shadow;
            opacity: 0;
        }

        &__switch {
            position: absolute;
            top: -22px;
            right: 13px;
            padding: 0;
        }

        &.customBorderBottom:after {
            position: absolute;
            bottom: 0;
            left: -2px;
            width: 279px;
            height: 1px;
            content: '';
            background: $border-grey;
        }
    }

    .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .switcher {
        padding: 0;

        &:hover {
            cursor: pointer;
        }

        input {
            position: absolute;
            z-index: -10;
            opacity: 0;
        }

        span {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background-color: $main-white;
            border: 2px solid $border-grey;
            border-radius: 4px;
            transition: all 0.1s ease;

            img {
                display: none;
                filter: $filter-main-white;
            }

            &.focused {
                border-color: $semi-black;
            }

            &.checked {
                background-color: $main-green;
                border-color: $main-green;

                img {
                    display: block;
                    width: 14px;
                    height: 14px;
                }
            }
        }
    }

    .candidate-name-wrapper {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    .flame-icon {
        width: 14px;
        vertical-align: text-bottom;
    }
}

.vacancy-pipeline ul[dnd-list] {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 275px;
    height: 100%;
    min-height: 42px;
    padding: 0;
    margin-top: 8px;
    margin-bottom: 0;
    overflow-x: hidden;
    overflow-y: auto;

    &.empty {
        position: relative;
        width: 100%;
        height: 100%;
        margin-left: -5px;
        background-color: $background-grey;
        border: 1px solid $border-grey;
        border-radius: 8px;

        &:before {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 90%;
            font-size: $secondary-font-size;
            color: $secondary-black;
            text-align: center;
            content: attr(data-content);
            transform: translate(-50%, -50%);
        }
    }

    &::-webkit-scrollbar {
        width: $thin-scrollbar-size;
    }
}

.vacancy-pipeline ul[dnd-list] .dndDraggingSource {
    display: none;
}

.vacancy-pipeline ul[dnd-list] .dndPlaceholder {
    display: flex;
    height: 93px;
    min-height: 150px;
    max-height: 250px;
    margin-bottom: 12px;
    background-color: $light-green;
    border-radius: 8px;
}

.right-arrow,
.left-arrow {
    position: absolute;
    top: 60%;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    cursor: pointer;
    background-color: $main-green;
    border-radius: 50%;
    opacity: 0.3;
    transition: opacity 0.3s ease-in;

    img {
        width: 16px;
        filter: $filter-main-white;
    }

    &:hover {
        opacity: 1;
    }
}

.right-side,
.left-side {
    position: fixed;
    top: 25%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    height: 70%;
    font-size: 30px;
    color: #999999;
    list-style-type: none;
    opacity: 1;
}

.right-side {
    right: 27px;
}

.left-side {
    left: calc(0vh + var(--sidebar-width) + 27px);
}

.right-arrow {
    right: 40px;
}

.left-arrow {
    left: calc(0vh + var(--sidebar-width) + 40px);

    img {
        margin-right: 2px;
    }
}

.empty-vacancy-pipeline {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 25px;
    font-size: $heading-small-font-size;
    color: $secondary-black;
}

.comment-wrapper {
    position: absolute;
    top: auto;
    bottom: 50%;
    z-index: 1;
    min-width: 295px;
    max-width: 295px;
    min-height: 80px;
    padding: 12px;
    background: $main-white;
    border: 1px solid $border-grey;
    border-radius: 8px;
    box-shadow: $border-box-shadow;

    &__title {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        &-first {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: $secondary-font-size;
            line-height: 18px;
            letter-spacing: 0.28px;

            span {
                &:first-child {
                    color: $main-black;
                    cursor: pointer;

                    &:hover {
                        color: $yellow;
                    }
                }

                &:last-child {
                    color: $semi-black;
                }
            }
        }

        img {
            position: absolute;
            top: 12px;
            right: 12px;
            @include control-icon-size;
            filter: $filter-semi-black;
        }
    }

    &__body {
        position: relative;
        display: flex;
        justify-content: flex-start;
        max-height: 140px;
        overflow: auto;
        font-size: $secondary-font-size;
        line-height: 18px;
        color: $main-black;
        letter-spacing: 0.28px;
        word-break: break-word;

        &::-webkit-scrollbar {
            width: $thin-scrollbar-size;
        }

        &-input {
            position: static;
            bottom: 0;
        }
    }
}

#cke_ckEditorCandidateComment {
    .cke_top {
        display: none;
    }

    .cke_editable {
        margin: 0;
    }
}

.popover {
    font-size: $secondary-font-size;
    color: $main-black;
    background: $main-white;
    border: 1px solid $border-grey;
    border-radius: 8px;
    box-shadow: $border-box-shadow;

    .arrow {
        display: none;
    }
}
