@import 'js/common/styles/mixins';
@import 'js/common/styles/global-variables.module';

.block-company-public-vacancy.block-public {
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
    background-color: #e3f2dd;

    .no-description-message {
        max-width: 410px;
        margin: 110px auto 180px;

        border: 10px solid #69d46d;
        border-radius: 25px;

        & .circles {
            padding: 11px 20px;

            text-align: right;

            border-bottom: 5px solid #69d46d;

            .circle {
                display: inline-block;
                width: 18px;
                height: 18px;
                margin-right: 7px;
                background-color: #69d46d;

                border-radius: 50%;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        & .message {
            padding: 20px 15px;
            font-size: 17px;

            & .vacancy-position {
                font-size: 18px;
                font-weight: 600;
            }

            & img {
                display: block;
                margin: 25px auto 0;
            }
        }
    }

    .cleverstaff {
        margin: 20px 0 20px;

        text-align: center;

        a {
            color: #202021;
            text-decoration: none;

            &:hover {
                color: #202021;
            }
        }

        span {
            display: inline-block;
            padding: 0;
            margin: 0;
            font-size: 15px;
            line-height: 20px;
        }

        img {
            display: inline-block;
            width: 20px;
            height: 20px;
        }
    }
}

.vacancy-default-template {
    .content {
        display: flex;
    }

    .icon-laptop {
        background-color: $main-green;
    }

    .group-wrapper {
        display: flex;
        align-items: center;
    }

    .back-button {
        display: flex;
        gap: 8px;
        align-items: center;
        width: fit-content;
        color: $main-green;
        cursor: pointer;

        .icon-chevron-left {
            width: 16px;
            height: 16px;
            background-color: $main-green;
        }
    }

    .apply-now {
        display: flex;
        justify-content: center;
        margin-top: 24px;
    }

    .public-vacancy-name {
        font-weight: 500;
        color: $secondary-black;
    }

    .vacancy-header {
        display: flex;
        gap: var(--main-gap);
        align-items: flex-start;
        justify-content: space-between;
        color: $semi-black;

        &__left-side {
            display: flex;
            flex-wrap: wrap;
            gap: var(--main-gap);
            row-gap: 0;
            align-items: center;
        }

        @media (max-width: 600px) {
            flex-direction: column;

            &__left-side {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        &__salary-icon,
        &__flame-icon {
            @include control-icon-size;
        }

        &__position {
            display: flex;
            gap: 8px;
            align-items: center;
            margin: 0;
            font-size: $heading-font-size;
            font-weight: 500;
            color: $main-black;
        }

        &__salary-icon {
            background-color: $main-green;
        }

        &__employment-type,
        &__salary {
            color: $main-green;
        }

        &__salary {
            display: flex;
            gap: 4px;
            align-items: center;
        }
    }

    .apply-button {
        flex-shrink: 0;
    }

    .employment-group {
        display: flex;
        gap: 4px;
        align-items: flex-end;
    }

    .vacancy {
        position: relative;
        width: 100%;

        &__last-update {
            font-size: 14px;
        }

        &__edit {
            position: absolute;
            top: 15px;
            right: 10px;
        }

        &__main-info {
            .group-wrapper {
                flex-wrap: wrap;
            }
        }

        &__hot-icon {
            img {
                margin-right: 5px;
                margin-bottom: 6px;
            }
        }

        &__name {
            margin-right: 15px;
        }

        &__industry {
            height: 20px;
            margin-right: 15px;
        }

        &__location {
            line-height: 14px;
            color: #828282;
        }

        &__employment-type {
            display: flex;
            margin: 5px 0;

            img {
                margin-right: 5px;
            }
        }

        &__salary {
            display: flex;
            color: #202021;
        }

        &__requirements {
            padding: 12px;
            @include border-large;

            .requirements-wrapper {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
                justify-content: space-between;
            }

            .requirements-item {
                display: flex;
                flex-direction: column;
                gap: 4px;

                &__title {
                    margin: 0;
                    font-size: $secondary-font-size;
                    color: $semi-black;
                }
            }
        }

        &__description {
            padding: 12px;
            @include border-large;

            .description-text {
                color: $main-black;
                word-break: break-word;
            }
        }
    }

    .similar-vacancies {
        &__title {
            font-size: $heading-font-size;
            font-weight: 500;
            color: $secondary-black;
        }

        &__items {
            display: flex;
            gap: var(--main-gap);
            padding-bottom: 8px;
            overflow-x: auto;

            &::-webkit-scrollbar {
                height: $normal-scrollbar-size;
            }
        }

        &__item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 220px;
            min-width: 220px;
            padding: 12px;
            text-decoration: none;
            background-color: $background-grey;
            @include border-large;
        }

        &__hot-icon {
            margin-right: 5px;
        }

        &__employmentType {
            color: $main-green;
            text-transform: capitalize;
        }

        &__industry {
            margin-left: 8px;
        }

        &__location {
            color: $semi-black;
        }
    }

    .no-vacancy-wrapper {
        .no-description-message {
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: none;
            padding: 4% 10.5% 10%;
            margin: 0;
            background-color: rgba(255, 255, 255, 0.3);

            border: none;
            border-radius: 15px;

            .message {
                display: flex;
                align-items: center;
                padding: 30px;
                font-size: 15px;
                background-color: #faded8;
                border-left: 2px solid #e75a3a;

                .vacancy-position {
                    font-weight: 600;
                }

                img {
                    margin-left: 6px;
                }
            }
        }
    }
}
