@import 'js/common/styles/global-variables.module';
@import 'js/common/styles/mixins';

.vacancy-page {
    &-right {
        display: flex;
        flex-direction: column;
        width: 30%;

        &-block {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding: 20px;

            &-item {
                width: 100%;
                margin-top: 20px;

                .datePaymentVacancyIcon {
                    opacity: 0;
                }

                .datePaymentVacancy {
                    width: fit-content;
                    height: fit-content;

                    &:hover .datePaymentVacancyIcon {
                        opacity: 1;
                    }
                }
            }

            &-salary {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: space-between;
                width: 100%;

                &-main {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;

                    &-interval {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        width: 60%;
                    }

                    &-divider {
                        width: 30px;
                        height: 1px;
                        margin-right: 10px;
                        margin-left: 10px;
                        background-color: #c0c0c0;
                    }
                }

                &-hide {
                    position: relative;
                    display: flex;
                    margin-top: 20px;

                    &-title {
                        margin-right: 5px;
                    }
                }
            }
        }
    }

    &-left {
        display: flex;
        flex-direction: column;
        width: 70%;
        padding-right: 10px;

        &-block {
            display: flex;
        }
    }

    &-header {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
        margin: 10px 30px 0;

        &-title {
            font-size: 24px;
            font-weight: 500;
        }
    }

    &-wrapper {
        display: flex;
        justify-content: space-between;
        margin: 10px 30px 0;
    }

    &-footer {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    &-block {
        width: 100%;
        padding: 20px;
        margin: 0 0 10px 0;
        background-color: #ffffff;
        border-radius: 5px;
        box-shadow: 0px 1px 10px rgba(59, 89, 152, 0.18);
        transition: all 0.5s;
    }

    &-info {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding: 20px 20px 0;

        &-category {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 35%;
            padding-top: 20px;
        }

        &-main {
            display: flex;
            align-items: flex-start;
            padding-top: 20px;

            &-name {
                width: 30%;
                margin-right: 20px;
                margin-bottom: 5px;
            }
        }
    }

    &-requirements {
        display: none;

        &-header {
            display: flex;
            margin-top: 20px;

            &-wrapper {
                width: 30%;
            }
        }
    }

    &-custom-fields {
        display: none;

        &-item {
            position: relative;
            width: 60%;
            margin-bottom: 10px;
        }
    }

    &-select-industry {
        font-size: 18px;
        letter-spacing: 1px;
    }

    &-item-in-block {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 10px;
        margin-top: 20px;
        box-shadow: 0px 1px 10px rgba(59, 89, 152, 0.18);
    }

    &-title {
        font-size: 12px;
        color: #c0c0c0;

        &-category {
            opacity: 0;
            transition: 0.3s all ease-in;
        }

        &.advice-with-girl {
            color: red;

            &::after {
                display: inline-block;
                width: 20px;
                height: 20px;
                content: url('../images/sprite/smileys.svg');
            }
        }
    }

    &-main-title {
        margin-bottom: 10px;
        font-size: 18px;
        font-weight: 600;
    }

    &-titles-block {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    &-margin-bottom {
        margin-bottom: 20px;
    }

    .title {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .flex-block-title {
        margin-bottom: 10px;
    }

    .vacancy-opacity {
        pointer-events: none;
        opacity: 0.5;
    }

    .hide-close-icon {
        pointer-events: none;
        opacity: 0;
    }

    .hide-add-lang {
        display: none !important;
        pointer-events: none;
    }

    .hide-add-lang-vacancy {
        pointer-events: none;
        opacity: 0 !important;
    }

    .show-vacancy-block {
        display: block;
    }

    .vacancy-test {
        width: 100%;
    }

    .disabled-region {
        pointer-events: none;
        opacity: 0.5;
    }

    .not-allowed {
        cursor: not-allowed;
    }

    region-input.region-container .select2-container .select2-choice .select2-arrow {
        background-color: #f1f1f1;
        border-left: 1px solid #ccc;
    }

    region-input.region-container .select2-container .select2-choice abbr {
        right: 40px;
    }

    region-input.region-container .select2-container {
        margin-bottom: 0;
    }

    .checkbox {
        display: none;

        + label {
            position: relative;
            display: block;
            padding-left: 24px;
            font-size: 14px;
            line-height: 1.5;
            cursor: pointer;

            a {
                color: #2f80ed;
            }

            &:before {
                position: absolute;
                top: 1px;
                left: 0;
                display: block;
                width: 16px;
                height: 16px;
                content: '';
                border: 1px solid #d6e0e7;
                border-radius: 3px;
                transition: all 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            }

            &:after {
                position: absolute;
                top: 4px;
                left: 6px;
                display: block;
                width: 4px;
                height: 8px;
                content: '';
                border-right: 2px solid #ffffff;
                border-bottom: 2px solid #ffffff;
                opacity: 0;
                transition: opacity 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
            }

            &:hover:before {
                border-color: #47ab43;
            }
        }

        &:checked + label {
            &:before {
                background: #47ab43;
                border-color: #47ab43;
            }

            &:after {
                opacity: 1;
            }
        }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        margin: 0;
        appearance: none;
    }

    .vacancy-page-custom-fields-item .form-title {
        word-break: break-word;
    }

    .hoverIcon {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        .form-title {
            word-break: break-word;
        }

        .showIcon {
            position: absolute;
            top: 60px;
            right: 15px;
            margin-top: -31px;
            margin-right: 15px;
            color: $theme-green;

            cursor: pointer;

            opacity: 0;
        }

        .editDate {
            display: flex;
            width: 100%;
            padding: 0;

            i {
                color: $theme-green;
            }

            span {
                cursor: pointer;
            }

            .layout-row {
                width: 100%;

                .md-button.md-icon-button {
                    margin: 0;
                }

                md-input-container.md-icon-float {
                    width: 100%;
                }
            }
        }

        .onlyDate {
            width: 100%;

            .layout-row {
                width: 100%;

                .md-icon-float {
                    width: 100%;
                }
            }
        }

        .onlyDate + .showIcon {
            position: absolute;
            top: 30px;
            right: 15px;
            align-items: center;
            margin-top: 5px;
        }

        &:hover .showIcon {
            opacity: 1;
        }
    }

    .select {
        .select-with-size {
            position: absolute;
            z-index: 9;

            width: 94%;
            height: inherit;
        }

        select {
            @include select-styling();

            padding: 0 0 0 9px;
        }
    }

    .form-control {
        height: 32px;
        padding: 10px 12px;
        border: 1px solid #cccccc;
        border-radius: 5px;

        &:focus {
            outline: none;
            box-shadow: none;
        }
    }

    .error {
        border: 1px solid #b94a48;
        border-radius: 5px;
    }

    &-role-category {
        width: 30%;
        margin-left: 20px;
        opacity: 1;
        transition: all 0.3s ease-in;
    }

    .hide-role-category {
        display: none;

        &:hover {
            cursor: default;
        }

        .select-label.custom-new {
            cursor: default;
        }
    }

    .margin-for-skill {
        margin-bottom: 20px;
    }

    .error-message-block {
        padding-top: 5px;
        padding-bottom: 5px;

        &__right {
            align-self: flex-end;
        }

        .error-message {
            margin-left: 12px;
            line-height: 10px;
            color: $red;
            opacity: 0;
            transition: all 0.3s ease-in;

            &.show-error-message {
                opacity: 1;
            }
        }

        .hide {
            display: none;
        }

        .attach-block {
            width: 45%;
        }
    }

    .static-error-massage {
        margin-top: 5px;
        line-height: 10px;
        color: red;
        opacity: 1;
    }

    .find-button {
        margin-left: 5px;
        color: #cccccc;

        &:hover {
            text-decoration: none;
        }
    }

    .skills-switcher {
        display: flex;
        align-items: center;
        color: #c0c0c0;

        .active {
            color: #000000;
        }

        .sides-margin {
            margin: 0 15px !important;
        }
    }

    input {
        &::placeholder {
            color: #999;
        }
    }

    md-input-container.md-icon-float > label {
        color: #999;
    }

    custom-select-new,
    custom-select-with-autocomplete {
        height: 33px;
        min-height: 33px;
    }

    .hide-on-desktop {
        display: none;
    }

    input.cmn-toggle-round:checked + label:after {
        margin-left: 13px;
    }

    &-description__content {
        display: flex;
        align-items: flex-end;
        height: 25px;
    }
}

.vacancy-page-item-in-block {
    .wrapper {
        display: block;
    }
}

.skills-input-wrapper {
    .select2-results {
        max-height: 170px;
    }

    .select2-search input {
        padding: 4px 40px 4px 5px !important;
    }
}

.select2-drop.select2-drop-above .select2-search input {
    margin-top: 0;
}

.select2-drop.select2-drop-above.select2-drop-active {
    padding-top: 4px;
}

.select2-results li.select2-result-with-children > .select2-result-label {
    font-size: 11px;
}

.select2-system-skill {
    font-size: 12px;
    font-weight: normal;
    color: #0e0e0e;
    letter-spacing: 0.5px;
}

.select2-custom-skill {
    font-weight: normal;
    color: #7e7a7ae6;
}

.vacancy-page-right-block-item {
    .vacancy-select-ops {
        position: relative !important;
    }
}

@media screen and (max-width: 1200px) {
    .vacancy-page {
        &-wrapper {
            flex-direction: column;
            margin: 10px 10px 0;
        }

        &-header {
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            height: 100px;
        }

        &-right {
            width: 100%;
        }

        &-left {
            width: 100%;
            padding-right: 0;
        }

        &-info {
            &-main {
                flex-direction: column;

                &-name {
                    width: 100%;
                }
            }

            &-category {
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-start;
                width: 100%;
            }
        }

        &-requirements {
            &-header {
                flex-direction: column;
                margin-top: 0;

                &-wrapper {
                    width: 100%;
                }
            }
        }

        &-role-category {
            width: 100%;
            margin-left: 0;
        }

        &-select-industry {
            font-size: 14px;
            letter-spacing: 0px;
        }

        &-right-block-salary-hide-title {
            font-size: 10px;
        }

        .hide-on-desktop {
            display: block;
        }
    }

    .hide-on-mobile {
        display: none !important;
    }

    .attach-block {
        width: 100%;
    }

    .title {
        font-size: 12px;
    }

    .vacancy-page-custom-fields-item {
        width: 100%;
    }

    .skills-on-mobile {
        padding: 0;
        box-shadow: none;

        .title {
            font-size: 14px;
        }
    }

    .select-label.custom-new .select-label-text {
        font-size: 10px;
    }

    .select2-container .select2-choice > .select2-chosen {
        font-size: 10px;
    }

    .select2-results li.select2-result-with-children > .select2-result-label {
        font-size: 9px;
    }

    .select2-results-dept-1 .select2-result-label {
        font-size: 9px;
    }

    .vacancy-page .margin-for-skill {
        margin-bottom: 50px;
    }

    .add-skill-on-mobile {
        margin-top: 50px;
    }

    .skill-on-mobile {
        position: absolute !important;
        top: 40px;
        width: 100% !important;
    }
}

.vacancy-description {
    &-right {
        width: 40%;
    }

    &-content {
        &-wrapper {
            display: flex;
            justify-content: space-between;
        }

        &-left {
            width: 50%;
            padding-right: 5px;
        }

        &-right {
            width: 50%;
            padding-left: 5px;
        }
    }

    .hide-on-public-icon {
        width: 20px;
        filter: $filter-semi-black;
    }

    &-main-info {
        display: flex;
        flex-direction: column;
        margin-top: 16px;

        @include default-wrapper();

        .responsible-i {
            position: absolute;
            margin-top: 2px;
            margin-left: 3px;
            background-size: auto !important;
        }

        .vacancy-responsible {
            display: flex;
            flex-direction: column;

            &__titles {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;

                div {
                    display: flex;
                    align-items: center;
                    width: 55%;

                    i {
                        margin-left: 8px;
                    }

                    .tooltip-info-black {
                        width: 350px;
                    }
                }
            }

            .responsible-hint-icon {
                display: inline-block;
                margin-bottom: -5px;
                margin-left: 0;
            }

            &__dropdowns {
                display: flex;
                align-items: center;
                justify-content: space-between;

                width: 100%;
                margin-bottom: 16px;

                &-first {
                    min-width: 48%;
                    max-width: 48%;
                    margin-right: 8px;
                }

                &-second {
                    width: 48%;
                }

                &-remove {
                    margin-left: 8px;

                    img {
                        @include control-icon-size;
                        filter: $filter-close-grey;
                    }
                }
            }

            &__add {
                display: block;
                width: 100%;

                button-with-icon button {
                    width: 100%;
                }
            }
        }

        &-row {
            display: grid;
            grid-template-columns: repeat(4, minmax(100px, 25%));
            gap: 12px;
            align-items: flex-start;
            justify-content: space-between;
            margin-top: 15px;

            @media screen and (max-width: $tablet-width) {
                grid-template-columns: repeat(auto-fit, minmax(max(220px, 48%), 1fr));
            }

            &-item {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: space-between;
                word-break: break-word;
            }

            .text-container {
                display: flex;
                flex-direction: column;
                gap: 8px;
                justify-content: flex-start;
                max-width: 100%;
                text-align: left;

                &__text {
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .more-info {
                @include green-link();
                width: fit-content;
                text-align: left;
            }
        }

        &-block {
            display: flex;
            flex: 1 1 0;
            flex-direction: column;
            gap: 16px;
            justify-content: flex-start;
        }
    }

    .main-title {
        font-size: 18px;
        font-weight: 600;
        line-height: 27px;
        color: #666666;
        letter-spacing: 0.004em;
    }

    .title {
        font-size: 14px;
        color: #838287;
    }

    .responsibleSelectStyles {
        max-width: 200px;
        padding-left: 0;
        margin-left: -4px;
        cursor: pointer;
        @include select-styling();

        .disable {
            color: $theme-light-grey;
        }
    }

    .creator_link {
        @include green-link();
        margin-left: 5px;
    }

    .main-info-item-wrapper {
        display: flex;
        gap: 4px;
        align-items: center;
        line-height: 1;

        .dateCreator {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: start;

            .creator_link {
                margin-left: 0;
                line-height: 1;
            }
        }

        img {
            width: 16px;
            margin-right: 4px;
            filter: $filter-semi-black;
        }

        .cardName {
            max-width: 300px;
            margin-bottom: 5px;
            text-decoration: underline;
            cursor: pointer;
        }

        .score-card-block {
            position: relative;
            display: flex;
            flex-direction: column;

            span {
                margin-bottom: 5px;
            }
        }
    }
}

.custom-field-full-html {
    margin-top: 24px;
    overflow: auto;
    word-break: break-word;
}

.block-vacancy-descr-wrapper {
    width: 60%;
    padding-right: 0;
    margin-bottom: 15px;
}

.margin-for-tab {
    margin-left: 0 !important;
}

.link-to-vacancy-edit-page {
    color: #ffffff;
    text-decoration: none;

    &:hover {
        color: #ffffff;
        text-decoration: none;
    }
}

.vacancy-nav-block {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    margin-right: 30px;

    .improve-vacancy {
        display: flex;
        align-items: center;
        margin: 0;

        &__main-block {
            box-sizing: border-box;
            display: flex;
            gap: 5px;
            align-items: center;
            height: 42px;
            padding: 3px 10px;
            text-decoration: none;
            cursor: pointer;
            background-color: $main-white;
            border: 1px solid $red;
            border-radius: 8px;
        }

        &__text {
            font-size: $main-font-size;
            font-weight: normal;
            color: $red;
            white-space: nowrap;
        }

        &__dancer-icon {
            width: 22px;
        }

        .info-icon {
            margin-left: 8px;
        }
    }

    &:last-child {
        margin-right: 0;
    }
}

@media screen and (max-width: 982px) {
    .vacancy-nav-mobile {
        align-items: flex-start !important;
    }

    .vacancy-page {
        &-left {
            padding: 18px;
        }
    }

    .link-to-vacancy-edit-page {
        margin-bottom: 20px;
    }

    .vacancy-description-right {
        width: 100%;
    }

    .block-vacancy-descr {
        flex-direction: column;
    }

    .block-vacancy-descr-wrapper {
        width: 100%;
    }

    .vacancy-description .responsibleSelectStyles {
        max-width: 100px;
    }

    .vacancy-description-content-wrapper {
        flex-direction: column;
    }

    .vacancy-description-content-left {
        width: 100%;
    }

    .vacancy-description-content-right {
        width: 100%;
    }

    .main-info-on-mobile {
        flex-direction: column;

        .vacancy-description-main-info-row-item {
            margin-top: 10px;
        }
    }
}

.lowPriorityStyles {
    color: #00b549 !important;
}

.mediumPriorityStyles {
    color: #dd8945 !important;
}

.highPriorityStyles {
    color: #ff3333 !important;
}

.greenStatus {
    color: #00b549 !important;
}

.redStatus {
    color: #ef5350 !important;
}

.yellowStatus {
    color: #dd8945 !important;
}

.hide-add-lang {
    display: none !important;
    pointer-events: none;
}

#cke_ckEditorVacancyAll {
    .cke_top {
        display: none;
    }
}

.typeVacancy {
    pointer-events: none;
}

@media (max-width: 576px) {
    .salary-tooltip {
        left: -10px !important;
        width: 250px;
    }

    .vacancy-page {
        .vacancy-lang-title {
            margin-left: 12px;
        }
    }

    .vacancy-page-title.advice-with-girl::after {
        width: 15px;
        height: 15px;
    }

    .vacancy-description-main-info .responsible-i {
        margin-top: -1px;
        margin-left: 0px;
    }

    .vacancy-page-header {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        height: fit-content;

        span {
            margin-bottom: 5px;
        }
    }

    .vacancy-page-titles-block {
        margin-bottom: 10px;
    }

    .vacancy-page .flex-block-title {
        margin-bottom: 15px;
    }

    .vacancy-page-left {
        padding: 0;
    }

    .vacancy-page-wrapper {
        margin: 10px 0 0 0;
    }

    .select2-results-dept-1 .select2-result-label {
        font-size: 14px;
    }
}
@media (max-width: 356px) {
    .vacancy-page-custom-fields-item .form-title {
        font-size: 12px;
    }
}

.checkbox_new_green {
    display: none;

    + label {
        position: relative;
        display: block;
        padding-left: 24px;
        font-size: 14px;
        line-height: 1.5;
        cursor: pointer;

        a {
            color: #2f80ed;
        }

        &:before {
            position: absolute;
            top: 1px;
            left: 0;
            display: block;
            width: 16px;
            height: 16px;
            content: '';
            border: 1px solid #d6e0e7;
            border-radius: 3px;
            transition: all 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
        }

        &:after {
            position: absolute;
            top: 4px;
            left: 6px;
            display: block;
            width: 4px;
            height: 8px;
            content: '';
            border-right: 2px solid #ffffff;
            border-bottom: 2px solid #ffffff;
            opacity: 0;
            transition: opacity 295ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        &:hover:before {
            border-color: #47ab43;
        }
    }

    &:checked + label {
        &:before {
            background: #47ab43;
            border-color: #47ab43;
        }

        &:after {
            opacity: 1;
        }
    }
}

.checkbox_new_green:disabled + label:before {
    background: #c4c4c4;
    border-color: #c4c4c4;
}
