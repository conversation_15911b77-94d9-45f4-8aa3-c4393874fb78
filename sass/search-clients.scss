#clients-search {
    .wrapper-search {
        padding-bottom: 14px;
    }
    .search-advance {
        .search-modal {
            .wrapper {
                .criteria-search-fields {
                    width: calc(38.883% - 10px);
                }
                .search-criteria .mCSB_inside > .mCSB_container {
                    margin-right: 51px;
                }
                custom-select-with-autocomplete-advanced-search span.select-label-text {
                    border: none;
                }
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    #clients-search {
        .search-advance {
            .search-modal {
                .wrapper {
                    .criteria-search-fields {
                        width: calc(50% - 10px);
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 768px) {
    #clients-search {
        .search-advance {
            .search-modal {
                .wrapper {
                    .criteria-search-fields {
                        width: 100%;
                    }
                }
            }
        }
    }
}
