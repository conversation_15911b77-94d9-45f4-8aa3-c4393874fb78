page-intro-component,
candidate-source-stat-page-intro-component {
    display: block;
    width: 100%;

    #intro-section {
        position: relative;
        display: flex;

        align-items: center;
        justify-content: center;
        width: 100%;
        min-height: 32px;
        padding: 0;
        margin: 20px 0;
        background-color: transparent;

        border: none;

        .back-button {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1000;
            width: max-content;
        }

        .back-btn {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1000;
        }

        h1 {
            margin: 0;
            font-size: 24px;

            color: #000;
        }
    }
}

@media screen and (max-width: $screen-xs-max) {
    page-intro-component,
    candidate-source-stat-page-intro-component {
        #intro-section {
            .back-btn {
                min-width: auto;
                height: 100%;
                padding-right: 5px;
                padding-left: 10px;
                margin-left: 10px;
                border-radius: 10px;
            }

            h1 {
                position: absolute;
                right: 0;
                width: calc(100% - 50px);
                text-align: center;
            }
        }
    }
}

.block-title {
    &-small {
        font-size: 14px;
        color: #838287;
    }
}

@media (max-width: 575px) {
    page-intro-component,
    candidate-source-stat-page-intro-component {
        #intro-section .back-btn {
            z-index: 999;
        }
    }
}
