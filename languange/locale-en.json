{"0": "0", "statistics": "Statistics", "calendar": "Calendar", "history": "Activity Log", "you_have_received_a_new_achievement": "You have received a new Achievement!", "award_type": "Award Type", "actions": "Actions", "Congratulations!": "Congratulations!", "closed": "filled", "You have successfully create the vacancy!": "You have successfully created the vacancy!", "Hurray! You received": "Hurray! You received", "Level": "Level", "interviews": "Interviews", "week": "Week", "week_2": "2 weeks", "month_1": "1 month", "month_2": "2 months", "month_3": "3 months", "Make an attachment required when applying for a Public Vacancy": "Make an attachment required when applying for a Public Vacancy", "Share with us your opinion about СleverStaff": "Share with us your opinion about СleverStaff", "How do you rate Cleverstaff in general?": "How do you rate CleverStaff in general?", "Would you recommend CleverStaff to your friends or colleagues?": "Would you recommend <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to your friends or colleagues?", "Reply later": "Reply later", "month": "Month", "year": "Year", "Year": "Year", "years": "years", "Please enter the correct salary values": "Please enter the correct salary values", "user": "User", "user2": "User", "Send an invitation again": "Send an invitation again", "Invited user": "Invited user", "less_than_1_minute": "less than 1 minute", "Not selected": "Not selected", "vacancies_filling_time": "Average time to fill a vacancy", "candidate_added": "Added Candidates", "Enter a value for the drop-down list": "Enter a value for the drop-down list", "You don’t have internet connection": "You don’t have internet connection", "total": "Total", "link": "link", "No_users_2": "0 users found", "You can return to training in the system chat": "You can return to this tutorial in the notifications window.", "Let's take a closer look at what is needed for what. After all, the main work takes place here, right up to the moment the vacancy is closed.": "Let's take a closer look at what is needed for what. After all, the main work takes place here, right up to the moment the vacancy is filled.", "Here are the stages of closing a vacancy. There is a figure opposite each stage, it shows the number of candidates at this stage.": "Here are the stages of filling a vacancy. There is a figure opposite each stage, it shows the number of candidates at this stage.", "The first vacancy has been successfully filled": "The first vacancy has been successfully filled!", "Closing a vacancy": "Filling a vacancy", "You also receive stones at the time of the closing of the vacancy. The number of stones is determined by the complexity of the vacancy and the interviews conducted by candidates": "You also receive stones at the time of the filling of the vacancy. The number of stones is determined by the complexity of the vacancy and the interviews conducted by candidates", "found": "Found", "clients": "Clients", "client": "Client", "researcher": "Researcher", "vacancies": "Vacancies", "limit_10_attachments": "We've set a limit of 10 attachments per day from one IP-address. Unfortunately, you’ve reached the specified limit. Please try again tomorrow.", "removeWrongElementsFromFile": "The file contains invalid elements. Remove them before adding the file.", "active_for_select": {"active": "Active", "inactive": "Inactive", "no_active_responsible": "No active responsible", "no_inactive_responsible": "No inactive responsible", "no_active_users": "No active users", "no_inactive_users": "No inactive users"}, "No_users": "No users", "No_responsible": "No responsible", "candidates": "Candidates", "candidates_2": "Workflow", "candidate": "Candidate", "activity": "Organizer", "contacts": "My contacts", "users": "Users", "search": "Search", "status": "Status", "opening_date": "Opening date", "responsible": "Responsible", "files": "Files", "file": "file", "text_should_be_no_longer_than_100_characters": "Text should be no longer than 100 characters", "information_should_be_given_numbers": "Information should be given in numbers", "add_candidate": "Add candidate", "add_contact": "Add Contact", "add_vacancy": "Add vacancy", "Available Email and/or Skype": "With contacts", "Unavailable Email and/or Skype": "Without contacts", "disconnect_to": "Disconnect", "Remote candidates can not be edited.": "Removed candidates can not be edited", "Remote candidates can not be added to the job": "Removed candidates can not be added to the job. First change status", "phone": "Phone Number", "participation_in_vacancies": "Participation in vacancies", "No matches found": "No matches found", "No matches found. Add email": "No results. You can add email manually", "home_page": "Home page", "public_page": "Public page", "Hide from job portal": "Hide vacancy from job portal", "Job Preview": "Preview", "company_name": "Company name", "Name of department company project": "Name of department / company / project", "solrExceptionClient": "Don't worry! Your clients is fully intact. But at the moment there are temporary difficulties with access to it. Our team is already working on this and in the near future access to your clients will be restored in full. We are sorry for the inconvenience!", "solrExceptionVacancy": "Don't worry! Your vacancies is fully intact. But at the moment there are temporary difficulties with access to it. Our team is already working on this and in the near future access to your vacancies will be restored in full. We are sorry for the inconvenience!", "solrExceptionCandidate": "Don't worry! Your candidate database is fully intact. But at the moment there are temporary difficulties with access to it. Our team is already working on this and in the near future access to your candidates will be restored in full. We are sorry for the inconvenience!", "all_vacancies": "All vacancies", "company": "All data", "Company": "Company", "Transfer_to_vacancy": "Transfer to vacancy", "Move_candidates_to_another_vacancy": "Move candidates to another vacancy", "Candidates has been added to this position": "Candi<PERSON> has been added to this position", "login": "<PERSON><PERSON>", "role": "Role", "longlist": "Long List", "long_list": "Long List", "short_list": "Short List", "shortlist": "Short List", "interview": "Interview", "approved": "<PERSON><PERSON>", "not_a_fit": "Not a fit", "declined_offer": "Declined Offer", "in_candidate": "in candidate", "candidate_2": "candidate", "candidate_3": "candidates", "candidate_4": "candidate", "candidate_5": "to the candidate", "candidate was added to the stage": "Candidate was added to the stage", "send_failure": "Send failure", "edit": "edit", "edit_profile": "Edit profile", "Activity_Log": "Activity Log", "Edit": "Edit", "Edit-field": "Edit", "Edit-stage": "Edit", "male": "Male", "Male": "Male", "female": "Female", "Female": "Female", "experience": "Experience", "work_experience": "Experience", "total experience": "Total work experience", "deadline": "Deadline", "deadline_table": "Deadline", "payment_date": "Payment Date", "Payment was added": "Payment was added", "Add Payment": "Add Payment", "New Payment for": "New Payment for", "The payment value is negative , you have to add a comment": "The payment value is negative , you have to add a comment", "salary": "Salary", "languages": "Languages", "sex": "Gender", "employment_type": "Employment Type", "added_by": "Added by", "added_by_male": "Added by", "for_the_vacancy": "for the vacancy", "Interview for": "Interview for", "region": "Location", "access": "Access", "description": "Description", "Show full description": "Show full description ", "Hide full description ": "Hide full description ", "interview_status": "Stage", "comment": "Comment", "date_of_interview": "Date of interview", "date_of_interview_2": "Date of interview", "vacancy_name": "Vacancy title", "required_experience": "Required experience", "Choose region": "Choose region", "choose_status": "Choose stage", "from": "From", "to date": "To", "Thanks": "Thanks", "position_level": "Qualification", "date_of_birth": "Date Of Birth", "birth_date": "Date Of Birth", "desired_salary": "<PERSON>d <PERSON>", "lives_in": "City ​​of residence", "education": "Education", "Education level": "Education level", "byCurrentTime": "By current time", "vacancies_applied_for": "Vacancies Applied For", "core_skills": "Core skills", "position": "Position", "industry": "Industry", "change_photo": "Change Photo", "delete_photo": "Remove Photo", "upload_photo": "Upload Photo", "This field should contain less than 50 characters.": "This field should contain less than 50 characters.", "This field should contain less than 100 characters.": "This field should contain less than 100 characters.", "This field should contain less than 30000 characters.": "This field should contain less than 3000 characters.", "cancel": "Cancel", "save": "Save", "unsave": "Don't save", "print": "Print", "download_docx": "Download docx", "site_address": "Website URL", "name": "First Name", "change_status": "Change stage", "change_status_1": "Change stage", "date": "Date", "message": "Message", "contact": "Contact", "scope": "Account Visibility Scope", "Account data visibility": "Account data visibility", "scope_message": "Visibility of vacancies, candidates, clients and users", "only_me1": "mine", "only_me2": "(everything I responsible for)", "only_me1 + only_me2": "where I act as responsible (vacancies, candidates, tasks, clients etc)", "Only mine": "My data", "sortingOptions": {"vacancyTitle": "By vacancy title", "location": "By location", "clientName": "By client name", "openingDate": "By opening date", "deadline": "By deadline", "priority": "By priority", "status": "By status"}, "Of all users": "all users", "all": "All", "onlyMy": "My data", "select candidate": "Type candidate's name", "of": "of", "not_searching": "Not searching", "passive_search": "Passive", "active_search": "Active", "only_remote": "Only remote", "reserved": "Reserved", "employed": "Employed", "freelancer": "Freelancer", "Freelancer": "Freelancer", "archived": "Deleted", "invite_users": "Invite new user", "ready_to_relocate": "Ready to relocate", "first_name": "Name", "full_name": "Full Name", "last_name": "Surname", "lastName": "Last Name", "open_vacancies": "Open vacancies", "open": "Open", "expects": "On Hold", "inwork": "In Progress", "payment": "Payment", "completed": "Filled", "canceled": "Canceled", "onhold": "On Hold", "text_for_modal_on_funnel_report": {"report_display": "The report displays the conversion on the vacancy (between the recruiting stages), effectiveness of each stage of the vacancy (will allow you to remove unnecessary elements), to track how many candidates leave each stage.", "effective_funnel": "Effective funnel saves the recruiter’s time.", "funnel_based": "The funnel report is based on the total number of candidates, which were added to the specific  vacancy, and the candidates movements between stages of the recruiting funnel.", "display_total_number": "Displays the TOTAL number of candidates who attended each stage.", "will_not_display": "The funnel WILL NOT display the relevant number of candidates at stages or at a specific stage on a particular day.", "general_rules": "General rules how to maintain and track data in the funnel report:", "rule_first": "Before you start to move candidates between stages in the vacancy, you need to consider on and configure the order of the recruiting stages (from the first one to the last);", "rule_second": "Move candidates through all the stages (top-down);", "rule_third": "If the candidate \"jumped\" through some stages, he will be automatically counted at all the missed stages just like he was there (otherwise the funnel will look like a Christmas tree);", "rule_four": "The report does not contain data from the refusal stages, and the candidate (who was moved to the one of the refusal stages) will be fixed in the report at the last attended stage before he was moved to the refusal stage;", "rule_five": "If the candidate was at the refusal stage, and later you decide to return this candidate to some stage, he will not be double-counted at the stages which he attended before. But will be counted at the “jumped/missed” stages and at stages, where he has not been before.", "please_note": "NB! Please note that if the candidate was removed from the vacancy or from the database, he would not be counted in the funnel report. If the candidate was added to the vacancy, but directly to the refusal stage, he will not be displayed in the funnel, only in the refusal reasons."}, "acquainted": "Accept", "acquainted_2": "Okay", "vacancy_status_assoc": {"open": "New", "onhold": "On Hold", "inwork": "In Progress", "replacement": "Replacement", "payment": "Payment", "completed": "Filled", "canceled": "Canceled", "deleted": "Deleted", "recommendation": "Template"}, "recommendation": "Template", "candidate_status_assoc": {"not_searching": "Not interested", "passive_search": "Passive search", "active_search": "Active", "employed": "Our employee", "freelancer": "Freelancer", "archived": "Deleted", "reserved": "Reserved", "work": "<PERSON><PERSON>", "our employee": "Our employee", "only_remote": "Only remote", "only_relocation_abroad": "Only relocation abroad", "completed": "Completed", "open": "Open", "deleted": "Deleted", "Processing": "Processing", "Finished": "Completed", "Error": "Error"}, "future": "Future", "in_work": "In Progress", "on_hold": "On hold", "all_done": "All Done", "deleted": "Deleted", "client_status_assoc": {"future": "Future", "in work": "In Progress", "in_work": "In Progress", "all_done": "All Done", "all done": "All Done", "canceled": "Canceled", "deleted": "Deleted", "on hold": "On hold", "on_hold": "On hold"}, "Vacancy stages have been changed": "Recruiting stages changed", "interview_status_assoc": {"applied_people": "Applied People", "longlist": "Long List", "approved": "<PERSON><PERSON>", "probation_failure": "Fail of probation", "offer_declined": "Offer declined", "fail_of_probation": "Fail of probation", "sent_offer": "Offer <PERSON>", "accept_offer": "Offer Accepted", "shortlist": "Short List", "interview": "Interview", "notafit": "Not a Fit", "declinedoffer": "Refused", "interview_two": "Interview 2", "test_task": "Test Task", "probation": "Probation", "telephone_interview": "Telephone Interview", "security_check": "Security Check", "job_offer": "Job Offer", "interview_with_the_boss": "Interview with the Boss", "interview_with_the_client": "Customer Interview", "tech_screen": "Tech Screen", "tech_interview": "Tech Interview", "hr_interview": "HR Interview", "no_response": "Not responding", "no_contacts": "No contacts", "": "", "completed": "Completed", "open": "Open", "acceptedCounterOffer": "Accepted counter offer", "foundAnotherJob": "Found another job", "is_not_looking_for_job": "Is not looking for job"}, "interview_status_assoc_full": {"applied_people": "Applied People", "longlist": "Long List", "approved": "<PERSON><PERSON>", "sent_offer": "Offer <PERSON>", "accept_offer": "Offer Accepted", "shortlist": "Short List", "interview": "Interview", "probation_failure": "Fail of probation", "offer_declined": "Offer declined", "fail_of_probation": "Fail of probation", "notafit": "Not a Fit", "declinedoffer": "Refused", "test_task": "Test Task", "probation": "Probation", "telephone_interview": "Telephone Interview", "security_check": "Security Check", "job_offer": "Job Offer", "interview_with_the_boss": "Interview with the Boss", "interview_with_the_client": "Customer Interview", "tech_screen": "Tech Screen", "tech_interview": "Tech Interview", "hr_interview": "HR Interview", "no_response": "Not responding", "no_contacts": "No contacts", "accepted_counter_offer": "Accepted counter offer", "is_not_looking_for_job": "Is not looking for job", "found_another_job": "Found another job", "": ""}, "sent_offer": "Offer <PERSON>", "accept_offer": "Offer Accepted", "notafit": "Not a Fit", "declinedoffer": "Refused", "probation_failure": "Fail of probation", "offer_declined": "Offer declined", "fail_of_probation": "Fail of probation", "test_task": "Test Task", "probation": "Probation", "telephone_interview": "Telephone Interview", "security_check": "Security Check", "job_offer": "Job Offer", "no_response": "Not responding", "no_contacts": "No contacts", "interview_with_the_boss": "Interview with the Boss", "interview_with_the_client": "Customer Interview", "tech_screen": "Tech Screen", "tech_interview": "Tech Interview", "hr_interview": "HR Interview", "Select vacancy stages": "Select recruiting stages", "accepted_counter_offer": "Accepted counter offer", "found_another_job": "Found another job", "is_not_looking_for_job": "Is not looking for job", "You can copy to the new vacancy": "You can copy to the new vacancy the candidates from the selected stages of the current vacancy. These candidates will be added to the 'Long List' stage", "recalls": "Applied People", "applied_people": "Applied People", "previous_history": "Previous history", "previous_history_excel": "Previous", "IT, computers, the Internet": "IT, computers, Internet", "Media, publishing, printing": "Media, publishing, printing", "Accounting, Auditing": "Accounting, Auditing", "Healthcare, hospital": "Healthcare, hospital", "Hotel and restaurant business, tourism": "Hotel and restaurant business, tourism", "Design, creativity": "Design, creativity", "Beauty, fitness and sports": "Beauty, fitness and sports", "Culture, music, show business": "Culture, music, show business", "Logistics, warehouse, Foreign Trade": "Logistics, warehouse, Foreign Trade", "Marketing, Advertising, PR": "Marketing, Advertising, PR", "Medicine, pharmacy": "Medicine, pharmacy", "Real Estate": "Real Estate", "Education": "Education", "Security, Safety": "Security, Safety", "Manufacturing": "Manufacturing", "Sales Jobs": "Sales Jobs", "Retail": "Retail", "Secretariat, outsourcing, ACS": "Secretariat, outsourcing, ACS", "Agriculture, agribusiness": "Agriculture, agribusiness", "Network Marketing and MLM": "Network Marketing and MLM", "Insurance": "Insurance", "Construction and architecture": "Construction and architecture", "Service industries": "Service industries", "Top management, senior management": "Top management, senior management", "Transport, Telecom": "Transport, Telecom", "HR Management, HR": "HR Management, HR", "Finance, bank": "Finance, bank", "Legal": "Legal", "Work without special training": "Work without special training", "Work for students, early career": "Work for students, early career", "Work at home": "Work at home", "Telecommunications": "Telecommunications", "Other areas of activity": "Other areas of activity", "IT Consulting": "IT Consulting", "Aviation & Aerospace": "Aviation & Aerospace", "E-Commerce": "E-Commerce", "Engineering Consulting": "Engineering Consulting", "Engineering Services": "Engineering Services", "Travel & Tourism": "Travel & Tourism", "Automotive": "Automotive", "Select candidate": "Select candidate(s)", "Charity & NGO": "Charity & NGO", "Chemicals": "Chemicals", "Consulting": "Consulting", "Consumer Goods": "Consumer Goods", "Engineering": "Engineering", "FMCG": "FMCG", "Oil & Gas": "Oil & Gas", "Mining": "Mining", "Energy industry": "Energy industry", "Government": "Government", "Sea": "Sea industry", "": "", "no_value": "No value", "industries_assoc": {"IT, computers, the Internet": "IT, computers, Internet", "Media, publishing, printing": "Media, publishing, printing", "Accounting, Auditing": "Accounting, Auditing", "Healthcare, hospital": "Healthcare, hospital", "Hotel and restaurant business, tourism": "Hotel and restaurant business, tourism", "Design, creativity": "Design, creativity", "Beauty, fitness and sports": "Beauty, fitness and sports", "Culture, music, show business": "Culture, music, show business", "Logistics, warehouse, Foreign Trade": "Logistics, warehouse, Foreign Trade", "Marketing, Advertising, PR": "Marketing, Advertising, PR", "Medicine, pharmacy": "Medicine, pharmacy", "Real Estate": "Real Estate", "Education": "Education", "Security, Safety": "Security, Safety", "Manufacturing": "Manufacturing", "Sales Jobs": "Sales Jobs", "Retail": "Retail", "Secretariat, outsourcing, ACS": "Secretariat, outsourcing, ACS", "Agriculture, agribusiness": "Agriculture, agribusiness", "Network Marketing and MLM": "Network Marketing and MLM", "Insurance": "Insurance", "Construction and architecture": "Construction and architecture", "Service industries": "Service industries", "Top management, senior management": "Top management, senior management", "Transport, Telecom": "Transport, Telecom", "HR Management, HR": "HR Management, HR", "Finance, bank": "Finance, bank", "Legal": "Legal", "Work without special training": "Work without special training", "Work for students, early career": "Work for students, early career", "Work at home": "Work at home", "Telecommunications": "Telecommunications", "Other areas of activity": "Other areas of activity", "IT Consulting": "IT Consulting", "Aviation & Aerospace": "Aviation & Aerospace", "E-Commerce": "E-Commerce", "Engineering Consulting": "Engineering Consulting", "Engineering Services": "Engineering Services", "Travel & Tourism": "Travel & Tourism", "Automotive": "Automotive", "Select candidate": "Select candidate(s)", "Charity & NGO": "Charity & NGO", "Chemicals": "Chemicals", "Consulting": "Consulting", "Consumer Goods": "Consumer Goods", "Engineering": "Engineering", "FMCG": "FMCG", "Oil & Gas": "Oil & Gas", "Mining": "Mining", "Energy industry": "Energy industry", "Government": "Government", "Sea": "Sea industry", "": "", "no_value": "No value"}, "Are you sure want delete client": "Are you sure want to delete this client", "Are you sure want delete contact": "Are you sure want to delete this contact", "single_copy": {"copied from vacancy": "copied from vacancy"}, "employment_type_assoc": {"full employment": "Full time", "fullEmployment": "Full time", "underemployment": "Part Time", "remote": "Remote", "trainingAndPractice": "Internship, Practice", "projectWork": "Project", "officeWork": "Office work", "temporaryWork": "Temporary", "relocate": "Relocate", "shiftWork": "shift work/rotational shift work", "val": "", "": "", "hybrid": "Hybrid", "has_test": "Test task is needed", "requires_cover_letter": "Cover letter is required"}, "office required": "A visit to the office is required", "relocate": "Relocate", "full employment": "Full time", "fullEmployment": "Full time", "underemployment": "Part Time", "remote": "Remote", "trainingAndPractice": "Internship, Practice", "projectWork": "Project", "seasonal, temporary work": "Temporary", "temporaryWork": "Temporary", "val": "", "expand": "Expand", "write_a_comment_why_do_you_add_responsible_user": "Your comment (optional)", "write_a_comment_why_do_you_change_candidate_status": "Write a comment about changing the candidate status (optional)", "Write_a_comment_why_do_you_change_vacancy_status": "Write a comment about changing the vacancy status", "Write_a_comment_why_do_you_change_employee_status": "Write a comment about changing the employee status", "delete_responsible": "Removing the responsible", "yes": "Yes", "maybe": "Maybe", "no": "No", "Short list": "Short List", "empty_vacancies": "There are no vacancies in your account", "full_text": "Text", "full_text_2": "Search by name", "More_criteria": "More criteria", "Less_criteria": "Fewer criteria", "Less": "Fewer", "Activity": "Organizer", "Organizer": "Organizer", "Candidates": "Candidates", "Clients": "Clients", "Contacts": "Contacts", "Vacancies": "Vacancies", "Users": "Users", "User": "User", "Add candidate": "Add candidate", "Edit client": "Edit client", "New client": "New client", "desired position": "Desired position", "chose_customer": "Clients", "Add contact": "Add contact", "Edit contact": "Edit contact", "Vacancy": "Vacancy", "Edit vacancy": "Edit vacancy", "Feedback": "Share Your Feedback", "Get candidates from email": "Get candidates from email", "Get candidates from zip": "Get candidates from zip", "css_lang": "en", "invites": "Invited", "share": "Share", "service temporarily unvailable": "Service is temporarily unavailable", "set responsible": "set responsible", "set_status_1": "Candidate ", "set_status_2": " status changed", "was_deleted": "has been deleted", "was_deleted_2": "has been deleted", "Candidate saved": "The candidate saved", "You successfully merged candidates’ profiles": "You successfully merged candidates’ profiles", "candidate not found": "Сandidate not found", "source not found": "Source not found", "client_save_1": "Client ", "client_save_2": " saved", "client not found": "Client not found", "client change state": "The client status changed", "contact not found": "Contact not found", "contact save": "The contact saved", "vacancy change status": "The vacancy status changed", "vacancy set responsible": "The responsible for the vacancy set", "vacancy remove responsible": "The responsible for the vacancy removed", "Vacancy posted on your LinkedIn": "This vacancy shared on your LinkedIn", "Vacancy posted on your Facebook": "This vacancy shared on your Facebook", "vacancy_save_1": "Vacancy ", "vacancy_save_2": " saved", "vacancy not found": "vacancy not found", "User not found": "User not found", "need role": "The special role is needed", "region change": "The region changed", "added_candidate": "Candidate added", "history_info": {"by_employee": "by employee", "employee_deleted": "Candidate was removed from employees", "vacancies_set_responsible_0": "appointed as responsible for the vacancy", "vacancies_set_responsible_1": "appointed as responsible for vacancies", "clients_set_responsible_0": "assigned as responsible for the client", "clients_set_responsible_1": "appointed as responsible for clients", "candidates_set_responsible_0": "appointed as responsible for the candidate", "candidates_set_responsible_1": "appointed as responsible for candidates", "by_client": "by client", "ADDed_in": "Added to", "Added_in": "Added to", "edit_contacts": "The candidate contacts were edited", "had been edited": "contacts were edited", "To_vacancy": "Vacancy", "In__vacancy": "In vacancy", "addeds": "were added to", "added_candidate": "Candidate added", "merged with": "Candidate profile merge: the", "merged_save": "is updated and the", "merged_delete": "is deleted", "coment": "comment", "duplicated_file": "This file is already attached", "added_candidate_from_superJob": "was added from SuperJob", "updated_candidate_from_superJob": "The candidate was updated from SuperJob", "added_candidate_from_cvlv": "was added from cv.lv", "updated_candidate_from_cvlv": "Candidate updated from cv.lv", "added_candidate_from_jobkg": "was added from Job.kg", "updated_candidate_from_jobkg": "The candidate was updated from Job.kg", "added_candidate_from_grc": "was added from GRC", "updated_candidate_from_grc": "The candidate was updated from GRC", "added_candidate_from_rabotauz": "was added from Rabota.uz", "updated_candidate_from_rabotauz": "The candidate was updated from Rabota.uz", "added_candidate_from_linkedin": "was added from LinkedIn", "added_candidate_from_hh": "was added from HeadHunter", "added_candidate_from_dou": "was added from Dou", "added_candidate_from_workua": "was added from Work.ua", "added_candidate_from_olx": "was added from Olx", "added_candidate_from_djinni": "was added from Djinni.co", "added_candidate_from_delucru": "was added from Delucru.md", "added_candidate_from_rabotamd": "was added from Rabota.md", "added_candidate_from_jobCannon": "was added from JobCannon", "updated_candidate_from_jobCannon": "was updated from JobCannon", "added_candidate_from_pracuj": "was added from Pracuj.pl", "added_lowercase": "added", "from_workUa": "from work.ua", "Personal data usage": "Personal data usage", "from rabotaUa": "from robota.ua", "from hh": "from hh", "from grc": "from GRC", "interview_add_from_workUa": "was added to Applied People stage from Work.ua", "interview_add_from_hh": "was added to Applied People stage from grc.ua", "added_candidate_from_rabotaua": "was added from Robota.ua", "added_candidate_from_jobs.tut.by": "was added from jobs.tut.by", "added_candidate_from_email": "was added from email", "updated_candidate_from_email": "Candidate updated from email", "updated_candidate_from_linkedin": "Candidate updated from LinkedIn.", "updated_candidate_from_hh": "The candidate was updated from <PERSON><PERSON><PERSON><PERSON>.", "updated_candidate_from_dou": "The candidate was updated from Dou", "updated_candidate_from_workua": "The candidate was updated from Work.ua.", "updated_candidate_from_olx": "The candidate was updated from Olx.", "updated_candidate_from_djinni": "The candidate was updated from Djinni.co", "updated_candidate_from_pracuj": "The candidate was updated from Pracuj.pl", "updated_candidate_from_rabotaua": "The candidate was updated from Robota.ua.", "changed_status": "Passive search", "edited_information": "The candidate profile was edited", "edited_information_in_candidate": "The candidate info edited", "changed_the_status_of_candidate": "The candidate status changed", "changed_the_status": "Status changed", "added_file": "Added file", "added_link": "Added the link", "was_added": "was added", "in_candidate": "to the candidate", "from_candidate": "from the candidate", "message_to_candidate": "Comment about the candidate", "message_client": "Comment for the client", "removed_file": "Removed file", "removed_link": "Removed the link", "message": "Message", "message_employee": "Employee comment", "add_message": "Added comment", "by_candidate": "about the candidate", "by_candidate_2": "about the candidate", "by_candidates": "about the candidates", "by_1": "about the", "assigned": "assigned", "as_responsible": "as responsible", "Changed the status of the client": "changed the status of the client", "The status of the client": "The status of the client", "was changed": "was changed", "the candidate": "", "the_candidate": "The candidate", "the_candidate_2": "The candidate", "Tag": "Tag", "added": "Added", "candidate": "Candidate", "the_candidate_lowercase": "candidate", "candidates": "Candidates", "candidates1": "candidates", "candidates_1": "candidates", "Added": "Added", "Added1": "Added", "Added(for 1 candidate)": "Added", "client_added": "Client added", "change_client": "Change Client", "state": "state", "change_state": "Change state", "edited_information_in_client": "Edited information in Client", "added_contact": "Added contact", "add_tO_interview": "Add to interview", "candidate_2": "candidate", "in_client": "in client", "Skills from description": "Skills from vacancy description", "You did not specify in the requirements": "You did not specify in the requirements", "How do you look at adding them?": "How do you look at adding them?", "How do you look at adding it?": "How do you look at adding it?", "Thanks, add": "Thanks, add", "No. Save without them": "No. Save without them", "No. Save without it": "No. Save without it", "edited_contact": "Edited contact", "Custom skill should be no more than 50 characters": "Custom skill should be no more than 50 characters", "Custom skill should be no more than 3 words": "Custom skill should be no more than 3 words", "deleted_contact": "Deleted contact", "in_client_v2": "in client", "to_the_interview": "to the", "added_vacancy": "The vacancy added", "edited_vacancy": "The vacancy edited", "Vacancy edited": "Vacancy edited", "Vacancy published": "Vacancy published", "edited_vacancy_short": "The vacancy", "remove": "Remove", "remove2": "removed", "removed": "Removed", "from_responsible": "from responsibles", "to_the_interview_in_vacancy": " to the interview in vacancy", "Please write the attractive description for candidates": "Please write the attractive description for candidates", "added_on": "added on", "added_in": "was added to", "added_in_2": "were added to", "added_in_3": "were added to", "in__vacancy": "In vacancy", "on_vacancy": "stage of the vacancy", "for_vacancy": "for vacancy", "in_vacancy": "in vacancy", "in_vacancy_2": "in vacancy", "was moved from": "was moved from", "was_moved": "was moved", "stage to": "stage to", "to vacancy": " to vacancy", "stage of vacancy": "recruiting stage", "changed_vacancy": "The vacancy changed", "message_to_vacancy": "Comment for the vacancy", "from_responsible_in_vacancy": "from responsible in vacancy", "as_responsible_in_vacancy": "as responsible in vacancy ", "as_responsible_in_candidate": "as responsible in candidate ", "as_responsible_in_client": "as responsible in client ", "client": "Client", "Thanks! The employer has got your application.": "Thanks! The employer has got your request.", "has blocked": " disabled", "has unblocked": " activated", "person change role": "role changed to", "Vacancy statuses has been changed": "Vacancy statuses changed", "Vacancy statuses has been changed2": "statuses changed", "candidate_add_to_group": "was added to the candidate", "candidate_add_to_groups": "was added to the candidates", "candidate_remove_from_group": "was removed from candidate", "from vacancy": "from vacancy", "by recommendation of CleverStaff": "by the recommendation of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Candidate test passed": "finished the", "test": "test", "Test": "Test", "The candidate": "The candidate", "was evaluated in the scorecard": "was evaluated in the", "profile was edited": "profile was edited", "were moved from": "were moved from", "were_moved_from": "were moved", "by": "by", "hide_tab": "has hidden module", "Personal Data Consent Status": "", "Personal Data Consent Status part 2": "Personal Data Consent Status has been changed", "show_tab": "has unabled module", "user": "User", "has removed employee": "has removed employee", "User has removed employee": "User has removed employee", "stage in combination with the email sent": "Stage in combination with the email sent.", "vacancy_hide": "User hided the vacancy", "hide_from": "from general access", "vacancy_show": "User opened the vacancy", "show_too": "for general access", "full in combination with the email sent": "In combination with the email sent.", "User added": "User added", "candidates to": "candidates to", "stage_hist": "stage", "The email has been sent to the candidate": "The email has been sent to the candidate", "user_deleted_a_stage": "User deleted a stage", "with_candidate": "with candidate"}, "Remove Skill": "Remove skill", "information": "Vacancy details", "Thank you": "Thank you", "oF": "of", "new": "New", "new_1": "New", "and": "and", "and2": "and", "from2": "from", "from3": "from", "New personnel": "New personnel", "Gordo.st": "Gordo.st", "with the filing of which we took for the development and implemented soon receive resumes from email-s": " for asking us to develop the technology of receiving resumes from emails", "who believed in CleverStaff early on and really helped his development": " who believed in CleverStaff and really helped to improve it on the first stages", "excellent BizDev, nice person, and our model in": "excellent B<PERSON><PERSON>ev, nice person, and our model in", "for ideological support and assistance": "for ideological support and assistance", "for its strong support and sincere faith in CleverStaff": "for your strong support and sincere faith in CleverStaff", "Singapore": "Singapore", "for your love, support and inspiration": " for your love, support and inspiration!", "With their arrival in CleverStaff appeared function exports candidates of Stff": "With your arrival to CleverStaff we developed the ability to import candidates from Stff", "which is his insistence accelerates the appearance of access (single role) for freelancers and export of candidates for a job": ". Your bright ideas and insistence helped us to implement recruiter-freelancer role and export of candidates to Excel", "She is the author of the idea of how to improve communication with LinkedIn and job search sites, so there was no duplicate candidates": "for your idea how to improve our integration with LinkedIn and job boards to exclude duplications of candidates", "for valuable support of CleverStaff": "for your valuable support of CleverStaff", "of the Netherlands for its support and advice for the development of the system": "from Netherlands for your support and advices about new features for CleverStaff", "We love you, and we will continue to make you happy": "We love you, and we will continue to make you happy", "Team": "Team", "add tags": "Add tags to", "candidate tags": "Tags", "Tag": "Tag", "Tags added": "Tags added", "Tags removed": "Tag removed", "Tags will be added for candidates you choose": "Tags will be added for candidates you choose", "If you want to express our gratitude to the system CleverStaff and her team, simply send us an email": "If you want to express your gratitude to CleverStaff Software and its team, just send us an email on", "Your support, ideas and suggestions are very valuable to us! As you can see, many improvements are made precisely the wishes and advice of our customers and this is just the beginning": "Your support, ideas and suggestions are very valuable for us! As you can see, many improvements are made by wishes and advices of our users", "And if you can write a couple of sentences in English about their impression of CS or what he has become useful to you, we are pleased to see on our website": "And if you can write a couple of sentences about your impression of CS or how useful it was for you, welcome to our page on", "Capterra directory": "Capterra directory", "This will help CleverStaff become known and to help further the number of recruiters worldwide": "It will help CleverStaff to become known and to help more recruiters worldwide", "Change file name": "Change file name", "Change link name": "Change link name", "Delete file": "Delete file", "Delete link": "Delete link", "enter job title": "Enter job title", "add": "Add", "Active vacancies": "Open vacancies", "progress": "Profile completeness", "up to 500": "up to 500", "500-1500": "500-1500", "1500-2500": "1500-2500", "2500-3500": "2500-3500", "3500-4500": "3500-4500", "more": "more", "user_notice": "User", "appreciated_the_candidate": "appreciated the candidate", "evaluate candidate": "evaluate candidate", "removed his evaluation of the candidate": "removed his evaluation of the candidate", "in_the_scorecard": "in the scorecard", "vacancy_change_status": "Change vacancy status:", "add_new_responsible": "Change the responsible", "Adding_new_customer_on_the_job": "Add new сlient", "change_candidate_status": "Candidate change status:", "change_employee_status": "Employee change status:", "you_have_earned_the_secret_achievement": "Opened a new secret achievement", "you_have_earned_the_secret_achievement_f": "Opened a new secret achievement", "adding_candidate": "Adding candidate", "change_client_status": "Client change status", "invitation_user": "Invite new user", "email_invitation_user": "New user's email", "email_invitation_user_2": "Enter new user's email", "invite": "Invite", "choose_client": "Select client", "choose_user": "Choose user", "choose_user2": "Choose responsible", "The field must not be empty or exceed 255 characters": "The field must not be empty or exceed 255 characters", "enter_first_name": "Enter First Name", "make_the_tariff_fixed": "Make the tariff fixed", "Enter_first_name": "Please input First Name field", "Enter_last_name": "Please input Last Name field", "Enter_first_name_and_last_name": "Please input First Name and Last Name fields", "enter_position_name": "Enter candidate's position", "password": "Password", "Confirm password": "Confirm password", "Sign Up and Sign In": "Sign Up and Sign In", "Enter your name": "Enter your name", "Enter your email": "Enter your email", "Enter your phone number": "Enter your phone number", "Enter your password": "Enter your password", "confirm": "Confirm", "Enter your password again": "Enter your password again", "old_password": "Current password", "new_password": "New password", "confirm_new_password": "Confirm new password", "enter_company_name": "Enter the name", "add_comment": "Add Comment", "add_comment_header": "Add comment", "add_vacancy_comment_header": "Add comment for vacancy", "write_a_comment": "Write a comment...", "password_changed": "Password was changed", "add_to_vacancy": "Add to Vacancy", "add_to_vacancy__": "Add a candidate to the vacancy", "add_to_vacancy_": "Add the candidates to the vacancy", "add_to_vacancy_one": "Add a candidate", "adding_candidate_to_vacancy": "Adding candidate to the vacancy", "user_was_invite_1": "The invite to this account has been sent to ", "not_match": "Passwords don't match", "photo": "Photo", "contacts_saved": "Contacts saved", "All_active_clients_vacancies": "All active client's vacancies", "All_inactive_clients_vacancies": "All inactive client's vacancies", "rate": "Rate", "Loading:": "Loading", "move": "Move", "desired_salary_should_contains_only_numbers": "Desired salary should contain only numbers", "interview_times": "Interview time", "or": "or", "Send by email": "Send vacancy via email", "Go to public vacancy": "Share vacancy via link", "advices": "Advices", "responsibles": "Responsible", "optional": "optional", "Welcome to CleverStaff!": "Welcome to CleverStaff!", "Please fill the form below to complete the registration": "Please fill the form below to complete the registration", "invites you at the account of the company": "invites you to ", "ATS & Recruitment Software": "ATS & Recruitment Software", "invites you at the account of the company2": "company account", "Maybe, you have similar candidates in your database": "Maybe, you have similar candidates in your database", "When you add a candidate from LinkedIn / job boards, you merge data from these sources to a candidate profile at CleverStaff.": "When you add a candidate from LinkedIn / job boards, you merge data from these sources to a candidate profile at CleverStaff.", "To unlinck the candidate from his/her CleverStaff profile click on the appropriate icon when pointing on the 'Linked profiles'.": "To unlinck the candidate from his/her CleverStaff profile click on the appropriate icon when pointing on the 'Linked profiles'.", "on our": " on our", "experience_": "Experience", "vacancy_in": "vacancy in", "no experience_": "No experience", "experience_assoc": {"no_experience": "Doesn't know", "no experience": "No experience", "least a year": "at least 1 year", "1-2 years": "1-2 years", "2-3 years": "2-3 years", "3-4 years": "3-4 years", "4-5 years": "4-5 years", "5-10 years": "5-10 years", "over 5 years": "over 5 years", "over 10 years": "10+ years", "": "", "can_be_e00_no_experience": "Possibly without experience", "e00_no_experience": "Without experience", "No_experience": "Doesn't have", "e01_less_than1year": "Less than 1 year", "e1_1year": "1 year", "e2_2years": "2 years", "e3_3years": "3 years", "e4_4years": "4 years", "e5_5years": "5+ years", "e6_10years": "10+ years", "1.year": "years", "5+.year": "years", "2.year": "year", "3-year": "years"}, "experience_autoselection": {"no_experience": "doesn't know", "no experience": "no experience", "least a year": "at least 1 year", "1-2 years": "1-2 years", "2-3 years": "2-3 years", "3-4 years": "3-4 years", "4-5 years": "4-5 years", "5-10 years": "5-10 years", "over 5 years": "over 5 years", "over 10 years": "10+ years", "": "", "e00_no_experience": "(not specified)", "e01_less_than1year": "less than 1 year", "e1_1year": "1 year", "e2_2years": "2 years", "e3_3years": "3 years", "e4_4years": "4 years", "e5_5years": "5+ years", "e6_10years": "10+ years"}, "experience_skills": {"": "", "no_experience": "doesn't have", "e00_no_experience": "(not specified)", "e01_less_than1year": "less than 1 year", "e1_1year": "1 year", "e2_2years": "2 years", "e3_3years": "3 years", "e4_4years": "4 years", "e5_5years": "5+ years", "e6_10years": "10+ years"}, "advancedSearchSkills": {"no_experience": "Doesn't have", "e00_no_experience": "Not specified", "e01_less_than1year": "Less than 1 year", "e1_1year": "1 year", "e2_2years": "2 years", "e3_3years": "3 years", "e4_4years": "4 years", "e5_5years": "5+ years", "e6_10years": "10+ years"}, "Are you sure want delete vacancy": "Are you sure want delete vacancy", "Write a comment why you want remove this candidate": "Write why... (optional)", "Write a comment why you want remove this vacancy": "Write why... (optional)", "Not specified": "Not specified", "Adding a contact person": "Adding a contact person", "Contact person": "Contact person", "Do you want to remove the responsible": "Do you really want to remove the responsible", "from vacancy": "from vacancy", "yes, remove": "Yes, remove", "Remove": "Remove", "Yes, delete": "Yes, delete", "No files": "No files", "Attach file": "Attach file", "Attach file and link": "Attach file or link", "Attach document": "Attach CV", "Editing a contact person": "Editing a contact person", "You can select a photo on your computer": "You can select a photo on your computer", "You can select a logo on your computer": "You can select a logo on your computer", "Or provide a link to photos on the internet": "Or provide a link to photos on the internet", "Or provide a link to logo on the internet": "Or provide a link to logo on the internet", "Add a photo": "Add a photo", "You must specify the position": "You must specify the position", "up to": "Paid till", "up to mobile": "<PERSON>", "on the interview results": "On the interview results", "Write a comment about changing client's status (optional)": "Write a comment about changing client's status (optional)", "You must select a candidate": "You must select a candidate", "Date invitations": "Date of invitation", "Date of registration": "Date of registration", "Date of disabling": "Date of disabling", "Not valid email": "Not valid email", "Able to manage clients and vacancies": "Able to manage clients and vacancies", "Full control on a company account. Able to manage users, clients, vacancies, and candidates": "Full control of company account. Able to manage users, clients, vacancies, and candidates", "Remove the client": "Remove the client", "Remove the vacancy": "Remove the vacancy", "Remove the candidate": "Remove the candidate", "city": "City", "vacancies_users": "User vacancies", "Process": "Process", "Source removed": "Source removed", "Or": "Or", "Upload CV": "Upload resume", "Insert Text": "Insert resume text", "Insert Link": "Import resume/profile via link", "Insert a link to resume online": "Insert the link to the resume online", "Insert resume text into the textarea below and click the Process button": "Insert the resume text into the text area below and click the Process button", "Insert link at site with CV and click the Treat button": "Insert the link to resume online and click the Process button", "Added new data": "Added new data", "This function is not available at the demo version": "This function is not available at the demo version", "Enter a valid url": "Enter a valid url", "Enter a URL name": "Please enter link, started with https://", "Write your comment (optional)": "Write your comment (optional)", "Edit the interview date for": "Edit the interview date for", "Postponement of the date of interview": "Postponement of the date of interview", "Appointed the date of interview": "Appointed the date of interview", "We found small amount of data, it doesn't look like resume.": "We found small amount of data, it doesn't look like resume.", "Incorrect link of LinkedinIn public profile. You can get correct link at the Contact Info.": "Incorrect link of LinkedinIn public profile. You can get correct link at the Contact Info.", "Error invite user": "Your account has reached a limit of 20 invites per day. Please try again tomorrow.", "Only": "Only", "Only_1": "Only", "Only mobile": "", "Only_1 mobile": "", "Export to Excel": "Export to Excel", "History exports to excel": "Log of exports to Excel", "Found on the websites of partners": "on job boards", "Link": "Link", "Age": "Age", "No results on this search params": "No results on this search params", "CleverStaff able to search candidates for the Ukrainian sites Employment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> able to search for candidates for the job boards in Ukraine, Russia, Belarus and Kazakhstan", "From(Age)": "Age: from (years)", "To(Age)": "Age: to (years)", "showMap": "Show map", "Notify the candidate": "Notify candidate via email", "Candidate's full name": "Candi<PERSON>'s full name", "Candidate's email": "Candidate's email", "Select": "Select", "Russia": "Russia", "Ukraine": "Ukraine", "Already have a candidate with such phone": "Already have a candidate with such phone", "Already have a candidate with such email": "Already have a candidate with such email", "Active": "Active", "Inactive": "Inactive", "Disable": "Disable", "Disabled": "Disabled", "Enable": "Enable", "Processing": "Processing", "processing": "processing", "Belarus": "Belarus", "Kazakhstan": "Kazakhstan", "salary_before": "Salary (up to)", "Please enter your password": "Please enter your password", "Password should contain at least one latin letter": "Password should contain at least one latin letter", "Password should contain at least one number": "Password should contain at least one number", "Password must be 8-30 characters long": "Password must be 8-30 characters long", "The password doesn't match to previous": "The password doesn't match to previous", "Incorrect current password": "Incorrect current password", "Password should contain at least one number, one Latin letter and be 8-30 characters long": "Password should be in Latin characters, contain at least one number and be 8-30 characters long", "VK": "VK", "Sign Up": "Sign Up", "You can't change the company, because": "You can't change the company, because", "Blocked": "Disabled users", "User won't be able to sign in and do anything in this account": "User won't be able to sign in and do anything in this account", "User can see your employees": "User can see your employees", "Transfer from the status of approved can only Admin": "Transfer from the status of approved can only Admin", "in vacancy": "in vacancy", "already in the candidate database": "have already been in candidate database of this account", "transfer in": "Transfer in", "Applicant to this vacancy": "Applicant to the vacancy", "send reject": "Send rejection", "by post": "by post", "candidates__": "candidates", "of user": "of the user", "Add Email": "Add <PERSON>", "Updated candidates": "Updated candidates", "Activity History": "Activity Log", "delete": "delete", "Write a comment (not required)": "Write a comment (optional)", "Write a comment why candidate long offer (required)": "Write a comment why candidate declined offer (required)", "Write a comment about changing candidate status to": "Write a comment about changing candidate status to", "(optional)": "(optional)", "last_event": "Last action", "notices": "notices", "month_MM": {"01": "January", "02": "February", "03": "March", "04": "April", "05": "May", "06": "June", "07": "July", "08": "August", "09": "September", "10": "October", "11": "November", "12": "December"}, "Recruiting funnel": "Recruiting Funnel", "Conversion": "Conversion", "Relative conversion": "Relative <br> conversion", "Absolute conversion": "Absolute <br> conversion", "Count": "Count", "Count2": "Number", "min": "min", "No data for display": "No data for display", "sec": "sec", "simpleVacancy": "Simple vacancy", "itVacancy": "IT vacancy", "You can not remove this status because it is there are candidates.": "You can not remove this stage. There are candidates in it.", "You can not remove this refuse because it is there are candidates.": "You can not remove this refuse. There are candidates in it", "Setting the stage": "Edit recruiting stage settings", "settings applied": "Settings have been applied", "No such candidates yet": "No candidates on this stage now", "This candidate was added from resume from email": "This candidate was added from resume from email", "This candidate was updated from resume from email": "This candidate was updated from resume from email", "This candidate was added from resume from excel": "This candidate was added from Excel", "This candidate was added from resume from archive": "This candidate was added from the resume archive", "This candidate was added from JobCannon": "This candidate was added from JobCannon", "only": "only", "only_region": "in region only", "Only_region": "Specific country", "Any": "any", "Yes any country": "Yes any country", "Yes any city": ", any city", "any": "Any", "Any city": "Any", "All": "all", "All Users Actions History": "Activity Log of all users", "history_of_action": "Activity Log", "Activate": "Activate", "Notifications": "Notifications", "You currently have no new notifications": "You currently have no new notifications", "Notifications-on-2-week": "Notifications for two weeks", "Settings": "Settings", "Setting": "Setting", "My Account": "My Profile", "Go to notification history": "Notification history", "active": "(active)", "You entered an incorrect password": "You entered an incorrect password for parsing", "New resumes has been received from email": "New resumes has been received from", "You have not got notifications": "You have no notifications", "Import from": "Import from", "Connect Gmail": "Connect Gmail", "Other": "Other", "Attention": "Attention", "Found": "", "ago": "ago", "Just done": "Just done", "done": "done", "Extension for": "Extension for", "Extension for Chrome": "Extension for Chrome/Edge", "worksite integration": "Website integration", "email integration": "Mail integration", "calendar integration": "Calendar integration", "Information about the company": "Career page", "Additionally": "Additionally", "Demo": "DEMO", "free": "free", "Social Networks": "Social Networks", "browser": "browser", "unread": "unread", "Efficiency": "Efficiency", "Invite": "Invite", "Control": "Control", "view": "View", "Specify": "Set", "in": "in", "at": "at", "parserValidEmail_1": "You email", "parserValidEmail_2": "is connected. Candidates for this account will be synchronized with a resume in incoming emails.", "text": "Text", "accountId": "Account ID", "accountAlias": "Account alias", "cancel_2": "Cancel", "minutes ago": " minutes ago", "just now": "just now", "hour ago": " hour ago", "hours ago": " hours ago", "today": "today", "today at": "today at", "tomorrow": "tomorrow", "Today": "Today", "Yesterday": "Yesterday", "yesterday": "yesterday", "day": "day", "days": "days", "days_short": "d", "days_1": "days", "dayTrial": "day left", "daysTrial": "days left", "days_1Trial": "days left", "Days": "Days", "Days-1": "Days", "Days-2": "Day", "Hours": "Hours", "Hours-1": "Hours", "Hours-2": "Hour", "Minutes": "Minutes", "Minutes-1": "Minutes", "Minutes-2": "Minute", "Opened-dashboard": "Open", "In worked": "Vacancies in progress", "Interviews_title": "Interview", "Interviews": "Interviews", "Interviews-0": "Interviews", "Interviews-1": "Interviews", "Interviews-2": "Interview", "Interviews-3": "Interview", "Select vacancies": "Select vacancies", "in_1": "in", "birthday_2": "Birthday", "country": "Country", "was": "was", "you": "you", "Write an answer": "Write an answer", "Are you sure you want to remove this sticker?": "Are you sure you want to remove this sticker?", "You changed company name": "You changed the company name", "changed the company name": "Changed company name", "The difficulty level of the vacancy has changed.": "The level of difficulty of the vacancy has been changed.", "page": "Page", "from_2": "from", "from_3": "out of", "Integrate_CleverStaff_vacancies_with_Facebook_company_page": "Publish your active vacancies on your company Facebook page", "Create_Jobs_tab": "Create a 'Jobs' tab", "jobs": "Jobs", "Share_on_the_company_page_in_Facebook": "Publish", "Share_on_the_company_page_in_Facebook_1": "Publish once more", "Add_to_Facebook_pages_Jobs": "Publishing the vacancy of company in Facebook", "You_add_this_vacancy_to_the_jobs_list": "You'll add this vacancy to the jobs list on your company's page Facebook on the Jobs tab.", "Choose_company_page": "Choose company page:", "Already_published": "(already published!)", "Go_to_settings": "Go to settings", "The_pages_on_which_you_can_post_your_vacancies": "You can publish vacancies on pages", "transfer": "Transfer", "Configuring_notifications_by_email": "Email notifications", "Email_notifications": "Email notifications", "A_check_mark_means_that_you_want_to_receive_messages_of_this_type": "A check mark means that you want to receive messages of this type", "To_receive_all_messages_from_CleverStaff_add": "To receive all messages from CleverStaff, <br/> add", "in_the_contact_list_of_your_mail": " in the contact list of your mail!", "sendInterviewUpdate": "Notification (in job interviews have changed)", "sendInterviewCreate": "Notification (assigned to the job interview)", "sendInterviewNotification": "Notifications about interviews on my vacancies, which are not assigned by me", "mailDailyReport": "Daily user activity report", "sendVacancyResponsible": "Notifications when I am appointed as responsible for vacancies", "sendNewRecall": "Notifications about new requests added to my vacancies", "Report has been sent": "Report has been sent", "You have already sent daily report today": "You have already sent daily report today, check your email!", "Delete_job_of_your_page_in_facebook": "Remove the job on your page in facebook", "Remove_the_tab_with_your_page_in_facebook": "Remove the tab with your page in facebook", "wrong_email": "Please enter valid email", "login or password incorrect": "Login or Password incorrect", "unknownEmail": "There is no user with this email in CleverStaff", "enter_name": "Please enter your name", "wrong_name": "Please enter valid name", "wrong_lastName": "Please enter valid last name", "enter_company": "Please enter your company name", "enter_password": "Please enter your password", "wrong1_password": "Password should contain only numbers and latin letters", "wrong2_password": "Password should contain at least one latin letter", "wrong3_password": "Password should contain at least one number", "wrong4_password": "Password must be 8-30 characters long", "wrong_password2": "The password doesn't match to previous", "You can transfer him to": "You can transfer him to another stage", "Candidate has been added to this position": "Candidate has been added to this position", "has already been added to this vacancy and he have status": "has already been added to this vacancy and he have status", "You has granted": "You has granted", "role to": "role to", "Get applicants from Social Networks. Share this vacancy on your": "Get applicants from Social Networks!<br/>Share this vacancy on your:", "Help": "Help", "Help your colleague to find candidates for this vacancy. Share it on your": "Help your colleague to find candidates for this vacancy. Share it on your:", "recruter": "Rec<PERSON>er", "admin": "Admin", "Unique visits": "Unique visits", "Applied candidates": "Applied candidates", "Posts in Social Networks": "Posts in Social Networks", "Contact has been deleted": "Contact has been deleted", "Your company": "Your company", "You must set a responsible": "You should set a responsible", "Only by position": "only by position", "by name": "By name", "by position": "By position", "our_base": "Our database", "age_1": "years", "Sending email to the customer_1": "Send candidates for review via email", "Sending email to the customer_4": "Send candidates for client review", "Sending email to the customer_3": "Vacancy", "Sending email to the customer_2": ", client ", "vacancy": "Vacancy", "vacancy_2": "vacancy", "by_vacancy": "by vacancy", "from_vacancy": "from vacancy", "To": "Choose recipients", "From": "From", "From_min": "(min)", "To_max": "(max)", "age to": "To", "Candidate for": "Candidate for", "Candidates for": "Candidates for", "Send": "Send", "Enter Email of recipient": "Enter Email of recipient", "Enter your Email": "Please choose sender's email", "Wrong Email of recipient": "Wrong Email of recipient", "Subject": "Subject line", "Send to the Client": "Send to email", "No results. You can add email manually": "No results. You can add email manually", "template_info_in_send_email_one": "Here will be emails of contact persons of ", "template_info_in_send_email_four": "them first or input any emails by yourself", "template_info_in_send_email_five": "Add", "Please add more detailed job description for candidates before publishing in social networks": "Please add more detailed job description for candidates before publishing in social networks", "Letter sent": "Letter sent", "and another": "and another", "were sent to the Client": "were sent to the client", "was sent to the Client_1": "was sent to the Client", "was sent to the Client": "was sent for review to the Client", "were sent to the Email": "sent to emails", "source_is": "Source", "Enter a source of this candidate": "Enter a source of this candidate", "Enter the desired position of the candidate": "Enter the desired position of the candidate", "words through AND": "words through AND", "words through OR": "words through OR", "exact match": "\"exact match\"", "exact": "\"exact\"", "has already been in your account and now he (she) is disabled. Here you can enable access for him (her)": "has already been in your account and now he (she) is disabled. Here you can enable access for him (her).", "has already been in your account and now he is disabled": "has already been in your account and now he is disabled", "has already been in your account and now she is disabled": "has already been in your account and now she is disabled", "has already working in your account": " has already working in your account", "You have already changed the password on this special link. For safety reasons it can not be done enough on this link. Please ask for a password reset again on": "You have already changed the password on this special link. For safety reasons it can not be done enough on this link. Please ask for a password reset again", "You have already changed the password on this special link": "You have already changed the password on this special link. For safety reasons it can not be done enough on this ", "You have already confirmed your registration at CleverStaff.": "You have already confirmed your registration at CleverStaff.", "You must move one of the candidates to status Hired": "Move at least one candidate to the \"Hired\" stage", "the invitation to the account": "Unfortunately, the invitation to the account", "is_no_longer_valid": "is no longer valid, because it was sent more than 14 days ago. Please contact your colleagues to send you a new invitation.", "user_never_accepted_the_invitation": "Unfortunately, the user never accepted the invitation. The link to the invitation is no longer valid.", "the_user_has_not_yet_accepted_the_invitation": "The user has not yet accepted the invitation.", "the invitation is valid for": "The invitation is valid for 14 days from the date of sending", "change_logo": "Change Logo", "remove_logo": "Remove Logo", "size_290x290": "Size: 290x290", "disable_account": "Disable user", "Hide clients": "Hide clients", "Thank you!": "Thank you for your feedback!", "We appreciate that": "We appreciate that.", "Suggest improvements": "Suggest improvements", "Report a problem": "Report a problem", "Submit": "Submit", "remote_1": "Remote", "Your feedback makes CleverStaff even better": "We will answer your request  as soon as possible.", "Do you have any other questions": "Do you have any other questions?", "Add a screenshot (optionally):": "Add a screenshot or file (optionally):", "What do you LIKE about CleverStaff new design?": "What do you LIKE about CleverStaff new design?", "What do you DISLIKE about CleverStaff new design?": "What do you DISLIKE about CleverStaff new design?", "Upload_image": "Upload from your computer", "Upload_another_one": "Upload another one", "file not selected": "File not selected", "select file": "Select file", "was updated from LinkedIn": "was updated from LinkedIn", "was updated from hh": "was updated from Head<PERSON>unter", "was updated from olx": "was updated from Olx", "was updated from dou": "was updated from Dou", "was updated from pracuj": "was updated from Pracuj.pl", "was updated from grc": "was updated from GRC", "was updated from jobkg": "was updated from Job.kg", "was updated from rabotaua": "was updated from Robota.ua", "was updated from rabotauz": "was updated from Rabota.uz", "was updated from superjob": "was updated from SuperJob", "was updated from workua": "was updated from Work.ua", "was updated from djinni": "was updated from Djinni.co", "was updated from delucru": "was updated from Delucru.md", "was updated from rabotamd": "was updated from Rabota.md", "What would you CHANGE about CleverStaff new design?": "What would you CH<PERSON><PERSON> about CleverStaff new design?", "Thank you so much! We appreciate your feedback": "Thank you so much! We appreciate your feedback", "We almost sure you will have new ideas on how to improve CleverStaff.": "We almost sure you will have new ideas on how to improve CleverStaff.", "Since now, the Feedback Page will be always here to help you share your ideas and attach screenshots in seconds. Just click on a Feedback Button:": "Since now, the Feedback Page will be always here to help you share your ideas and attach screenshots in seconds. Just click on a Feedback <PERSON><PERSON>:", "New ideas appeared? Share them right now": "New ideas appeared? Share them right now", "Suggest improvement": "Suggest improvement", "Return to home page": "Return to home page", "New Design Feedback": "New design: <PERSON><PERSON><PERSON>", "Upload from your computer": "Upload from your computer", "Recruiters": "Recruiters", "Has an access only to vacancies and candidates he/she is responsible for. Free user, unlimited number": "Has access only to vacancies and candidates he is responsible for. Free subscription, unlimited number of subscribers", "Cannot see the full database. Able to manage only clients, vacancies, and candidates he/she is responsible for": "Cannot see entire database. Able to manage only clients, vacancies, and candidates he/she is responsible for", "Upload logo": "Upload logo", "Only admin can set logo": "Only admin can set logo", "Vacancy report": "Vacancy report", "Vacancies report2": "Vacancies report", "Responsible": "Responsible", "Date from": "Date from", "date from": "from", "date to": "Date to", "to-2": "to", "Update data": "Update report", "Download PDF": "Download PDF", "Download Excel": "Download Excel", "Report": "Report on vacancies", "Candidate will be created automatically": "* The candidate will be added to database automatically", "New candidate has been added": "New candidate added", "Reserve": "Candidate in Reserve", "Open in new tab": "Open in new tab", "Add to vacancy": "Add to vacancy", "Edit candidate": "Edit", "Edit comment": "Edit comment", "Remove candidate": "Remove", "has been added to": " added to", "Last comment": "Last comment by", "Unacceptably large number": "Unacceptably large number", "This candidate was added from resume from linkedin": "This candidate was added from linkedin", "This candidate was updated from resume from linkedin": "This candidate was updated from linkedin", "This candidate was added from resume from headhunter": "This candidate was added from headhunter", "This candidate was added from resume from GRC": "This candidate was added from GRC", "This candidate was updated from resume from headhunter": "This candidate was updated from headhunter", "This candidate was added from resume from dou": "This candidate was added from dou", "This candidate was updated from resume from dou": "This candidate was updated from dou", "This candidate was added from resume from pracuj": "This candidate was added from pracuj.pl", "This candidate was updated from resume from pracuj": "This candidate was updated from pracuj.pl", "This candidate was added from resume from olx": "This candidate was added from olx", "This candidate was updated from resume from olx": "This candidate was updated from olx", "This candidate was added from resume from work.ua": "This candidate was added from work.ua", "This candidate was added from resume from djinni.co": "This candidate was added from djinni.co", "This candidate was added from resume from delucru.md": "This candidate was added from delucru.md", "This candidate was updated from resume from delucru.md": "This candidate was added from delucru.md", "This candidate was added from resume from rabota.md": "This candidate was added from rabota.md", "This candidate was updated from resume from rabota.md": "This candidate was updated from rabota.md", "This candidate was updated from resume from work.ua": "This candidate was updated from work.ua", "This candidate was added from resume from rabota.ua": "This candidate was added from robota.ua", "This candidate was updated from resume from rabota.ua": "This candidate was updated from robota.ua", "This candidate was added from resume from super job": "This candidate was added from super job", "This candidate was updated from resume from super job": "This candidate was updated from super job", "This candidate was added from resume from cvlv": "This candidate was added from cv.lv", "This candidate was added from resume from jobsTutBy": "This candidate was added from jobs.tut.by", "This candidate was updated from resume from jobsTutBy": "This candidate was updated from jobs.tut.by", "This candidate was updated from resume from cvlv": "This candidate was updated from cv.lv", "This candidate was added from resume from jobkg": "This candidate was added from job.kg", "This candidate was updated from resume from jobkg": "This candidate was updated from job.kg", "This candidate was added from resume from rabotauz": "This candidate was added from rabota.uz", "This candidate was updated from resume from rabotauz": "This candidate was updated from rabota.uz", "This candidate was added from resume from grc": "This candidate was added from GRC", "This candidate was updated from resume from grc": "This candidate was updated from GRC", "Job_poste_on_the_page_in_Facebook": "Vacancy on the facebook page of a company:", "Remove_Jobs_tab_in_the_settings": "You sure you want to remove integration with Fb?", "can_only_Facebook_a_application": " Remove CleverStaff for Facebook application can only Facebook application settings", "This client has active vacancy": "This client has active vacancies", "You_sure_you_want_to_remove_the_position_of_your_tabs_Jobs_on_Facebook": "You sure you want to remove the position of your tabs Job<PERSON> on Facebook", "Here will be search results": "Here will be search results", "Last actions on my vacancies": "Last actions on my vacancies", "Supported formats": "Supported formats", "Please enter 2 characters": "Please enter 2 characters", "Pay for using CleverStaff": "Payment Page", "CleverStaff is paid for": "The monthly payment is only", "USD per month for each active user": "USD for each active user.", "CleverStaff is paid for-2": "CleverStaff is paid for", "USD per day": "USD per day (", "USD per month for each active user-2": "USD per month) for each active user.", "Months": "Months", "User2": "user", "Users2": "users", "Month2": "month", "month_small": "month", "Months2": "months", "Months3": "months", "Purpose": "Purpose", "Sum": "Sum", "Pay1 discount": "Get your 10% discount by paying 4 months of use in one bill or pay for 12 months and get 20% off.", "Pay2 discount": "Get your 10% bonus by paying 4 months of use in one bill or pay for 12 months and get 20% bonus.", "Amount": "Amount", "Pay for": "Pay for", "Pay": "Pay", "Balance and payment": "Balance and Payment", "For": "For", "For2": "for", "Charge for": "Charge for", "Your archive successfully loaded": "The file has been uploaded successfully", "Company settings": "Company settings", "Bulk resume upload in ZIP-archive": "Bulk resume upload in ZIP-archive", "All resumes will be upload to this account on your behalf": "All resumes will be uploaded to this account on your behalf", "ZIP-archive resume files in doc, docx, pdf, odt. All resumes in one folder": "ZIP-archive of resume files in doc, docx, pdf, rtf. All resumes in one folder", "ZIP-archive resume files in doc, docx, pdf, odt. Resumes grouped into folders by job title or technology": "ZIP-archive of resume files in doc, docx, pdf, rtf. Resumes are grouped in folders by job or technology titles", "ZIP-archive candidates from Stff. This is XML files with names like candidate-0x0A1234E567C890A0.xml": "ZIP-archive candidates from Stff. This is XML files with names like candidate-0x0A1234E567C890A0.xml", "Only resumes files in the archive, no extra files": "Yes, only resume files, without any extra files", "The archive has not only a resumes, need to ignore the extra": "No, not only resumes. Need to ignore excess files", "Choose and download the ZIP-archive": "Choose and download the ZIP-archive", "File name": "File name", "Only recruiters, admins and freelancers can adding candidates in vacancy": "Only recruiters, admins and freelancers can adding candidates in vacancy", "Only recruiters, admins and freelancers can editing candidates": "Only recruiters, admins and freelancers can editing candidates", "Loaded": "Loaded", "Size": "Size", "Status": "Status", "Select the candidate country in the archive:": "Select the candidates' country in the archive:", "If they are not from the same country, you can choose 2 or 3 countries": "If they are not from the same country, you can choose 2 or 3 countries", "Select the format in the archive:": "Choose the format of resumes in the archive:", "The archive has only resumes?": "Does the archive contain only resumes?", "If your resumes folders like in the picture:": "If your resume folders are like in the picture:", "simply pack the root folder in the ZIP-archive. This is a good option": "<PERSON><PERSON> simply pack the root folder to ZIP-archive. This is a good variant", "You can just upload all resumes in one big folder and pack": "You can just upload all resumes in one big folder and then pack it", "If you have any candidates in the program Stff, they can be exported in two steps": "If you have any candidates in the program Stff, they can be exported in two steps", "Create a script export (Menu -> Tools -> Administration -> Other -> Scripts exports). Uploaded types of objects - the candidate. Specify the name of the script and save": "Create a script export (Menu -> Tools -> Administration -> Other -> Scripts exports). Uploaded types of objects - the candidate. Specify the name of the script and save", "Upload (Menu -> Tools -> Export -> Your script that you received from p.1. You will receive a folder with files of the candidate-0x0A1234E567C890A0.xml. All you need to pack a folder in the ZIP-archive and send it here. So the candidates of the Stff will take a CleverStaff.": "Upload (Menu -> Tools -> Export -> Your script that you received from p.1. You will receive a folder with files of the candidate-0x0A1234E567C890A0.xml. All you need to pack a folder in the ZIP-archive and send it here. So the candidates of the Stff will take a CleverStaff", "Candidate removed": "Candidate deleted", "Company name": "Company name", "Candidates from": "Candidates from", "better to add by using CleverStaff extension for": "better to add by using CleverStaff extension for", "Just follow the link": "Just follow the link", "and click 'Save to CleverStaff'": "and click 'Save to CleverStaff'", "Install it now": "Install it now", "Me": "Me", "Search criteria": "Search criteria", "Hidden by hh": "Hidden by hh", "Hidden": "Hidden", "To the website": "To the website", "All the default search criteria": "All default search criterions", "Up to 200 MB per 1 download | Up to 800 MB overall": "Up to 200 MB per 1 download | Up to 800 MB overall", "no more than 200 MB": "no more than 200 MB", "Edited": "edited", "edited_action": "edited", "This feature is available only to administrators and recruiters": "This feature is available only to administrators and recruiters", "This feature is available only to administrators": "This feature is available only to administrators", "Error connecting integrate with email. Connect it again": "Error connecting integrate with email. Please connect it again on", "email-integration-page": "page integrate with email", "items": "items", "employee_status_assoc": {"work": "Employed", "dismiss": "Fired", "vacation": "Vacation"}, "add_employee": "Add employee", "department": "Department", "enter_department": "Enter Department name", "select_department": "Select department name", "enter_position": "Enter Position name", "Delete comment": "Delete comment", "Comment removed": "Comment removed", "Edit post": "Edit Post", "Date of employment": "Date of employment", "Enter the correct date": "Enter the correct date", "Date of the work in the department": "Date of the work in the department", "Date of admission to the post": "Date of admission to the post", "Salary date": "Date of establishment of salary", "Show employees": "Show employees", "Candidate profile": "Candidate profile", "Add a candidate in seconds": "Quick resume adding", "Edit candidate profile in seconds": "Edit candidate profile in seconds", "Salary has been changed": "Salary changed", "Position has been changed": "Position changed", "Department has been changed": "Department changed", "dismiss": "Fired", "Salary is not specified": "Salary is not specified", "Hello": "Hello", "Your role in this account will be": "Your role in this account will be", "it will be your login in CleverStaff": "Your login in CleverStaff", "Add client": "Add a client", "Add vacancy": "Add new vacancy", "Close vacancy": "Fill a vacancy", "Placeholder comment": "Leave a comment if you want", "Close": "Close", "Closed": "Filled", "Accept_1": "Accept", "Left": "Left", "achievements": "achievements", "Congratulations": "Congratulations", "Text for social achieves": "You have completed serious quest in recruitment software https://cleverstaff.net and got the title CleverStaff Guru!", "your profile": "your profile", "News posted on your LinkedIn": "News posted on your LinkedIn", "News posted on your Facebook": "News posted on your Facebook", "News was not published.": "News was not published", "File is loading. Please stay on this page, so as not to interrupt the upload": "File is loading. Please stay on this page in order not to interrupt the uploading", "No connection to the server, check the Internet connection": "No connection to the server, check the Internet connection.", "Attach link": "Attach Link", "Title": "Title", "Mass mailing title": "Mass mailing title", "Show details": "Show details", "Show comments only": "Show comments only", "No candidates for export according to criteria": "No candidates for export according to criteria", "Start Date": "Start Date", "Probationary period": "Probationary period (optional)", "2 weeks": "2 weeks", "1 month": "1 month", "2 months": "2 months", "3 months": "3 months", "Add new custom stage": "Add new custom stage", "common stage": "Common stage (without date)", "interview stage": "Stage with the selection of a date and time (for interviews)", "Add custom stage": "Add custom stage", "Stage name": "Stage name", "Company stages": "Our custom stages", "Our vacancies": "Our Open Vacancies", "Open_vacancies_for_company": "Our job portal", "Additional stages": "Additional stages", "Edit custom stage": "Edit custom stage", "Please note that an additional": "Please note that an additional 1.1% fee of the payment amount will be charged for each payment. Also, your bank may charge an additional fee, including for conversion.", "This status is used in the vacancy": "This stage is used in the vacancy", "it can not be removed": "it can not be removed", "Stage with the same name already exists": "Stage with the same name already exists", "Open date": "Open date", "Report on vacancies": "Vacancies report", "Show candidates in stages": "Show candidates in stages", "Statuses": "Show vacancies status", "Vacancy stages": "Recruiting stage", "Select vacancy statuses": "Select vacancy statuses", "Make a report": "Make a report", "Update report": "Update report", "Standard stages": "Standard stages", "It is necessary to select at least one stage": "It is necessary to select at least one stage", "It is necessary to select at least one status": "It is necessary to select at least one status", "Please fill in all date fields": "Please fill in all date fields", "Make the current set of stages as default for new vacancies": "Make the current set of stages as default for new vacancies", "Default stages for vacancies has been saved": "The default recruitment stages have been saved for new vacancies", "Make default": "Make default", "default": "<PERSON><PERSON><PERSON>", "Date added to database": "By date added to database", "Date of last activity": "By date of last activity", "Date of last comment": "By date of last comment", "Employment start date": "By employment start date", "Relevancy": "Relevancy", "Sort by": "Sort by", "Stages": "Stages", "stage": "stage", "Choose candidates on stage": "Choose candidates on stage ", "Paid user": "Paid subscription", "location": "Location", "By all users": "By all users", "By users": "By users", "Select users": "Select users", "Select user": "Select user", "Directory of units": "Directory of departments", "Add unit": "Add department", "Administration of units": "Administration of departments", "Creating of unit": "Creating of department", "Department successfully created": "Department was created successfully", "Unit name": "Department name", "Department catalog": "Departments catalogue", "Parent unit": "Parent department", "Editing of unit": "Editing of department", "Deleting of unit": "Deleting of department", "This is our employee": "This is our employee", "our employee": " our employee", "Adding an employee": "Adding an employee", "Add task": "Add task", "Run on": "Complete till", "Created": "Created on", "Adding a task": "Add task", "Task text": "Task description", "Task descr": "Task description", "Task": "Task", "Task_v2": "Task", "Call": "Call", "Meeting": "Meeting", "Viewed": "Viewed", "Not viewed": "Not viewed", "Integration_with_Calendar": "Integrate calendar with", "There_are": "When you integrate your CleverStaff account with calendar, it will be impossible to miss", "activate pop-up windows": "Please enable pop-ups, when you integrate your CleverStaff account with a calendar", "interviews_of_your_vacancies": "Scheduled interviews with candidates", "your_tasks": "Your tasks related to candidates, vacancies and clients", "target_dates_deadlines_of_your_vacancies": "Vacancy deadlines", "You_will_be_notified_about": "Once integrated, you will get your CleverStaff events notifications according to your Google / Outlook Calendar settings.", "You_will_be_notified_about_outlook": "You will get notifications regarding your CleverStaff events according to your Outlook Calendar settings", "You_will_be_able_to_turn": "You can turn off the integration anytime you want.", "You_will_be_able_to_create_meetings": "You will also be able to create Google Meet / Microsoft Teams meetings for online interviews.", "Turn_on_the_integration": "Integrate", "with": "with", "by candidate": "about the candidate", "for candidate": "for candidate", "by client": "by client", "by client 2": "by client", "candidate_1": "candidate", "View more": "View more", "Task saved": "Task saved", "Header text": "Task title", "Current company": "Current place of work", "Current position": "Current position", "Tasks": "Tasks", "Tasks_v2": "Tasks", "Please enter a title for the task": "Please enter a title for the task", "Please enter a date": "Please enter a date", "Please enter a date and time": "Please enter the date and time", "Please enter a date and time of interview": "Please enter the date and time of the interview", "to1": "to", "Task created": "Task created", "Task status edited": "Task status \"{{task_name}}\" edited", "Task status edited 2": "Task status edited", "Skill": "Skill", "How many years": "How many years", "Skill is already added": "Skill is already added", "Enter the address": "Enter the address", "Skills with rating": "Skills as rating", "Skill with rating": "Skill as rating", "Skills with text": "Skills as text", "Task status": "Task status", "changed": "changed", "Update candidate by this resume": "Update candidate by this resume", "Merge this profile with another candidate": "Merge this profile with another candidate", "Merge profile with another candidate": "<PERSON><PERSON> profile with another candidate", "Merge": "<PERSON><PERSON>", "Data for the candidate will be permanently overwritten, except for comments. Are you sure you want to refresh the data on this candidate from this file?": "Data for the candidate will be irreversibly overwritten, except for comments. Are you sure you want to refresh the data on this candidate from this file?", "Information about the candidate of the updated file": "Information about the candidate updated from the file", "Information about the candidate": "Information about the candidate", "of the updated file": "updated from the file", "Add a resume and update the description of the candidate": "Add a resume and update the description of the candidate", "Update": "Update", "Attach": "Attach", "This user already responsible for this task": "This user already responsible for this task", "The task must have at least one responsible": "The task must have at least one responsible", "Perfomers": "Performers", "Delete perfomer": "Delete performer", "Delete task": "Delete task", "Add perfomer": "Add performer", "Attachments": "Attachments", "Download file": "Download file ", "Download files": "Download files ", "Reasons for refusal": "Refusal reasons", "Reason for refusal": "Refusal reason", "Add the cause of refuse": "Add refusal reason", "Addind a new cause for refusal": "Adding a new refusal reason", "Editing of cause for refusal": "Stage editing", "Choose the email template": "Choose the email template", "Employee id": "Employee ID", "Show a list": "Show as a single list", "Find": "Find", "Mark all as read": "Mark all as read", "Allowed file formats": "Allowed file formats", "Your company removed": "Your company removed", "Email templates": "Email templates", "Integration with email": "Email integration", "Email Access1": "Import CVs from mailbox", "Email Access3": "Send emails from the system", "Enter_at_least_one_symbol": "Enter at least one symbol", "Preview": "Template preview", "Please delete all comments": "Please delete all comments", "Configure integration with email": "Configure integration with email", "Enter email server settings": "Enter email server settings", "Next": "Next", "Quickly_add_candidate": "Quick resume adding", "Back": "Back", "Ok": "Ok", "Exit": "Log out", "Select email": "Select email", "Send from": "Send from", "Letter subject": "Subject line", "Subject line": "Subject", "Use this template to send the interviw invitation & details when you move candidates to job stages with an interview.": "Use this template to send the interview invitation & details when you move candidates to job stages with an interview.", "Use this template to describe candidates that thay do not meet the vacancy criteria.": "Use this template to describe candidates that they do not meet the vacancy criteria.", "Use this template to send your candidates the letter with the vacancy proposal": "Use this template to send your candidates the letter with the vacancy proposal", "Enter the subject line": "Enter the subject line", "Enter the contact person email address": "Enter the client’s contact person email", "To send a letter from your mailbox, you need to": "To send a letter", "add integration with email": "add integration with email", "Interview_template": "This template is for sending the invitation to an interview. Please specify the place of interview. You can attach the file with the roadmap. This message can be sent while changing candidate stage to the interview", "Refuse_template": "This template is for sending the refuse message to applicants. It can be sent on the applicant page", "See_template": "This email template is for informing candidates about your vacancy. It can be sent on the first tab (vacancy details) of the vacancy page. Press 'Send vacancy by email'", "Complete manual": "Add manually", "enter_the_customer_email": "Enter customer email", "Mass operations": "Bulk operations", "candidateCreateInterviewNotification": "Invitation to an interview", "Invitation to an interview": "Invitation to an interview", "Invitation to an interview of the candidate": "Invitation to an interview", "refuseCandidateInVacancy": "Declined/refused candidates", "seeVacancy": "Vacancy proposal", "Proposal of the vacancy": "Proposal vacancy", "Import from Mail": "Import from e-mail", "Upload resume archive": "Upload resume archive", "Enter a location": "Enter the address", "Character Restriction": "Character limit - 3000 characters", "Add and notify": "Add and notify", "Add and save": "Add and save", "Go to payment service": "Go to payment service", "Go to payment page": "Go to payment page", "We do not have open vacancies at this time": "We don't have open vacancies at this time", "Change stage and notify": "Change stage and notify", "Choose date and time": "Choose date and time", "Choose date": "Choose date", "Cancelled": "Canceled", "notCompleted": "Not completed", "email": "Email", "Accept changes": "Accept changes", "Set interview time": "Set interview time", "The level of match between candidates skills and job requirements": "The match rate of the candidate’s profile to job requirements", "Match rate": "By the match rate", "Added date to vacancy": "By date added to the vacancy", "Date of last action on this vacancy": "By date of last action on this vacancy", "By added user": "By user added", "Upgrade now and you will be able to": "Upgrade now and you will be able to:", "Download report as Excel": "Download report as Excel", "New vacancy": "New vacancy", "Were on the stage": "Were on the stage", "Email templates vacancy": "Email templates", "Upgrade your plan to": "Upgrade your plan to", "Load": "Load", "at our blog": "at our blog.", "email us on": "Email us on", "There are no employees in you account. Start from Departents catalogue": "There are no employees in you account. Start from Departents catalogue.", "New employee": "New employee", "This candidate added to": "This candidate added to ", "active vacancies": " active vacancies", "Here will be the information about your interviews and tasks": "Here will be the information about your interviews and tasks", "No actions on your vacancies yet": "No actions on your vacancies yet", "You successfully integrated your Outlook Calendar with your CleverStaff account": "You successfully integrated your Outlook Calendar with your CleverStaff account", "Now in your Outlook Calendar will be displayed the following events": "Now in your Outlook Calendar will be displayed the following events", "All these events will be displayed only for vacancies you are responsible for": "All these events will be displayed only for vacancies you are responsible for", "Your current role is a 'Hiring Manager'.": "Your current role is a 'Hiring Manager'.", "As a 'Hiring Manager' you can:": "As a 'Hiring Manager' you can:", "See only vacancies you are responsible for.": "See only vacancies you are responsible for.", "See only candidates added to your vacancies.": "See only candidates added to your vacancies.", "Write comments for vacancies and candidates. Recruiters will see your notes.": "Write comments for vacancies and candidates. Recruiters will see your notes.", "Сhange candidates' stages for vacancies. E.G., from 'Long List' to 'Short List' or to 'Not a fit'.": "Move candidates to the next recruiting stage, e.g. from 'Long list' to 'Short list' or 'Not a fit'.", "Your role at your company account could be changed only by Administrator": "Your role at your company account could be changed only by Administrator.", "Upload Excel": "Import from Excel", "Add tasks for vacancies and candidates": "Add tasks for vacancies and candidates.", "Responsible types": "Role in vacancy", "Responsible for a client": "Responsible for a client", "Choose users responsible for this vacancy and functions they perform in it.": "Choose users responsible for this vacancy and the role they perform in it.", "This vacancy role is only a title and does not affect the user role & capabilities in the account.": "This vacancy role is only a title and does not affect the user role & capabilities in the account", "The balance of your company account is": "The balance of your company account is", "Daily payments history of your company account": "Daily payments history of your company account", "Daily payment": "Daily payment", "until": "until", "Able to manage clients, vacancies and candidates": "Able to manage clients, vacancies and candidates", "Enter_new_users_email": "Enter a new user's email and select a role. Once this is done, the user receives an invitation to join your account via email", "Enter_new_users_email_1": "Enter a new user's email. Once this is done, the user receives an invitation to join your account via email. User's role will be", "CleverStaff News Feed": "CleverStaff News Feed", "CleverStaff News Feed shares posts from the CleverStaff Users facebook page. We created the news feed to": "CleverStaff News Feed shares posts from the ‘CleverStaff Users’ facebook page. We created the news feed to", "Share important news, updates and improvements which happen at CleverStaff": "Share important news, updates and improvements which happen at CleverStaff", "Share questionnaires and understand which features to add first and which ones you like": "Share questionnaires and understand which features to add first and which ones you like / dislike", "Build the CleverStaff community where our users can meet, friend and cooperate": "Build the CleverStaff community where our users can meet, friend and cooperate", "You can freely interact with the news feed and post content, which will be published after a review to guarantee its quality": "You can freely interact with the news feed and post content, which will be published after a review to guarantee its quality", "Rules": "Rules", "Here are some rules which make the news feed content useful and interesting": "Here are some rules which make the news feed content useful and interesting", "tech_or_support_requests_1": "Please contact our support at ", "tech_or_support_requests_2": ", if you have any tech or support requests, or want to give feedback, complain about a bug or suggest improvements", "to_share_feedback_1": "Please fill the ", "to_share_feedback_2": ", if you want to share feedback, report a problem, or suggest improvements", "feedback form": "feedback form", "No panic if your post did not publish instantly: we moderate all content. Thats why you can not find any intrusive, annoying and unpleasant content": "No panic if your post did not publish instantly: we moderate all content. That's why you can not find any intrusive, annoying and unpleasant content", "Please use English in group posts and conversations so that everyone could understand you": "Please use English in group posts and conversations so that everyone could understand you", "Thank you for your understanding. We appreciate it": "Thank you for your understanding. :) We appreciate it!", "Info": "Info", "Ooops": "<PERSON><PERSON><PERSON>", "Now you cannot access your profile because the": "Now you cannot access your profile because the", "account on CleverStaff was not paid": "account on <PERSON>lever<PERSON><PERSON>ff was not paid", "Please contact the Admin of this account сompany account, or to a person who invited you to this account": "Please contact the Admin of this account сompany account, or to a person who invited you to this account", "Once this account is paid, you will be able to access to it": "Once this account is paid, you will be able to access to it", "If you have any questions -- email us at": "If you have any questions -- email us at", "Your friends at CleverStaff": "Your friends at CleverStaff", "Please fill at least one of the contacts fields": "Please fill at least one of the 'contacts' fields", "text_should_be_no_longer_than_50_characters": "Text should be no longer than 50 characters", "text_should_be_no_longer_than_300_characters": "Text should be no longer than 300 characters", "text_should_be_no_longer_than_200_characters": "Text should be no longer than 200 characters", "text_should_be_no_longer_than_3000_characters": "Text should be no longer than 3000 characters", "text_should_be_no_longer_than_30k_characters": "Text should be no longer than 30000 characters", "They will be added to the Long List stage, except of candidates previously added for this vacancy": "They will be added to the Long List stage, except of candidates previously added for this vacancy", "support": "Support", "Remark": "Remark", "New payment model for user account": "New payment model for user account", "Why? This will show the full payment history and will dynamically display the prepaid period if the number of account users is changed.": "Why? This will show the full payment history and will dynamically display the prepaid period if the number of account users is changed.", "finish_vacancy": "Deadline", "payment_vacancy": "Payment", "Frequently asked questions": "Frequently asked questions", "Ask question": "Ask question", "Report problem on this page": "Report problem on this page", "Suggest improvement or request feature": "Suggest improvement or request feature", "Enter your question": "Enter your question", "Describe your issue": "Describe your issue", "What could we do improve": "What could we do / improve?", "E.G.: I dont know how to": "E.G.: I don’t know how to ...", "E.G.: Add *** to a Candidates tab": "E.G.: Add *** to a ‘Candidates’ tab...", "Inactive vacancies": "Inactive vacancies", "Sort by relevance impossible until you enter a value in the Text Search": "Sort by relevance impossible until you enter a value in the Text Search", "Clear": "Clear", "Changing the date of interview": "Changing the date of interview", "Profile": "Profile", "Login to Your Account": "Login to Your Account", "If you want to log in, click the button  below:": "If you want to log in, click the button  below:", "Please enter your request": "Please enter your request", "Please describe the issue": "Please describe the issue", "FAQ": "FAQ", "Enter your question or a keyword": "Enter your question or a keyword", "Frequently Asked Questions": "Frequently Asked Questions", "Find your Questions & Answers below": "Find your Questions & Answers below:", "Guide and work in system": "Guide and work in system", "How to add / remove new users": "How to add / remove new users?", "To add a new user go to Company tab → Users → Invite new user. Enter the email and the role for a user in the pop-up and click on Invite": "To invite a user, go to the tab “Account” → “Users” → “Invite a new user”. Enter the email and a role of the new user in the pop-up window and click the “Invite” button", "Once this is done, the new user receives an email with the invitation link to join your company account at CleverStaff": "The new user will receive an email with an invitation to register with the system. New user will receive an access to the system only when he will follow the link in this letter and be registered", "How to set email notifications": "How to set email notifications?", "To set email notifications open your profile menu and choose the Email notifications": "To set up email notifications, open the menu in the upper right corner and select Notification Settings. There you can enable or disable some notifications to your email, as well as request by yourself to send a daily report", "How can I publish vacancies in social networks": "How to publish vacancies in social networks?", "Choose a vacancy and go to Vacancy details. Find the Social networks block and choose a network to share the vacancy in a timeline:  Facebook, LinkedIn, Twitter, Vkontakte": "To publish a vacancy, you should go to the vacancy page and go to its description. On the right there are buttons for posting the vacancy in your social news feed", "Networks": "Networks: Facebook, LinkedIn, Twitter, Vkontakte.", "Also, you could share your vacancies at your company Facebook page at Comapany → Company settings → Create a Jobs Tab": "Also, you can post a vacancy on your company’s Facebook page", "How to show/restore deleted/cancelled vacancies": "How to show/restore deleted/cancelled vacancies?", "If you want to show/restore deleted/cancelled vacancies go to Vacancies and choose the Deleted/Cancelled": "To display deleted / closed vacancies, select “Advanced search” on the “Vacancies” tab, then select the “Removed / Closed” status criteria", "Binotel": "Integration with the Binotel IP-telephony service", "Integration with telephony allows you to": "Integration with telephony allows you to:", "Integration first": "1) to make calls by clicking on the phone number in the candidate's profile;", "Integration second": "2) open the candidate's profile in the moment of an incoming call from the phone number specified in the system.", "Configure integration": "To configure the integration, you need to connect IP telephony from Binotel in a Pro level package or higher by following the link", "How binotel": "How to do it:", "_bfirst": "1) on the website", "bfirst_": "click on the call button in the lower right corner of the screen and wait for the call from Binotel specialists for 30 seconds to create an account;", "bsecond": "2) or, if you already have an account, call Binotel 044 333 40 23 technical support number with a request to set up integration of your Binotel account with CleverStaff account.", "Payment, plans, and agreements": "Payment, plans, and agreements", "What are the payment options for using the system": "What are the payment options?", "How to pay": "You need to select the number of paid users in your account and specify the number of months to pay. The system will automatically calculate the required amount. After you can choose the payment method", "tab": "tab", "Has an access only to vacancies he/she is responsible for and only to candidates added by this specific user. Cannot see other users. Paid user": "Access only to vacancies for which he / she is responsible and only to candidates added by himself / herself. Does not see other users. Paid user.", "Is it possible to sign an agreement? What do we need to make it possible": "Is it possible to sign an agreement? What do we need to make it possible?", "Which currency should we use to pay": "Which currency should we use?", "CleverStaff prices are in USD but you could pay in any currency according to the USD rate of the National bank of your country": "Payment by credit card is made in a comfortable currency for you with the reference to the exchange rate of the National Bank of Ukraine. You can create a bill (invoice) in 4 currencies: UAH, USD, EUR and RUB", "Data security and users access levels": "Data security and users access levels", "Are there any warranties that our database won’t be used by the third parties": "Are there any warranties that our database won’t be used by the third parties?", "We use the SaaS model (Software as a service): this means we provide software only. Our users use their own database. If the user agreement is canceled, we'll provide you full export of your candidates database": "We use the SaaS model (Software as a service): this means we provide software only. Our users can access only their own database. If the user agreement is canceled, we'll provide you full export of your candidates database.", "We build our business on our users trust. We NEVER share user databases with the third parties. In a case, if we could share databases, everyone would know about it: in our business the loss of reputation means the loss of customers": "We build our business on our users trust. We NEVER share any databases with the third parties. In a case, if we could share databases, everyone would know about it: in our business the loss of reputation means the loss of customers.", "Are there any user roles and access limitation for then": "Are there any user roles and access limitation for then?", "Able to manage clients, vacancies and candidates. Paid user": "Manage clients, vacancies, and candidates. Paid user.", "Full control on a company account. Able to manage users, clients, vacancies, and candidates. Paid user": "Full control of company account. Manage users, customers, vacancies and candidates. Paid user.", "Has an access only to vacancies and candidates he/she is responsible for. Free user. You can add as many Hiring Managers as you want": "Access only to vacancies and candidates for which he / she is responsible. Free user, unlimited additions.", "Cannot see the full database. Able to manage only clients, vacancies, and candidates he/she is responsible for. Paid user": "Does not have access to the entire database of the company. Manage only clients, vacancies, and candidates for which he is responsible. Paid user.", "Cannot see the full database and other users. Able to see only vacancies he/she responsible for and candidates he/she added": "Cannot see entire database and other users. Able to see only vacancies he is responsible for and candidates which he adds", "How to custom the scope of visible data if I work in multiple CleverStaff accounts": "How to custom the scope of visible data if I work in multiple CleverStaff accounts?", "If you have multiple CleverStaff accounts you could switch them being logged in. Click on the Scope button in the upper left corner of the interface and choose the account you need. Once this is done, you will see all candidates, vacancies and other data for this account": "If you have multiple CleverStaff accounts, you can switch between them without logging out. To do this, open the menu in the upper right corner and select “account visibility”. In the new window, you can choose another account", "Database export / import and data migration": "Database export / import and data migration", "Is it possible to manage our employees database? Any HR module available": "Is it possible to manage our employees’ database? Any HR module available?", "You can add an additional HR module for CleverStaff. This module helps you track & manage your company employees. Contact our support to add this module": "Additionally, you can install an HR module to the system. This module is designed for employees in your own company. To add this module, write a request to the technical support in any convenient way", "via Support form in the header": "using the integrated chat in the lower right corner", "email us at": "at an email", "email us at 1": "email", "call us at": "call us at", "call us at 1": "call", "The module will be activated in 1-3 hours. Once it’s active, your will notice Employees button (visible for Admin role only) at your Company settings tab": "The module will be activated during one day, after payment for the module integration. After activation, the “Employees” tab will appear in the Account section, which will be visible only to users with the “Admin” role. Each Admin can enable the module itself and for other users, as well as disable it if necessary", "How to migrate the candidates database to CleverStaff": "How to migrate the candidates’ database to CleverStaff?", "You can import the candidates database yourself at Candidates tab → Our database → Bulk import → click on": "The base with the candidates can be imported independently in the tab “Candidates” → “Our database” in the “Bulk operations” block:", "Upload CVs one-by-one via Upload resume button": "Upload CVs one-by-one via “Upload resume” button", "Upload resume archive to import the upload the archive with your candidates resumes in ZIP / RAR. You can add resumes in .doc, .docx, PDF and .odt formats": "“Upload resume archive” to transfer the archive with your candidates' zip / rar CVs. CVs can be in .doc, .docx, PDF and .odt formats", "Import from Excel to upload an Excel file with your candidates data": "“Import from Excel”, to upload an Excel file with your candidates’ data", "If your base is": " ", "Can I upload CVs from email? How to set the integration": "Can I upload CVs from email? How to set the integration?", "To integrate with email go to Candidates tab → Import from e-mail and enter the data for successful integration": "To integrate email, open the menu in the upper right corner and select “Email Integration”. In the new window, click the “integrate mailbox” button", "If you have a corporate email, probably you should input the smtp host, port, secure data which you could receive from your system administrator.": "If you have a corporate mail, most likely you need to enter the data smtp host, port, secure. You can get this information from your system administrator.", "Other questions": "Other questions", "We want to customize the CleverStaff for our company": "How to customize the system for our company needs?", "Our team constantly improves the system. All improvements are discussed with the": "The team is constantly working to improve CleverStaff. All improvements are consistent with the development team, depending on the complexity of the improvements", "There are two CleverStaff versions: the Cloud and the Enterprise ones": "There are 2 versions of our software: cloud and server. If we are talking about the cloud version, it all depends on the nature of the improvements and their usefulness to other users. Improvements to the server version occur individually at the request of users", "We are always waiting for your ideas & suggestions: email us at": "We always welcome your suggestions and ideas! Email us at", "What are the price and terms of CleverStaff customizations": "What are the price and terms of CleverStaff customizations?", "or chat": "or by the integrated chat in the lower right corner", "The price and the terms of customizations depend on the plan you choose, on the number and the complexity of customizations": "The cost and timing of improvements depend on the amount of improvements, their complexity and the timing of implementation.", "To answer those questions we need more details -- please email us at": "To answer these questions, it is important for us to understand your request in more detail. To do this, email us at", "What is the cost of an Enterprise (server) CleverStaff version? Is it possible to customize it if we buy it? What are the rates for the system support after we buy the system?": "How much does the Enterprise (server) CleverStaff version cost?", "The cost of cost of an Enterprise (server) version we discuss individually: email us at": "We will discuss the cost of the license individually: email us at", "and we instantly contact you": "and we will contact you immediately", "After you buy an Enterprise version it belongs to you on 100% and you can customize it in any way. Or, we can do it for you -- it will be an additional option": "After purchasing the server version, it is completely at your disposal. You can make adjustments at it at your discretion. Or, we can do this for you with an additional cost", "What is the system performance and the fault tolerance": "What is the system performance and the fault tolerance?", "CleverStaff works reliably. The cloud version has a load balancing between different servers. The algorithms are well-optimized for huge volumes of data and concurrent users": "CleverStaff works reliably. The cloud version has a load balancing between different servers. All algorithms are well-optimized for huge volumes of data and concurrent users", "We regularly run the load testing and check if the system works quickly, reliably and smoothly in the environment of constant load growth": "We regularly run the load testing and check if the system works quickly, reliably and smoothly in the environment of constant load growth.", "This invitation link is incorrect or was sent by mistake.": "This invitation link is incorrect or was sent by mistake.", "Please ask your account Admin to send a new one.": "Please ask your account Admin to send a new one.", "Didnt find the answer?": "Didn't find the answer?", "Please enter your proposal": "Please enter your proposal", "HR Module": "HR Module", "One common place for recruiters and HRs": "One common place for recruiters and HRs", "Hire people and store your employees’ personal data with all records and documents in one place": "Hire people and store your employees’ personal data with all records and documents in one place", "Directory of Departments": "Directory of Departments", "Employees’ personal profiles": "Employees’ personal profiles", "Combine recruiting and HR": "Combine recruiting and HR", "Add units, set their hierarchy and attach employees": "Add units, set their hierarchy and attach employees", "Keep all the information, skills, contacts, and documents in the personal profile employee": "Keep all the information, skills, contacts, and documents in the personal profile employee", "Add hired employees in 1 click": "Add hired employees in 1 click", "Do not separate the recruiting and HR management": "Do not separate the recruiting and HR management", "Activate the HR module for $200 / year": "Activate the HR module for $200 / year", "Pay and activate HR Module": "Pay and activate HR Module", "Log in or refresh the page if you are logged in on a different tab": "<span>Log in or refresh the page</span>if you are logged in on a different tab", "Passwords strength": "Passwords strength", "characters": "characters", "CleverStaff browser extension to integrate & save candidates from LinkedIn and job boards in 1 click": "CleverStaff browser extension to integrate & save candidates from LinkedIn and job boards:", "This filter 'by date' in the report shows the last date when a candidate was moved to a new vacancy stage.": "This filter 'by date' in the report shows the last date when a candidate was moved to a new recruiting stage.", "The filter is not applied for dates of vacancy creation and date when a candidate was added to a vacancy.": "The filter is not applied for dates of vacancy creation and date when a candidate was added to a vacancy.", "Please note": "Please note:", "Please note!": "Please note!", "Invite User / Client": "Invite user", "Sure! We could send you our standard agreement. Please send us the following details at": "Sure! We could send you our standard agreement. Please send us the following details at", "Bank details": "Bank details", "CEO name": "CEO name", "Estimated agreement term": "Estimated agreement term", "The number of users": "The number of users.", "Author": "Author", "add_this_task": "Task added", "The task due date edited": "The task due date edited:", "The task due date editedV2": "The task due date edited", "Task edited": "Task edited", "add performer": "Performer added:", "remove performer": "One of task performers deleted:", "Task status changed": "Task status changed", "All actions": "All actions", "All actions-v2": "All actions", "Comment added": "Comment added", "Balance": "Balance", "Once you integrate your email, candidates CVs sent to your email inbox will be added the candidates database of your company": "Once you integrate your email, candidates' CVs sent to your email inbox will be added to the candidates' database of your company.", "The synchronization runs 1 time per hour": "The synchronization runs 1 time per hour.", "To set the integration click on Integration with email and follow the instructions. You will have to type the SMTP Host and SMTP Port of your email. You could request this data from your system administrator": "To set the integration click on 'Integration with email' and follow the instructions. You will have to type the SMTP Host and SMTP Port of your email. You could request this data from your system administrator.", "Email log displays all your email correspondence with a candidate via email import": "“Email log” displays all your email correspondence with a candidate via email import", "This feature works only after you integrate your email with your CleverStaff account. If you did not integrate your email, do it here: https://cleverstaff.net/!#/email-integration": "This feature works only after you integrate your email with your CleverStaff account. If you did not integrate your email, do it here: https://cleverstaff.net/!#/email-integration", "CleverStaff updates the email history 1 time per 1 hour. The time and date of the last integration + the number of imported emails are listed below": "CleverStaff updates the email history 1 time per 1 hour. The time and date of the last integration + the number of imported emails are listed below", "For account Admins only": "For account Admins only", "Read FAQ first": "Read FAQ first", "Instant support at Skype": "Instant support at Skype", "Account": "Account", "Account name": "Account name", "account users": "account users", "entire account": "entire account", "Account settings": "Account settings", "Website": "Website", "Facebook page": "Facebook page", "Send email": "Send email", "Please add candidates’ email first": "Please add candidate’s email first", "Please add clients’ email first": "Please add clients’ email first", "Sign in with": "Sign in with", "Enter the data": "Enter the data", "This feature works only after you integrate your email with your CleverStaff account": "This feature works only after you integrate your email with your CleverStaff account", "Integrate with email": "Integrate with email", "CVs added": "CVs added:", "After changing the user role to hiring manager you won't be able to return the role within 7 days": "After changing the user role to hiring manager you won't be able to return the role within 7 days", "After disable user you won't be able to enable user within 7 days": "After disable user you won't be able to enable user within 7 days", "After enable user you won't be able to disable user within 7 days": "After enable user you won't be able to disable user within 7 days", "Incorrect phone number": "Incorrect phone number", "Linked profiles": "Linked profiles:", "Show profile": "Show profile", "Unlink profile": "Unlink profile", "Last active stage": "Last active stage", "Previous active stage": "Previous active stage", "Candidates added": "Candidates added", "Last date": "Last date", "Refused candidates": "Declined candidates", "Client is an organization, department or project with a list of vacancies to be filled": "Client is an organization, department or project with a list of vacancies to be filled. ", "To add a vacancy, you need to create a Client first": "To add a vacancy, you need to create a Client first. ", "Fill Clients; profiles and you will be able to see their active/inactive vacancies' list, description, contacts, status, attachments, and responsible users": "Fill Client profiles and you will be able to see their active/inactive vacancies' list, description, contacts, status, attachments, and responsible users. ", "Also, you can add tasks and comments for Clients": "Also, you can add tasks and comments for Clients.", "Set the scope of visible data (vacancies, candidates, clients and users) in your interface: region, responsibility and company (if you have more than one CleverStaff account)": "Set the scope of visible data (vacancies, candidates, clients and users) in your interface: region, responsibility and company (if you have more than one CleverStaff account).", "Create a ‘Jobs’ tab on your company Facebook page and publish active vacancies from your CleverStaff account": "Create a ‘Jobs’ tab on your company Facebook page and publish active vacancies from your CleverStaff account. ", "NOTE: As a Facebook API has one-end integration, you should re-publish your vacancies if you want to change them": "<strong>NOTE:</strong> As a Facebook API has one-end integration, you should re-publish your vacancies if you want to change them.", "The recruiting funnel is based on the number of candidates on vacancy and on transitions between statuses": "The recruiting funnel is based on the number of candidates on vacancy and on transitions between stages.", "The recruiting is funnel calculated considering the fact that all vacancy stages are consistent": "The recruiting funnel is calculated considering the fact that all recruiting stages are consistent.", "You can add/remove, swap, and customize all your vacancy stages, except Long list and Hired, in the Stages block on a vacancy page": "You can add/remove, swap, and customize all your recruiting stages, except 'Long list and 'Hired', in the Stages' block on a vacancy page.", "Recruiting funnel shows the conversion between the stages. Vacancy stages with the lowest conversion are the zone of growth. Thus recruiting funnel helps to identify bottlenecks and to focus on them": "Recruiting funnel shows the conversion between the stages. Recruiting stages with the lowest conversion are the zone of growth. Thus recruiting funnel helps to identify bottlenecks and to focus on them.", "It is useful to compare the recruiting funnel different recruiters to funnel recruiting of the company": "It is useful to compare the recruiting funnel different recruiters to funnel recruiting of the company.", "Add promo image": "Add promo image", "Set to default": "Make default", "Please choose image 290 x 290 px or larger": "Please choose image 290 x 290 px or larger", "Show the report description": "Show the report description", "The Pipeline report helps you define problem vacancies, bottlenecks, and enhance the work on them in advance. The report illustrates": "The Pipeline report helps you define problem vacancies, bottlenecks, and enhance the work on them in advance. The report illustrates", "active vacancies ('Open' and 'In progress' status) with responsible recruiters": "active vacancies ('Open' and 'In progress' status) with responsible recruiters", "2 last active stages, the last day of any activity, and the number of candidates processed that day": "2 last active stages, the last day of any activity, and the number of candidates processed that day", "the number of candidates hired and refused": "the number of candidates hired and refused", "vacancies with hired candidates marked with green color": "vacancies with hired candidates marked with green color", "'hot' vacancies sorted by deadline date marked with red color": "'hot' vacancies sorted by deadline date marked with red color", "vacancies with no activity within 5 days marked with yellow color": "vacancies with no activity within 5 days marked with yellow color", "days ago": "days ago", "Date and time": "Date and time", "Edited_1": "Edit", "User news": "User news", "This is a notification for important news you should know as our user": "This is a notification for important news you should know as our user.", "Please click the button below to close the notification.": "Please click the button below to close the notification.", "Important News from CleverStaff": "Important News from CleverStaff", "Got it": "Got it", "Show previous news": "Show previous news", "Add/Edit custom field": "Add/Edit custom fields", "This tag has already assigned": "This tag has already assigned", "field type": "Field type:", "Tests and forms": "Tests module", "My tests": "My tests", "Create new test": "Create new test", "Prepare tests for candidates and employees: choose question types, correct answers, time limits for passing, and send tests via email. ": "Prepare tests for candidates and employees: choose question types, correct answers, time limits for passing, and send tests via email.", "You can limit the time for passing the test.": "You can limit the time for passing the test.", "The timer will start since a candidate clicks on the 'Start test' button.": "The timer will start since a candidate clicks on the 'Start test' button.", "If the time flies before candidates finishes the test, all filled fields will be saved.": "If the time flies before candidates finishes the test, all filled fields will be saved.", "Once the test is passed, you will receive an email with the link on results.": "Once the test is passed, you will receive an email with the link on results.", "Answer": "Answer", "Add a new answer": "Add new answer option", "Test description": "Test description", "Test title": "Test title", "Test deleted": "Test deleted", "Question text": "Question text", "Select from the list": "Select from a list", "select_from_the_list": "Select from the list", "input vacancy name": "Input vacancy name", "Points": "points", "Points_2": "points when all chosen options are correct", "hours": "hours", "hours_short": "h", "minutes_short": "min", "minutes": "minutes", "Save test": "Save Test", "Add a question": "Add a question", "Question": "Question", "Received": "Received", "Passed": "Passed", "Detailed results": "Detailed results", "Delete test": "Delete test", "This is the correct answer": "Correct answer", "You created the new test:": "You created the new test: ", "The test was deleted": "The test was deleted", "Are you sure to delete the test": "Are you sure to delete the test", "This test  will be removed from your company account.": "This test  will be removed from your company account.", "All test results will remain at candidate profiles.": "All test results will remain at candidate profiles.", "Enter a numeric value from 0 to 1000.": "Enter a numeric value from 0 to 1000.", "This field is not obligatory.": "This field is not obligatory.", "Text question": "Text question.", "You should fill all obligatory fields.": "You should fill all obligatory fields.", "Test results": "Test results", "A list of all the tests created in your account by results. View detailed results, send tests to new candidates, edit and delete them.": "A list of tests with results created in your account with results. Check detailed results, send tests to new candidates, edit and delete them.", "send_test": "Send test", "Send test to the candidate": "Send test", "Choose candidates and send a link on the test via email": "Choose candidates and send a link on the test via email", "The email with a link to the test was successfully sent": "The email with a link to the test was successfully sent.", "Results of the candidates": "Candidate results", "Tests results": "Test results", "Show all": "Show all", "Candidate did not pass this test": "Candidate did not pass the test", "Once you pass the test, your answers will be sent to a recruiter. You have only one attempt to pass the test": "Once you pass the test, your answers will be sent to a recruiter. You have only one attempt to pass the test.", "Time limit": "Time limit", "Previous": "Previous", "Time left": "Time left", "Detailed test results": "Detailed Results", "Right answers": "Right answers", "Attempt": "Attempt", "No results": "There are no results for this test", "Choose one option": "Choose one option", "Enter your answer": "Enter your answer", "Thank you_1": "Thank you!", "Thank you_2": "Thank you!", "Your answers are saved and sent to the recruiter": "Your answers are saved and sent to the recruiter.", "Choose multiple answers": "Choose multiple answers", "Candidate test passed": "Candidate finished the \"{{testName}}\" test.", "Candidate test finish": "Candidate finished the test <span class='underline'>\"{{testName}}\"</span>", "Candidate test finish admin": "Candidate finished the test <a target='_blank' href=!#/candidate/test/{{testHref}}>{{testName}}</a>", "Changes are saved": "Changes are saved", "Do you want to finish the test": "Do you want to finish the test?", "You have only one attempt to pass the test": "You have only one attempt to pass the test.", "Go back": "Go back", "Save and finish the test": "Save and finish the test", "Date of passing": "Date of passing", "Result": "Result", "Enter your answer first": "Select your answer first.", "Change email": "Change email", "candidate_comments": "candidate comments", "vacancy_comments": "vacancy comments", "vacancy_changes": "vacancy changes", "all_actions": "all actions", "This test has already been taken by you. Please contact the recruiter who sent you this test link, if you want to try it again.": "This test has already been taken by you. Please contact the recruiter who sent you this test link, if you want to try it again.", "Time is over": "time is over", "Edit tag for all candidates": "Edit tag for all candidates", "languages_1": "Languages", "Dismiss of": "Dismiss of", "characters left": "Characters left", "min length": "min length - 300, currently ", "it has": "it has", "Edit the template": "Edit the template:", "Alphabetical order (A-Z)": "By alphabetical order (A-Z)", "Upgrade your plan to invite new user": "Upgrade your plan to invite new user", "After 7 day Trial only one user is able to access the Free plan account (despite of Hiring Manager)": "After 7 day Trial only one user is able to access the Free plan account (despite of Hiring Manager).<br/><br/> Upgrade your account ($25/mo per each user) to open all features and invite colleagues.", "The left number shows candidates added by each user and the % of all added candidates in this account.": "The left number shows candidates added by each user and the % of all added candidates in this account.", "The right number shows candidates without name/contacts and the % of all candidates added by this user.": "The right number shows candidates without name/contacts and the % of all candidates added by <b>this user</b>.", "in work": "in work", "In work": "In work", "calendar_error": "Calendar integration error", "email_error": "Email integration error", "Please delete CleverStaff from the list of apps connected to your Google account": "Please delete CleverStaff from the list of apps connected to your Google account", "You should delete an app from the account you are going to integrate with a calendar": "You should delete an app from the account you are going to integrate with a calendar.", "You should delete an app from the account you are going to integrate with a email": "You should delete an app from the account you are going to integrate with a email.", "After that, try to integrate the calendar again": "After that, try to integrate the calendar again.", "After that, try to integrate the email again": "After that, try to integrate the email again.", "Vacancy status": "Vacancy status", "last comment": "Last comment", "Refusal reasons": "Refusal reasons", "Allow exporting candidates to Excel": "Allow exporting candidates to Excel", "Days left until your trial expires": "Days left until your trial expires", "All features are unlimited within your trial. You could invite unlimited number of users to test the system.": "All features are unlimited within your trial. You could invite unlimited number of users to test the system.", "If your account will not be paid until trial end date": "If your account will not be paid until trial end date:", "it will be automatically changed to ‘1 RECRUITER’ plan with limited features;": "it will be automatically changed to ‘1 RECRUITER’ plan with limited features;", "all invited users will be blocked until account is paid": "all invited users will be blocked until account is paid", "We change our payment model for user account to ‘billing’ and apply it for your account as well.": "We change our payment model for user account to ‘billing’ and apply it for your account as well.", "What was changed? This will be the same prepayment subscription model with the only difference: payments are automatically charged on a daily basis depending on the number of users in the account.": "What was changed? This will be the same prepayment subscription model with the only difference: payments are automatically charged on a daily basis depending on the number of users in the account.", "Read more details about the payment model and how a balance is re-counted": "Read more details about the payment model and how a balance is re-counted", "at our blog.": "at our blog.", "It will appear on the": "It will appear on the", "‘Payment’ page": "‘Payment’ page.", "By clicking ‘Accept changes’ you accept the terms of": "By clicking ‘Accept changes’ you accept the terms of", "public offer of service agreement.": "public offer of service agreement.", "blog": "https://cleverstaff.net/news/2017/billing/", "publicOffer": "https://cleverstaff.net/terms.html", "This test is inactive now. Please contact the recruiter if you want to pass the test": "This test is inactive now. Please contact the recruiter if you want to pass the test.", "add_from_google_drive": "Add from google.drive", "was_add_from_workua": "from Work.ua", "from rabotaUa": "from Robota.ua", "was_add_from_rabotaua": "from Robota.ua", "was_add_from_hh": "from HeadHunter", "was_add_from_grc": "from GRC", "was_add_from_djinni": "from Djinni", "from_delucru": "from Delucru.md", "Pre_Intermediate": "Pre Intermediate", "No_experience": "Doesn't know", "no_experience": "Doesn't know", "Intermediate": "Intermediate", "Upper_Intermediate": "Upper Intermediate", "Advanced": "Advanced", "Native": "Native", "Language": "Language", "Proficiency level": "Proficiency level", "Proficient": "Proficient", "upper intermediate": "upper intermediate", "lower intermediate": "lower intermediate", "advanced": "advanced", "native": "native", "fluent": "fluent", "intermediate": "intermediate", "basic": "basic", "elementary": "elementary", "Languages (choose/add)": "Languages (choose/add):", "Select language": "Select language", "Choose/add": "Choose/add", "write_a_tag_name": "Write the tag name", "This language has already been added": "This language has already been added", "tarif": " Tarif:", "Payments": "Payments", "including": "including", "of the bonus": "of the bonus", "Are you sure you want to delete this field?": "Are you sure that you want to delete this field?", "Reports": "Reports", "User statistics": "User statistics", "Choose vacancy to build a report": "Choose vacancy to build a report", "Limited access to a candidate": "Limited access to a candidate", "This candidate is not visible for your role as he/she was not added to a vacancy you responsible for. You can check this profile via link you received from another user of this account only.": "This candidate is not visible for your role as he/she was not added to a vacancy you responsible for.", "To get the full access to this candidate, the other user should add the candidate to the vacancy you responsible for.": "To get the full access to this candidate, the other user should add the candidate to the vacancy you responsible for.", "The user will not be able to see your customers,  create or edit vacancy": "The user will not be able to see your customers,  create or edit vacancy", "This function is not available": "This function is not available", "'Statistics' report shows the results of every account user: the quantity and the percentage of added candidates, vacancies, interviews, an average time to fill a vacancy for a specific time period.": "'Statistics' report shows the results of every account user: the quantity and the percentage of added candidates, vacancies, interviews, an average time to fill a vacancy for a specific time period.", "Reffilling the balance": "Reffilling the balance", "Profile preview": "Profile preview", "CV preview": "CV preview", "undefined": "Level undefined", "A candidate": "The candidate", "Candidate": "Candidate", "on": "on", "to": "on", "Add custom fields": "Add custom fields", "employees": "Employees", "hr-module": "HR module", "example": "Example", "Unfortunately": "Unfortunately", "this vacancy": "the vacancy", "is out-of-date": "is out-of-date", "is deleted": "is deleted", "candidateEmail": "Enter e-mail of the candidate", "enter_email_candidate": "Enter e-mail of the candidate", "enter valid data greater than zero": "enter valid data greater than zero", "Percentile": "Percentile", "Add image": "Add image", "Image size:": "Image size:", "skills_rating_assoc": {"_all": "all", "_1": "1", "_2": "2", "_3": "3", "_4": "4", "_5": "5"}, "Help with data migration": "Help with data migration", "Please choose image 960 x 380 px or larger": "Please choose image 960 x 360 px or larger", "Please reset your email integration.": "Please reset your email integration.", "Send test": "Send test", "Choose a test to send it via email": "Choose a test to send it via email", "Choose test": "Choose test", "Choose a test to send it via email to a candidate": "Choose a test to send it via email to a candidate", "Please add an email before sending a test to this candidate": "Please add an email before sending a test to this candidate", "only_relocation_abroad": "Only relocation abroad", "Relocate": "Relocate", "You have no access to candidate list on this stage. Please contact the user with an Admin role to access the candidate list.": "You have no access to candidate list on this stage. Please contact the user with an Admin role to access the candidate list.", "You have no access to candidate": "You have no access to candidate", "Candidates on this stage are visible for a Hiring manager role": "Candidates on this stage are visible for a Hiring manager role", "Candidates on this stage are invisible for a Hiring manager role": "Candidates on this stage are invisible for a Hiring manager role", "Please enter test title": "Please enter test title", "Number of positions": "Number of positions", "Hi": "Hi,", "hi": "Hi", "Need help with the database migration from other ATS/recruitment software?": "Need help with the database migration from other ATS/recruitment software?", "Let us help!": "Let us help!", "My Reports": "My Reports", "Design custom report": "Custom report designer", "Short description": "Short description", "Custom reports designer allows you build your own reports with appropriate parameters.": "Сompose the reports with the only information needed to you.", "Only users with the Admin role are able to save the custom report parameters.": "Only users with the Admin role are able to save the custom report parameters.", "Add / Remove report fields": "Add / Remove report vacancy fields", "Client": "Client", "Budget": "Budget", "Location": "Location", "Location_2": "Location", "Responsible for a vacancy": "Responsible for a vacancy", "Date of creation": "Date of creation", "Deadline": "Deadline", "Payment date": "Payment date", "Employment type": "Employment type", "Candidates in work": "Candidates in work", "Hired": "<PERSON><PERSON>", "Unsuitable": "Unsuitable", "Days in work": "Days in work", "Save report settings": "Save report settings", "Only users with Admin role are able to save the report parameters": "Only users with Admin role are able to save the report parameters", "Add title and save": "Add title and save", "Enter the title of the new report": "Enter the title of the new report", "Enter the new title of the report": "Enter the new title of the report", "Title of the new report *": "Title of the new report", "This report will appear at ‘Reports’ / ‘My reports’ tab and will be available for account users with the appropriate data access.": "This report will appear at ‘Reports’ / ‘My reports’ tab and will be available for account users with the appropriate data access.", "Report parameters could be edited and deleted only by user with the Admin role.": "Report parameters could be edited and deleted only by user with the Admin role.", "Short description (up to 80 characters)": "Short description (up to 80 characters)", "Report saved": "Report saved", "_Report": "Report", "budget": "Budget", "Edit report parameters": "Edit report parameters", "Delete report": "Delete report", "Edit report title and description": "Edit report title and description", "Delete Report": "Delete Report", "Are you sure to delete the report?": "Are you sure to delete the report?", "This report will be removed from your company account.": "This report will be removed from your company account.", "The selected date can not be more than the current date.": "The selected date can not be more than the current date.", "dateFinish": "Deadline", "employmentType": "Employment type", "candidatesAdded": "Candidates added", "candidatesInWork": "Candidates in work", "candidatesApproved": "<PERSON><PERSON>", "candidatesRefused": "Declined", "daysInWork": "Days in work", "dc": "Date of creation", "datePayment": "Payment date", "please select at least one report criteria": "Please select at least one report criteria", "I would appreciate hearing your feedback regarding this candidate": "I would appreciate hearing your feedback regarding this candidate:", "I would appreciate hearing your feedback regarding these candidates": "I would appreciate hearing your feedback regarding these candidates:", "I'll be waiting for your feedback": "I'll be waiting for your feedback.", "Percentile shows the percent of candidates, who received fewer points for passing the test, than a specific candidate with the percentile value": "Percentile shows the percent of candidates, who received fewer points for passing the test, than a specific candidate with the percentile value. E.G.: if a percentile for a candidate 'A' is 75, this candidate passed the test better, than 75% of other candidates", "Change Image": "Change Image", "Delete Image": "Delete Image", "add candidates to the stage": "Add candidates to the stage", "enter_email": "Enter email", "enter_phone": "Enter phone", "Reports constructor": "Reports constructor", "Show candidates in the report": "Show candidates in the report", "Hi!": "Hi!", "Please pass the": "Please pass the ", "test": " test", "saved": "saved", "Choose data that differs in two candidate profiles and save the correct one": "Choose data that differs in two candidate profiles and save the correct one", "numberOfPositions": "Number of positions", "Candidate has been removed from the database": "Candidate has been removed from the database", "Removed from database": "Removed from database", "Restore": "Rest<PERSON>", "Remove from this vacancy": "Remove from this vacancy", "send": "Apply now", "Apply now": "Apply now", "Application form": "Request form", "Haven`t received the report?": "Haven`t received the report?", "Request daily report from": "Request daily report from", "Get the report": "Get the report", "You will receive this report within 10 minutes": "You will receive this report within 10 minutes", "The report will be sent to the email your account is registered to": "The report will be sent to the email your account is registered to", "Please choose the date": "Please choose the date", "Recruiters shared no vacancies for you yet. Ask them to do that or add new vacancy by yourself. They will see it.": "Recruiters shared no vacancies for you yet. Ask them to do that.", "enter a comment": "Enter a comment", "Choose fields for Merge": "Choose fields for merge", "candidate_": "candidate", "Show more": "Show more", "Elements on the page": "Elements on the page", "Invited users": "Invited users", "Delete source for all candidates": "Delete source for all candidates", "was sent": "was sent", "hide": "<PERSON>de", "Please pick the candidates": "Please pick the candidates", "Service is temporarily unavailable": "Service is temporarily unavailable", "Topic": "Topic", "Phone": "Phone", "Internal mailing name": "Campaign title", "Create email campaign": "Create email campaign", "To_": "To", "Step": "Step", "Mailing create": "Mailing create", "Email text composition": "Compose the letter", "Preview and sending": "Preview and sending", "Text of the letter": "Text of the letter", "Your letter topic, receiver will read in his Inbox": "Your letter topic, receiver will read in his Inbox", "Mailing name for your internal usage. Visible only for you.": "Mailing name for your internal usage. Visible only for you.", "My mailings": "My mailings", "Test letter": "Test letter", "Save mailing": "Save mailing", "Continue": "Continue", "Saved": "Saved", "Sent": "<PERSON><PERSON>", "Sent test email": "Test letter is sent", "Mailing deleted": "Mailing deleted", "Mailing removals": "Mailings removal", "Send date": "Send date", "Are you sure you want to delete the selected receiver?": "Are you sure you want to delete the selected receiver?", "Mailing": "Mailing", "created": "created", "Preferable contact method": "Preferable contact method", "Mark as preferrable contact method": "Mark as preferable contact method", "Unmark as preferable contact method": "Unmark as preferable contact method", "You has granted role": "You has granted role", "_for": "for", "_Previous": "Previous", "_Next": "Next", "Boolean search info": "<p><b>Boolean Search Rules in CleverStaff:</b> </p><p>1. To search for the candidates, you can use three operators \"AND\", \"OR\", \"NOT\", and also \"\" for phrases and () to group search parameters in the query.</br>2. You have to use parentheses, if you use several operators.</br> 3. All operators must be written in capital letters.</br>4. Operator \"NOT\" is used at the end of the search query.</br>5. Do not use the same search parameter twice in one query.</p><p><b>For Example:</b></br>1. (C++ OR C#) AND \"Software Engineer\"</br>2. QA AND (Selenium OR Python) NOT \"Manual Testing\"</p>", "Bool search info for vacancies": "<p><b>Boolean Search Rules in CleverStaff:</b> </p><p>1.To search for the vacancies, you can use three operators \"AND\", \"OR\", \"NOT\", and also \"\" for phrases and () to group search parameters in the query.</br>2. You have to use parentheses, if you use several operators.</br> 3. All operators must be written in capital letters.</br>4. Operator \"NOT\" is used at the end of the search query.</br>5. Do not use the same search parameter twice in one query.</p><p><b>For Example:</b></br>1. (C++ OR C#) AND \"Software Engineer\"</br>2. QA AND (Selenium OR Python) NOT \"Manual Testing\"</p>", "Bool search info for clients": "<p><b>Boolean Search Rules in CleverStaff:</b> </p><p>1.To search for the clients, you can use three operators \"AND\", \"OR\", \"NOT\", and also \"\" for phrases and () to group search parameters in the query.</br>2. You have to use parentheses, if you use several operators.</br> 3. All operators must be written in capital letters.</br>4. Operator \"NOT\" is used at the end of the search query.</br>5. Do not use the same search parameter twice in one query.</p><p><b>For Example:</b></br>1. (C++ OR C#) AND \"Software Engineer\"</br>2. QA AND (Selenium OR Python) NOT \"Manual Testing\"</p>", "Please add the candidates to this stage": "Please add the candidates to this stage", "Time range": "Time range:", "Time ranges": "Time range", "currentWeek": "Current week", "previousWeek": "Previous week", "currentMonth": "Current month", "previousMonth": "Previous month", "currentYear": "Current year", "previousYear": "Previous year", "customRange": "Custom range", "Have you already seen our awesome vacancy": "Have you already seen our awesome vacancy", "Check it out!": "Check it out!", "Days left": "{{days_left}} days left", "Days left!": "{{days_left}} days left!", "Day left!": "{{days_left}} day left!", "No Day left!": "{{days_left}} days left!", "This vacanсy doesn't contain candidates now": "This vacanсy doesn't contain candidates now", "The 'rules' of profiles merge": "The 'rules' of profiles merge:", "Only fields with different values are available for selection": "Only fields with different values are available for selection.", "If the same field in both profiles has empty and filled values, the filled value will be saved in the merged profile by default": "If the same field in both profiles has empty and filled values, the filled value will be saved in the merged profile by default.", "Tags in the merged profile will be saved from both original ones": "Tags in the merged profile will be saved from both original ones.", "first": "first", "second": "second", "Merge with another profile": "Merge with another profile", "Download as docx": "Download as docx", "Position": "Position", "Show": "Show", "Search": "Search", "telework_1": "Remote", "Tag_name_saved": "Tag name saved", "Mark at least one test answer as correct": "Mark at least one test answer as correct", "Alphabetically": "Alphabetically", "Last Name": "Last Name", "By relevance": "By relevance", "By last name from Z to A": "By last name", "By last name from A to Z": "By alphabetical", "up to 30 000 profiles": "up to 30 000 profiles", "Please choose the candidate database export option": "Please choose the candidate database export option:", "Candidates + comments + history": "Candidates + comments + history", "Export History": "Export History", "export": "Export", "List": "List", "Sender name and email": "Sender", "Email template": "Email template", "Mailing saved": "Mailing saved", "Candidate incorrect email": "Incorrect receiver email", "Must be at least one recipient": "Must be at least one recipient", "Mass mailings": "Mass mailings", "Prepared ": "Prepared", "Recipients": "recipients", "Recipients_all": "Recipients", "recipient": "recipient", "recipients": "recipients", "Send to": "Send to", "You have no prepared mailing campaign now. Create a new one": "You have no prepared mailing campaign now. Create a new one.", "New mailing": "New mailing", "Select candidate(s)": "Select candidate(s)", "Email is not specified": "Email is not specified", "Sent_stat": "<PERSON><PERSON>", "Delivered_stat": "Delivered", "Opened_stat": "Opened", "Clone mailing": "Clone mailing", "Mailing cloned": "The mailing is cloned", "Fill in the new mailing name": "Fill in the new mailing name", "Count of recipients should be less than 1000": "Count of recipients should be less than 1000", "Read": "Read", "Hide Mailing Service": "Hide mailing service", "User won't be able to create, send and view created mailings.": "User won't be able to create, send and view created mailings.", "The price of mailing is": "The price of mailing is {{price}}$", "User won`t be able to create, send and view created mailings.": "User won`t be able to create, send and view created mailings.", "Mailings are hidden for the user": "Mailings are hidden for the user", "letter": {"plural": ["letters"], "single": ["letter"], "singleB": ["Letter"]}, "free_1": {"plural": ["free", "free"], "single": ["free"]}, "Available letters amount": "{{amount}} free {{parsedWords.letter}} are available. Of these, {{mailsToSend}} letters will be used.", "You do not have enough money on your balance to make a mailing.": "Not enough money on your balance to make a mailing. ", "Mailings are available for the user": "Mailings are available for the user", "Create the copy of current mailing for the further sending": "Create the copy of current mailing for the further sending", "Spam": "Spam", "to_candidates": "to the candidates", "on stage": "on the", "EDRPOU placeholder": "National State Registry of Ukrainian Enterprises and Organizations", "Company EDRPOU": "Company EDRPOU/Individual Taxpayer Number", "companyEDRPOU": "Company EDRPOU/Individual Taxpayer Number", "up to 25": "up to <span class='bold'>25</span>", "Discount": "Discount", "Get a 10% discount if you pay more than 4 months and 20% pay for the year": "Get a 10% discount if you pay more than 4 months and 20% pay for the year", "years old1": "year", "years old2": "years", "If you need to migrate your database from any other ATS / recruiting software (Zoho, Podio, Bullhorn, etc.), we will do it for you with all interconnections, comments and tasks": "If you need to migrate your database from any other ATS / recruiting software (Zoho, Podio, Bullhorn, etc.), we will do it for you with all the interconnections, comments and tasks.", "Comments only": "Comments only", "replacement": "Replacement", "Changes saved": "Changes saved", "No recipients": "No recipients", "Wrong emails": "There are recipients with empty or not valid emails in the list. Please fill the emails in or remove such recipients from this list", "Adding recipients": "Adding recipients", "Select the vacancy and the stage. Candidates from the selected stage will become the recipients of the mailing. Only candidates with e-mail will be added": "Select the vacancy and the stage. Candidates from the selected stage will become the recipients of the mailing. Only candidates with e-mail will be added", "You have not sent mailings yet": "You have not sent mailings yet.", "_Invited user": "Invited users", "Suggested candidates": "Recommended candidates", "Add to the Found stage": "Add to the \"Long list\" stage", "Matching": "Matching", "Here will be displayed candidates suitable for this vacancy. CleverStaff chooses the most suitable candidates for the desired position, place of residence and salary": "Here will be displayed candidates suitable for this vacancy. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chooses the most suitable candidates for the desired position, place of residence and salary", "Comment changed": "Comment changed", "ready_to_relocate2": "Ready to relocate", "The account name changed to": "The account name changed to", "Your company has no active vacancies in this account": "Your company has no active vacancies in this account", "Enter your answer first text": "Enter your answer first", "delete_responsible_2": "Do you really want to remove the responsible", "The server URL": "The server URL", "Domain/user name": "Domain/user name", "The Exchange server URL": "The Exchange server URL should look like https://***/EWS/exchange.asmx", "Exchange version": "Exchange version", "Domain/username is the required field for those cases when logging into an account for exchange via Domain/username, rather than an email address": "Domain/user name is the required field <b>only</b> for those cases when logging into an account for exchange via Domain/user name, rather than an email address", "Please attach your CV file": "Please attach your CV file", "Enter more data for search": "Enter more data for search", "The vacancy": "The vacancy", "_edited": "edited", "_edited_2": "edited", "Hide from all public vacancies list:": "Hide vacancy from public list:", "Too much Desired Salary salary value": "Text must be no more than 10 characters", "Please enter your Name": "Please enter your Name", "Please enter your Last Name": "Please enter your Last Name", "Enter phone number": "Please enter phone number", "role_level": "Level", "Select vacancies in the report": "Select vacancies in the report", "Start the test": "Start the test", "The time for taking this test is limited. The timer will be displayed on the test page.In case you do not finish the test before the deadline, the test page will be closed, all the data entered will be saved and sent to the recruiter": "Время на прохождение этого теста ограничено.Таймер будет отображаться во время прохождения теста.По окончанию времени страница теста автоматически закроется, все введённые вами данные будут сохранены и отправлены рекрутеру", "Next question": "Next question", "Candidate contacts": "", "Finish the test": "Finish the test", "Do you want to finish the test?": "Do you want to finish the test?", "Click on the picture to enlarge": "Click on the picture to enlarge", "yes_1": "Yes", "Fill in the information necessary for the selection of suitable candidates": "Fill in the information necessary for the selection of suitable candidates", "Select candidates": "Select candidates", "Fill all fields": "Fill all fields", "Hiring Manager will be responsible for this vacancy after registration in account.": "Hiring Manager will be responsible for this vacancy after registration in account.", "Hiring Manager will be responsible for this vacancy after registration in account. You may not choose a Vacancy, but in this case, the NM will only have access to creating an Application for a Vacancy and creating new Vacancies immediately after registration.": "Hiring Manager will be responsible for this vacancy after registration in account. <br>You may not choose a Vacancy, but in this case, the HM will only have access to creating a Request for a Vacancy.", "Searching": "Searching...", "Paid_user": "Paid user", "Free_user": "Free user", "fullAccess": "Full access", "limitedAccess": "Limited access", "freeAccess": "Free access", "Select the role of the new user": "Select the role of the new user", "Funnel and vacancy report": "Funnel and vacancy report", "Pipeline report": "Pipeline report", "The recruitment funnel displays the conversion of candidates in vacancies and helps to identify the bottlenecks or forgotten candidates.": "The recruitment funnel displays the conversion of candidates in vacancies and helps to identify the bottlenecks or forgotten candidates", "Go to report": "Go to report", "Go to reports": "Go to reports", "Displays the effectiveness of recruiters in numbers and percentages for the chosen period.": "Displays the effectiveness of recruiters in numbers and percentages for the chosen period", "Displays problem vacancies and responsible employees and helps to determine which areas need to be strengthened.": "Displays problem vacancies and responsible employees and helps to determine which areas need to be strengthened", "_Custom": "Custom", "Show more reports": "Show more reports", "Show fewer reports": "Show fewer reports", "Did not find the right report": "Did not find the right report?", "Build your own in Report Designer": "Build your own in Report Designer!", "Build your report": "Build your report", "Remove phone number": "Remove phone number", "Task deleted": "Task deleted", "Сandidates in stages": "Сandidates in stages", "Export in Excel": "Export in Excel", "Update log of exports to Excel": "Update", "Preparing the candidates for the export, you can download it in Export history when the export completes": "The process of candidates export is running, once it finishes you can download it in the", "Export history": "Export history", "You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in minutes.": "You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in {{minutes}} minutes.", "The test has been sent to the candidate": "The test <span class='underline'>\"{{testName}}\"</span> has been sent to the candidate", "The test sent to the candidate": "The test <a target='_blank' href=!#/candidate/test/{{testHref}}>{{testName}}</a> has been sent to the candidate", "The test has been sent after the application on the vacancy": "The test <span class='underline'>{{testName}}</span> has been sent after the request on the vacancy", "Number of candidates for which there were actions for the selected period": "Number of candidates for which there were actions for the selected period", "from_4": "from", "for": "for", "Account visibility": "Account visibility", "Account visibility changed": "Account visibility changed", "Yes, remove everywhere": "Yes, remove everywhere", "Are you sure you want to remove this source?": "Are you sure you want to remove \"{{source_name}}\" source?", "It will also be removed from other candidate profiles": "It will also be removed from other candidate profiles.", "Origin removed": "Origin removed", "was sent to candidate": "was sent to the candidate", "Mailing duplicated emails": "There are candidates with the same email in the list", "openings": "Openings", "There are candidates in the list without email": "There are {{count}} candidates in the list without email.", "There are candidates in the list without email _1": "There are {{count}} candidates in the list without email.", "There are candidate in the list without email": "There are candidate in the list without email.", "Delete him": "Delete them", "Delete them": "Delete them", "Candidates removed": "Candidates removed", "Please save the changes": "Please save the changes", "Personalization": "Personalization", "Mailings": "Mailings", "With the help of mailings you can send letters to candidates, get the reports on emails delivery and views": "With the help of mailings you can send letters to candidates, get the reports on emails delivery and views", "Usage of variables allow you to send personalized emails and the analytics will show the effectiveness of your mailings.": "Usage of variables allow you to send personalized emails and the analytics will show the effectiveness of your mailings.", "You can even implement the A/B test and determine which letters work best.": "You can even implement the A/B test and determine which letters work best", "The cost of each letter is 0.01 USD (100 letters - 1 USD)": "The cost of each letter is <b>0.01</b> USD (100 letters - 1 USD)", "Please enter the name of the recipient": "Please enter the name of the recipient", "Please enter the last name of the recipient": "Please enter the last name of the recipient", "Tariffs": "Rates", "Specify_the_tariff": "Specify the tariff", "Tariff_scale": "Tariff scale", "Please integrate email into the system or enable mailing sending on": "Please integrate email into the system or enable mailing sending on", "the email integration settings page": "the email integration settings page", "SMTP secure protocol": "SMTP secure protocol", "You tried to integrate corporate email": "You tried to integrate corporate email {{email}}.", "To ensure that your mailings would not be blocked by postal services and would not be sent to Spam, you need to change the settings of your domain name": "To ensure that your mailings would not be blocked by postal services and would not be sent to Spam, you need to change the settings of your domain name", "Settings are applied only once and work for all the email addresses in ": "Settings are applied only once and work for all the email addresses in ", "We would not want to burden you with additional complicated actions, but this is the crucial thing for high-quality letters delivery.": "We would not want to burden you with additional complicated actions, but this is the crucial thing for high-quality letter delivery.", "Three entries have to be added in the admin panel of your domain name. Please send the text below to your domain administrator to do this.": "Three entries have to be added in the admin panel of your domain name. Please send the text below to your domain administrator to do this.", "Copy the text for administrator": "Copy the text for administrator", "Please update the verification status by clicking on the 'Check settings' button after the administrator applies the changes. The maximum time for the settings to apply is 72 hours, but usually the records are updated in a couple of hours.": "Please update the verification status by clicking on the \"Check settings\" button after the administrator applies the changes. The maximum time for the settings to apply is 72 hours, but usually the records are updated in a couple of hours.", "DKIM Status": "DKIM Status", "SPF Status": "SPF Status", "Check settings": "Check settings", "Text copied": "Text copied", "To send a letter from your mailbox,allow sending mails from the email integration page": "To send a letter from your mailbox, allow sending mails from the <a href='#/email-integration' target='_blank'>email integration page</a>", "The link to": "The link to", "My tasks": "My tasks", "There is no custom reports yet.Create your first": "There is no custom reports yet. Create your first", "I have read and I accept the": "I have read and I accept the ", "Privacy Policy": "Privacy Policy ", "and the": "and the ", "Terms and Conditions": "Terms and Conditions", "Please read and accept the Privacy Policy and the Terms and Conditions": "Please read and accept the Privacy Policy and the Terms and Conditions", "I give my consent for my personal data processing for this and other vacancies": "I give my consent for my personal data processing for this and other vacancies", "View": "View", "All data": "All data", "My data": "My data", "level": "level", "Our_vacancies": "Our vacancies", "About_company": "About company", "Are you sure want change candidate status to Delete": "Are you sure want change candidate {{name}} status to \"Deleted\"?", "Remove candidate from a system": "Remove candidate from the system", "Remove candidates from a system": "Remove candidates from the system", "Candidate name has been removed from the database": "Candidate {{name}} has been removed from the database", "Are you sure want permanently delete candidate from the system": "Are you sure want <span class='bold'>permanently</span> delete candidate {{name}} from the system?", "Are you sure want delete candidate from the system": "Are you sure that you want delete candidate {{name}} from the database?", "Delete from a system": "Delete from a system", "If you select this option, the candidate will be removed from the system. All comments, tasks and history of actions will be erased": "If you select this option, the candidate will be <span class='bold'>permanently</span> removed from the system. All comments, tasks and history of actions will be erased", "User removed candidate from a system": "User removed candidate <span class='danger'>{{candidate}}</span> from a system", "User removed candidate from a system female": "User removed candidate <span class='danger'>{{candidate}}</span> from a system", "You need to give your consent for your personal data to processing proceed": "You need to give your consent for your personal data to processing proceed", "state ": "Status", "No such appointmentId.": "Unfortunately, such test does not exist. For more information, contact the recruiter who sent you this test.", "Value added tax identification number example": "for example, ************", "Address": "Address", "no_time_on_stage": "<p>The candidate was transferred by stages, 'jumping over' this stage. According to the logic of the report, it should be taken into account at all stages through which it was carried out.</p>", "Unfortunately, this vacancy is no longer available": "Unfortunately, this vacancy is no longer available", "Unfortunately, this job portal is no longer available": "Unfortunately, this job portal is no longer available", "Postal Code": "Postal Code", "Representative Full Name": "Representative Full Name", "Representative Position": "Representative Position", "Please fill in all the fields below": "Please fill in all the fields below", "The access to use Cleverstaff software": "The access to use CleverStaff software", "Wire Transfer Invoice Generation": "Wire Transfer Invoice Generation", "Order Information": "Order Information", "Customer Information": "Customer Information", "Company Name": "Company Name", "Value added tax identification number": "Individual Taxpayer Number", "Generate invoice": "Generate invoice", "Please fill in the field": "Please fill in \"{{name}}\" field", "Please fill all the fields": "Please fill all the fields", "fullName": "Representative Full Name", "companyName": "Company Name", "companyId": "Value added tax identification number", "address": "Address", "postalCode": "Postal Code", "Have you checked your billing infomation?": "Have you checked your billing infomation?", "Your invoice will be downloaded automatically in coulple of eyeblinks after confirmation": "Your invoice will be downloaded automatically in coulple of eyeblinks after confirmation", "Download the invoice": "Download the invoice", "No, I'll check it again": "No, I'll check it again", "Check information": "Check information", "Your account balance": "Your account balance", "Your account is active until": "Your account is active until", "Account is active until": "Account is active until", "Paid users amount": "Paid users amount", "Current tariff": "Current tariff", "Payment history": "Top ups history", "Transitions history": "Transactions history", "Pay for cleverstaff usage": "Pay for CleverStaff usage", "Scale": "Scale of bonuses", "Users_1": "Users", "Bonus": "Bonus", "bonus": "bonus", "Total": "Total Due", "Months_1": "Months", "Months_2": "Months", "Get a 10% bonus if you pay more than 4 months and 20% pay for the year": "Get a <span class='bold'>10%</span> bonus if you pay for more than <span class='bold'>4</span> months and <span class='bold'>20%</span> if you pay <span class='bold'>for the year</span>", "Will be credited total USD (bonus USD)": "You will receive <span class='bold'>{{total}} USD</span> <span class='break'>( <span class='bold'>{{bonus}} USD</span> bonus )</span>", "Will be credited total USD (bonus USD and discount)": "You will receive <span class='bold'>{{total}} USD</span> <span class='break'>(discount <span class='bold'>{{discount}}%</span> )</span>", "Pay by card": "Pay by card", "Pay by invoice": "Pay by invoice", "Payment FAQ": "<span class='bold'>Have any questions about the payment?</span> Contact us: <a href='mailto:<EMAIL>'><EMAIL></a>", "Payment Questions": "If you still have questions, write to us on the mail <a href='mailto:<EMAIL>'><EMAIL></a>", "Payment created": "Payment created", "Payment-received": "Payment received", "Payment": "Payment", "Your payment history": "Your payment history", "Your transition history": "Daily payments history of your company account", "Contact us": "Contact us", "Manage applicants, interviews, automate everything": "30 USD per user<br><br> Manage applicants, interviews,<br> automate everything.", "Billed annually, Fixed price, API, Your domain name, Customizations.": "Billed annually, <br>Fixed price, API,<br> Your domain name, <br>Customizations.", "Your autonomous license on your own server.": "Your autonomous license <br> on your own server.", "free plan descriptions text": "As a user of '1 Recruiter' plan (free tariff), you have limited access to CleverStaff features. <br> Open all the possibilities of CleverStaff for recruiting on a new level! <br> To do this, go to the paid tariff: <span class='break'><strong> 'Team Work' </ strong>, <strong> 'Corporate' </ strong>, or <strong>'Enterprise' </ strong>.</span>", "View the report for all vacancies, and statistics of the user's work": "View the report for all vacancies, and statistics of the user's work", "Download information about candidates in Excel": "Download information about candidates in Excel", "View and download the report on the vacancy, as well as the recruiting funnel": "View and download the report on the vacancy, as well as the recruiting funnel", "View recommended candidates": "View recommended candidates for vacancy", "Download resume candidates in docx-format": "Download resume candidates in docx-format", "Add customers to your account": "Add customers to your account", "Use CleverStaff API": "Use CleverStaff API", "Configure email templates for candidates": "Configure email templates for candidates", "Payment was not confirmed via SMS": "Payment was not confirmed via SMS", "You didn't make any transitions yet": "You didn't make any daily payments yet", "Congratulations! You have created  account of in CleverStaff": "Congratulations! You have created  account of in CleverStaff", "We have sent a confirmation mail on your email. Please check it to activate your new account.": "We have sent a confirmation mail on your email. Please check it to activate your new account.", "If no letter from CleverStaff in the inbox, check your Spam folder or wait for 10 minutes, letter delivery may be delayed.": "If no letter from <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in the inbox, check your Spam folder or wait for 10 minutes, letter delivery may be delayed.", "Home": "Home", "You have already registered an account in Cleverstaff": "You have already registered an account in CleverStaff!", "Earlier you already registered in Cleverstaff, and activated the account by following this link. Please sign in to use the system": "Earlier you already registered in Cleverstaff, and activated the account by following this link. Please sign in to use the system.", "Sorry, this candidate was removed from the system": "Sorry, this candidate was removed from the system", "Not enough money, so your account is blocked. Please top up your account and it will be unlocked": "Not enough money, so your account is blocked. Please top up your account and it will be unlocked", "has been moved to the 'Hired' stage. Would you like to close this vacancy?": "has been moved to the <strong class='color-black'> \"Hired\" </strong> stage. Would you like to fill this vacancy", "has been moved to the 'Hired' stage": "has been moved to the \"Hired\" stage.", "Let`s start with a vacancy": "Let`s start with a <b>Vacancy</b>", "On this page, you can specify any requirements for candidates. To save you time, this time we will fill the vacancy for you.": "On this page, you can specify any requirements for candidates. To save you time, this time we will fill the <b>Vacancy</b> for you.", "Want to close": "Would you like to fill this vacancy", "We now have a Vacancy.": "We now have a <b>Vacancy</b>.", "Close it": "Fill it", "Leave it opened": "Leave it opened", "The_Candidate": "Candidate", "The whole list": "The whole list", "Remove employee": "Remove employee", "Employee removed": "Employee removed", "Candidate added in vacancy and sent mail": "Candidate added in vacancy. Letter sent", "Are you sure want delete employee": "Are you sure want to delete this employee", "The employee will be permanently removed from the system. All data, comments and history of the employee's actions will be removed": "The employee will be <b>permanently</b> removed from the system. All data, comments and history of the employee's actions will be removed.", "Consent status undefined": "The status of the consent to process personal data is <b>undetermined</b>", "Consent status denied": "This candidate has <b>denied</b> the consent for personal data processing request", "Consent status confirmed": "The candidate has <b>given</b> the consent for personal data processing", "Consent status confirmed by": "This status has been manually changed by the user", "Title Consent status confirmed": "The candidate has given the consent for personal data processing", "Title Consent status confirmed by": "This status has been manually changed by the user", "Change the status": "Change the status", "Send the email to this candidate with the consent request": "Send the email to this candidate with the consent request", "Delete candidate, the consent is unobtainable or denied": "Delete candidate, the consent is unobtainable or denied", "Personal Data Consent Status has been changed": "Personal Data Consent Status has been changed", "Insert name": "Insert name", "Sender email": "Sender`s email", "Add email": "Add another email", "Use default template": "Use default template", "Save current text as template": "Save current text as template", "Email with the request for": "Email with the request for", "personal data processing has been sent": "personal data processing has been sent", "Automatic notifications": "Automatic notifications", "You have given your consent to process your personal data": "You have given your consent to process your personal data.", "You have denied the request for your personal data processing": "You have denied the request for your personal data processing. Unfortunately, we will be forced to delete your profile and do not use it as part of our vacancies fulfilling process.", "Should you intend to change your consent status denying": "Should you intend to change your consent status - select the denying option in the received email.", "Should you intend to change your consent status granting": "Should you intend to change your consent status - select the granting option in the received email.", "refused to give his consent for processing his personal data": "banned the use of his personal data", "has given his consent for processing his personal data": "gave permission to use his personal data", "Enter the text of the letter": "Enter the text of the letter", "Delete account": "Delete account", "Are you really sure you want to delete this account?": "Are you really sure you want to delete this account?", "The process the account deletion, as well as all data in it, will be launched. During the next 7 days you can stop the deletion, but after, all the data will be permanently deleted. The funds in your account are non-refundable in this case.": "The process the account deletion, as well as all data in it, will be launched. During the next 7 days you can stop the deletion, but after, all the data will be permanently deleted. The funds in your account are non-refundable in this case.", "Please leave us your feedback about the reasons for your account deletion": "Please leave us your feedback about the reasons for your account deletion", "CleverStaff account name has been deleted": "CleverStaff account {{name}} has been deleted", "User has deleted the account. days": "User <span class='bold'>{{userName}}</span> has deleted the account <span class='bold'>{{accName}}</span>. </br> In <span class='bold'>{{days}}</span> days all the account data will be completely deleted", "User has deleted the account. day": "User <span class='bold'>{{userName}}</span> has deleted the account <span class='bold'>{{accName}}</span>. </br> In <span class='bold'>{{days}}</span> days all the account data will be completely deleted", "User has deleted the account. day-1": "User <span class='bold'>{{userName}}</span> has deleted the account <span class='bold'>{{accName}}</span>. </br> In <span class='bold'>{{days}}</span> day all the account data will be completely deleted", "Admin has deleted the account. days": "<span class='bold'>Admin</span> has deleted the account <span class='bold'>{{accName}}</span>.  In <span class='bold'>{{days}}</span> days all the account data will be completely deleted.", "Admin has deleted the account. day": "<span class='bold'>Admin</span> has deleted the account <span class='bold'>{{accName}}</span>.  In <span class='bold'>{{days}}</span> days all the account data will be completely deleted.", "Admin has deleted the account. day-1": "<span class='bold'>Admin</span> has deleted the account <span class='bold'>{{accName}}</span>.  In <span class='bold'>{{days}}</span> day all the account data will be completely deleted.", "Please contact your account administrator to cancel the deletion process": "Please contact your account administrator to cancel the deletion process", "Restore account": "Restore account", "Are you sure you want to cancel the account deletion process?": "Are you sure you want to cancel the account deletion process?", "Your account will be deleted in 7 days": "Your account will be deleted in 7 days", "Account was restored": "Account was restored", "Personal Data consent status is undefined. Please use the context menu to manage this status": "Personal Data consent status is undefined. Please use the context menu to manage this status", "This candidate has denied the consent for Personal Data processing request. Please the context menu to manage this status": "This candidate has denied the consent for Personal Data processing request. Please use the context menu to manage this status", "Delete user": "Delete user", "has been successfully removed from your account": "has been successfully removed from your account", "was removed from the account": "was removed from the account", "User deleted from account": "User deleted from account", "byUser": "users", "Write a comment about remove user": "Write a comment about remove user", "You are the only user in the account. You can delete the whole account on the account settings page.": "You are the only user in the account. You can delete the whole account on the account settings page.", "The user will be permanently removed from the account. He will no longer be responsible for the vacancies, candidates, clients and tasks, bit his activity log will still be visible to you.": "The user will be <span style='font-weight: 500;'>permanently </span> removed from the account. He will no longer be responsible for the vacancies, candidates, clients and tasks, but his activity log will still be visible to you.", "Are you sure you want to remove this user?": "Are you sure you want to remove this user?", "You are able to change only the names of the buttons. Complete removal is impossible": "You are able to change only the names of the buttons. Complete removal is impossible", "You didn't make any payments yet": "You didn't make any transitions yet", "was removed from candidate": "was removed from candidate", "profile was removed from candidate": "profile was removed from candidate", "You are now the only administrator in the account. To delete your profile, assign another user as an administrator": "You are now the only administrator in the account. To delete your profile, assign another user as an administrator", "Settings have been saved": "Settings have been saved", "Not verified": "Not verified", "Verified": "Verified", "Statuses are updated": "Statuses are updated", "Thank you for your domain name update. Now you can use the mailing tool": "Thank you for your domain name update. Now you can use the mailing tool", "Please select an email that is integrated into the system": "Please select an email that is integrated into the system", "Choose the stage": "Choose the stage", "After the trial period ends, you need to pay for each user of your account. Otherwise, it will be blocked.": "After the trial period ends, you need to pay for each user of your account. Otherwise, it will be blocked.", "Scope explanation": "<b>Your account view scope</b><br> You can select the data visibility scope: all account data that is visible to you, the data you are responsible for.", "Select the data visibility scope: all data or only yours": "Select the data visibility scope: all data or only yours", "Reminder about job openings": "Reminder about job openings", "middle_name": "Middle name", "Create a profile photo": "Create a profile photo", "Recalls": {"at_the_vacancy": "On the vacancy", "added_1": "added", "added_2": "added", "candidate_1": "candidate", "candidate_2": "candidates", "candidate_3": "candidates"}, "Add candidate's:": "Add candidates:", "Mailing expense": "Mailing expense", "Swap name and surname": "<PERSON>wa<PERSON> name and surname", "Add tags to candidate": "Add tags to {{amount}} candidates", "Add tags to candidate1": "Add tags to {{amount}} candidate", "Add candidates to vacancy": "Add the candidates to the vacancy ({{amount}})", "Add candidate to vacancy": "Add a candidate {{name}} to the vacancy", "Delete candidates": "Delete {{amount}} candidates", "Delete from 2 to 4 candidates": "Delete {{amount}} candidates", "Delete one candidate": "Delete candidate", "till now": "till now", "Use our browser extension to save the candidate from this link": "Use our browser extension to save the candidate from this link", "Are you sure that you want to delete candidates": "Are you sure that you want to change status of candidates {{candidates}} to \"Deleted\"?", "Are you sure that you want to delete candidates N": "Are you sure that you want to change {{candidates}} candidates status to \"Deleted\"?", "Are you sure that you want to delete candidate": "Are you sure that you want to change candidate {{candidate}} status to \"Deleted\"?", "Candidates were deleted": "Candidates status has been changed to \"Removed\"", "add_comment_candidates": "Add comment {{amount}} candidates", "for candidates": "for candidates", "Uploaded file should have format png or jpeg": "Uploaded file should have format png or jpeg", "User changed candidates status": "User <a class='user' href={{href}}>{{name}}</a> changed candidates status", "User changed candidates status amount": "User <a class='user' href={{href}}>{{name}}</a> changed status for", "Comment on deletion": "Comment on deletion:", "Are you sure want delete candidates from the system": "Are you sure that you want to delete candidates {{ candidates }} from the database?", "Are you sure want delete candidates from the system N": "Are you sure that you want to delete {{ candidates }} candidates from the database?", "Candidates amount has been removed from the database": "Candidates has been removed from the database", "If you select this option, candidates will be removed from the system. All comments, tasks and history of actions will be erased": "If you select this option, these candidates will be <span class='bold'>permanently</span> removed from the system. All comments, tasks and history of actions will be erased", "User name removed candidate Removed Candidate from the database.": "User removed candidate <span class='danger'>Removed Candidate</span> from the database", "User name removed N candidates Removed Candidate from the database.": "User removed {{amount}} candidates from the database", "You can select up to 120 candidates": "You can select up to 120 candidates", "Comment:": "Comment: ", "Add a comment to candidate": "Add a comment to candidate", "Add a comment to candidates": "Add a comment to candidates", "Add a comment to": "Add a comment to", "You can permanently remove the user from your account. All his data (vacancies, candidates etc.) will be left in your account. Please note: removed user cannot be restored": "You can permanently remove the user from your account. All his data (vacancies, candidates etc.) will be left in your account. Please note: removed user cannot be restored.", "You are able to generate the invoice": "You can pay for CleverStaff in two ways: by credit card and by invoice. You can pay", "here_1": "here", "by yourself ": "by yourself.", "Filter the information to be displayed in your interface by such criteria:": "Filter the information to be displayed in your interface by such criteria:", "Personal br Information": "Personal information", "Professional br Information": "Professional information", "Other-1": "Other", "By surname": "By surname", "Age:": "Age:", "Search by criteria": "Search by criteria", "Level is not specified": "Level is not specified", "Tags": "Tags", "Template was saved": "<PERSON><PERSON><PERSON> was saved", "Personal Information": "Personal Information", "Professional Information": "Professional Information", "Surname": "Surname", "last name": "Last Name", "Name": "Name", "firstName": "First Name", "Country": "Country", "Sex": "Gender", "City": "City", "Position-search": "Position", "Experience": "Experience", "Languages": "Languages", "Salary up to": "Salary up to", "Employment": "Employment", "Save template by default": "Save template by default", "Find candidates": "Find candidates", "Choose a country first": "Choose a country first", "Remove all": "Remove all", "Search criteria:": "Search criteria:", "Enter candidate name": "Enter candidate's name", "Enter candidate last name": "Enter candidate's last name", "By client": "By client", "By days in work": "By days in work", "By deadline date": "By deadline date", "The email to the candidate was sent": "The email to the candidate was sent", "The email to the candidate": "The email to the candidate", "The email to the client": "The email to the client", "was sent email": "was sent", "You can attach only 1 file with your CV": "You can attach only 1 file with your CV", "Hide the vacancy from other users": "Hide the vacancy from other users", "Hide vacancy from other users": "You can hide the vacancy from other users - regardless of their role in the system, only you will see it or add candidates there.<br/> You can share it with other users by assigning them as responsible for the vacancy.<br/> Responsible users continue to see the vacancy any way", "Vacancy visibility changed": "Vacancy visibility changed", "Remove tag from account": "Remove tag from account", "Are you sure want to delete tag from account?": "Are you sure want to delete tag {{tagName}} from account?", "After deleting tag, it will be removed from all candidates": "After deleting tag, it will be removed from all candidates", "Remove tag from candidate": "Remove tag from candidate", "Tag completely removed from system": "Tag completely removed from system", "Specify the reason for deleting the account": "Specify the reason for deleting the account", "sendVacancyStateChangeNotification": "Notification of vacancy status change", "sendPaymentNotification": "Notice of upcoming payment for a CleverStaff subscription", "Logo of the vacancy": "Logo of the vacancy", "was removed": "was removed", "cover_picture": "Cover picture", "max size is 5 mb": "Max size is 5 Mb", "remove_picture": "Remove picture", "Facebook caches the information you post.": "<b>Facebook</b> caches the information you post.", "If you want to make corrections to the publication, within 30 days from the initial placement, in the tape of social network, the original title, description and image of the vacancy will be displayed.": "If you want to make corrections to the publication, within <b>30 days</b> from the initial placement, in the tape of social network, the original title, description and image of the vacancy will be displayed.", "On the 31st day the information will be updated automatically according to the changes made.": "On the <b>31st day</b> the information will be updated automatically according to the changes made.", "Duplicate the vacancy": "Duplicate the vacancy", "You can create a new vacancy by copying the contents of the vacancy": "You can create a new vacancy by copying the contents of the vacancy", "Only you will be appointed as responsible to the vacancy.": "Only you will be appointed as responsible to the vacancy", "Enter the new vacancy name": "Enter the new vacancy name", "Copy candidates from the vacancy": "Copy candidates from the vacancy", "List of vacancies": "List of vacancies", "Only admin or recruiter can set logo": "Only admin or recruiter can set logo", "All data stored in this field will be hidden on objType. Are you sure you want to delete this field?": "All data stored in this field will be hidden on {{objType}}. Are you sure you want to delete this field?", "candidates_1": "candidates", "vacancies_1": "vacancies", "clients_1": "clients", "Drop-down list": "Drop-down list", "Field type": "Field type", "Field name": "Field name", "Field type(not editable):": "Field type (not editable):", "Field name:": "Field name:", "Add new field": "Create new field", "Edit field": "Edit field", "vacancy_l": "Vacancies", "candidate_l": "Candidates", "client_l": "Clients", "Delete": "Delete", "Name of items in the drop-down list": "Name of items in the drop-down list:", "Use the custom fields creation function to overboost your performance": "Use the custom fields creation function to overboost your performance", "The possibility to create your own custom fields in different sections of CleverStaff is your opportunity to :": "The possibility to create your own custom fields in different sections of CleverStaff is your opportunity to :", "Tune the system in more precise way to match with your workflows": "Tune the system in more precise way to match with your workflows", "Choose from 4 types of fields you can create": "Choose from 4 types of fields you can create", "Display the custom fields information in the analytical reports": "Display the custom fields information in the analytical reports", "Perform the search within these fields": "Perform the search within these fields", "Enter value for the drop-down list": "Enter value for the drop-down list", "Please enter 1 character": "Please enter 1 character", "Please enter at least one value for the drop-down list": "Please enter at least one value for the drop-down list", "Add at least one option to the drop-down list": "Add at least one option to the drop-down list", "Create custom fields, which will be displayed in vacancies, candidates and clients description.": "Create custom fields, which will be displayed in vacancies, candidates and clients description.", "You can remove or edit them at any time": "You can remove or edit them at any time.", "Show reports by users": "Show reports by users", "General Statistics": "General Statistics", "Statistics for": "", "You select up to 4 users": "You select up to 4 users", "Relative": "Relative", "Absolute": "Absolute", "Total in vacancy": "Total on stage", "Candidates by users": "Candidates by users", "Amount by user": "{{name}}", "Percent of total amount": "% of total <br> amount", "User did not add/change stages for candidates in the selected period for this vacancy": "User did not add/change stages for candidates in the selected period for this vacancy", "Choose a region first": "Choose a region first", "Payment page": "Balance and payment", "Display": "Display", "You may hide or display salary on the public vacancy page": "You may hide or display salary on the public vacancy page", "Salary is displayed on the public vacancy page": "Salary is displayed on the public vacancy page", "Salary isn`t displayed on the public vacancy page": "Salary isn`t displayed on the public vacancy page", "Now youre in": "Now you're in", "You can select up to 5 languages": "You can select up to 5 languages", "English": "English", "sendTaskAssignmentNotification": "Notifications when I am appointed as responsible for tasks", "You were appointed as responsible for the task": "You were appointed as responsible for the task", "You were appointed as responsible": "You were appointed as responsible", "has marked the task": "has marked the task", "as not actual": "as not actual", "has completed the task": "has completed the task", "deleted the task": "deleted the task", "client_1": "client", "by_1": "by", "Candidates profile was updated when applying for a vacancy": "Candidate's profile was updated when applying for a vacancy", "User has changed the name of the stage from": "User has changed the name of the stage from", "Added new custom stage": "Added new custom stage", "Send via default mail client": "Send via default mail client", "account_1": "account", "for sending the test to the candidate": "for sending the test to the candidate", "Email subject": "Email subject", "Reset email template to default": "Reset email template to default", "Personalized data": "Personalized data", "Candidate name": "Candidate name", "Candidate full name": "Candidate full name", "Client name": "Client name", "Vacancy name": "Vacancy name", "Test name": "Test name", "Link to test": "Link to test", "The template should contain a link to the test": "The template should contain a link to the test: \"[[Here will be an individual link to the test for each candidate]]\"", "Please fill the email text field in": "Please fill in the message field", "Please fill the subject field in": "Please fill in the subject field", "Please fill the name field in": "Please fill in the name field", "for accept offer": "for offer acceptance", "Use this template on the “Offer accepted” stage to send all the needed details and the date of start to your new employee": "Use this template on the “Offer accepted” stage to send all the needed details and the date of start to your new employee", "Your name": "Your name", "offerAccepted": "Offer acceptance letter", "Send test on application": "Send test on request", "Off": "Off", "On": "On", "This function allows you to automatically send the test to the applied candidate. You can also set the time between the candidate’s application and dispatch of the test. Moreover you may add a cover letter with a link to the test here": "This function allows you to automatically send the test to the applied candidate. You can also set the time between the candidate’s request and dispatch of the test. Moreover you may add a cover letter with a link to the test here", "Please choose the test to be sent to applicants on this vacancy": "Please choose the test to be sent to applicants on this vacancy", "Back to the step number": "Back to the step {{number}}", "Proceed": "Proceed", "Go back-1": "Go back", "Please select the test to be sent": "Please select the test to be sent", "Please set the time gap between the candidate’s application and test dispatch": "Please set the time gap between the candidate’s request and test dispatch", "Message with the test task for the applicants. You can change this text": "Message with the test task for the applicants. You can change this text.", "Vacancy test default template title": "Take the test to proceed with the selection process on the vacancy [[vacancy name]]", "Vacancy test default template text": "Dear [[candidate name]], we are glad you’ve applied to the vacancy [[vacancy name]]. To proceed with your selection process we need you to take the test. Please find the direct link to the test at the end of this letter. <br><br> <PERSON><PERSON><PERSON>, [[company name]]. <br><br> <a data-editable='[false]' style='color: #3f90fd;font-size: 14px'>Link to the test</a>", "Link to the test": "Link to the test", "Please fill the subject of the email": "Please fill the subject of the email", "The test Test_name will be automatically sent to the candidate in minutes minutes after his response to this vacancy": "The test {{test_name}} will be automatically sent to the candidate in {{minutes}} minutes after his response to this vacancy", "Finish": "Finish", "Automatic test sending is enabled. The test testName will be sent to the candidate in minutes minutes after his application": "Automatic test sending is enabled. The test {{testName}} will be sent to the candidate in {{minutes}} minutes after his request", "Are you sure you want to turn off automatic test sending?": "Are you sure you want to turn off automatic test sending?", "Switch off": "Switch off", "Turn off automatic test sending": "Turn off automatic test sending", "Enabled the automatic sendings of the test test_name on the vacancy  vacancy_name": "Enabled the automatic sendings of the test <a href='{{testHref}}'>{{test_name}}</a> on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "Disabled the automatic sendings of the test test_name on the vacancy  vacancy_name": "Disabled the automatic sendings of the test <a href='{{testHref}}'>{{test_name}}</a> on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "Enabled the automatic sendings of the test test_name": "Enabled the automatic sendings of the test <a href='{{testHref}}'>{{test_name}}</a>", "Disabled the automatic sendings of the test test_name": "Disabled the automatic sendings of the test <a href='{{testHref}}'>{{test_name}}</a>", "This vacancy requires test completion. This test will be sent to the email entered in your application form": "This vacancy requires test completion. This test will be sent to the email entered in your request form", "The test test_name has been sent to the candidate candidate_name after he had applied": "The test <a href='{{testHref}}'>{{test_name}}</a> has been sent to the candidate <a href='{{candidateHref}}'>{{candidate_name}}</a> after he had applied", "The test test_name has been sent after the application on the vacancy vacancy_name": "The test <a href='{{testHref}}'>{{test_name}}</a> has been sent after the request on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "The test test_name on the vacancy vacancy_name has been sent to the candidate candidate_name after he had applied": "The test <a href='{{testHref}}'>{{test_name}}</a> has been sent to the candidate <a href='{{candidateHref}}'>{{candidate_name}}</a> after he had applied on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "Create the test": "Create the test", "To proceed you have to create the test to be sent": "To proceed you have to create the test to be sent", "Send the email on test results": "Send the email on test results", "Please select the threshold percentile for passing the test": "Please select the threshold percentile for passing the test", "Select the percentile": "Select the percentile", "Please select the percentile to proceed": "Please select the percentile to proceed", "Email text that candidate will receive in case of successful test passing.. You can change this text": "Email text that candidate will receive in case of successful test passing. You can change this text", "Your test result": "Your test result", "Test response - success": "<p>Dear [[candidate name]], you’ve successfully passed the test. We will contact you shortly!<br><br> <PERSON><PERSON><PERSON>, [[company name]]</p>", "Email text that candidate will receive in case of test passing fail. You can change this text": "Email text that candidate will receive in case of test passing fail. You can change this text", "Test response - failure": "Dear [[candidate name]], you’ve failed the test. We appreciate your attempt but we are unable to continue your selection process. Good luck to you! <br><br><PERSON><PERSON><PERSON>, [[company name]].", "Please fill the text of the email field in": "Please fill the text of the email field in", "Are you sure you want to turn off automatic test results email sending?": "Are you sure you want to turn off automatic test results email sending?", "Turn off automatic test results sending": "Turn off automatic test results sending", "Automatic test results email sending is enabled with the percentile threshold percentile": "Automatic test results email sending is enabled with the {{percentile}} threshold percentile", "To configure email sending, based on the test results, you have to complete the setup of sending the test on application": "To configure email sending, based on the test results, you have to complete the setup of sending the test on request", "Enabled the automatic sendings of the emails with the test test_name result": "Enabled the automatic sendings of the emails with the test <a href='{{testHref}}'>{{test_name}}</a> result", "Disabled the automatic sendings of the emails with the test test_name result": "Disabled the automatic sendings of the emails with the test <a href='{{testHref}}'>{{test_name}}</a> result", "Enabled the automatic sendings of the emails with the test test_name result on the vacancy vacancy_name": "Enabled the automatic sendings of the emails with the test <a href='{{testHref}}'>{{test_name}}</a> result on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "Disabled the automatic sendings of the emails with the test test_name result on the vacancy vacancy_name": "Disabled the automatic sendings of the emails with the test <a href='{{testHref}}'>{{test_name}}</a> result on the vacancy <a href='{{vacancyHref}}'>{{vacancy_name}}</a>", "Test in use removal": "Test in use removal", "remove test enabled in vacancy": "The test <a href='{{testHref}}'>{{test_name}}</a> is currently used for the automatiс sending of the test on the vacancy:", "remove test enabled in vacancies": "The test <a href='{{testHref}}'>{{test_name}}</a> is currently used for the automatiс sending of the test on the vacancies:", "test in use remove submit": "In case you remove this test, this automatic test sending function will be disabled. Are you sure you want to proceed?", "Removed the test test_name. The automatic sendings of the test test_name on the vacancy vacancy_name was disabled": "Test was removed. The automatic sendings of the test on the vacancy", "Removed the test test_name. The automatic sendings of the test test_name on the vacancies vacancies_name were disabled": "Test was removed. The automatic sendings of the test on the vacancies", "were disabled": "were disabled", "was disabled": "was disabled", "Email with test test_name result has been sent": "Email with test <a href='{{testHref}}'>{{test_name}}</a> result has been sent", "Email with test test_name result has been sent to the candidate candidate_name": "Email with test <a href='{{testHref}}'>{{test_name}}</a> result has been sent to the candidate <a href='{{candidateHref}}'>{{candidate_name}}</a>", "Send to the video interview": "Send to the video interview", "Please add their emails their profiles to send candidates to the video interview": "Please add emails and phone numbers in candidates' profiles to invite them to the video interview", "Please add their emails their profiles to send candidate to the video interview ": "Please add email and phone number of the candidate to invite him to the video interview", "Send the candidate to the video interview on the VCV vacancy": "Send the candidate to the video interview on the VCV vacancy:", "Send the candidates to the video interview on the VCV vacancy": "Send the candidates to the video interview on the VCV vacancy:", "No email found": "No email found", "No phone number found": "No phone number found", "Select a vacancy": "Select a vacancy", "Select a vacancies": "Select a vacancies", "Please note that candidate does not have email. If you click the Send button, only those candidates who have contact email will be sent to the video interview": "Please note that candidate does not have email and/or phone number. If you click the \"Send\" button, only those candidates who have both of this contacts will be sent to the video interview", "Please note that some candidates do not have email. If you click the Send button, only those candidates who have contact email will be sent to the video interview": "Please note that some candidates do not have email and/or phone number. If you click the \"Send\" button, only those candidates who have both of this contacts will be sent to the video interview", "Candidate has been sent to the video interview for VCV vacancy vacancy_VCV. As soon as he takes it, you will receive the link to the interview": "Candidate has been sent to the video interview for VCV vacancy {{vacancy_VCV}}. As soon as he takes it, you will receive the link to the interview", "The candidate has taken the video interview on VCV vacancy vacancy_VCV. Click on this icon to view the interview": "The candidate has taken the video interview on VCV vacancy {{vacancy_VCV}}. Click on this icon to view the interview", "Candidate has been sent to the video interview on the VCV vacancy vacancy_VCV": "Candidate {{candidate_name}} has been sent to the video interview on the VCV vacancy {{vacancy_VCV}}", "have been sent to the video interview on the VCV vacancy vacancy_VCV": "have been sent to the video interview on the VCV vacancy {{vacancy_VCV}}", "Candidates have been sent to the video interview on the VCV vacancy vacancy_VCV": "Candidates have been sent to the video interview on the VCV vacancy {{vacancy_VCV}}", "Video interview link": "Video interview link", "Candidate candidate_name has taken the video interview on the VCV vacancy vacancy_VCV. To view it, click View or use this link:": "Candidate {{candidate_name}} has taken the video interview on the VCV vacancy {{vacancy_VCV}}.<br> To view it, click \"View\" or use this link:<br>", "View-1": "View", "Candidate {candidate_name} has been sent to the video interview for the {vacancy_name_VCV} vacancy in VCV": "Candidate <a href='{{candidate_href}}'>{{candidate_name}}</a> has been sent to the video interview for the {{vacancy_VCV}} vacancy in VCV", "Candidate has been sent to the video interview for the {vacancy_name_VCV} vacancy in VCV": "Candidate has been sent to the video interview for the {{vacancy_VCV}} vacancy in VCV", "Candidate {candidate_name} has been sent to the video interview for the {vacancy_name_VCV} vacancy in VCV in vacancy": "Candidate <a href='{{candidate_href}}'>{{candidate_name}}</a> has been sent to the video interview for the {{vacancy_VCV}} vacancy in VCV on the vacancy <a href='{{vacancy_href}}'>{{vacancy_name}}</a>", "on the vacancy": "on the vacancy <a href='{{vacancy_href}}'>{{vacancy_name}}</a>", "Candidate has taken the video interview on VCV vacancy {vacancy_name_VCV}": "Candidate has taken the video interview on VCV vacancy {{vacancy_VCV}}", "Candidate {candidate_name} has taken the video interview on VCV vacancy {vacancy_name_VCV}": "Candidate <a href='{{candidate_href}}'>{{candidate_name}}</a> has taken the video interview on VCV vacancy {{vacancy_VCV}}", "The candidate was added from excel file": "The candidate was added from Excel file {{file_name}}", "was added from excel file": "was added from Excel file {{file_name}}", "The candidate was added from archive": "The candidate was added from archive {{archive_name}}", "The candidate was added from recall": "The candidate was added from recall", "was added from archive": "was added from archive {{archive_name}}", "The candidate was updated from excel file": "The candidate was updated from Excel file {{file_name}}", "The candidate was updated from archive": "The candidate was updated from archive {{archive_name}}", "was updated from excel file": "was updated from Excel file", "was updated from archive": "was updated from archive {{archive_name}}", "Specify the time": "Specify the time", "Enter_new_users_email_2": "Enter a new user's email", "Candidate number in the list": "Candidate number in the list", "level of language": "Language level", "ChooseTheLevel": "Choose the level", "Low level": "Low level", "Below the average": "Below the average", "Average level": "Average level", "Above average": "Above average", "High level": "High level", "Percentiles": "Percentiles", "Interpretation": "Interpretation", "Enter email": "Enter email:", "Job description": "Job responsibilities", "incorrect time for the test": "Incorrect time entered for passing the test", "RequiredCustomFields": {"vacancy": "Make this field mandatory for filling in the creation of a vacancy", "candidate": "Make this field mandatory for filling in the creation of a candidate", "client": "Make this field mandatory for filling in the creation of a client"}, "RequiredCustomFields_hint": {"vacancy": "This field is mandatory to fill in when creating a vacancy", "candidate": "This field is mandatory to fill in when creating a candidate", "client": "This field is mandatory to fill in when creating a client"}, "Empty mandatory field": "Fill mandatory field", "add_from_parsing": "Added manually", "Invited by user": "Invited by <a href='{{userHref}}'>{{userName}}</a>", "By user action": "By users action", "auth_enter_email": "Enter email:", "auth_enter_psw": "Enter password:", "contact_phone": "Contact phone", "contact_name": "Contact name", "emailToSendResume": "Where to send a resume", "Here could be listed the search criteria for each custom field that you add to the candidate profile. Firstly create your custom fields and you'll be able to search for the info stated there": "Here could be listed the search criteria for each custom field that you add to the candidate profile. Firstly <a class='internal-link' href='#/company/custom-fields'> create your custom fields </a> and you'll be able to search for the info stated there", "Here could be the search criteria for each custom field added to the candidate profile. Your account admin is able to add them": "Here could be the search criteria for each custom field added to the candidate profile. Your account admin is able to add them", "Search criteria for each additional custom field added to the candidate profile will be listed here.  Add more custom fields and you'll be able to search your database as a Pro!": "Search criteria for each additional custom field added to the candidate profile will be listed here.  <a class='internal-link' href='#/company/custom-fields'> Add more custom fields </a> and you'll be able to search your database as a Pro!", "Search criteria for each additional custom field added to the candidate profile will be listed here. Your account admin is able to add them": "Search criteria for each additional custom field added to the candidate profile will be listed here. Your account admin is able to add them", "Only unique candidates counts. A unique candidate - is a candidate , who was at the stage on this vacancy only once": "Only unique candidates counts. A unique candidate - is a candidate , who was at the recruiting stage only once", "longlist/applied_people": "Long list/Applied People", "The order of the vacancy stages was set as default by user": "The order of the recruiting stages was set as default by user", "Vacancy stages order was set as default": "Recruiting stages order was set as default", "Candidates profile": "Candidates profile", "was updated when applying for a vacancy": "was updated when applying for a vacancy", "Integrate your email to get the possibilities to": "Integrate your email to get the possibilities to:", "Integrate your mailbox": "Integrate your mailbox", "emIntPossibilities": {"mailing-title": "To send mass mailings on candidate database.", "massMailing": "Create and send bulk emails to candidates from the system", "personalMailing": "Correspond with candidates from system interface", "import": "Automatically import CV files from your mailbox. The profiles of these candidates will be generated in the system", "signature": "Set your own signature in emails (the text that is automatically added to the end of each message)"}, "Integrated mailbox list": "Integrated mailbox list", "Add new mailbox": "Add new mailbox", "Discard changes": "Discard changes", "Choose your mailbox provider": "Choose your mailbox provider", "New mailbox integration": "New mailbox integration", "Mailbox settings": "Mailbox settings", "Integration status": "Mail integration status", "Please select your mailbox provider": "Please select your mailbox provider", "To the step": "To the step", "Configure your mailbox settings": "Configure your mailbox settings", "Go back to Step 1": "Go back to step 1", "Go back to Step 2": "Go back to step 2", "Your mailbox integration status": "Your mailbox integration status", "Imap/Pop3 connection status": "Imap connection status", "Smtp connection status": "Smtp connection status", "successfully integrated mailbox": "You have successfully integrated your mailbox. Click <b>‘Finish’</b> to continue.", "Finish_1": "Finish", "Remove integrated mailbox": "Remove integrated mailbox", "Are you sure you want to remove mailbox?": "Are you sure you want to remove mailbox?", "Change": "Change", "Signature": "Signature", "Save changes": "Save changes", "Change mailbox settings": "Change mailbox settings", "on_1": "on", "off": "off", "for the": "for the", "Number of CVs added from the mailbox": "Number of CVs added from the mailbox", "This mailbox has successfully been removed": "This mailbox has successfully been removed", "Please select your mailbox provider to proceed to the next step": "Please select your mailbox provider to proceed to the next step", "Please note that you also may need to configure your mailbox to allow CleverStaff to import the CVs from your Inbox": "Please note that you also may need to configure your mailbox to allow CleverStaff to import the CVs from your Inbox", "Your email address": "Your email address", "pop3Imap hint": "<b>POP3</b> - protocol for e-mails receiving . <b>IMAP</b> - more modern protocol that supports the reception and the ability to manage emails directly on the mail server. Preferably use <b>IMAP</b>", "Imap incoming mail server address": "Imap incoming mail server address", "Pop3 incoming mail server address": "Pop3 incoming mail server address", "Smtp outgoing mail server address": "Smtp outgoing mail server address", "Imap Host": "Imap Host", "Pop3 Host": "Pop3 Host", "Smtp Host": "Smtp Host", "Imap Port": "Imap Port", "Pop3 Port": "Pop3 Port", "Smtp Port": "Smtp Port", "Imap incoming mail server port": "Imap incoming mail server port", "Pop3 incoming mail server port": "Pop3 incoming mail server port", "Smtp outgoing mail server port": "Smtp outgoing mail server port", "SSL/TLS hint": "SSL and TLS both provide a way to encrypt a communication channel. TLS is the successor to SSL. You need to select the right protocol your mailbox provider requires", "Other version": "Other version", "Please enter correct email": "Please enter correct email", "Please fill in the": "Please fill in the field", "You have changed some mailbox integration settings. Do you want to save or discard them?": "You have changed some mailbox integration settings. Do you want to save or discard them?", "All the changes have been canceled": "All the changes have been canceled", "Save them": "Save them", "Connection failed": "Connection failed", "Connected": "Connected", "CV import info": "The CVs would be automatically imported from this mailbox. Candidate profiles would be instantly generated and added to your account database", "Please enter your email address": "Please enter your email address", "Specify the version of Exchange": "Specify the version of Exchange", "This mailbox is already integrated": "This mailbox is already integrated", "Set your custom signature for this mailbox": "Set your custom signature for this mailbox", "This Gmail already integrated": "This mailbox is already integrated in another account.", "Integration with this mailbox in other accounts will be lost": "Integration with this mailbox in other accounts will be lost.", "General information": "General information", "Here could be the search criteria for each custom field added to the client profile. Your account admin is able to add them": "Here could be the search criteria for each custom field added to the client profile. Your account admin is able to add them", "Here could be listed the search criteria for each custom field that you add to the client profile. Firstly create your custom fields and you'll be able to search for the info stated there": "Here could be listed the search criteria for each custom field that you add to the client profile. Firstly <a class='internal-link' href='#/company/custom-fields'> create your custom fields </a> and you'll be able to search for the info stated there", "Search criteria for each additional custom field added to the client profile will be listed here.  Add more custom fields and you'll be able to search your database as a Pro!": "Search criteria for each additional custom field added to the client profile will be listed here.  <a class='internal-link' href='#/company/custom-fields'> Add more custom fields </a> and you'll be able to search your database as a Pro!", "Search criteria for each additional custom field added to the client profile will be listed here. Your account admin is able to add them": "Search criteria for each additional custom field added to the client profile will be listed here. Your account admin is able to add them", "Find clients": "Find clients", "Find vacancies": "Find vacancies", "Partial match": "Partial match", "Search by full words": "Full match", "Search by vacancies": "Search by vacancies", "Search by candidates": "Find candidates", "Search by clients": "Search by client`s name and contact persons", "Here could be the search criteria for each custom field added to the vacancy. Your account admin is able to add them": "Here could be the search criteria for each custom field added to the vacancy. Your account admin is able to add them", "Here could be listed the search criteria for each custom field that you add to the vacancy. Firstly create your custom fields and you'll be able to search for the info stated there": "Here could be listed the search criteria for each custom field that you add to the vacancy. Firstly <a class='internal-link' href='#/company/custom-fields'> create your custom fields </a> and you'll be able to search for the info stated there", "Search criteria for each additional custom field added to the vacancy will be listed here. Your account admin is able to add them": "Search criteria for each additional custom field added to the vacancy will be listed here. Your account admin is able to add them", "Search criteria for each additional custom field added to the vacancy will be listed here.  Add more custom fields and you'll be able to search your database as a Pro!": "Search criteria for each additional custom field added to the client profile will be listed here.  <a class='internal-link' href='#/company/custom-fields'> Add more custom fields </a> and you'll be able to search your database as a Pro!", "Search may take more than 10 seconds": "The search may take more than 10 seconds", "CleverStaff selects candidates from your database": "<p><PERSON><PERSON><PERSON><PERSON>taff picks candidates from your database by match</br>with criterias from vacancy description, such as: \"Position\",</br>\"Salary\", \"Location\", \"Knowledge of languages\"</p>", "CleverStaff will show candidates whose certain criteria do not match": "<p>Salary and location of such candidates may not match</br>with vacancy requirements</p>", "Select all": "Select all", "Select all-v2": "Select all", "Select all-v3": "Select all", "Fill_in_these_fields_to_fill_the_progress_bar": "<p>Please, fill the following fields for for effective work with the candidate: <ul><li>Last Name</li><li>First Name</li><li>Location</li><li>Contacts</li><li>Desired Position</li><li>Desired Salary</li><li>Employment Type</li><li>Slills</li><li>Languages</li><li>Total work experience</li></ul></p>", "Found n": "Found: <span>{{amount}}</span>", "Found_candidates": "Found {{candidates}} candidates.", "Found_candidate": "Found {{candidates}} candidate.", "Change_autoSelect_settings": "To change the auto-select settings, go to", "Parameter_settings": "Parameter settings", "Go to advanced search": "Go to advanced search", "save with at least one specified parameter": "You can save with at least one specified parameter", "parameters as standard for this vacancy": "Set these parameters as standard for this vacancy", "parameters as standard for all vacancies": "Set these parameters as standard for all vacancies", "Select the options by which": "Select the options by which the most suitable candidates for your vacancies will be displayed.", "Language skills": "Language skills", "not specified": "not specified", "not specified auto selection": "not specified", "Find candidates for a vacancy": "Find candidates for a vacancy", "here are no candidates": "Unfortunately, there are no candidates in your database that meet the specified requirements.", "effective selection employment type": "For more effective selection of candidates, specify the type of employment", "effective selection role": "For more effective selection of candidates, specify the required role", "effective selection experience": "For more effective selection of candidates, specify the required experience", "effective selection skills": "For more effective selection of candidates, specify the skills", "effective selection location": "For more effective selection of candidates, specify the location", "effective selection languages": "For more effective selection of candidates, specify the knowledge of languages", "note that the specified employment type": "Please note that the specified type of employment will be added and published in the job description in our system and on external resources", "note that the specified role": "Please note that the specified role will be added and published in the job description in our system and on external resources", "note that the specified experience": "Please note that the specified experience will be added and published in the job description in our system and on external resources", "note that the specified skills": "Please note that these skills will be added and published in the job description in our system and on external resources.", "note that the specified languages": "Please note that these languages will be added and published in the job description in our system and on external resources", "note that the specified location": "Please note that the specified location will be added and published in the job description in our system and on external resources", "Search by candidates no bool": "Find candidates", "Confirm change number of paid users": "Confirm change number of paid users", "We noticed that you have chosen fewer number of users than you have at your account.": "We noticed that you have chosen fewer number of users than you have at your account.", "Please pay attention that everyday payment bill will be made for all the paid active users.": "Please pay attention that everyday payment bill will be made for all the paid users.", "If you want to change the number of paid users (constantly or temporary) you can do it here:": "If you want to change the number of paid users (constantly or temporary) you can do it here:", "If you have any questions, our technical support will help you": "If you have any questions, our technical support will help you.", "clear": "Clear", "pay_1": "Pay", "Go back_": "Go back", "Minimum months to pay - 2": "Minimum number of months for payment - 2", "By_comments": "By comments", "Search_by_comments": "Search by comments", "Please select the stage": "Please select the stage", "Integration with this mailbox is lost. Integrate mail again for recovery": "Integration with this mailbox is lost. Integrate mail again for recovery.", "Invited by": "Invited by", "Select candidates to send": "Select candidates to send", "Make first payment and receive a 10% discount!": "Make first payment and receive a 10% discount!", "You have used the referral link": "You have used the referral link to register.", "chance to receive a discount": "CleverStaff gives you the chance to receive a <b>10%</b> discount if you will make your first payment within <b>14</b> days from your registration date!", "This offer will end on {discount_end_date}, hurry up!": "This offer will end on <b>{{discount_end_date}}</b>, hurry up!", "Pay now": "Pay now", "Receive bonus": "Receive bonus", "You’ve got a bonus!": "You’ve got a bonus!", "Referral recommendation worked": "Your recommendation worked - <b>{{orgName}}</b> paid CleverStaff <b>${{amount}}</b>!", "Your bonus is already available to you": "You helped your colleagues choose a really good solution! Your bonus <b>${{bonusAmount}}</b> is already available to you.", "Your referral bonus is already available to you": "Your referral bonus <b>${{bonusAmount}}</b> is already available to you.", "You are doing well!": "Keep it up!", "Referral Program": "Referral Program", "Referral program": "Referral program", "Share your referral link": "Share your referral link with your mates and <br>receive 100% of their first payments (up to $500 each). Your referrals will receive a 10% discount on first payment!", "How It Works": "How It Works", "General": "General", "GeneralScore": "General", "My Earnings": "My Earnings", "Copy to clipboard": "Copy to clipboard", "Send the link to your friend": "Send the link to your friend", "Email of your friend": "Email of your friend", "Your message": "Your message", "Please fill in the email field": "Please fill in the email field", "STEP": "STEP", "Forward to step {step}": "Forward to step {{step}}", "Back to step {step}": "Back to step {{step}}", "Find your personal referral link on the referral page in CleverStaff": "Open referral program page in CleverStaff and find your personal <span class='bold'>referral link</span>", "Share this link with your mates or post it anywhere": "<span class='bold'>Share this link</span> with your mates or post it <span class='bold'>anywhere else</span>", "Sender uses your link to register in CleverStaff and makes the first payment": "Referral uses your link to register in CleverStaff and makes <span class='bold'>the first payment</span> with <span class='bold'>10% discount</span>", "You receive 50% of your referral first-month payment (but no more than $400)": "You receive 50% of your referral's <span class='bold'>first payment</span> (up to $400)", "You choose the way to get your rewards. You can either transfer money to your bank account or top up your CleverStaff account. Easy - Peasy!": "You choose the way to get your rewards. You can either transfer money to your <span class='bold'>bank account</span> or top up <span class='bold'>your</span> CleverStaff <span class='bold'>account</span>.<br> Easy - Peasy!", "Earnings title": "Your personal referral link usage statistics are provided in the table below. You can track the status and amounts of your rewards", "Who gave the referral link": "Who gave the referral link:", "Need to pay to the user": "Need to pay to the user:", "Date of registration in CleverStaff": "Date of registration in CleverStaff", "Payment status": "Payment status", "Reward amount": "Reward amount", "Withdrawal status": "Withdrawal status", "Withdrawal history": "Withdrawal history", "Total withdrawing reward ${total}": "Total withdrawing reward ${{total}}", "Total withdrawn ${total}": "Total withdrawn ${{total}}", "Withdraw my Earnings": "Withdraw my Earnings", "Referral issue": "Please do not hesitate to contact us by email: <a href:'mailto:<EMAIL>'><EMAIL></a>, if you have any problems or questions regarding our referral program", "Link copied": "Link has been copied to clipboard", "Choose rewards": "Choose rewards", "Withdraw my rewards": "Withdraw my rewards", "Top up my CleverStaff account": "Top my CleverStaff account up", "Withdraw money to my bank account": "Withdraw money to my bank account", "Yes, top it up": "Top it up", "Are you sure you want to top up your CleverStaff?": "Are you sure you want to top your CleverStaff account up with <span class='bold'>${{rewardAmount}}</span>?", "Are you sure you want to withdraw ${total_reward_amount} to your bank account?": "Are you sure you want to withdraw <span class='bold'>${{rewardAmount}}</span> to your bank account?", "Please provide your actual email address": "Please provide your actual email address", "Please provide correct email address": "Please provide correct email address", "Pay till {discount_end_date} and receive a 10% discount": "Pay till <span class='bold'>{{discountEndDate}}</span> and receive a <span class='bold'>10%</span> discount", "discount": "discount", "You have no new earnings": "You have no earnings yet", "withdrawn": "withdrawn", "Bank transfer": "Bank transfer", "Crypto": "Crypto", "crypto-payment_v1": "Send BTC on account (Bitcoin BTC): ", "crypto-payment_USDT": "Send USDT on account (USDT TRC20): ", "crypto-payment_USDT_ERC20": "Send USDT on account (USDT ERC20): ", "crypto-payment_v2": "For Binance users - send USDT or BTC to:", "with a comment on who": "with a comment on who to credit", "send a screenshot": "Send a screenshot of the transaction to ", "CleverStaff account top up": "CleverStaff account top up", "Integrate mailbox for send emails from the system": "To send a letter, add integration with email", "Your referral company name": "Your referral company name", "Subject_1": "Subject", "Maximum number of characters exceeded by": "Maximum number of characters exceeded by", "Fill your friend’s email in": "Fill your friend’s email in", "Withdraw request successful title": "Request successfully sent", "Withdraw request successful text": "Your request has been successfully sent. CleverStaff finance department will contact you by the email address you provided  above.<br><br> Please contact our technical support via chat or email, if you do not receive a response within a few days", "Link has been sent to your friend": "Link has been sent to your friend", "Selected:": "Selected:", "Selected": "Selected", "Minimum months to pay - 3": "Minimum number of months for payment - 3", "% from total of acc": "% from total <br>of account", "% from user’s": "% from their", "manually": "Added manually", "recall": "Recall", "linkedin": "LinkedIn", "hh": "HH", "workua": "Work.ua", "rabotaua": "Robota.ua", "rabota.ua": "Robota.ua", "superjob": "Superjob", "superjob.ua": "Superjob.ua", "cvlv": "CVLV", "Candidates mooved to the stage": "Candidates mooved to the stage", "jobstutby": "Jobs.tut.by", "jobkg": "Job.kg", "job.kg": "Job.kg", "other": "Other", "Access is denied": "Access is denied", "History_1": "History", "Test was removed. The automatic sendings of the test were disabled": "Test was removed. The automatic sendings of the test were disabled", "User performance": "User performance", "Displays weekly performance of different users for chosen vacancies within a picked period of time": "Displays weekly performance of different users for chosen vacancies within a picked period of time", "Display weekly stats": "Display weekly stats", "Show vacancies_2": "Show vacancies", "Show users": "Show users", "Select vacancy": "Select vacancy", "Select status": "Select status", "Generate report": "Generate report", "wait": "On hold", "Export to Excel_2": "Export to Excel", "Report from": "Report from", "Candidates moved to the stage": "Candidates moved to the stage", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Total_January": "Total January", "Total_February": "Total February", "Total_March": "Total March", "Total_April": "Total April", "Total_May": "Total May", "Total_June": "Total June", "Total_July": "Total July", "Total_August": "Total August", "Total_September": "Total September", "Total_October": "Total October", "Total_November": "Total November", "Total_December": "Total December", "Total_Period": "Total Period", "Not available with time interval 3 months or more.": "Not available with time range 3 months or more.", "No such stage in this vacancy": "No such stage in this vacancy", "disqualified": "Disqualified", "You can select up to 10 vacancies": "You can select up to 10 vacancies", "vacancy_status_amount": {"open": "New {{amount}}", "expects": "On Hold {{amount}}", "inwork": "In Progress {{amount}}", "onhold": "On Hold {{amount}}", "replacement": "Replacement {{amount}}", "payment": "Payment {{amount}}", "completed": "Filled {{amount}}", "canceled": "Canceled {{amount}}", "deleted": "Deleted {{amount}}", "recommendation": "Template {{amount}}"}, "All users": "All users", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "Mo": "Mo", "Tu": "Tu", "We": "We", "Th": "Th", "Fr": "Fr", "Sa": "Sa", "Su": "Su", "Enter correct email": "Enter correct email", "Country not found": "Country not found", "City not found": "City not found", "Can`t find city?": "Can’t find the city you need? Find it in google maps", "Choose the country": "Choose the country", "Choose the city": "Choose the city", "Add new location (google search)": "Add new location (google search)", "Hiring date": "Hiring date", "Probation period": "Probation period", "Thanks! You’ve updated your application on this vacancy.": "Thanks! You’ve updated your request on this vacancy.", "Candidate sources report": "Candidate sources report", "Reports will show statistics on the types of sources and help you understand which source gives a greater percentage of closed vacancies.": "Reports will show statistics on the types of sources and help you understand which source gives a greater percentage of closed vacancies", "Number of candidate sources": "Number of candidate sources", "The report will show statistics on the number of candidates  sources in the context of users and time interval": "The report will show statistics on the number of candidates  sources in the context of users and time interval", "Source": "Source", "Source:": "Source:", "Added:": "Added:", "Added": "Added", "% from total": "% from total", "The share of each source in the total number of added candidates": "The share of each source in the total number of added candidates", "Sources efficiency": "Sources efficiency", "Sources efficiency description": "The report will show the sources of successful candidates - those who have been transferred to the \"Hired\" stage", "Show vacancies": "Show vacancies:", "Total hires for the period": "Total hires for the period", "Hiring": "<PERSON><PERSON>", "Source efficiency": "Source efficiency", "Number of successful candidates by source": "Number of hired candidates by source", "The hiring is": "Hiring match candidates moved to the \"Hired\" stage", "of the total account": "of the total account", "from hiring": "from hiring", "other -s": "Other", "add_manually": "Added manually", "add_from_recommendation": "Recommendation", "add_from_email": "Import from email", "add_from_excel": "Import from Excel", "add_from_archive": "Add from archive", "add_from_recall": "Recall", "add_from_grc": "GRC", "add_from_linkedinNew": "LinkedIn", "add_from_hh": "HH", "add_from_olx": "Olx", "add_from_dou": "<PERSON><PERSON>", "add_from_pracuj": "Pracuj.pl", "add_from_workua": "Work.ua", "add_from_djinni": "<PERSON><PERSON><PERSON>", "add_from_rabotauz": "Rabota.uz", "add_from_rabotaua": "Robota.ua", "add_from_delucru": "Delucru.md", "add_from_rabotamd": "Rabota.md", "add_from_superjob": "Superjob", "add_from_cvlv": "CV.lv", "add_from_jobstutby": "Jobs.tut.by", "add_from_jobkg": "Job.kg", "add_from_jobcannon": "JobCannon", "add_from_wandify": "Wandify", "Total by source": "Total by source", "Total added by users": "Total added by users", "The number of candidates added from different sources by user": "The number of candidates added from different sources by user", "The number of candidates added from different sources by users": "The number of candidates added from different sources by users", "candidates out of": "candidates out of", "Export report to xlsx": "Export this report in .xlsx", "List of sources": "List of sources", "Sources": "Sources", "Have no custom sources": "You do not have additional to the list of system sources. You can add up to 20 of your sources and manually change them on the candidate profile.", "Add source": "Add source", "Enter the source name": "Enter the source name", "Add new source": "Add new source", "Source added": "Source added", "Edit source": "Edit source", "Are you sure you want to remove source": "Are you sure you want to remove the source", "System sources": "System sources", "Custom sources": "Custom sources", "All events": "All events", "Which I created": "Only mine that I created", "System sources info": "System sources are automatically inserted in the following cases: adding candidates from work sites, manually adding (creating in the system), receiving vacancy responses and automatically importing from mail. You can add your own sources and replace the system sources in the candidate profile. System sources cannot be deleted.", "Custom sources info": "You can add up to 35 of your sources and manually change them on the candidate profile.", "Source max length": "Source name must not exceed 30 characters", "Source list": "Source list", "deleted by administrator": "(deleted by administrator)", "Region was removed": "Region was removed", "Job search sites": "Job boards", "published": "published", "archived_status": "Vacancy is archived on the HeadHunter portal. To unarchive, go to the HeadHunter account settings and activate the vacancy", "Are you sure you want to delete a vacancy posted on the portal?": "Are you sure you want to delete a vacancy posted on the portal {{portal}}?", "The vacancy will be placed in the completed section on the portal": "The vacancy will be placed in the completed section on the portal {{portal}}", "Are you sure you want to delete a integration with portal?": "Are you sure you want to disable integration with {{portal}}?", "Post Job to portal": "Post Job to {{portal}}", "Edit Job to portal": "Edit Job to {{portal}}", "Publish": "Publish", "Archive": "Archive", "Save changes(publish)": "Save changes", "Specialization": "Specialization", "Professional role": "Professional role", "Subcategory": "Subcategory", "Select no more than 3": "Select no more than 3", "Select specialization": "Select specialization", "Select professional role": "Choose role", "The list of countries corresponds to the countries available on the HeadHunter portal": "The list of countries corresponds to the countries available on the \"HeadHunter\" portal", "Type of allocation": "Type of allocation", "Specify the type of accommodation": "Specify the type of accommodation", "Select vacancy type": "Select vacancy type", "Vacancy type": "Vacancy type", "Vacancy description": "Vacancy description", "Additional information": "Additional information", "Specify the type of job placement according to the rate of your account": "Specify the type of job placement according to the rate of your account", "Select vacancy employment type": "Select employment type", "Select vacancy schedule": "Select schedule", "Schedule": "Schedule", "Select vacancy experience": "Select experience", "Save the changes made to the current vacancy in the system?": "Save the changes made to the current vacancy in the system?", "Change job title": "Change job title", "Change job description": "Change job description", "Specify recruiter contacts": "Specify recruiter contacts", "Post link": "Post link", "Post link (for the candidate)": "Post link (for the candidate)", "Post link (for the recruiter)": "Post link (for the recruiter)", "Vacancy successfully published": "Posting vacancy {{vacancyName}} on <PERSON><PERSON>unt<PERSON> was successful!", "Vacancy successfully edited": "Editing vacancy {{vacancyName}} on <PERSON><PERSON>unt<PERSON> was successful!", "Feedback_integration": "<PERSON><PERSON><PERSON>", "Views_integration": "Views integration", "Statistic": "Statistic", "End date of publication": "End date of publication", "Vacancy successfully archived": "Vacancy {{vacancyName}} successfully archived", "was published on": "was published on {{portal}}", "has been published on the portal": "has been published on the {{portal}} portal", "has been edited on the portal": "has been edited on the {{portal}} portal", "has been archived on the portal": "has been archived on the {{portal}} portal", "has been removed from the portal": "has been removed from the {{portal}} portal", "by user": "by <a href={{href}}>{{username}}</a>", "Congratulations! Integration with HeadHunter is connected": "Congratulations! Integration with <PERSON><PERSON><PERSON><PERSON> is connected!", "Rabota.ua intergation setup": "Robota.ua intergation setup", "Enter your robota.ua login": "Enter your robota.ua login", "Enter your robota.ua password": "Enter your robota.ua password", "please select at least one job type": "Please select at least one job type", "please select at least one sphere": "Please select at least one field of activity", "please select at least one recruiter": "Please select at least one recruiter", "selectCategory": "Please select a category", "Publish type": "Publish type", "please select Publish type": "Please select publish type", "You can extend the publication in the publication settings on the": "You can extend the publication in the publication settings on the", "Continue posting": "Extend the publication", "portal": "portal", "You are logged out of your rabota.ua account": "You are logged out of your robota.ua account", "You are logged out of your djinni account": "You are logged out of your djinni.co account", "Work.ua intergation setup": "Work.ua intergation setup", "Djinni intergation setup": "Djinni intergation setup", "Delucru integration setup": "Delucru.md intergation setup", "login_2": "<PERSON><PERSON>", "Enter your work.ua login": "Enter your work.ua login", "Enter your djinni login": "Enter your djinni login", "Enter your work.ua password": "Enter yourEnter your work.ua password", "Enter your djinni password": "Enter your djinni password", "Job type": "Job type", "Job type 2": "Employment", "category": "Category", "Publication type": "Publication type", "salary comment": "Salary comment", "The type of publication corresponds to the types of vacancy placement on work.ua": "The type of publication corresponds to the types of vacancy placement on work.ua", "The type of publication corresponds to the types of vacancy placement on rabota.ua": "The type of publication corresponds to the types of vacancy placement on robota.ua", "Post contacts": "Post contacts", "Select whether the recruiter’s name and phone number should be indicated in the vacancy at work.ua": "Select whether the recruiter’s name and phone number should be indicated in the vacancy at work.ua", "Change the name of the vacancy in the system": "Change the name of the vacancy in the system", "Change vacancy description in the system": "Change vacancy description in the system", "Extend": "Extend", "Renewal of publication on the portal": "Renewal of publication on the {{portal}}", "Publication deleted": "Publication deleted", "Unfortunately, it was not possible to connect the integration to your HeadHunter account": "Unfortunately, it was not possible to connect the integration to your HeadHunter account.", "Setting up regular payments": "Regular payments settings", "Connect the card for automatic recharge": "<p>Connect card for automatic debit</p>", "Add Card": "Add card", "Active card": "Active Card", "Are you sure you want to remove the card?": "Are you sure to delete this card?", "Regular Payment Error": "The automatic debit from the card failed due to problems with connecting the card. ", "Card Id": "Card ID: ", "Please check mail": "Please check the mail ", "Empty_search": {"Use": "Use", "the advanced search": " the advanced search", ",using more criteria to obtain the necessary data.": ", using more criteria to obtain the necessary data"}, "candidates_onSearch": "candidates", "vacancies_onSearch": "vacancies", "clients_onSearch": "clients", "Pleas add description": "Please fill well the vacancy description to get matched candidates", "Integration with work sites": "Integration <br> with work sites", "Integration with job search sites": "Integration with job search sites", "Integration with job search site": "Integration with job search site", "Connect your work account to publish and collect statistics": "Connect your work account to publish vacancies, automatically receive applications and collect statistics", "Connect your work account to publish vacancies": "Connect your work account to publish vacancies", "Integration with _portal successfully removed": "Integration with {{portal}} successfully removed", "To post a vacancy on _portal connect the integration in the section:": "To post a vacancy on {{portal}} connect the integration in the section:", "To post a vacancy on _portal connect or link a vacancy in CleverStaff with an already published one the integration in the section:": "To post a vacancy on {{portal}} or link a vacancy in CleverStaff with an already published one connect the integration in the section:", "Integration Setup": "Integration Setup", "You do not have integration with the _portal yet.": "You do not have integration with the {{portal}} yet.", "Post this vacancy on the company’s Facebook page": "Post this vacancy on the company’s Facebook page", "Integrate": "Integrate", "Publish _portal": "Publish on {{portal}}", "Enter a date to generate report": "Enter a date to generate report", "Score cards": "Scorecards", "Scorecards": "Scorecards", "Create score card": "Create scorecard", "Score card creation": "Scorecard creation", "Card editing": "Card editing", "Card name": "Card name", "Section name": "Section name", "Item name": "Item name", "Enter name": "Enter name", "Add item": "Add item", "Add section": "Add section", "Create": "Create", "Cancel": "Cancel", "Cancel_1": "Cancel", "Score card successfully created": "Score card successfully created", "Score card successfully edited": "Score card successfully edited", "List of linked vacancies": "List of linked vacancies", "Card preview": "Card preview", "Are you sure you want to delete score card?": "Are you sure you want to delete score card?", "All ratings and comments on this card for all vacancies and candidates will be deleted.": "All ratings and comments on this card for all vacancies and candidates will be deleted.", "Apply changes": "Apply changes", "Editing score card": "Editing score card", "Editing the selected card will entail changes in other vacancies.": "Editing the selected card will entail changes in other vacancies.", "Create copy selected score card": "Create copy selected score card", "Edit without copying": "Edit without copying", "_cardCount more": "{{moreCard}} more", "Scorecard successfully duplicated": "Scorecard successfully duplicated", "Scorecard successfully deleted": "Scorecard successfully deleted", "scoreCards": {"You have": "You have {{scoreCardCount}}", "oneScoreCard": "score card", "twoFourScoreCard": "score cards", "moreThanFourScoreCard": "score cards", "exportOneScorecardsXls": "Export scorecard to .xlsx", "exportAllScorecardsXls": "Export all scorecards to .xlsx"}, "Score card with that name already exists": "Score card with that name already exists", "You can not create a score card without one filled section and one filled item": "You can not create a score card without one filled section and one filled item", "Available scorecards": "Available scorecards", "Default": "Default card", "Old Default": "Old Default", "Are you sure you want to replace the card?": "Are you sure you want to replace the card?", "Card scores will be deleted": "Card scores will be deleted.", "Replace": "Replace", "Pay attention": "Pay attention!", "Evaluate candidate": "Evaluate candidate", "Evaluate": "Evaluate", "no evaluations": "no evaluations", "Start typing": "Start typing", "Overall rating": "Overall rating", "You have already evaluated this candidate": "(You have already evaluated this candidate)", "Get it": "Get it", "Score card": "Scorecard", "Candidate successfully evaluated": "Candidate successfully evaluated", "Your candidate scorecard has been deleted": "Your candidate scorecard has been deleted", "Bad": "Bad", "Normal": "Normal", "Good": "Good", "scores": {"oneScore": "score", "twoFourScore": "scores", "moreThanFourScore": "scores"}, "Added scorecard": "Added scorecard", "Added scorecards": "Added scorecards", "Removed scorecard": "Removed scorecard", "Removed scorecards": "Removed scorecards", "general": "general", "More info": "More", "Some of the emails are invalid, so we could not send them a newsletter.": "Some of the emails are invalid, so we could not send them a newsletter.", "By overall score": "By overall score", "Integration with the portal has been disabled": "Integration with the {{portal}} portal has been disabled", "Integration with the portal has been enabled": "Integration with the {{portal}} portal has been enabled", "other country": "Other country", "Maximum size": "Maximum size", "mailing_2": "The newsletter", "add_mailing": "Created a new newsletter", "clone_mailing": "copied", "Copied!": "Copied!", "update_mailing": "released", "send_mailing": "was sent", "remove_mailing": "deleted", "send_mailing_candidate": "The newsletter", "send_mailing_candidate_female": "sent newsletter", "send_mailing_candidate_other": "was sent.", "operators": "Operators", "vacancy link": "Vacancy link", "interview date and time": "Interview date and time", "attendance date and time": "Attendance date and time", "recruiter's name": "<PERSON><PERSON><PERSON><PERSON>'s name", "recruiter's phone": "Rec<PERSON><PERSON>'s phone", "recruiter's Skype": "Rec<PERSON><PERSON>'s Skype", "recruiter's Facebook": "Recruiter's Facebook", "recruiter's LinkedIn": "Recruiter's LinkedIn", "Your corporate mailbox is not connected": "Your corporate mailbox is not connected", "You have not turned on sending mass mailing from the corporate mailbox": "You have not turned on sending mass mailing from the corporate mailbox", "Please connect your corporate email to use mass-mailing": "Please connect your corporate email to use mass-mailing", "To use mass mailings you must enable sending emails from the integrated corporate mailbox in the CleverStaff system": "To use mass mailings you must enable sending emails from the integrated corporate mailbox in the CleverStaff system", "You have not turned on sending personal mailing from this mailbox": "You have not turned on sending personal mailing from this mailbox", "To use personal mailing you must connect a mailbox in the СleverStaff system": "To use personal mailing you must connect a mailbox in the СleverStaff system", "To use personal mailing you must enable sending emails from the integrated mailbox in the CleverStaff system": "To use personal mailing you must enable sending emails from the integrated mailbox in the CleverStaff system", "You": "You", "do not have enough money": "do not have enough money", "Do not have enough money": "Do not have enough money", "in your CleverStaff balance. To send letters you need to top up the balance.": "in your CleverStaff balance. To send letters you need to top up the balance.", "The cost of mailing will be deducted from your balance in the CleverStaff system.": "The cost of mailing will be deducted from your balance in the CleverStaff system.", "You want to send letters": "You want to send letters", "Estimated cost": "Estimated cost", "Top up": "Top up", "Attention is important!": "Attention is important!", "Mailbox integration": "Mailbox integration", "Personal mailing info": "You may send and receive personal emails to your candidates and colleagues in the CleverStaff interface.", "Mass mailing info": "Ability to send mass\nmailing from the CleverStaff system", "personal-mailing": {"first-part": "Personal", "second-part": "mailing"}, "mass-mailing": {"first-part": "Mass", "second-part": "mailing"}, "Not corporate email info": "The mass-mailing feature requires a corporate email. Email service providers (gmail, yahoo, etc) don't allow the mass-mailing.", "Personal mailing log": "Personal mailing log", "Your mailbox is not connected": "Your mailbox is not connected", "You have not activated the mailbox": "You have not activated the mailbox", "No letters": "No letters", "Connect email on": "Connect email on", "mail integration page": "mail integration page", "New letter": "New letter", "Creating a letter": "Creating a letter", "whom": "Whom", "Subject line of the letter": "Subject line of the letter", "Recipient's Mails": "Recipient's Mails", "Templates": "Templates", "Select a mail to send letters to it (will become the default mail)": "Select a mail to send letters to it (will become the default mail)", "Return default template text": "Return default template text", "Mail to which the letter will be sent (by default)": "Mail to which the letter will be sent (by default)", "The candidate has no email address.": "The candidate has no email address.", "Send letter": "Send letter", "You are going to send letters": "You are going to send letters", "Group": "Group", "Mass": "Mass", "Free": "Free", "Choose": "<PERSON><PERSON>", "Go to integration": "Go to integration", "more email is available": "{{value}} more email is available", "email is available": "{{value}} more email is available", "Add recipient": "Add recipient", "already selected": "already selected", "Start typing the name": "Start typing the name", "Delete all recipients without mail": "Delete all recipients without mail", "Not all recipients have emails": "Not all recipients have emails", "mail not specified": "mail not specified", "NO INFORMATION": "NO INFORMATION", "Account balance": "Account balance", "Maximum length of score card name is 45 characters": "Maximum length of score card name is 45 characters", "Score card was assign to vacancy": "Score card {{card}} was assign to vacancy <a href='#/vacancies/{{vacancy.localId}}' target='_blank'>{{vacancy.position}}</a>", "Score cards was assign to vacancy": "Score cards {{card}} was assign to vacancy <a href='#/vacancies/{{vacancy.localId}}' target='_blank'>{{vacancy.position}}</a>", "was assign to vacancy (singular)": "was assign to vacancy <a href='#/vacancies/{{vacancy.localId}}' target='_blank'>{{vacancy.position}}</a>", "was assign to vacancy (plural)": "was assign to vacancy <a href='#/vacancies/{{vacancy.localId}}' target='_blank'>{{vacancy.position}}</a>", "Score card was deleted": "Removed scorecard {{card}} from the vacancy <a href='#/vacancies/{{vacancy.localId}}'>{{vacancy.position}}</a>", "Score cards was deleted": "Score cards {{card}} was deleted from vacancy <a href='#/vacancies/{{vacancy.localId}}'>{{vacancy.position}}</a>", "was deleted from vacancy (singular)": "from the vacancy <a href='#/vacancies/{{vacancy.localId}}'>{{vacancy.position}}</a>", "was deleted from vacancy (plural)": "was deleted from vacancy <a href='#/vacancies/{{vacancy.localId}}'>{{vacancy.position}}</a>", "was deleted from vacancy": "was deleted from the", "Attention!": "Attention!", "Dear users, due to changes in the security policy of Google, the method of integration with the Gmail mail service has changed. Please re-integrate your Gmail inbox.": "Dear users, due to changes in the security policy of Google, the method of integration with the Gmail mail service has changed. Please re-integrate your Gmail inbox.", "gmail-reintegration_title": "To successfully integrate your Gmail mailbox, follow these steps:", "gmail-reintegration_step1-text1": "Open the page for this", "gmail-reintegration_step1-link": "link", "gmail-reintegration_step1-text2": "(check that you are logged in under the desired account)", "gmail-reintegration_step2": "Open settings", "gmail-reintegration_step3": "Go to the IMAP / POP3 Settings tab, select “Enable IMAP” and save the settings with the button at the bottom of the page.", "For a more effective selection of candidates, specify the salary": "For a more effective selection of candidates, specify the salary.", "Please note that the specified salary will be added and published in the job description in our system and on external resources": "Please note that the specified salary will be added and published in the job description in our system and on external resources.", "Skip": "<PERSON><PERSON>", "sendInterviewCanceledNotification": "Send interview canceled notification", "wrong  file format": "Wrong file format", "candidate was updated by resume": "Candidate was updated by resume", "detail": "Detail", "Complete": "Complete", "Job experience": "Job experience", "Are you sure you want to update data": "Are you sure you want to update data?", "Delete changes made?": "Delete changes made?", "All entered data will be deleted": "All entered data will be deleted", "Are you sure you want to delete this work experience information?": "Are you sure you want to delete this work experience information?", "Data will be deleted permanently": "Data will be deleted permanently", "Are you sure you want to delete this educational information?": "Are you sure you want to delete this educational information?", "Add education": "Add education", "Add a job": "Add a job", "Period": "Period", "Educational institution": "Educational institution", "Specialty": "Specialty", "For example: KNU": "For example: KNU", "For example: business management": "For example: business management", "For example: manager": "For example: manager", "For example: Google": "For example: Google", "collapse": "Collapse", "Current balance": "Current balance", "Paid Users": "Paid Users", "Your tariff": "Your tariff", "Total will be credited": "Total will be credited", "payment history": "Payment history", "History of daily charges to your account.": "History of daily charges to your account", "Payment history on your account.": "Payment history on your account", "Card payment": "Card payment", "You have one card attached": "You have one card attached.", "You have cards attached": "You have {{cards}} cards attached", "You can change the default card at any time.": "You can change the default card at any time.", "to_pay": "To pay", "to remove the card": "To remove the card", "Card removed from list": "Card removed from list", "paymentsNotifications": {"accountBlockedInTwoDays": "After 3 days, your balance will run out of funds. Auto payment will be charged to {{card_number}}", "paymentsDisabledTwoDays": "After 3 days, your balance will run out of funds. Set up auto payment and the balance will be replenished automatically.", "accountBlockedInOneDay": "Your balance will run out of funds tomorrow. Please make sure that the balance of your {{card_number}} card contains the required amount for auto payment", "paymentsDisabledOneDay": "Your balance will run out of funds tomorrow. Choose a convenient payment method and make a payment."}, "There are not enough funds on your balance.": "There are not enough funds on your balance. ", "Your account will be blocked after 2 days.": "Your account will be blocked in 2 days. ", "Your account will be blocked tomorrow.": "Your account will be blocked tomorrow. ", "It is necessary to replenish the balance in the": "It is necessary to replenish the balance in the ", "Payment Settings": "Payment Settings", "This amount will be charged every 7 months": "This amount will be charged every {{monthCount}} months", "This amount will be charged every 1 months": "This amount will be charged every {{monthCount}} month", "This amount will be charged every 2 months": "This amount will be charged every {{monthCount}} months", "This amount will be charged every 21 months": "This amount will be charged every {{monthCount}} months", "You will receive the same bonus on every charge": "if the number of users does not change", "Set": "Set", "Delete selected card?": "Delete selected card?", "You have selected the last active card. After it is deleted, auto payment will be suspended. To resume automatic payments, connect a new one.": "You have selected the last active card. After it is deleted, auto payment will be suspended. To resume automatic payments, connect a new one.", "Standard": "Standard", "Corporate": "Corporate", "1 Recruiter": "1 Recruiter", "Choose from list": "Choose from list", "No payment stories yet": "No payment stories yet", "CleverStaff usage": "CleverStaff usage", "CleverStaff usage (Regular payment)": "CleverStaff usage (Regular payment)", "Refilling the balance": "Refilling the balance", "Refilling the balance with referral bonus": "Refilling the balance with referral bonus", "Regular Payment": "Regular Payment", "There are not integrated cards": "There are not integrated cards", "This card already exists": "This card already exists", "It is not possible to add a map because you added it earlier. Want to connect another?": "It is not possible to add a map because you added it earlier. Want to connect another?", "Payment type": "Payment method", "Approved (Regular payment)": "Approved (Regular payment)", "Refunded": "Refunded", "removed": "Removed", "by_client": "by client", "Approved": "Approved", "Declined (Regular payment)": "Declined (Regular payment)", "Expired": "Expired", "Declined": "Declined", "Pending": "Pending", "Fill": "Fill", "from workUa": "from work.ua", "refillingTheBalance": "Refilling balance", "Delivered": "Delivered", "Opened": "Opened", "Free emails will remain": "Free emails will remain", "No candidates selected yet": "No candidates selected yet", "Enter the name of the newsletter": "Enter the name of the newsletter", "new letter from": "New letter from", "new letters from_1": "new letters from", "new letters from_2": "new letters from", "new letters from_3": "new letters from", "To view": "Show", "Disconnect": "Disconnect", "Mailbox": "Mailbox", "Max. size of 1 file is 5mb": "Max. size of 1 file is 5mb", "Invalid file type": "Invalid file type", "Maximum number of recipients - 15": "Maximum number of recipients - 15", "Accept offer": "Accept offer", "Coming soon": "Coming soon", "CleverStaff updated version": "CleverStaff updated version and", "privacy policy": "privacy policy", "Agree": "Agree", "was added from job.kg": "was added from Job.kg", "was added from grc": "was added from GRC", "Create Mass Mailing": "Create Mass Mailing", "isEnoughLettersForMailing": {"zero": "You have run out of emails to send a mass mailing. The balance of free letters will be replenished on the 1st of the next month", "notEnough": "You do not have enough letters to send the mailing list. ", "oneLetter": "It is possible to send {{lettersNumber}} letter. ", "upToFiveLetters": "It is possible to send {{lettersNumber}} letters. ", "otherLetters": "It is possible to send {{lettersNumber}} letters. ", "oneRecipient": "{{recipientsNumber}} recipients have been added to the mailing list.  ", "upToFiveRecipients": "{{recipientsNumber}} recipients have been added to the mailing list.  ", "otherRecipients": "{{recipientsNumber}} recipients have been added to the mailing list. ", "toDeleteOne": "To continue sending the mailing list, delete the {{recipientsNumberToDelete}} recipient", "toDeleteOthers": "To continue sending the mailing list, delete the {{recipientsNumberToDelete}} recipients"}, "Every first day of the month 2000 letters appear on your account": "Every first day of the month 2000 letters appear on your account", "Sender name": "Sender name", "Sender mail": "Sender mail", "Mass mailing text": "Mass mailing text", "Recipients added": "Recipients added", "You can send 1 test letter": "You can send 1 test letter", "Return to step 1 to edit": "Return to step 1 to edit", "Return to step 2 to edit": "Return to step 2 to edit", "Invalid email address": "Invalid email address", "Are you sure you want to delete the mailing?": "Are you sure you want to delete the mailing?", "The mailing will be permanently deleted": "The mailing will be permanently deleted", "Remove mailing": "Remove mailing", "Cloning mailing": "Cloning mailing", "The mailing will be copied and added to the Prepared Mailings section": "The mailing will be copied and added to the Prepared Mailings section", "Statistics collection": "Statistics collection", "forgot password": "Forgot password?", "We sent the password reset link on your email": "We sent the password reset link on your email", "There is no user with this email in CleverStaff": "There is no user with this email in CleverStaff.", "Reset password": "Reset password", "We sent the password reset link on your email.": "We sent the password reset link on your email", "send_to_email": "Send to email", "Post this vacancy on a job board": "Post this vacancy on a job board", "Successful regular payment": "Your balance is replenished by {{price}} $", "charge_for": "Charge for {{users}} users", "Yes, download the invoice": "Yes, download the invoice", "Have you dublechecked your billing infomation?": "Have you doublechecked your billing infomation?", "Do you want to add card?": "Do you want to add card?", "After adding a card, payment will be automatically made in the amount of": "After adding a card, payment will be automatically made in the amount of", "You are trying to log in too often": "You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in {{time}} minutes.", "You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in 15 minutes": "You are trying to log in too often. It looks like you are trying to use the same account for different users. It contradicts our user agreement. Please try again in 15 minutes", "This candidate was added manually": "This candidate was added manually", "This candidate was added from recall": "This candidate was added from recall", "Your payment has been accepted. Check the payment status in the payment history": "Your payment has been accepted. Check the payment status in the payment history.", "Set new password": "Set new password", "Change and sign in": "Change and sign in", "Repeat password": "Repeat password", "Password must contain at least 8 characters, include letters, numbers and special characters": "Password must contain at least 8 characters, include letters and numbers", "Passwords do not match": "Passwords do not match", "Incorrect password! Password must contain at least 8 characters, include letters, numbers and special characters": "Incorrect password! Password must contain at least 8 characters, include letters and numbers", "by_link": "link", "Update status": "Update status", "You have payment in progress": "You have payment in progress", "Invalid Amount": "Failed to make payment. Check the correctness of the transferred amount in the request.", "interview_add": {"candidate": "candidate", "candidates": "candidates", "added_to_stage": "was added to"}, "If you want to unblock your account with a deferred payment - contact Support chat": "If you want to unblock your account with a deferred payment - contact Support chat", "Company Registration short": "Company Registration#", "connecting a card for regular payments. Automatic charge will occur at the earliest 3 days before the end of the prepaid period. You can set/change the amount to be charged on the payment page": "You're connecting a card for regular payments. Automatic charge will occur at the earliest 3 days before the end of the prepaid period. You can set/change the amount to be charged on the payment page.", "please note that your bank may charge an additional fee, including for conversion": "Please note that your bank may charge an additional fee, including for conversion.", "for the regular payments, we recommend to add UAH card, instead of foreign currency card, to avoid paying for conversion fee. According to the legislation of Ukraine, the payment system issues an invoice to your bank in UAH, not in USD": "For the regular payments, we recommend to add UAH card, instead of foreign currency card, to avoid paying for conversion fee. According to the legislation of Ukraine, the payment system issues an invoice to your bank in UAH, not in USD.", "updated_candidate_from_email": " updated from email", "To verify the card $1 will be temporarily locked": "To verify the card $1 will be temporarily locked. ", "This promo code is not valid": "This promo code is not valid", "Enter your last name": "Enter your last name", "work": "<PERSON><PERSON>", "Please enter tag name.": "Please enter tag name.", "Enter tag name": "Enter tag name", "public_offer": "Public offer", "Tag already exists": "Tag already exists", "Are you sure you want to delete": "Are you sure you want to delete", "from all candidates ?": "from all candidates?", "Tag added": "Tag added", "Tag edited": "Tag edited", "Tags deleted": "Tags deleted", "For merging_1": "For merging tag", "For merging_2": "For merging tags", "select existing or create new tag": "select main tag or create new tag.", "Integrate your account with the job boards to post vacancies, get applicants and statistics": "Integrate your account with the job boards to post vacancies, get applicants and statistics", "You already have tag": "You already have tag", "Do you want to merge": "Do you want to merge", "Choose tag or create new": "Choose tag or create new", "Tag deleted": "Tag deleted", "Tags merged": "Tags merged", "Tags merged and new tag is created": "Tags merged into new tag", "merged with": "was merged to", "in_candidates": "in candidate", "in_candidates_big": "Candidate", "in_candidates_1": "in candidates", "they merged with": "were merged to", "profile`s": "profile`s", "was added": "was created", "User name has removed tag": "User name has removed tag", "from the system": "from the system", "edited to": "edited to", "from candidates": "from candidates", "from candidate": "from candidate", "gmail-reintegration_step5": "Go to", "gmail-reintegration_step5_1": " and select 'App Passwords'", "gmail-reintegration_step6": "Select “Other (Custom name)” and enter a name, for example, CleverStaff. Then press the “Generate” button.", "gmail-reintegration_step7": "The screen displays the code you need to copy.", "gmail-reintegration_step8": "This code you need to enter on the mail integration page of the password field.", "Read details": "Read more", "Linkedin account is integrated": "Linkedin account is integrated", "In the future, publication will occur automatically": "In the future, publication will occur automatically", "by clicking 'Post to LinkedIn' button on the vacancy page": "by clicking  Linkedin icon on the description vacancy page", "That is an error": "That is an error", "Please try again": "Please try again", "Try again later": "Try again later", "Now our browser extension is available in the Google Web Store!": "Now our browser extension is available in the Google Web Store!", "Wrong Google account. Try integrate calendar again": "Wrong google account. Try integrate calendar again", "Vacancy posted to your Linkedin account": "Vacancy posted to your Linkedin account", "by me": "by me", "Select category": "Select category", "You have no skills. You can add them.": "Skill is not found. You can add it", "You have no skills.": "Skill is not found.", "No matches found. Add skill": "No matches found. You can add skill", "Select or create skill": "Select or create skill", "Select role": "Select role", "Select experience": "Select experience", "Select employment type": "Select employment type", "Select number of positions": "Select number of positions", "defaultValue": "No matter", "choose_category": " Select category", "TeamLead": "Team Lead", "vacancy_creation_title": "Please select industry, customer, and enter a title of the vacancy. After choosing a industry, the remaining fields will become available to you", "from_IT": "IT position", "other_industry": "Other", "Requirements for a candidate": "Requirements for a candidate", "requirements_title": "Please fill the requirements that candidates have to match", "Required experience": "Required experience", "Skills": "Skills", "systemSkillsGroup": "System skills", "customSkillsGroup": "Custom skills", "Skill experience": "Skill experience", "Required": "Required", "level_lang": "Level", "Please enter a description for a future vacancy.": "Here is the attractive description of your vacancy", "Custom fields": "Custom fields", "System fields": "System stages", "custom_fields_title": "Please fill the custom fields", "salary_title": "Please select a type of employment, location, and salary for this vacancy", "Hide salary from candidates": "Hide salary from candidates", "Hide salary from": "Hide salary from", "Show salary from": "Show salary to", "candidates_end": "the candidates", "Enter budget of vacancy": "Enter budget of vacancy", "Vacancy filled incorrectly. Please try again.": "Vacancy filled incorrectly. Please try again", "FullStack": "Full Stack", "location_of_vacancy": "Location", "e01_less_than1year": "Less than 1 year", "e1_1year": "1 year", "e2_2years": "2 years", "e3_3years": "3 years", "e4_4years": "4 years", "e5_5years": "5+ years", "e6_10years": "10+ years", "e00_no_experience": "(no value)", "add_new_lang": "Add language", "add_new_skill": "Add skill", "select industry": "Select industry", "select_experience": "Select required experience", "add_skill": "Add at least one skill", "Comments": "Service information", "comments_title": "You can save service comments here. They are visible for all users, except Hiring Manager", "Will be a plus": "Will be a plus", "selectLevel": "Select level", "Select option": "Select option", "Status of vacancy": "Status of vacancy", "hide-on-public-vacancy-page": "This information will be hidden on the public page of the vacancy.", "hide-on-public-vacancy-page-and-clients": "This information will be hidden on the public page of the job and available to responsible for this vacancy, except for users in the role of Hiring Manager", "add new responsible": "Add new responsible", "recruiter": "Rec<PERSON>er", "interviewer": "Interviewer", "hiringManager": "Hiring Manager", "emptyValue": "(no value)", "Company's public page": "Company's public page", "requiredSkills": "Required skills", "low": "Low", "medium": "Medium", "top": "High", "MediumScore": "Medium", "Priority of vacancy": "Priority of vacancy", "Select priority of vacancy": "Select priority of vacancy", "Priority": "Priority", "you_can_write_comment_why_you_remove_this_vacancy": "You can leave a comment about why you deleted this vacancy", "Priority changed": "Priority changed", "Vacancy dublicated": "Vacancy dublicated", "requirements": "Requirements", "Experience in": "Experience in", "Created_vacancy": "Created", "Location - No matter": "Location - No matter", "You have the same skills. They must be unique.": "You have the same skills. They must be unique.", "Select stage": "Select stage", "Select probationary period": "Select probationary period", "My profile in CleverStaff": "My profile in CleverStaff", "in CleverStaff": "in CleverStaff", "Master": "Master", "Bachelor": "Bachelor", "Associate": "Associate", "HighSchool": "High School", "Student": "Student", "tag_hint_for_second_roles": "To create and edit tags, contact your administrator", "Hide details": "<PERSON>de", "Show more details": "Show more", "Add new contact": "Add contact", "too much characters in phone": "Text must be no more than 20 characters", "too much characters in contact": "Text must be no more than 50 characters", "emptyExperience": "Experience not specified", "edited_big": "Edited", "too much characters in contact_2048": "Text must be no more than 2048 characters", "too much characters in contact_150": "Text must be no more than 150 characters", "too much characters in contact_100": "Text must be no more than 100 characters", "Please improve this vacancy to attract candidates": "Please improve this vacancy to attract candidates", "improveVacancyForItHint": "IT vacancies with tech requirements and attractive description will be proposed to candidates with relevant experience.  <span>💪<span>", "modalWithTextOnFunnelReport": "Conversion shows how effective are your stages in percentage terms. Relative conversion shows the value in comparison with the previous stage. Absolute conversion shows the value of each stage in comparison with the very first stage of the Vacancy.", "mphone": "Phone", "facebook": "Facebook", "telegram": "Telegram", "whatsApp": "WhatsApp", "viber": "Viber", "skype": "Skype", "github": "Git", "behance": "<PERSON><PERSON><PERSON>", "djinni": "<PERSON><PERSON><PERSON>", "homepage": "Home Page", "Candidate testing": "Candidate testing", "List of candidates": "List of candidates", "Vacancy application": "Vacancy request", "Fill out a vacancy application": "Fill out a vacancy request", "Edit the application form": "Edit the request form", "Skills required": "Skills required", "New applications": "New requests", "Refused applications": "Refused requests", "In progress and fulfilled applications": "In progress and fulfilled requests", "Select the standard fields that are used in the form when completed by the customer. Create custom fields as well": "Select the standard fields that are used in the form when completed by the customer. Create custom fields as well.", "Field": "Field", "Required(field)": "Required", "Specifying the scope of the vacancy is mandatory in the application for transformation into a vacancy in the future": "Specifying the scope of the vacancy is mandatory in the request for transformation into a vacancy in the future.", "Refuse_application": "Refuse", "Vacancy application saved": "Vacancy request has been saved", "Vacancy application filled incorrectly": "Vacancy request is filled in incorrectly", "Edit vacancy application": "Edit vacancy request", "Are you sure you want to permanently delete the vacancy application": "Are you sure you want to permanently delete the vacancy request", "Vacancy application deleted": "Vacancy request has been deleted", "You have no applications yet": "You have no requests yet", "Application status: “Refused“": "Request status: “Refused“", "Vacancy application was refused": "Vacancy request was refused", "was created": "was created", "New application was created": "New request was created", "Application form was edited": "Request form was edited", "Application transferred to vacancy": "Request transferred to vacancy", "in_January": "January", "in_February": "February", "in_March": "March", "in_April": "April", "in_May": "May", "in_June": "June", "in_July": "July", "in_August": "August", "in_September": "September", "in_October": "October", "in_November": "November", "in_December": "December", "candidate_1 was added in": "candidates were added in", "_and": "and", "candidate_1 in": "candidates in", "de": "Сlosing date", "antal": "<PERSON><PERSON>", "smartsolution": "Smart Solution", "personhunters": "Person Hunters", "intersog": "Intersog", "rsm": "Rsm", "astarta": "Astarta", "The email template has been changed for the entire system by the user": "The email template has been changed for the entire system by the user", "connect": "Connect", "disconnect": "Disconnect", "Please enter the URL of the job you want to link": "Please enter the URL of the vacancy you want to link", "Are you sure you want to break the integration with the published vacancy?": "Are you sure you want to break the integration with the published vacancy?", "Invalid link": "Invalid link", "You can link a vacancy in CleverStaff and an already published vacancy at work.ua/rabota.ua in order to get responses and statistics from it": "You can link a vacancy in CleverStaff and an already published vacancy at {{value}} in order to get responses and statistics from it", "Use an avatar from social networks": "Use an avatar from social networks", "This file format is not supported, please choose another one with such possible extensions:": "This file format is not supported, please choose another one with such possible extensions:", "Similar vacancies": "Similar vacancies", "Upload a logo": "Upload a logo", "Download cover": "Download cover", "Move cover": "Move cover", "street": "Street", "Choose a background color": "Choose a background color", "There are no vacancies for the query": "There are no vacancies for the query", "Search for in job descriptions": "Search for «{{value}}» in job descriptions", "The description searching did not return any results": "The description searching did not return any results", "Drag_to_reposition": "Drag a cover to reposition", "active_vacancy_0": "active vacancy", "active_vacancy_1": "active vacancies", "active_vacancy_2": "active vacancies", "I want to integrate regular / personal Gmail.": "I want to integrate regular / personal Gmail.", "I want to integrate corporate Gmail.": "I want to integrate corporate Gmail.", "To connect corporate Gmail": "To connect corporate Gmail, you need to enable two-step verification in your mail settings. This is a Google requirement to integrate such mail into other services. Thus, they make such integrations more secure. Two-step verification can be enabled only by your email administrator (the one who created and configured it). The information below should be copied and sent to him.", "Instruction to connect corporate Gmail": "How to enable 2-step verification:\n  1. Go to Google Admin Console <a href=\"https://admin.google.com/u/0/ac/home\" target=\"_blank\">https://admin.google.com/u/0/ac/home</a>\n  2. Click on 'Security'\n  3. Click on '2-Step Verification'\n  4. Enable the 'Allow users to turn on 2-Step Verification' checkbox if it is disabled.\n  5. Scroll down the page and click 'Save'", "Copy for Administrator": "Copy for Administrator", "Go to the Security": "Go to the \"Security\"", "In the \"Signing in to Google\" block, enable \"2-Step Verification\" and follow the instructions": "In the \"Signing in to Google\" block, enable \"2-Step Verification\" and follow the instructions", "Return to the \"Security\" section and in the \"Signing in to Google\" section, click on \"App Passwords\"": "On the \"2-Step Verification\" page, scroll to the bottom of the page and click on \"App passwords\"", "In the dropdown \"Select application\" select \"Mail\", in the dropdown \"Select device\" select \"Other\" and enter the name \"CleverStaff": "In the dropdown \"Select application\" select \"Mail\", in the dropdown \"Select device\" select \"Other\" and enter the name \"CleverStaff\"", "Click \"Generate\", copy the password and click \"Finish\"": "Click \"Generate\", copy the password and click \"Finish\"", "When integrating mail in a mailbox in CleverStaff, specify the application password generated above in the password field": "When integrating mail in a mailbox in CleverStaff, specify the application password generated above in the password field.", "Please enter this code into the Google Authenticator or FreeOTP Authenticator app. You can also scan the QR code using one of the specified applications.": "Please enter this code into the Google Authenticator or FreeOTP Authenticator app. You can also scan the QR code using one of the specified applications.", "authorization_code": "Authorization code", "input_authorization_code": "Input authorization code", "Invalid authorization code": "Invalid authorization code", "The code has been copied": "The code has been copied to clipboard", "Be sure to keep this code in a safe place and do not share it with anyone. You will need it if you change the device where Google Authenticator is installed": "Be sure to keep this code in a safe place and do not share it with anyone. You will need it if you change the device where Google Authenticator or FreeOTP Authenticator is installed.", "Are you sure you want to disable two factor authorization?": "Are you sure you want to disable Two-Step Verification?", "Are you sure you want to disable two factor authorization for this user?": "Are you sure you want to disable Two-Step Verification for this user?", "Two-Step Verification": "Two-Step Verification", "Enter confirmation code": "Enter confirmation code", "Enter the verification code from the authentication app": "Enter the verification code from the authentication app", "Two-Step Verification is enabled": "Two-Step Verification is enabled", "Now, every time you log in to CleverStaff, we will ask you for an additional code, which you can get in the Google Authenticator / FreeOTP Authenticator app": "Now, every time you log in to CleverStaff, we will ask you for an additional code, which you can get in the Google Authenticator or FreeOTP Authenticator app", "This user has enabled Two-Step Verification for himself": "This user has enabled Two-Step Verification for himself", "Incorrect confirm key": "Incorrect confirm key", "You are asked to ask for a 6-digit login code every time you login to CleverStaff. This is an additional measure to protect your account. Enter the 6-digit code from the app you chose when activating 2-Step Verification (Google Authenticator/FreeOTP Authenticator)": "You are asked to ask for a 6-digit login code every time you login to CleverStaff. This is an additional measure to protect your account. Enter the 6-digit code from the app you chose when activating 2-Step Verification (Google Authenticator/FreeOTP Authenticator)", "Can't get your code?": "Can't get your code?", "If you cannot get a login code, then contact your Account Administrator, he may turn off two-step authentication for you. Or contact CleverStaff support:": "If you cannot get the login code, please: <ul><li>contact the Administrator of your account to disable two-step authentication;</li><li>contact CleverStaff support: <a href='mailto:<EMAIL>'><EMAIL></a></li></ul>", "What is Two-Step Verification and How to Use it": "What is Two-Step Verification and How to Use it", "cardPaymentTooltip": "Please note that the system will automatically charge the next payment two days before the end of the prepaid amount. <br /><br />If you've connected several cards, you can set one of them as default. If insufficient funds on it or it's not valid, then software will use next your card for payment.<br /><br />If you want to cancel recurring payments, you need to remove the card", "Who should be put in charge of its candidates, vacancies and customers?": "Who should be put in charge of its candidates, vacancies, customers and tasks?", "application_1": "request", "application_2": "requests", "application_3": "requests", "skills": "Skills", "regionId": "Region", "responsibilities": "Job responsibilities", "offer": "Description of the requirements for the candidate", "companyDescription": "Description of proposals for the candidate", "Are you sure you want to return to active the vacancy application": "Are you sure you want to return to active the vacancy request", "Vacancy application activated": "Vacancy request activated", "priority": "Priority", "Date of transfer to the stage": "Date of transfer to the stage:", "Application form_vacancy": "Request form", "Vacancy_application": "Request", "You can only link to the published one (currently active) a vacancy with rabota.ua": "You can only link to the published one (currently active) a vacancy with robota.ua", "You can only link to the published one (currently active) a vacancy with djinni": "You can only link to the published one (currently active) a vacancy with djinni.co", "Link vacancy": "<PERSON> the vacancy", "You can break the integration with the published vacancy": "You can break the integration with the published vacancy", "A published vacancy only be linked to one vacancy in CleverStaff. With the vacancy you specified, there is already an integration with": "<span>A published vacancy only be linked to one vacancy in CleverStaff. With the vacancy you specified, there is already an integration with <a href=\"!#/vacancies/{{id}}\">{{position}}</a>. You can disable integration in the Vacancy Description.</span>", "You can disable integration in the Vacancy Description.": "You can disable integration in the Vacancy Description.", "sendNewVacancyApplication": "Notification about vacancy request creation", "sendVacancyApplicationChange": "Notification about the status change of the request for a vacancy", "Delete cover": "Delete cover", "Are you sure you want to remove the cover?": "Are you sure you want to remove the cover?", "By_currentWorkingPlace": "Current working place", "Workplace": "Workplace", "currentWorkingPlace": "Current working place", "Last activity by candidate": "Last activity about the candidate", "refusals": "refusals", "Vacancies report": "Vacancies report", "Stages histogram": "Stages histogram", "Try again": "Try again", "Build a shared funnel": "Build a shared funnel", "Build a general stages chart": "Build a general stages chart", "Show responsible for the selected vacancies": "Show responsible for the selected vacancies", "Show selected vacancies in report": "Show selected vacancies in report", "List up to 10 candidates at each stage": "List up to 10 candidates at each stage", "List of all candidates in stages": "List of all candidates in stages", "Opening date": "Opening date", "deleteLimit": {"user": "User", "removed": "removed", "candidates_within_one_day_and_reached_the_daily_limit_for_removing_candidates": "candidates within one day and reached the daily limit for removing candidates", "users_within_one_day_and_reached_the_daily_limit_for_removing_users": "users within one day and reached the daily limit for removing users", "You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow": "You have reached the daily limit for removing candidates. You can continue tomorrow", "You_have_reached_the_daily_limit_for_removing_users_You_can_continue_tomorrow": "You have reached the daily limit for removing users. You can continue tomorrow", "You have reached the daily limit for deleting candidates": "You have reached the daily limit for deleting candidates. Today you can delete more", "candidates": "candidates."}, "CleverStaff - it is a pure 100% Ukrainian product and we've": "CleverStaff - it is a pure 100% Ukrainian product and we've", "refused": "refused", "link to news": "https://cleverstaff.net/blog/en/farewell-to-russian-clients/", "to cooperate with the clients from Russian Federation. Glory to Ukraine!": "to cooperate with the clients from Russian Federation. Glory to Ukraine!", "The amount of time in the stage": "The amount of time in the stage", "Allow HM access to attachments on the candidates profile": "Allow HM access to attachments on the candidates profile", "Access to candidate salary and salary in jobs": "Allow HM access to salaries of candidates and vacancies", "Show attachments": "Show attachments", "Show salary": "Show salary", "opened access to the candidates attachments for": "opened access to the candidates attachments for", "closed access to the candidates attachments for": "closed access to the candidates attachments for", "opened access to the salary of the candidate for": "opened access to salaries of candidates and vacancies for", "closed access to the salary of the candidate for": "closed access to salaries of candidates and vacancies for", "and send a screenshot": "and send a screenshot of the transaction to ", "Send the money to Paypal user": "Send the money to PayPal user", "in paypal": "", "Completeness": "Completeness", "on vacancy": "on vacancy", "Level_gamification": "Level {{count}}", "Next_2": "Next", "Done_1": "Done!", "Result_tab": "Results", "Collection": "My collection", "Reward_experience": "Reward - {{count}} of experience", "As of": "As of", "Count of achievements as for now": "As of now, for", "achievement-1": "achievement", "achievement-2": "achievements", "achievement-3": "achievements", "It`s time to find the right Candidate for her": "It's time to find the right <b>Candidate</b> for it.", "It's time to close the vacancy": "It's time to fill the vacancy.", "add_candidate_2": "Add candidate", "It remains to add Sandra to our vacancy and we will be ready for the CleverStaff magic.": "It remains to add <b><PERSON></b> to our vacancy and we will be ready for the <b>CleverStaff</b> magic.", "Thanks for the invitation! See you.": "Thanks for the invitation! </br>See you.", "Lets see how the stages of selection of candidates go on the example of Sandra.": "Let's see how the stages of selection of candidates go on the example of <b><PERSON></b>.", "We still have one candidate - Sandra. And shes now at the Longlist stage. Lets set up an interview for Sandra.": "We still have one candidate - <b><PERSON></b>. And she's now at the Longlist stage. Let's set up an interview for <b><PERSON></b>.", "Close_vacancy": "Fill a vacancy", "Achievement Notifications": "Achievement notifications", "user_progress_race_month": "month", "user_progress_race_quarter": "quarter", "user_progress_race_year": "year", "place in the race of the": "place in the race of the", "onempty": "", "и по": "and", "received_the_award": "Received the award", "received_the_award_his": "Received the award", "received_the_award_she": "Received the award", "очко": "points", "очка": "points", "очков": "points", "кандидатов": "candidates", "кандидата": "candidate", "которого": "you previously", "которых": "you previously", "вакансию": "vacancy", "вакансии": "vacancies", "добавили на": "added to the", ",": "", "secret_achieve_title": {"likeCleverStaffFacebookPage": "And we like you :)", "vacancyClosedLastDay": "It was close", "vacancyClosedEightAndMoreStages": "Half marathon", "vacancyClosedTwelveAndMoreStages": "It's a complete marathon", "mailTemplateCreated": "Just do it", "uploadCvArchive": "My treasure!", "connectWithSupport": "Glad to meet you :)", "createJobsTab": "Don't pass by", "createCustomField": "My way or the highway", "searchInFAQ": "Knowledge is a power!", "addAllInformationToProfile": "Get to know yourself", "useReferralProgram": "Your friend is our friend :)", "moreThan20TasksInOrganizer": "EVERYTHING UNDER CONTROL !!!", "moreThan100CandidatesOnVacancy": "Is that you, <PERSON>?"}, "achieve_title": {"newTaskInOrganizer": "Sisyphus and guys", "newCandidatesSearch": "Where are you?", "newCandidateExtendedSearch": "Where are you that one?", "addCandidateToOneVacancy": "Hallelujah, it's raining candidates!", "addCandidateToDatabase": "Many a little make a mickle", "candidateOnApprovedState": "Just call me the Oracle", "addCandidateToAnyInterviewStage": "Interview master", "addCandidateToSentOffer": "Offer master", "candidateAddFromAdvice": "Man's best friend", "newMeetingTask": "Experienced negotiator", "newCallTask": "Experienced listener", "newAnyTask": "Experienced glider", "addCandidateScore": "Nothing personal, but ...", "addCandidateComment": "Experienced commentator", "closeVacancyInThreeDaysBeforeDeadline": "One step ahead", "addNewVacancyAnyLevel": "An employee is required for work", "addNewVacancyFirstLevel": "Embarrassment of riches", "addNewVacancySecondLevel": "He who seeks will find", "addNewVacancyThirdLevel": "Hero work", "sendMailsViaPersonalMailing": "Mailing master", "addCandidateManually": "Working like a beaver", "addCandidateManually_1": "Working like a beaver", "addCandidateManually_2": "Working like a beaver", "addCandidateFromLinkedIn": "LinkedOut!", "sendTestToCandidate": "Have a test", "addClient": "You have been recommended", "addSource": "Broad outlook", "addTag": "Millennial", "generateReport": "Retrospective", "useCleverStaff": "Thank you for choosing <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newCrystalByLevel": "Collector", "likeCleverStaffFacebookPage": "And we like you :)", "vacancyClosedLastDay": "It was close", "vacancyClosedEightAndMoreStages": "Half marathon", "vacancyClosedTwelveAndMoreStages": "It's a complete marathon", "mailTemplateCreated": "Just do it", "uploadCvArchive": "My treasure!", "connectWithSupport": "Glad to meet you :)", "createJobsTab": "Don't pass by", "createCustomField": "My way or the highway", "searchInFAQ": "Knowledge is a power!", "addAllInformationToProfile": "Get to know yourself", "useReferralProgram": "Your friend is our friend :)", "moreThan20TasksInOrganizer": "EVERYTHING UNDER CONTROL !!!", "moreThan100CandidatesOnVacancy": "Is that you, <PERSON>?"}, "achieve_description": {"newTaskInOrganizer": "Complete {{count}} tasks from the very beginning", "newCandidatesSearch": "Perform {{count}} Candidate searches", "newCandidateExtendedSearch": "Perform {{count}} advanced candidate searches", "addCandidateToOneVacancy": "Add more than {{count}} candidates for one vacancy", "addCandidateToDatabase": "Add {{count}} candidates to the system from the very beginning", "candidateOnApprovedState": "{{count}} of your candidates went to work", "addCandidateToAnyInterviewStage": "Transfer {{count}} candidates to the interview stage", "addCandidateToSentOffer": "Send the offer {{count}} to the candidates", "candidateAddFromAdvice": "Add {{count}} candidates from autoselection", "newMeetingTask": "Schedule {{count}} meetings", "newCallTask": "Schedule {{count}} calls", "newAnyTask": "Create {{count}} tasks", "addCandidateScore": "Give {{count}} marks to candidates", "addCandidateComment": "Add {{count}} comments to candidates", "closeVacancyInThreeDaysBeforeDeadline": "Fill {{count}} vacancies three days before the deadline", "closeVacancyInThreeDaysBeforeDeadline_1": "Fill {{count}} vacancy three days before the deadline", "addNewVacancyAnyLevel": "Create {{count}} vacancies of all time", "addNewVacancyFirstLevel": "Create {{count}} vacancies with simple difficulty (*)", "addNewVacancySecondLevel": "Create {{count}} vacancies with medium difficulty (**)", "addNewVacancyThirdLevel": "Create {{count}} vacancies with heavy difficulty (***)", "sendMailsViaPersonalMailing": "Send {{count}} emails using individual mailing", "addCandidateManually_1": "Fill in {{count}} resume manually", "addCandidateManually": "Fill in {{count}} resumes manually", "addCandidateFromLinkedIn": "Download {{count}} candidates from LinkedIn", "sendTestToCandidate": "Submit {{count}} tests to candidates", "addClient": "Add {{count}} clients", "addSource": "Add {{count}} sources", "addTag": "Add {{count}} tags", "generateReport": "Generate {{count}} reports", "useCleverStaff": "Be a CleverStaff member for {{count}} days", "newCrystalByLevel": "Earn any {{count}} level stone", "likeCleverStaffFacebookPage": "Like CleverStaff Facebook Pages", "vacancyClosedLastDay": "Vacancy filled on the last day of the deadline", "vacancyClosedEightAndMoreStages": "A vacancy with 8 or more selection stages is filled", "vacancyClosedTwelveAndMoreStages": "A vacancy with 12 or more selection stages is filled", "mailTemplateCreated": "Email template created", "uploadCvArchive": "Download the resume archive", "connectWithSupport": "Write to support", "createJobsTab": "Create a Jobs tab", "createCustomField": "Use the custom field feature", "searchInFAQ": "Use FAQ search", "addAllInformationToProfile": "Complete your profile", "useReferralProgram": "Use the referral program", "moreThan20TasksInOrganizer": "Have more than 20 active tasks in the organizer", "moreThan100CandidatesOnVacancy": "Add more than 100 candidates for 1 vacancy", "addNewVacancyFirstLevel_1": "Create {{count}} vacancy with simple difficulty (*)", "addNewVacancySecondLevel_1": "Create {{count}} vacancy with medium difficulty (**)", "addNewVacancyThirdLevel_1": "Create {{count}} vacancy with heavy difficulty (***)", "addCandidateFromLinkedIn_1": "Download {{count}} candidate from LinkedIn", "addClient_1": "Add {{count}} client", "addSource_1": "Add {{count}} source", "generateReport_1": "Generate {{count}} report", "candidateOnApprovedState_1": "{{count}} of your candidates went to work", "addCandidateScore_1": "Give {{count}} marks to candidates", "addCandidateComment_1": "Add {{count}} comments to candidates", "addNewVacancyAnyLevel_1": "Create {{count}} vacancies of all time", "sendTestToCandidate_1": "Submit {{count}} tests to candidates", "addTag_1": "Add {{count}} tags", "addSource_3": "Add {{count}} sources"}, "hot_tasks": {"newTaskInOrganizer": "You just need to complete {{count}} to get the \"Sisyphus and guys\" award", "newCandidatesSearch": "Perform Candidate Search {{count}} and get the \"Where are you?\" award", "newCandidateExtendedSearch": "To get the \"Where are you that one?\" award, perform advanced candidate search {{count}}", "addCandidateToOneVacancy": "To get the \"Hallelujah, it's raining candidates!\" award, add {{count}} for one vacancy", "addCandidateToDatabase": "To get the \"Many a little make a mickle\" award, add {{count}} to the system", "candidateOnApprovedState": "To get the \"Just call me the Oracle\" award, transfer {{count}} to the Hired stage", "addCandidateToAnyInterviewStage": "To get the \"Interview master\" award, you just need to transfer {{count}} to interview stage", "addCandidateToSentOffer": "Send {{count}} and you will win the \"Offer Master\" award and stones!", "candidateAddFromAdvice": "To get the \"Man's best friend\" award, add {{count}} from autoselection", "newMeetingTask": "Add {{count}} to your Organizer! And get the \"Experienced negotiator\" award!", "newCallTask": "Just {{count}} into the Organizer and the \"Experienced listener\" award is yours!", "newAnyTask": "Create {{count}} and the \"Experienced glider\" award is in your pocket!", "addCandidateScore": "To get the \"Nothing personal, but ...\" award, evaluate {{count}}", "addCandidateComment": "{{count}} in the candidate's profile and get the \"Experienced commentator\"  award", "closeVacancyInThreeDaysBeforeDeadline": "Fill {{count}} three days before the deadline and get \"One step ahead\" award", "addNewVacancyAnyLevel": "To get the \"An employee is required for work\" award, create {{count}}!", "addNewVacancyFirstLevel": "Create {{count}} with simple difficulty (*) and get the \"Embarrassment of riches\" award", "addNewVacancySecondLevel": "Create {{count}} with medium difficulty (**) and get the \"He who seeks will find\" award", "addNewVacancyThirdLevel": "Create {{count}} with heavy difficulty (***) and get the \"Hero work\" award", "sendMailsViaPersonalMailing": "Just {{count}} and the \"Mailing master\" award is yours!", "addCandidateManually": "To get {{stones}} for the \"Working like a beaver\" award, fill in manually {{count}} more candidates", "addCandidateFromLinkedIn": "To get the \"LinkedOut!\" award, save {{count}} from Linkedin", "sendTestToCandidate": "Send {{count}} and the \"Have a test\" award is yours!", "addClient": "Want to win the \"You have been recommended\" award? Add {{count}}!", "addSource": "To get the \"Broad outlook\" award, add {{count}} in the account settings", "addTag": "Want to win the \"Millennial\" awards? Add {{count}} and you will get it!", "generateReport": "Generate {{count}} and get \"Retrospective\" award", "newCrystalByLevel": "Earn any {{count}} level stone"}, "news_feed_notifications_solo": {"user_old_registration_success": "Hooray, Congratulations on the start of an exciting journey! This is the news feed where you will receive notifications related to our <a class='closeStones' href='#/achievements/results'>gamification</a>.", "new_achievement": "You have earned the achievement \"<a class='closeAchivment' href='#/achievements/awards?name={{originalAchivment}}'>{{descr}}</a>\" (Level is {{count}}) and <a class='closeStones' href='#/achievements/results'>{{crystalsForAchievement}} stones</a>.", "your_candidate_move": "Yesterday you earned {{earnedExpPoints}} experience for {{descr}} {{vacanciesList}}", "your_candidate_move_0": "Earned {{earnedExpPoints}} points experience for candidates you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "your_candidate_move_1": "Earned {{earnedExpPoints}} points experience for candidates you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "your_candidate_move_2": "Earned {{earnedExpPoints}} points experience for candidates you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "your_candidate_move_0_one": "Earned {{earnedExpPoints}} points experience for candidate you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "your_candidate_move_1_one": "Earned {{earnedExpPoints}} points experience for candidate you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "your_candidate_move_2_one": "Earned {{earnedExpPoints}} points experience for candidate you previously added to the vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a>.", "vacancy_opened": "You have opened a new vacancy <a href='#/vacancies/{{descr}}'>{{vacancyName}}</a> (difficulty {{count}}).", "vacancy_closed": "{{vacClosed}}", "user_change_role_hm": "Congratulations! You have changed the role from Hiring Manager to {{roleAfterHr}}! A new unit is now available for you \"<a href='#/achievements/results'>Achievements</a>\". You can always go again <a href='#/onboarding'>onboarding</a> or continue its execution if you have not completed it.", "level_up": "Congratulations! Your level has been increased to {{count}}!", "level_up_1_m": "Congratulations! Your level has been increased to {{count}} - \"Pathfinder\"!", "level_up_2_m": "Congratulations! Your level has been increased to {{count}} - \"Experienced Pathfinder\"!", "level_up_3_m": "Congratulations! Your level has been increased to {{count}} - \"Superior Pathfinder\"!", "level_up_4_m": "Congratulations! Your level has been increased to {{count}} - \"Beater\"!", "level_up_5_m": "Congratulations! Your level has been increased to {{count}} - \"Experienced Beater\"!", "level_up_6_m": "Congratulations! Your level has been increased to {{count}} - \"Superior Beater\"!", "level_up_7_m": "Congratulations! Your level has been increased to {{count}} - \"Hunter\"!", "level_up_8_m": "Congratulations! Your level has been increased to {{count}} - \"Experienced Hunter\"!", "level_up_9_m": "Congratulations! Your level has been increased to {{count}} - \"Born Hunter\"!", "level_up_10_m": "Congratulations! Your level has been increased to {{count}} - \"Hunt Master\"!", "level_up_11_m": "Congratulations! Your level has been increased to {{count}} - \"Hunt Hero\"!", "level_up_12_m": "Congratulations! Your level has been increased to {{count}} - \"Hunt Champion\"!", "level_up_13_m": "Congratulations! Your level has been increased to {{count}} - \"Hunt Icon\"!", "level_up_14_m": "Congratulations! Your level has been increased to {{count}} - \"Star Hunter\"!", "level_up_15_m": "Congratulations! Your level has been increased to {{count}} - \"Epic Hunter\"!", "level_up_16_m": "Congratulations! Your level has been increased to {{count}} - \"Legendary Hunter\"!", "level_up_17_m": "Congratulations! Your level has been increased to {{count}} - \"Master of the Woods\"!", "level_up_18_m": "Congratulations! Your level has been increased to {{count}} - \"King of the Hill\"!", "level_up_19_m": "Congratulations! Your level has been increased to {{count}} - \"The Chosen One\"!", "level_up_20_m": "Congratulations! Your level has been increased to {{count}} - \"God of the Hunt\"!", "level_up_1_f": "Congratulations! Your level has been increased to {{count}} - \"Pathfinder\"!", "level_up_2_f": "Congratulations! Your level has been increased to {{count}} - \"Experienced Pathfinder\"!", "level_up_3_f": "Congratulations! Your level has been increased to {{count}} - \"Superior Pathfinder\"!", "level_up_4_f": "Congratulations! Your level has been increased to {{count}} - \"Beater\"!", "level_up_5_f": "Congratulations! Your level has been increased to {{count}} - \"Experienced Beater\"!", "level_up_6_f": "Congratulations! Your level has been increased to {{count}} - \"Superior Beater\"!", "level_up_7_f": "Congratulations! Your level has been increased to {{count}} - \"Huntress\"!", "level_up_8_f": "Congratulations! Your level has been increased to {{count}} - \"Experienced Huntress\"!", "level_up_9_f": "Congratulations! Your level has been increased to {{count}} - \"Born Huntress\"!", "level_up_10_f": "Congratulations! Your level has been increased to {{count}} - \"Hunt Master\"!", "level_up_11_f": "Congratulations! Your level has been increased to {{count}} - \"Hunt Heroine\"!", "level_up_12_f": "Congratulations! Your level has been increased to {{count}} - \"Hunt Champion\"!", "level_up_13_f": "Congratulations! Your level has been increased to {{count}} - \"Hunt Icon\"!", "level_up_14_f": "Congratulations! Your level has been increased to {{count}} - \"Star Huntress\"!", "level_up_15_f": "Congratulations! Your level has been increased to {{count}} - \"Epic Huntress\"!", "level_up_16_f": "Congratulations! Your level has been increased to {{count}} - \"Legendary Huntress\"!", "level_up_17_f": "Congratulations! Your level has been increased to {{count}} - \"Mistress of the Woods\"!", "level_up_18_f": "Congratulations! Your level has been increased to {{count}} - \"Queen of the Mountains\"!", "level_up_19_f": "Congratulations! Your level has been increased to {{count}} - \"The Chosen One\"!", "level_up_20_f": "Congratulations! Your level has been increased to {{count}} - \"Goddess of the Hunt\"!", "user_finished_onboarding": "Congratulations! You have successfully completed your training!", "user_registration_success": "Hooray, Congratulations on your successful registration!", "candidate_move_0": "You transferred the candidate to the \"{{state}}\" stage and received +{{earnedExpPoints}} points experience.", "candidate_move_1": "You have transferred \"{{count}}\" candidates to the {{state}} stage and received +{{earnedExpPoints}} points experience.", "candidate_move_2": "You have transferred \"{{count}}\" candidates to the {{state}} stage and received +{{earnedExpPoints}} points experience.", "new_crystals_0_0": "Congratulations! You got <a class='closeStones' href='#/achievements/results'>{{count}} stone</a>!", "new_crystals_0_1": "Congratulations! You got <a class='closeStones' href='#/achievements/results'>{{count}} stones</a>!", "new_crystals_0_2": "Congratulations! You got <a class='closeStones' href='#/achievements/results'>{{count}} stones</a>!", "crystals_record_month": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_quarter": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_year": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_month_0": "Congratulations! You have set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a>", "crystals_record_month_1": "Congratulations! You have set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_month_2": "Congratulations! You have set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_quarter_0": "Congratulations! You have set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a>", "crystals_record_quarter_1": "Congratulations! You have set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_quarter_2": "Congratulations! You have set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_year_0": "Congratulations! You have set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a>", "crystals_record_year_1": "Congratulations! You have set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_year_2": "Congratulations! Y ou have set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "new_achievement_group": "The users: <span>{{users}}</span> got the achievement \"<a class='closeAchivment' href='#/achievements/awards?name={{originalAchivment}}'>{{descr}}</a>\" level {{groupedInformation[0].count}} and as a reward <a class='closeStones' href='#/achievements/results'>{{groupedInformation[0].crystalsForAchievement}} stones</a>.", "new_secret_achievement_group": "The users: <span>{{users}}</span> got the secret achievement.", "new_secret_achievement_solo": "The user <span>{{users}}</span> got the secret achievement.", "new_secret_achievement_solo_f": "The user <span>{{users}}</span> got the secret achievement.", "new_secret_achievement": "You have discovered a new secret achievement \"<a class='closeAchivment' href='#/achievements/awards?name={{originalAchivment}}'>{{descr}}</a>\" and received <a class='closeStones' href='#/achievements/results'>{{crystalsForAchievement}} stones!</a>", "user_progress_race": "{{users}} took the {{showAllRace}}!", "user_progress_race_month": "Current race results of this {{descr}}", "user_progress_race_quarter": "Current race results of this {{descr}}", "user_progress_race_year": "Current race results of this {{descr}}", "race_month_result": "Results of the race of the month:{{hintIcon}} <br> 1. {{users[0]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[0]}} {{crystalLang[0]}}</a>.<br>2. {{users[1]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[1]}} {{crystalLang[1]}}</a>.<br>3. {{users[2]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[2]}} {{crystalLang[2]}}</a>.", "race_quarter_result": "Results of the race of the quarter:{{hintIcon}} <br> 1. {{users[0]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[0]}} {{crystalLang[0]}}</a>.<br>2. {{users[1]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[1]}} {{crystalLang[1]}}</a>.<br>3. {{users[2]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[2]}} {{crystalLang[2]}}</a>.", "race_year_result": "Results of the race of the year:{{hintIcon}} <br>1. {{users[0]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[0]}} {{crystalLang[0]}}</a>.<br>2. {{users[1]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[1]}} {{crystalLang[1]}}</a>.<br>3. {{users[2]}} - <a class='closeStones' href='#/achievements/results'>{{crystals[2]}} {{crystalLang[2]}}</a>."}, "news_feed_notifications_team": {"new_achievement": "Got achievement \"<a class='closeAchivment' href='#/achievements/awards?name={{originalAchivment}}'>{{descr}}</a>\" (Level is {{count}}) and <a class='closeStones' href='#/achievements/results'>{{crystalsForAchievement}} stones</a>.", "vacancy_opened": "Has opened a new vacancy {{vacancyLink}} (difficulty {{count}}).", "vacancy_closed": "{{vacClosed}}", "level_up": "Has reached level {{count}}.", "new_secret_achievement": "Opened a new secret achievement.", "your_candidate_move": "{{creator}} moved your candidate <a href='#/candidates/{{descr}}'>candidate</a> to the {{state}} stage.", "user_registration_success": "Hooray, Congratulations on your successful registration!", "user_progress_race": "Took the {{showAllRace}}!", "user_progress_race_month": "Current race results of this {{descr}}", "user_progress_race_quarter": "Current race results of this {{descr}}", "user_progress_race_year": "Current race results of this {{descr}}", "new_crystals_0_0": "Has received <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a> Congratulations!", "new_crystals_0_1": "Has received <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a> Congratulations!", "new_crystals_0_2": "Has received <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a> Congratulations!", "crystals_record_month": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_quarter": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_year": "Set a new personal record of the {{descr}} {{users}}", "crystals_record_month_0": "Has set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a>", "crystals_record_month_1": "Has set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_month_2": "Has set a new personal record of the month - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_quarter_0": "Has set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stone!</a>", "crystals_record_quarter_1": "Has set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_quarter_2": "Has set a new personal record for the quarter - <a class='closeStones' href='#/achievements/results'>{{count}} stones!</a>", "crystals_record_year_0": "Has set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stone</a>.", "crystals_record_year_1": "Has set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stones</a>.", "crystals_record_year_2": "Has set a new personal record of the year - <a class='closeStones' href='#/achievements/results'>{{count}} stones</a>.", "level_up_1_m": "Reached level {{count}} and got a rank \"Pathfinder\"!", "level_up_2_m": "Reached level {{count}} and got a rank \"Experienced Pathfinder\"!", "level_up_3_m": "Reached level {{count}} and got a rank \"Superior Pathfinder\"!", "level_up_4_m": "Reached level {{count}} and got a rank \"Beater\"!", "level_up_5_m": "Reached level {{count}} and got a rank \"Experienced Beater\"!", "level_up_6_m": "Reached level {{count}} and got a rank \"Superior Beater\"!", "level_up_7_m": "Reached level {{count}} and got a rank \"Hunter\"!", "level_up_8_m": "Reached level {{count}} and got a rank \"Experienced Hunter\"!", "level_up_9_m": "Reached level {{count}} and got a rank \"Born Hunter\"!", "level_up_10_m": "Reached level {{count}} and got a rank \"Hunt Master\"!", "level_up_11_m": "Reached level {{count}} and got a rank \"Hunt Hero\"!", "level_up_12_m": "Reached level {{count}} and got a rank \"Hunt Champion\"!", "level_up_13_m": "Reached level {{count}} and got a rank \"Hunt Icon\"!", "level_up_14_m": "Reached level {{count}} and got a rank \"Star Hunter\"!", "level_up_15_m": "Reached level {{count}} and got a rank \"Epic Hunter\"!", "level_up_16_m": "Reached level {{count}} and got a rank \"Legendary Hunter\"!", "level_up_17_m": "Reached level {{count}} and got a rank \"Master of the Woods\"!", "level_up_18_m": "Reached level {{count}} and got a rank \"King of the Hill\"!", "level_up_19_m": "Reached level {{count}} and got a rank \"The Chosen One\"!", "level_up_20_m": "Reached level {{count}} and got a rank \"God of the Hunt\"!", "level_up_1_f": "Reached level {{count}} and got a rank \"Pathfinder\"!", "level_up_2_f": "Reached level {{count}} and got a rank \"Experienced Pathfinder\"!", "level_up_3_f": "Reached level {{count}} and got a rank \"Superior Pathfinder\"!", "level_up_4_f": "Reached level {{count}} and got a rank \"Beater\"!", "level_up_5_f": "Reached level {{count}} and got a rank \"Experienced Beater\"!", "level_up_6_f": "Reached level {{count}} and got a rank \"Superior Beater\"!", "level_up_7_f": "Reached level {{count}} and got a rank \"Huntress\"!", "level_up_8_f": "Reached level {{count}} and got a rank \"Experienced Huntress\"!", "level_up_9_f": "Reached level {{count}} and got a rank \"Born Huntress\"!", "level_up_10_f": "Reached level {{count}} and got a rank \"Hunt Master\"!", "level_up_11_f": "Reached level {{count}} and got a rank \"Hunt Heroine\"!", "level_up_12_f": "Reached level {{count}} and got a rank \"Hunt Champion\"!", "level_up_13_f": "Reached level {{count}} and got a rank \"Hunt Icon\"!", "level_up_14_f": "Reached level {{count}} and got a rank \"Star Huntress\"!", "level_up_15_f": "Reached level {{count}} and got a rank \"Epic Huntress\"!", "level_up_16_f": "Reached level {{count}} and got a rank \"Legendary Huntress\"!", "level_up_17_f": "Reached level {{count}} and got a rank \"Mistress of the Woods\"!", "level_up_18_f": "Reached level {{count}} and got a rank \"Queen of the Mountains\"!", "level_up_19_f": "Reached level {{count}} and got a rank \"The Chosen One\"!", "level_up_20_f": "Reached level {{count}} and got a rank \"Goddess of the Hunt\"!", "race_month_result_0": "", "race_month_result_1": "", "race_month_result_2": ""}, "Latest": "Latest", "quarter": "Quarter", "quarter_small": "quarter", "There are no results for your search": "There are no results for your search", "The interview went well and Sandra agrees to all the conditions. Its time to close the vacancy. Transfer Sandra to the Hired.": "The interview went well and <b><PERSON></b> agrees to all the conditions. It's time to fill the vacancy. Transfer <b><PERSON></b> to the <b>Hired</b>.", "Do not forget to complete quests further, so you can significantly increase the efficiency of your work and truly appreciate all the benefits of CleverStaff.": "Do not forget to complete quests further, so you can significantly increase the efficiency of your work and truly appreciate all the benefits of <b>CleverStaff</b>.", "happy_hunts": "\"Happy hunting, my recruiters!", "happy_hunting_my_recruits": "And let the Stones of Prosperity go to the worthy!\"", "artemis": "Artemis", "CleverStaff speeds up the recruiting of candidates and makes the whole process easier": "CleverStaff speeds up the recruiting and makes the whole process easier", "You can return to the tutorial in the system chat": "You can return to this tutorial in the notifications window", "greetings-i-am-artemis": "Greetings! I am Artemis,<br>the goddess of hunting, animal<br>and vegetable fertility.", "I want recruiting to": "I want recruiting to celebrate small and outstanding successes, congratulations on professional growth. I want our very important work to be exciting 😊", "i-will-turn-all-your": "Your experience will be digitized and marked with a level. There are 20 levels total. The higher your level, the closer you are to the status of the", "i-will-turn-all-your-sex": "of the hunt, that is, to me! 😉", "See you soon! I will support you.": "See you soon! I will support you.", "Me and my faithful falcon": "Here I and my faithful falcon will celebrate your successes and the growth of your experience.", "The CleverStaff team implemented it": "The CleverStaff team, gifted with my inspiration, made it happen.", "I used to be the best hunter in the world,": "I used to be the best hunter in the world,", "but now hunting is not as relevant to": "but now hunting is not as relevant to", "people as it used to be. In modern": "people as it used to be. In modern", "conditions, I embody the art of": "conditions, I embody the art of", "headhunting for talents. Now Im the": "headhunting for talents. Now I'm the", "For your successes you will receive": "For your successes you will receive", "Prosperity Stones. Each stone adds": "Prosperity Stones. Each stone adds", "+1 to one of the five main": "+1 to one of the five main", "characteristics of a successful": "characteristics of a successful", "recruiter: Knowledge, Experience,": "recruiter: Knowledge, Experience,", "Empathy, Passion and Charisma!": "Empathy, Passion and Charisma!", "Kateryna Krasnopolska": "<PERSON>", "goddess": "goddess", "god": "god", "Received_job_solo": "You received job award", "Pathfinder": "Pathfinder", "Experienced Pathfinder": "Experienced Pathfinder", "Superior Pathfinder": "Superior Pathfinder", "Beater": "<PERSON>er", "Beater_f": "<PERSON>er", "Experienced Beater": "Experienced Beater", "Experienced Beater_f": "Experienced Beater", "Superior Beater": "Superior Beater", "Superior Beater_f": "Superior Beater", "Huntress": "<PERSON>ress", "Hunter": "<PERSON>", "Experienced Huntress": "Experienced Huntress", "Experienced Hunter": "Experienced Hunter", "Born Huntress": "<PERSON>", "Born Hunter": "<PERSON>", "Hunt Master": "Hunt Master", "Hunt Heroine": "<PERSON>", "Hunt Hero": "<PERSON> Hero", "Hunt Champion": "Hunt Champion", "Hunt Champion_f": "Hunt Champion", "Hunt Icon": "Hunt Icon", "Star Huntress": "Star Huntress", "Star Hunter": "Star Hunter", "Epic Huntress": "Epic Huntress", "Epic Hunter": "Epic Hunter", "Legendary Huntress": "Legendary <PERSON>", "Legendary Hunter": "Legendary Hunter", "Mistress of the Woods": "Mistress of the Woods", "Master of the Woods": "Master of the Woods", "Queen of the Mountains": "Queen of the Mountains", "King of the Hill": "King of the Hill", "The Chosen One": "The Chosen One", "The Chosen One_f": "The Chosen One", "Goddess of the Hunt": "Goddess of the Hunt", "God of the Hunt": "God of the Hunt", "level_up": "Your level has been increased to", "You can go through the tutorial again": "You can go through the <a href='!#/onboarding'>tutorial</a> again", "You can go through the tutorial": "You can go through the <a href='!#/onboarding'>tutorial</a>", "edited_vacancy_short": "The vacancy", "Difficulty level:": "Difficulty level:", "onboarding": "Onboarding", "Source report": "Candidate sources report", "stones": " stones", "камень": " stones", "камня": " stones", "камней": " stones", "Your current score for": "Collected stones", "You_newsfeed": "You", "Performance report": "Performance report", "Access will be restored soon": "Access will be restored soon.", "Please try again later": "Please try again later", "Unfortunately, CleverStaff is not available at this moment": "Unfortunately, CleverStaff is not available at this moment.", "The order of the name and surname for candidates and users": "The order of the name and surname for candidates and users", "FIO": "Last name", "FIO v2": "First name", "IOF": "First name", "IOF v2": "Last name", "Send test candidate to email": "Send test candidate to email", "The to age must be greater than the from age": "The \"to\" age must be greater than the \"from\" age", "Congratulation Artemida": "Congratulations!", "happy for you": "I'm very happy for you", "Congratulations_you": "Congratulations!", "You get": "You get", "for filling this vacancy": "for filling this vacancy", "received an award": "received an award", "close vacancy popup": "You get", "close vacancy popup other": "За закриття цієї вакансії нагороду", "good job": "Good job", "well done": "Well done! Good job", "A specialist of this level should have some work experience. Please specify the required work experience.": "A specialist of this level should have some work experience.<br> Please specify the required work experience.", "1_place_big": "The first", "2_place_big": "The second", "3_place_big": "Third", "1_place": "the first", "2_place": "the second", "3_place": "third", "race_month_result": "of the month", "race_quarter_result": "in the quarter", "race_year_result": "of the year", "place_results": "place according to the results", "good_results": "this is a very good result.", "big_lad": "You're a big lad!", "big_lady": "You're a big lad!", "wow": "Wow", "Its good result": "This is a great result, keep it up!", "Please enter 3 or more characters": "Please enter 3 or more characters", "Please enter 2 or more characters": "Please enter 2 or more characters", "Please enter 1 or more character": "Please enter 1 or more character", "Please enter": "Please enter", "or more characters": "or more characters", "You cannot change the status to the same": "You cannot change the status to the same", "Search actual": "Search", "The position is mandatory": "The Position parameter is mandatory for Auto Selection", "Only for IT vacancies": "only for ІТ vacancies", "Advanced search searches for strict": "Advanced search option is used to focus your search better for a strict and accurate match of all the parameters, and auto-matching is more flexible in the selection of candidates because each parameter has different gradations of compliance with the specified vacancy requirements. Therefore, results may vary", "reduced the number of the search criteria": "We've reduced the number of the search criteria, to find the right candidates", "we could not find candidates in your database": "Unfortunately, we could not find candidates in your database even with the minimum requirements", "You have added a candidate to the stage": "You have added a candidate to the stage:", "Upgrade of Recommended candidates": "Summer upgrades of CleverStaff.", "mentioned you": "mentioned you on", "page on candidate": "the candidate page", "page on vacancy": "the vacancy page", "page on client": "the client page", "mention placeholder": "Write a comment. You can mention another user via @", "mention you": "Write a comment about changing candidate status to (norequired). You can mention another user via @", "gmail-reintegration_step4-textUpd": "Gmail integration is only available for accounts that have 2-Step Verification enabled. You can check if your Google account has 2-Step Verification set up here.", "gmail-reintegration_step4-textUpd2": "Please set up 2-Step Verification for your Google account if you want to integrate mail with CleverStaff.", "candidate success removed": "Candidates have been removed from the vacancy", "Removing candidates from a job": "Removing candidates from a vacancy", "candidates from vacancies": "candidates from vacancies?", "candidate removed": "Has been removed", "Remove candidates from a job": "Remove candidates from a job", "Delete_1": "Delete", "The test testname has been sent to the candidate name admin": "The candidate <a href={{href}}>{{candidateName}}</a> has been sent test <a href={{testHref}}>{{testName}}</a>", "The test testname has been sent to the candidate name": "The candidate <a href={{href}}>{{candidateName}}</a> has been sent test <span class='underline'>{{testName}}</span>", "The test testname has been passed admin": "The candidate <a href={{href}}>{{candidateName}}</a> finished the test <a target='_blank' href={{testHref}}>{{testName}}</a>", "The test testname has been passed": "The candidate <a href={{href}}>{{candidateName}}</a> finished the test <span class='underline'>{{testName}}</span>", "For vacancies in the Closed status, the filter is applied by closing date": "For vacancies in the \"Closed\" status, the filter is applied by \"closing date\"", "selected period": "selected period", "add_employee_comment_header": "Adding a comment for an employee", "user-not-available": "Your role (Researcher) cannot see the profile of other users.", "You have successfully unsubscribed from sending tips": "You have successfully unsubscribed from sending tips", "All actions for": "All actions for", "vacancies filled": "vacancies filled", "by_user": "by user", "Select a vacancy field in the report": "Select a vacancy fields in the report", "Refuse stages": "Refuse stages", "work ua no experience required": "no experience required", "work ua more than 1 year": "more than 1 year", "work ua more than 2 years": "more than 2 years", "work ua more than 5 years": "more than 5 years", "Download a list of files that have not been added to the database": "Download a list of files that have not been added to the database", "Create google-meet": "Create a meeting in Google Meet", "Create teams-meet": "Create a meeting in Microsoft Teams", "Create skype-meet": "Create a meeting in Microsoft Skype", "Create teams-skype-meet": "Create a meeting in Microsoft Teams/Skype", "Select interview time": "Select the time and date of the interview", "Add guests": "Add guests", "Add guests using the list or manually": "Add guests using the list or manually", "Add meet comment": "Add a description for an appointment", "Descr for meeting": "Write a description for the meeting", "google_meet_error": "We didn't receive a meeting link from Google. Please try again later. If that doesn't work, please let our support know about the issue.", "meet-info": "The administrator and creator of the Google Meet will be the account that you have integrated in Calendar. All meeting participants, including the candidate, will receive a standard invitation on your behalf with the date, meeting link, description, if you add one. You can send a separate email to the candidate by selecting the “Notify the candidate via email” option and then clicking on the “Change stage and notify” button. In this case, the candidate will be sent a separate invitation, and the rest of the participants will receive a standard one", "meet-info2": "The administrator and creator of the Google Appointment will be the account that you have integrated in Calendar. All meeting attendees, including the candidate, will receive a standard invitation on your behalf with the date, meeting link, and meeting description, if you add one", "withoutEmail": "Candidate didn't provide email", "withoutGoogleCalendar": "Integrate your Google calendar in the Organizer tab and select the date and time of the interview", "withoutOutlookCalendar": "Integrate your Outlook calendar in the Organizer tab, then choose a date and time for the interview. Microsoft has restricted the ability to use Teams to its corporate clients only. However, others can use Skype only. Once the Outlook calendar is connected, we will be able to determine what you have access to.", "integrateYourGoogleCalendar": "Integrate Google calendar to edit meeting.", "integrateYourOutlookCalendar": "Integrate Outlook calendar to edit meeting.", "Removed plguin": "You have successfully removed our plugin.", "You can always install it again": "You can always install it again", "here": "here", "Email was not sent, please check mail integration settings": "Email was not sent, please check mail integration settings", "Only 10 scorecards": "A maximum of 10 scorecards can be attached to a vacancy", "Need one scorecard": "Vacancy must have at least one scorecard", "General impression": "General impression", "Scorecard placeholder": "You can leave a general impression of the candidate's evaluation", "Rate all criteria": "Please rate mandatory criteria in the scorecard", "Default scorecard": "Set as default card", "Select scorecard search": "To apply this sorting, please select one of the scorecards in the advanced search", "Scores": "Scores", "WithoutScores": "There are no ratings or comments for this card", "change default scorecard": "changed the default scorecard to", "Score-sort": "By the rate", "Scorecards and criteria": "Assessment results", "NoCardScore": "Candidate not rated on the cards", "NoCardsScore": "Candidate not rated on the card", "Universal Skills": "Universal Skills", "Sociability": "Sociability", "English level": "English level", "Leadership skills": "Leadership skills", "Stress tolerance": "Stress tolerance", "To continue, you need to answer the question": "To continue, you need to answer the question", "getPointsAfterFinish": "You will receive experience for completing the tutorial after it is completed.", "Date added to the database of the candidate": "Date added to the database", "in the vacancy": "in the vacancy", "Added new candidates": "Added new candidates", "and other": "and other", "My report": "My report", "Custom Reports": "Custom reports", "from the client": "from the customer", "tasksStatuses": {"inwork": "In Progress", "completed": "Filled", "Cancelled": "Canceled"}, "Updated candidates2": "Updated candidates", "resume file found": "Resume file found", "added new candidates": "Added new candidates", "Block change stage": "Block change stage", "Block the ability to transfer a candidate by stages in Vacancies.": "Block the ability to transfer a candidate by recruitment stages in vacancies", "The ability to transfer a candidate by stages in vacancies was changed for user": "The ability to transfer a candidate by stages in vacancies was changed for user", "was updated and": "was updated and", "were updated and": "were updated and", "Some of the candidates who responded were already in your database, so we updated them": "Some of the candidates who responded were already in your database, so we updated them", "profiles and add them to the stage": "profiles and add them to the stage", "updated1": "updated", "We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such candidates": "We noticed duplicate candidates in the reviews for this vacancy and did not create separate profiles for them, but combined them. Number of such recalls", "Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy.": "Therefore, there are a lot of vacancies for the vacancies themselves, and we can challenge them. In this case, it is necessary to take up duplicates at the same time for each vacancy", "Like default card": "(installed as default card)", "scorecard-vacancy": "on vacancy", "by card": "by card", "Email_templates": "Email templates", "Allow to change automatic actions in stages": "Allow to change automatic actions in stages", "The user will be able to set, change and delete automatic actions": "The user will be able to set, change and delete automatic actions when the candidate is transferred to the stage", "Access to editing SMS and email templates": "Access to editing SMS and email templates", "The user will be able to install, change and delete SMS and email templates": "The user will be able to install, change and delete SMS and email templates for the entire account", "My templates": "My templates", "General templates": "General templates", "Choose a letter template": "Choose a letter template", "Create a letter template": "Create a letter template", "Setting up automatic actions": "Setting up automatic actions", "Setting up automatic actions for stage": "Setting up automatic actions for stage", "Choose a template": "Choose a template", "Send via": "Send via", "You can only enter numbers up to 9999": "You can only enter numbers up to 9999", "Use for this stage in all vacancies": "Use for this stage in all vacancies", "Choose a test": "Choose a test", "Test selection": "Test selection", "You cant change the name for this template": "You can't change the name for this template", "The name of the template": "Template name", "Are you sure you want to permanently delete the template": "Are you sure you want to permanently delete the template", "Would you like to save new changes to the template": "Would you like to save new changes to the template", "s": "Seconds", "m": "Minutes", "h": "Hour", "d": "Days", "to 9999": "to 9999", "set your template for this step": "set your template for this step", "You have not created any template yet": "You have not created any template yet", "Several candidates were not sent a letter and test, because. email was not specified during verification.": "Several candidates were not sent a letter and test, because сandidates don't have email.", "Several candidates were not sent a test because Candidates dont have email.": "Several candidates were not sent a test because Candidates don't have email.", "Several candidates were not sent a letter because Candidates dont have email.": "Several candidates were not sent a letter because Candidates don't have email.", "The letter and the test were not sent because Candidate has no email.": "The letter and the test were not sent because Candidate has no email.", "The test was not sent because Candidate has no email.": "The test was not sent because Candidate has no email.", "The letter was not sent because Candidate has no email.": "The letter was not sent because Candi<PERSON> has no email.", "created a template": "created an email template", "edited template": "edited an email template", "deleted template": "deleted an email template", "removed the email template that was used for the step": "removed the email template that was used for the", "Automatic mail sending is disabled at this stage.": "Automatic mail sending is disabled at this stage.", "Automatic mail sending is disabled at these stages.": "Automatic mail sending is disabled at these stages.", "notice_stages": "stages", "notice_stage": "stage", "Automatic test submission is disabled at this stage.": "Automatic test submission is disabled at this stage.", "Automatic test submission is disabled for these stages.": "Automatic test submission is disabled for these stages.", "deleted the test template that was used for": "deleted the test template that was used for", "set automatic sending of a letter for the stage": "set automatic sending of a letter for the stage", "for vacancy": "for vacancy", "for all vacancies": "for all vacancies", "For all vacancies": "For all vacancies", "Throughout the entire duration of the account": "Throughout the entire duration of the account", "set automatic test submission for the stage": "set automatic test submission for the stage", "edited the automatic sending of a letter for the stage": "edited the automatic sending of a letter for the stage", "edited the automatic sending of the test for the stage": "edited the automatic sending of the test for the stage", "Delete sending email for this stage": "Delete sending email for this stage", "Delete sending test for this stage": "Delete sending test for this stage", "For this stage sending a letter is set": "For this stage sending a letter is set", "For this stage sending a test is set": "For this stage is set to send a test", "and test:": "and test:", "and test": "and test", "canceled automatic sending of a letter for a stage": "canceled automatic sending of a letter for a stage", "canceled automatic sending of a test for a stage": "canceled automatic sending of a test for a stage", "when moving to the stage": "when moving to the stage", "when moving to the stage not send": "When moving to the stage", "in vacancies": "in vacancies", "was not sent to the candidate because he did not have an email.": "was not sent to the candidate because he did not have an email", "was not sent to the candidate because he did not have an email._letter": "was not sent to the candidate because he did not have an email", "was sent to the candidate during the transfer to the stage": "was sent to the candidate during the transfer to the stage", "was sent to the candidate during the transfer to the stage_letter": "was sent to the candidate during the transfer to the stage", "Submitted test": "Submitted test", "Test not sent": "Test not sent", "Test not sent no email": "test not sent", "due to lack of email": "due to lack of email", "The letter was not sent": "was not sent", "email was not sent": "email was not sent", "because he doesnt have an email address.": "because he doesn't have an email address.", "because they dont have email.": "because they don't have email.", "Parts of candidates": "Parts of candidates", "test was not sent": "test was not sent", "The Applied People stage is mandatory. You can also assign an auto-action to these stages.": "The Applied people stage is mandatory. You can also assign an auto-action to this stage.", "The Found step is mandatory. You can also assign an auto-action to these stages.": "The Long List step is mandatory. You can also assign an auto-action to this stage.", "The Not a fit step is mandatory. You can also assign an auto-action to these stages.": "The Not a fit step is mandatory. You can also assign an auto-action to this stage.", "warning about gmail password": "Important! At the next stage, you need to enter the password generated in the Gmail settings, NOT the password from your mail account:", "The candidate's link to the profile has been removed": "", "User has been deleted": "User has been deleted", "or pay id": "or Pay ID", "Select tags": "Select tags", "Get started": "Get started", "Edit Reports": "Edit report", "Allow export of results to Excel and upload reports": "Allow export of reports and candidates lists to Excel", "allowed exporting reports to Excel for": "is allowed to export reports and candidates lists to Excel", "disabled export of reports to Excel from": "is disabled to export reports and candidates lists to Excel", "is allowed access to the Employees tab": "is allowed access to the “Employees“ tab", "is disabled access to the Employees tab": "is disabled access to the “Employees“ tab", "Please enter a title and URL": "Please enter a title and URL", "Please enter a title": "Please enter a title", "Employee editing": "Employee editing", "Please enter a URL": "Please enter a URL", "Average time": "Average time", "Number of candidates at the stage": "Number of candidates at the stage", "The chart displays the actual progression of candidates through the stages and does not account for any stages that a candidate may have skipped. Additionally, the chart shows the average time a candidate spends at each stage.": "The chart displays the actual progression of candidates through the stages and does not account for any stages that a candidate may have skipped. Additionally, the chart shows the average time a candidate spends at each stage.", "The histogram of stages differs from the funnel because": "The histogram of stages differs from the funnel because in this chart, candidates are considered and displayed only at the stages they were actually moved to. If a stage was skipped, the candidate will not be counted at that stage. Additionally, this chart indicates the average time candidates spend at each stage.", "Histogram of stages": "Histogram of stages", "For correct work of tests module": "Please disable any extensions in your browser, especially text translation extensions, for the test to function correctly.", "testForCandidate": "Test for candidate", "Test for candidate": "Test for candidate", "sendSuccessResultAfterTest": "The test passed successfully", "Success result after test": "Success result after test", "sendFailedResultAfterTest": "Candidate failed the test", "sendGoogleMeetInvite": "Invitation to an interview via Google Meet", "Sending Google Meet invite": "Invitation to an interview with Google Meet", "recallConfirm": "Auto-replay on the vacancy request", "Auto sending confirmed recall": "Auto sending confirmed recall", "Auto sending failed result after test": "Auto sending failed result after test", "Failed result after test": "Failed result after test", "Delete sending email for this step": "Delete sending email for this step", "Delete sending email for this stage in all vacancies": "Delete sending email for this stage in all vacancies", "Delete test submission for this step": "Delete test submission for this step", "Delete test submission for this stage in all vacancies": "Delete test submission for this stage in all vacancies", "An email template with this title already exists": "An email template with this title already exists", "Automatic test submission is set for the stage": "Automatic test submission is set for the stage", "Vacancy proposal": "Vacancy proposal", "Auto-replay on the vacancy application": "Auto-replay on the vacancy request", "The test passed successfully": "The test passed successfully", "Invitation to an interview via Google Meet": "Invitation to an interview via Google Meet", "Candidate failed the test": "Candidate failed the test", "Declined/refused candidates": "Declined/refused candidates", "was not sent to the candidate_test": "was not sent to the candidate", "was not sent to the candidate": "was not sent to the candidate", "was not sent to the candidate_mail": "was not sent to the candidate", "Automatic email sending for a stage": "Automatic email sending for a stage", "The automatic test sending at the": "The automatic test sending at the", "was set_mail": "was set", "was set_test": "was set", "was canceled": "was canceled", "was edited_auto_action": "was edited", "was sent to the candidate": "was sent to the candidate", "was sent to the candidate_test": "was sent to the candidate", "while being transferred to the": "while being transferred to the", "Editing the rejection reason": "Editing the rejection reason", "histogram_description": "The histogram of stages differs from the funnel because in this chart, candidates are considered and displayed only at the stages they were actually moved to. If a stage was skipped, the candidate will not be counted at that stage. Additionally, this chart indicates the average time candidates spend at each stage.", "Remind via": "Remind via:", "Remind and change status": "Remind and change status via:", "User must have email": "Users must have mail integrated.", "User must have email second": "If personal mail is not integrated, requests will be sent from our system mail", "Send mass gdpr-request": "Sent a request for the use of personal data", "Confirm action": "Confirm action", "Status Gdpr": "Status GDPR", "System cleverstaff email": "CleverStaff system mail", "Personal integrate email": "Personal integrated mail", "Send email but candidate dont response": "The request to use personal data was sent. <b>No response received</b>", "Send email for candidate": "The request to use personal data <b>was sent</b>", "The period has expired": "Personal data usage permit <b>has expired</b>", "hourNotify": "Hours", "dayNotify": "Days", "weekNotify": "Weeks", "monthNotify": "Months", "yearNotify": "Years", "But": "But", "for the candidate": "for the candidate", "pdcs_email_not_sent_solo": "The request to use personal data could not be sent to the candidate", "pdcs_email_not_sent_solo-1": "due to lack of the email in contact details", "candidate_edit_pdcs_confirmed": "Candidate gave permission to use personal data", "candidate_edit_pdcs_rejected": "Candidate banned the use of personal data", "candidate_edit_pdcs_email_sent": "The request to use personal data was sent to the candidate", "candidate_edit_pdcs_email_expired": "The request to use personal data was sent to the candidate. No response received", "candidate_edit_pdcs_expired": "Candidate personal data usage permit has expired", "candidate_edit_pdcs_confirmed_name": "gave permission to use personal data", "candidate_edit_pdcs_rejected_name": "banned the use of personal data", "candidate_edit_pdcs_email_sent_name": "The request to use personal data was sent to the candidate", "candidate_edit_pdcs_email_mass": "The request to use personal data was sent to the", "candidate_edit_pdcs_expired_name": "personal data usage permit has expired", "No response received": "No response received", "a request for the use of personal_mass": "The request to use personal data was sent to", "could not send the request_mass": "The request to use personal data could not be sent to", "could not send the request_mass-1": "candidates due to lack of the email in contact details", "candidates were not sent an email_mass": "The request was not sent to", "candidates were not sent an email_mass-1": "candidates due to a sending error", "a request for the use of personal_solo": "The request to use personal data was sent to the candidate", "could not send the request_solo": "The request to use personal data could not be sent to the candidate", "could not send the request_solo-1": "due to lack of the email in contact details", "candidates were not sent an email_solo": "The request was not sent to the candidate", "candidates were not sent an email_solo-1": "due to a sending error", "Send candidate gdpr": "The request to use personal data was sent to the candidate", "A request for the use of his personal data has been sent": "A request for the use of personal data has been sent. No response received", "A request for the use of personal data": "A request for the use personal data has been sent", "The period of use of the permission expired": "The period of use of the permission to use personal data has expired", "The candidate did not give permission": "The candidate did not give permission for the processing of personal data", "The candidate has given permission": "The candidate has given permission for the processing of personal data", "Candidate consent status undefined": "The status of the consent to process personal data is undetermined", "candidates did not respond to a request": "candidates did not respond to a request for the use of personal data", "candidate did not respond to a request": "candidate did not respond to a request for the use of personal data", "candidate did not respond to a request-2": "candidates did not respond to a request for the use of personal data", "candidates have expired their permission": "candidates have expired their permission to use personal data", "candidate have expired their permission": "candidate have expired their permission to use personal data", "set_pdcs_send_settings": "Automatic request to use personal data was enabled", "delete_pdcs_send_settings": "Automatic request to use personal data was disabled", "edit_pdcs_send_settings": "Automatic request to use personal data was edited", "set_pdcs_notification_settings": "The reminder that the candidate did not respond to the request to use their personal data was set", "delete_pdcs_notification_settings": "The reminder that the candidate did not respond to the request to use their personal data was disabled", "edit_pdcs_notification_settings": "The reminder that the candidate did not respond to the request to use their personal data was edited", "set_pdcs_expire_settings": "The expiry date of the personal data usage permit was set", "delete_pdcs_expire_settings": "The expiry date of the personal data usage permit was disabled", "edit_pdcs_expire_settings": "The expiry date of the personal data usage permit was edited", "edit_pdcs_template": "The request template of the personal data usage was edited", "edit_pdcs_email_setting": "Mail type for sending privacy request has been changed to", "personalEmail": "personal", "systemEmail": "system", "gdprStatuses": {"confirmed": "Permission received", "rejected": "Permission not received", "expired": "Permission expired", "email_sent": "Request sent", "email_expired": "No response received", "undefined": "Status undefined"}, "autoSendSettings": {"sendPlugin": "Added via plugin", "sendIntegration": "Added through integrations with job search sites", "sendManual": "Added manually", "sendArchive": "Added via Archive", "sendExcel": "Added via Excel import", "sendEmail": "Added via mail"}, "gdprAutoSend": "Automatic request to use personal data", "gdprNotify": "Set the reminder that the candidate did not respond to the request to use their personal data", "gdprExpire": "Set the expiry date of the personal data usage permit", "gdprTemplate": "Create your own email request template", "gdprMailbox": "Select an email to send requests", "GDPR Settings": "GDPR Settings", "closedBy": "Closed by", "closedByMale": "Closed by", "closedByFemale": "Closed by", "Count vacancy in status": "The number of vacancies in the status", "Average time of close vacancy": "Average time to fill a vacancy", "Count state interviews": "Scheduled interviews", "dashboardBlockTitles": {"Current": "Current", "Periodic": "Periodic", "Conversion": "Conversion"}, "Dashboard-info-1": "Comparison between the number of submitted offers and the number of accepted offers", "Dashboard-info-2": "The average time from the creation of the vacancy to the hiring of the candidate", "Dashboard-info-3": "The average time from the creation of the vacancy to the offer accepting by the candidate", "Dashboard-info-4": "The average number of interviews that need to be done before sending an offer", "Average time to close a vacancy hint": "Considers the time when vacancies are in Open, In progress, Payment and Replacement statuses", "Offer accepted ratio": "Sent offers into accepted offers", "Time to hire": "Time to hire", "Time to fill": "Time to fill", "Interview to hire ratio": "Interviews into sent offers", "Please select two or more vacancies": "Please select two or more vacancies", "System stages": "System stages", "To use for this stage in all jobs, add or change the settings": "To use for this stage in all vacancies, add or change the settings", "Choose template and test to send and set time to send template and test": "Choose template and test to send and set time to send template and test", "Choose template and test to send": "Choose template and test to send", "Set time to send template and test": "Set time to send template and test", "Choose template to send and set time to send template": "Choose template to send and set time to send template", "Choose test to send and set time to send test": "Choose test to send and set time to send test", "Set time to send template": "Set time to send template", "Set time to send test": "Set time to send test", "Choose template to send": "Choose template to send", "Choose test to send": "Choose test to send", "You cant set another template for a Google Meet interview": "You can't set another template for a Google Meet interview", "Select one or more candidates": "Select one or more candidates", "myTasksKanban": {"all_data": "All data", "my_data": "My data", "Today": "Today", "Tomorrow": "Tomorrow", "Future": "Future", "Expired": "Expired", "You have no tasks yet": "You have no tasks yet", "No tasks or events for today": "No tasks or interviews for today", "No tasks or events for tomorrow": "No tasks or interviews for tomorrow", "No tasks or events are planned for the future yet": "No tasks or interviews in the future", "No overdue tasks and interviews": "No overdue tasks and interviews", "Choose the candidate": "Select the candidate", "Choose the vacancy": "Select the vacancy", "Choose the client": "Select the client", "Candidate": "Candidate", "Vacancy": "Vacancy", "Client": "Client"}, "Task type": "Task type", "Overview": "Overview", "Calendar integration error": "The calendar is temporarily unavailable. We are already working on its return. You can view all your events in Google calendar", "Other_email": "Other", "Register": "Register", "Your personal account manager": "Your personal account manager", "Anastasiya Kolpakova": "<PERSON><PERSON><PERSON><PERSON>", "Evgeniya Antoniuk": "<PERSON><PERSON><PERSON><PERSON>iu<PERSON>", "Anastasia Yakovenko": "<PERSON>", "Please fill in this field": "Please fill in this field", "Please fill in this field 2": "Ensure this value has at least 300 characters", "Monitoring": "Dashboard", "Re-connect card": "Re-connect card", "were moved": "were moved from", "The employee is saved": "The employee is saved", "Adding an ICS file to emails": "Adding an ICS file to emails", "Interview_ICS": "Interview", "Add interview and task notifications ICS": "Add interview and task notifications ICS file to emails, which will allow you to add events to any calendar that supports this format", "Modified by candidate": "Modified by candidate", "Modified by user": "Modified by user", "shiftWork": "Shift work/rotational shift work", "Activate SAML authorization": "Activate SAML authorization", "Certificate": "Certificate", "If SAML authorization is activated, users will not be able to log into the account by entering their username and password or through their Google and Facebook accounts.": "If SAML authorization is activated, users will not be able to log into the account by entering their username and password or through their Google and Facebook accounts.", "Direct link to user login": "Direct link to user login", "Are you sure you want to disable SAML authorization?": "Are you sure you want to disable SAML authorization?", "If you disable it, the old way of user authorization will no longer be available, and an email will be sent to all users asking them to set a login password.": "If you disable it, the old way of user authorization will no longer be available, and an email will be sent to all users asking them to set a login password.", "Please make sure you have entered the correct data, as we cannot verify it at this stage.": "Please make sure you have entered the correct data, as we cannot verify it at this stage.", "If the data is incorrect, users will not be able to log in to their account and SAML authorization can only be disabled through CleverStaff support.": "If the data is incorrect, users will not be able to log in to their account and SAML authorization can only be disabled through CleverStaff support.", "SAML Connection guide": "Connection guide", "saml-guide": {"point-1": "1. Open the Azure Active Directory portal", "point-2": "2. Select \"Enterprise applications\"", "point-3": "3. Select \"New requests\"", "point-4": "4. Click \"Create your own application\", enter a name, and click \"Create\".", "point-5": "5. Select the Single Sign On settings.", "point-6": "6. Select the SAML method.", "point-7": "7. Enter the values obtained on the settings page in CleverStaff in the fields \"Identifier\n(Entity ID)\" and \"Reply URL (Assertion Consumer Service URL)\" fields.", "point-8": "8. Configure fields with information about users.\nRequired attribute for integration with CleverStaff:\ncleverstaff.user.email.", "point-8-2": "Optional attributes: \ncleverstaff.user.name, cleverstaff.user.surname.", "point-9": "9. Copy and enter the following values into the appropriate fields on the CleverStaff settings page: Certificate (Base64), Login URL (paste into the SSO url field), Azure AD Identifier (paste into the Entity id field).", "point-10": "10. Save the settings in CleverStaff."}, "Connect your BambooHR account to transfer hired employees from CleverStaff": "Connect your BambooHR account to transfer hired employees from CleverStaff", "Type of candidate transfer to BambooHR": "Type of candidate transfer to BambooHR", "BambooHR connection description": "You can connect only one user from your BambooHR account to your entire CleverStaff account. All employees will be created in BambooHR on his/her behalf. <strong>Important!</strong> Only the user who has the right to create employees in BambooHR should be connected.", "BambooHR company tooltip text": "The subdomain used to access your account (If you access BambooHR at https://mycompany.bamboohr.com, then the subdomain is \"mycompany\")", "BambooHR api tooltip text": "To generate an API key, users should log in and click their name in the upper right-hand corner of any page to get to the user context menu (in BambooHR). If they have sufficient permissions, there will be an \"API Keys\" option in that menu to go to the page", "BambooHR transfer type tooltip text": "If \"auto\" type is selected, candidates will be transferred to the connected BambooHR account automatically after selecting the candidate's status Our Employee. If \"manual\", then after selecting the status, you will need to click the Transfer to BambooHR button on the candidate's profile", "Auto": "Auto", "Manual": "Manual", "Only the Administrator can activate this integration": "Only the Administrator can activate this integration", "Integration with BambooHR connected": "Integration with BambooHR connected", "The type of candidate transfer with BambooHR has been changed": "The type of candidate transfer with BambooHR has been changed", "All fields are required": "All fields are required", "Transfer to BambooHR": "Transfer to BambooHR", "Candidate sent to BambooHR": "Candidate sent to BambooHR", "Integrations": "Recruiting/HR integrations", "BambooHR account has been integrated": "BambooHR account has been integrated", "BambooHR integration has been disabled": "BambooHR integration has been disabled", "BambooHR account has been re-integrated": "BambooHR account has been re-integrated", "Candidate data": "Candidate data", "has been transferred to the BambooHR integrated account": "has been transferred to the BambooHR integrated account", "Candidate data has been transferred to the BambooHR integrated account": "Candidate data has been transferred to the BambooHR integrated account", "API Key and/or My company are not valid": "API Key and/or My company are not valid", "search_by_stage": "Search by stage", "Send to client": "Send to client", "Add tags on pipeline": "Add tags", "Delete candidates pipeline": "Remove candidates", "All the candidates were added by you": "All the candidates were added by you", "Date pipeline interview": "Interview time", "No candidates in vacancy": "No candidates on this Vacancy now", "Add prop": "Added by", "Add prop_f": "Added by", "Pipeline": "Ka<PERSON><PERSON>", "More_info": "More", "Prohibit deletion of candidates for the user": "Prohibit deletion of candidates for the user", "The user will not be able to delete candidates": "The user will not be able to delete candidates", "was prohibited from removing candidates": "was prohibited from removing candidates", "Administrator": "The administrator", "Administrator name has prohibited you from deletion candidates": "The administrator {{name}} has prohibited you from deletion candidates", "Enter My company": "Enter My company", "Enter API Key": "Enter API Key", "Save as template": "Save as template", "Change the candidates source": "Change the candidate's source", "Change responsible": "Change responsible", "Change the desired position": "Change the desired position", "Responsible changed": "Responsible changed", "The desired position has changed": "The desired position has changed", "The candidate's source has changed": "The candidate's source has changed", "The user changed the source to": "The user changed the source to", "The user changed the desired position to": "The user changed the desired position to", "for the candidates": "for the candidates", "The user changed the source of the candidate to": "The user changed the source of the candidate to", "The user changed the desired candidate position to": "The user changed the desired candidate position to", "Add custom fields in account settings": "Add custom fields in account settings", "30 min": "30 min", "45 min": "45 min", "1 hour": "1 hour", "1.5 hours": "1.5 hours", "2 hours": "2 hours", "2.5 hours": "2.5 hours", "3 hours": "3 hours", "Length of the interview": "Length of the interview", "Title for the calendar": "Calendar event title", "Default title for the calendar": "Interview on vacancy {{vacancy}} with {{candidate}}", "Blocked Skype/Teams Meetings": "Your Administrator (of Microsoft Services) has blocked your access to use Microsoft Teams/Skype. Please contact him/her about unblocking your access and then re-integrate your calendar.", "meet-info-outlook": "The administrator and creator of the interview will be the account you have integrated into Calendar. All the interview participants, including the candidate, will receive a standard Microsoft Outlook invitation on your behalf with the date, meeting link, and description if you add one. Ensure that sending invitation emails is not disabled in your Microsoft account settings (you can contact your company's IT administrator to check this).", "Email memo": "Please note if you have not integrated your email (or someone who will transfer the candidate to this stage), all the letters will be sent from the CleverStaff system email.", "type": "Type", "A maximum of 5 responsible": "A maximum of 5 performers is allowed", "Post for the meeting": "Post for the meeting", "Beginning stories": "Start of activity", "No interviews": "There are no interviews", "Default-settings-save": "Chosen parameters for recommended candidates have been saved as default", "tasks-feed-hi-update": "Good to see you, {{user}}!", "tasks-feed-hi-1": "Hi, {{user}}!", "tasks-feed-hi-2-3": "Hi, {{user}}!", "tasks-feed-hi-4": "Hi, {{user}}!", "tasks-feed-hi-0": "Hi, {{user}}!", "tasks-feed-text-update": "We have good news about the system update, read the details", "tasks-feed-text-1-v2": "Don't forget about the interview at {{time}}. Have a nice conversation! 😊", "tasks-feed-text-1-v1": "Don't forget about the task for today! Have a nice day! 😊", "tasks-feed-text-2-3-v1": "Good luck in today's tasks, and when you have time, pay attention to the Hot Task on Gamification 🔥", "tasks-feed-text-2-3-v2": "Good luck in today's interviews, and when you have time, pay attention to the Hot Task on Gamification", "tasks-feed-text-2-3-v1-not": "Good luck in today's tasks, and when you have time, pay attention to possible awards for you through gamification", "tasks-feed-text-2-3-v2-not": "Good luck in today's interviews, and when you have time, pay attention to possible awards for you through gamification", "tasks-feed-text-2-3-v1-hm": "Good luck in today's tasks!", "tasks-feed-text-4": "Wow, you have really crazy weather day today!😍 I wish everything goes well and with pleasure!", "tasks-feed-text-0": "We suggest you pay attention to gamification tasks! With a bit of effort you can get a reward and even", "tasks-feed-text-0-end": "more stones! 💎", "colleagues-competitions": "Competition between the colleagues", "surpass-month": "Beat", "earn-stones": "Earn {{stones}} {{text}} in {{days}}", "Win an award": "Get the award", "Hot topic": "Hot topic", "You can integrate your email here": "You can integrate your email <a href=\"#/email-integration\" target=\"_blank\">here</a>.", "Select all found": "Select all found", "Please enter the current time": "Please enter the current time or it will be set automatically (+3 hours)", "has been updated with Voithos AI. Updated": "has been updated with Voithos AI. Updated", "Voithos AI updated": "Voithos AI updated", "Wandify updated": "Wandify updated", "Wandify not updated": "Wandify did not update", "Candidates' data": "Candidates' data", "The candidate's data was updated using Wandify. Updated": "The candidate's data was updated using Wandify. Updated", "The candidate's data was not updated using Wandify because it is currently up to date": "The candidate's data was not updated using Wandify because it is currently up to date", "The candidate's contact details were not updated using Wandify because they are currently up to date": "The candidate's contact details were not updated using Wandify because they are currently up to date", "has been updated with Wandify. Updated": "has been updated with Wandify. Updated", "has not been updated with Wandify": " has not been updated with Wandify because the candidate's data is currently up to date", "Candidates' data was updated with Wandify": "data was updated using Wandify", "Candidates' data was not updated with Wandify": "was not updated using Wandify because it is currently up to date", "for_candidate": "for candidate", "This block is not available when a “Vacancy creation date” filter is selected": "This block is not available when a “Vacancy creation date” filter is selected", "recallTypes": {"string": "String", "select": "Drop-down list", "text": "Text", "date": "Date", "datetime": "Date and time", "textFile": "Text and file", "file": "File", "selectFile": "Drop-down list and file"}, "Fill a field": "Fill in the field", "Select a value": "Select a value", "Select a value and file": "Select a value and a file", "Select a text and file": "Fill in the field and select the file", "Default-recall-template": "Default template", "delete-recall-ico": "Delete reply", "Delete-recall-from-candidate": "Are you sure you want to delete the candidate's responses ?", "Recalls-from-public-page": "Answers from the request form on a public page", "do_m": "Made changes", "do_f": "Made changes", "change-to-template": "to the request form", "edit-recall-template": "has been edited", "create-recall-template": "has been created", "remove-recall-template": "has been removed", "save-recall-template": "has been saved", "enter-text": "Enter text", "one-file": "file", "more-files": "files", "Need-one-enabled": "At least one field must be mandatory", "Need-one-field": "A template must contain at least one field", "Recall-template": "Request form template", "Limit-count-files": "Limit the number of files", "Create-recall-template": "Creating a response template", "Edit-recall-template": "Editing the feedback template", "Enter a name recall template": "Enter the name of the template", "Move-up": "Move the field up", "Move-down": "Move the field down", "big-file-size": "Large file size", "Remove-recall-field": "Delete field", "Disable-recall-field": "Disable field", "Add-recall-template": "Add a template", "Recall-constructor": "Request form builder on a public page", "Make-field-mandatory": "Make the field required", "Remove-recall-template": "Are you sure you want to remove this template? In all vacancies where this template is set, the default template will be added automatically", "notValidName": "Name contains invalid characters", "calendarTitle": "Enter the calendar event title", "Enter the calendar event title": "Enter the calendar event title", "Evaluation on vacancy": "Appreciation in the vacancy", "Evaluation by card": "Appreciation in the scorecard", "Link to": "Link to", "earn-stones for today": "Earn {{stones}} {{text}} for today", "too": "also", "The date filter in the report": "This filter 'by date' in the report shows the last date when a candidate was moved to a new recruiting stage.", "The filter by creation date report": "The filter by creation date applies only to vacancies with the Open status. The filter does not apply to the date the job applicant was added.", "For vacancies in the Closed status": "For vacancies in the \"Closed\" status, the filter is applied by \"closing date\"", "If you select vacancy creation": "If you select \"Vacancy creation date\", only those vacancies that were created during the selected period will be included in the report", "Last transfer per stage": "Last transfer per stage", "job creation date": "Vacancy creation date", "Name transliteration": "Search is based on a full and partial match", "Surname transliteration": "Search is based on a full and partial match", "Possible doubles": "Possible doubles", "fileTemporaryUnavailable": "Plik chwilowo niedostępny", "blocked-for-anonymous-candidate": "This function is not available for an anonymous candidate", "This candidate is now looking for a job": "This candidate is looking for a job", "block-autotests": "This feature is available during the trial period only with the integrated email", "consentBanner": {"Cookie settings": "Cookie settings", "We use cookies to provide you with the best possible experience": "We use cookies to provide you with the best possible experience. They also allow us to analyze user behavior in order to constantly improve the website for you.", "Accept All": "Accept All", "Accept Selection": "Accept Selection", "Reject All": "Reject All", "Necessary": "Necessary", "Analytics": "Analytics", "Preferences": "Preferences", "Marketing": "Marketing", "Personalized ads": "Personalized ads", "Should you want to find out more about our cookie policy": "Should you want to find out more about our cookie policy - press", "read more": "read more"}, "Format": "Format", "Failed to enable integration: please check your email and password": "Failed to enable integration: please check your email and password", "Candidate Location": "Candidate location", "Vacancy was not shared": "Vacancy was not shared", "Please save your corporate facebook page correctly": "Please save your corporate facebook page correctly", "Please save web site address correctly": "Please save web site address correctly", "Open positions": "Open positions", "Salary type": "Salary type", "Exact amount": "Exact amount", "Negotiable": "Negotiable", "Delucru job active": "Active", "Delucru job inactive": "Inactive", "Post a job": "Post a job", "Benefits": "Benefits", "Other filters": "Other filters", "Transport": "Transport", "Type of contract": "Type of contract", "Salary payment": "Salary payment", "You have posted an inactive Delucru vacancy": "You have posted an inactive vacancy. Once you activate it on Delucru.md, insert the link here to receive applications and statistics.", "Perfect candidate": "This candidate is a perfect match for you", "need": "need to", "need 2": "required", "need 3": "need to be", "need from": "need from", "need from salary": "should be from", "need to salary": "should be up to", "Advice preview": "The match rate of the candidate’s profile to job requirements", "Missing": "Missing", "Unknown": "Unknown, specify", "Any level": "any level is suitable", "Any exp": "аny experience is acceptable", "Can relocate": "Can relocate", "Details have been clarified": "Details have been clarified", "Check details what": "Check what details you should clarify with the candidate", "Missing email": "The invitation cannot be sent due to the lack of a contact person’s email address", "Open all comments for Hiring Manager": "Enable comments on <PERSON><PERSON><PERSON> and <PERSON><PERSON> for the Hiring Manager", "openedAccess_male": "enabled", "openedAccess_female": "enabled", "closedAccess_male": "disabled", "closedAccess_female": "disabled", "hmAccessToComments": "{{ verb }} access to all comments for Hiring Manager", "aio_edit_action": "The profile has been supplemented by the VoithosAI suggestion", "aio_edit_action_mass": "supplemented candidate <a class=\"not-user\" href=\"{{ localId }}\">{{ fullName }}</a> profile by the VoithosAI suggestion", "What to ask a candidate for this vacancy": "What to ask a candidate for this vacancy", "Upload date": "Upload date", "Add events to the primary (personal) calendar": "Add events to the primary (personal) calendar", "You will be added as a participant to all events": "You will be added as a participant to all events. You can hide the events from the «CleverStaff events» calendar to prevent duplication.", "Enter Alpha Name": "Enter Alpha Name", "sms_templates": "SMS templates", "Choose SMS template": "Choose SMS template", "Integration with AlphaSMS connected": "Integration with AlphaSMS connected", "API Key and/or Alpha Name are not valid": "API Key and/or Alpha Name are not valid", "Send SMS": "Send SMS", "Creating SMS": "Creating SMS", "SMS template": "SMS template", "Create SMS template": "Create SMS template", "phone number is not specified": "phone number is not specified", "Delete all recipients without number": "Delete all recipients without phone number", "Not all recipients have numbers": "Not all recipients have phone numbers", "SMS sent": "SMS sent", "Send before": "Send before", "before 9999": "before 9999", "Choose SMS to send and set time to send SMS": "Choose SMS to send and set time to send SMS", "Choose SMS to send": "Choose SMS to send", "Set time to send SMS": "Set time to send SMS", "For this stage sending SMS is set": "For this stage sending SMS is set", "and letter": "and letter", "and_letter": "letter", "AlfaSMS account has been integrated.": "AlfaSMS account has been integrated.", "AlfaSMS integration has been disabled.": "AlfaSMS integration has been disabled.", "created SMS template": "created SMS template", "edited SMS template": "edited SMS template", "deleted SMS template": "deleted SMS template", "set automatic sending of an SMS for the stage": "set automatic sending of an SMS for the stage", "edited the automatic sending of an SMS for the stage": "edited the automatic sending of an SMS for the stage", "canceled automatic sending of an SMS for a stage": "canceled automatic sending of an SMS for a stage", "The SMS to the candidate": "SMS was sent to candidate", "was sent SMS": "was sent", "Missing number": "The SMS cannot be sent to candidate <a class=\"not-user\" href=\"{{ localId }}\">{{ fullName }}</a> due to the lack of a number", "Missing number cand": "The SMS cannot be sent to candidate due to the lack of a number", "alpha_sms_error": "Error sending SMS to candidate <a class=\"not-user\" href=\"{{ localId }}\">{{ fullName }}</a> on the AlphaSMS side", "alpha_sms_error_cand": "Error sending SMS to candidate on the AlphaSMS side", "alphasms": "Connect your AlphaSMS account to send SMS to candidates", "alphasms2": "You can integrate your existing account, which will allow you to send SMS to candidates, create SMS templates, and enable SMS in automated actions.", "alphasms3": "The template with the \"Interview Date\" operator can only be used for auto-sending SMS at \"Interview\" stage types.", "alphasms4": "SMS messages are limited to 70 characters when using Cyrillic and\n140 characters when using Latin script. Longer messages will be split\ninto multiple SMS. Each SMS is charged separately", "alphasms5": "Recipient numbers", "alphasms6": "The phone number to which the SMS will be sent (or add another one from the list manually).", "alphasms7": "Create a task and add to the calendar", "alphasms8": "Add a phone number", "alphasms9": "AlphaSMS account is connected", "alphasms10": "Interview with candidate {{dateTime}}", "alphasms11": "Please note that the template has been modified previously.", "alphasms12": "You can find the alpha name in your AlphaSMS account under Dashboard → Settings → Sender", "alphasms13": "You can find the API key in your AlphaSMS account under Dashboard → Settings → API", "block-autosms": "This feature is available only with AlphaSMS integration", "All are selected": "All are selected", "requiredScoreCardQuestion": "Make this field mandatory for filling", "Data has been updated": "Data has been updated", "Reconnect": "Reconnect", "Wandify account has been integrated": "Wandify account has been integrated", "Wandify account has been reintegrated": "Wandify account has been reintegrated", "Wandify integration has been disabled": "Wandify integration has been disabled", "Connect your Wandify account to update candidate's data": "Connect your Wandify account to update candidate's data and get recommended candidates for your vacancies", "You have integrated your Wandify account": "You have integrated your Wandify account. Also you can disable current integration, connect another account, reintegrate (reconnect) when changing the API key in your Wandify account", "You can integrate your account here if you are already a Wandify user": "You can integrate your account here if you are already a Wandify user. Otherwise, you can create a new one and receive special conditions. We will send the data below to the Wandify service, if you don't have an account, one will be created for you", "Update candidate's data (Such data can be updated:": "Update candidate's data from Wandify. Сan be updated Full name, position, location, experience, skills, languages, education, certificates", "Update candidate's contact details using Wandify": "Update contact details from Wandify", "You don't have Wandify integration yet": "You don't have Wandify integration yet. To access this feature, an account will be created for you, and the integration will be activated. After that, you will receive 14 days of free usage without any payment requirements or the need to connect a bank card. </br></br> Do you agree to sign up for Wandify and set up the integration?", "Search on Wandify": "Search on Wandify", "Connect to Wandify": "Connect", "Candidate was added as a recommendation from Wandify": "Candidate was added as a recommendation from Wandify", "Wandify contacts has been updated": "The candidate's <a href='#/candidates/{{localId}}'>{{ fullName }}</a> contact details were updated using Wandify", "wandifyToast": {"oneProfileUpdated": "The candidate's data was updated using Wandify", "oneProfileNotUpdated": "The candidate's data was not updated using Wandify", "manyProfileUpdated": "Candidates data was updated using Wandify", "manyProfileNotUpdated": "Candidates data was not updated using Wandify", "oneContactUpdated": "The candidate's contact details were updated using Wandify", "oneContactNotUpdated": "The candidate's contact details were not updated using Wandify", "manyContactUpdated": "Candidates contact details were updated using Wandify", "manyContactNotUpdated": "Candidates contact details were not updated using Wandify"}, "Candidates' data is being updated in the background. We will notify you once it’s complete": "Candidates' data is being updated in the background. We will notify you once it’s complete", "Update contacts": "Update contact details", "Update profile": "Update profile", "Allow other users in the account to update": "Allow other users in the account to update the candidate’s profile and contact details using this integration", "You have integrated the Wandify account": "You have integrated the Wandify account", "Wandify candidates to update limit": "You can update up to 20 candidates at a time", "Message text": "Message text", "Select date and time": "Select date and time", "Experience is not enough here": "Experience is not enough here", "by the Voithos AI suggestion": "by the Voithos AI suggestion", "Tech vacancy": "Tech vacancy", "Non-tech": "Non-tech"}