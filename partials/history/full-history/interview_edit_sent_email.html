<div class="col-lg-10">
    <span>
        {{ 'Candidate' | translate }}
        <a ng-if="$root.useAmericanNameStyle" ng-href="#/candidates/{{ history.candidate.localId }}">
            {{ history.candidate.fullNameEn }}
        </a>
        <a ng-if="!$root.useAmericanNameStyle" ng-href="#/candidates/{{ history.candidate.localId }}">
            {{ history.candidate.fullName }}
        </a>
        {{ 'history_info.was moved from' | translate }}
        <span ng-if="history.stateOldName" class="stage  passed {{ history.stateOldName }}">
            {{ history.stateOldName | translate }}
        </span>
        <span ng-if="!history.stateOldName" class="stage  passed {{ history.stateOld }}">
            {{ history.stateOld | translate }}
        </span>
        {{ 'history_info.stage to' | translate }}
        <span ng-if="history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNewName | translate }}
        </span>
        <span ng-if="!history.stateNewName" class="stage {{ history.stateNew }}">
            {{ history.stateNew | translate }}
        </span>
        <span ng-style="$root.currentLang == 'ru' ? { 'margin-left': '-2px' } : { 'margin-left': '0px' }">
            {{ 'history_info.stage in combination with the email sent' | translate }}
        </span>
    </span>

    <span ng-show="history.descr">
        <br />
        <span ng-show="history.dateEdit" class="pull-left date">
            ({{ 'edited_big' | translate }} {{ history.dateEdit | dateFormatSimpleAction : true | translate }})&nbsp;
        </span>
        {{ 'comment' | translate }}:
        <br />
        <span ng-bind-html="history.descr|textForNotice:false"></span>
    </span>
</div>
