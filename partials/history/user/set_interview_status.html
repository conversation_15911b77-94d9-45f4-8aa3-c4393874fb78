<div class="col-lg-10">
    <span>{{ 'Vacancy stages have been changed' | translate }}</span>
    <a href="#/vacancies/{{ history.vacancy.localId }}">
        <span>{{ history.vacancy.position + ($root.me.personParams.clientAccessLevel !== 'hide' ? ', ' : '') | limitToEllipse : 300 }}</span>
    </a>
    <a ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" href="#/clients/{{ history.vacancy.clientId.localId }}">
        <span>({{ history.vacancy.clientId.name }})</span>
    </a>
    <br />
    <!-- prettier-ignore -->
    <span ng-repeat="stage in history.stagesArray track by $index">
        {{ stage | translateStage }}<span ng-if="!$last">,</span>
    </span>
    <span style="color: #000" ng-bind-html="user.lastAction.descr|cutName:true:70|textForNotice:false"></span>
</div>
