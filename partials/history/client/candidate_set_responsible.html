<div class="col-lg-10">
    <br />
    <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}">
        <span once-text="history.targetPerson.fullNameEn"></span>
    </a>
    <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ history.targetPerson.userId }}">
        <span once-text="history.targetPerson.fullName"></span>
    </a>
    {{ 'history_info.assigned' | translate }} {{ 'history_info.as_responsible' | translate }}
    <a href="#/vacancies/{{ history.vacancy.localId }}">
        <span once-text="history.vacancy.position |limitToEllipse:300"></span>
    </a>
    <span ng-show="history.candidate">
        {{ 'history_info.in_candidate' | translate }}
        <a href="#/candidates/{{ history.candidate.localId }}">
            <span ng-if="$root.useAmericanNameStyle" once-text="history.candidate.fullNameEn"></span>
            <span ng-if="!$root.useAmericanNameStyle" once-text="history.candidate.fullName"></span>
        </a>
    </span>
    <br />
    <span ng-show="!history.editCommentFlag" ng-bind-html="history.descr|textForNotice:false"></span>
</div>
