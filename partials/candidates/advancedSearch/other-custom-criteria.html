<h4 class="adv-search-filters_column-head">{{ 'Other-1' | translate }}&nbsp;/ {{ 'Custom fields' | translate }}</h4>

<div
    id="other-fields-column"
    ng-if="vm.search.getFieldsStateByType('other', false) || vm.customFieldsSearch.getFieldsSelectedState({ some: true })"
    class="adv-search-filters_column-body"
>
    <div ng-if="vm.search.fields.gdpr.state.isSelected" class="field industry-field">
        <h5 class="adv-search-filters_filter-label">{{ vm.search.fields.gdpr.placeholder | translate }}</h5>
        <select-multi-virtualized
            placeholder="vm.search.fields.gdpr.placeholder"
            options="vm.search.fields.gdpr.data"
            path-to-label="'name'"
            translate-options="true"
            selected-values="vm.search.fields.gdpr.selectedStatuses"
            on-change="(vm.onChangeGdprSelect)"
            position="'fixed'"
            path-to-key="'name'"
            is-searchable="false"
            select-all-text="'Select all-v2' | translate"
        ></select-multi-virtualized>
    </div>

    <div ng-if="vm.search.fields.origin.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Source' | translate }}</h5>
        <select-single-virtualized
            placeholder="vm.search.fields.origin.placeholder"
            is-searchable="true"
            options="vm.allOriginsData"
            translate-options="true"
            selected-value="vm.search.fields.origin.value"
            on-change="(vm.onChangeSelectField)"
            additional-arg="vm.search.fields.origin"
            disabled="vm.search.fields.origin.noValue"
            position="'fixed'"
        ></select-single-virtualized>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.origin)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.origin.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.comment.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'By_comments' | translate }}</h5>
        <input-component
            placeholder="'By_comments'"
            value="vm.search.fields.comment.value || ''"
            on-change="(vm.onChangeInputField)"
            additional-arg="vm.search.fields.comment"
        ></input-component>
    </div>

    <div ng-if="vm.search.fields.activity.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Last activity by candidate' | translate }}</h5>

        <div class="adv-search-filters__date-picker-group">
            <mdp-date-picker
                class="date-field-picker"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                mdp-open-on-click
                mdp-placeholder="{{ 'Date from' | translate }}"
                ng-model="vm.search.fields.dateDmFrom.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="vm.search.fields.activity.value"
                    ng-click='vm.search.fields.activity.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-picker>

            <mdp-date-picker
                class="date-field-picker"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                mdp-open-on-click
                mdp-placeholder="{{ 'to date' | translate }}"
                ng-model="vm.search.fields.dateDmTo.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="vm.search.fields.activity.value"
                    ng-click='vm.search.fields.activity.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-picker>
        </div>
    </div>

    <div ng-if="vm.search.fields.addedToDb.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Date added to the database of the candidate' | translate }}</h5>

        <div class="adv-search-filters__date-picker-group">
            <mdp-date-picker
                class="date-field-picker"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                mdp-open-on-click
                mdp-placeholder="{{ 'Date from' | translate }}"
                ng-model="vm.search.fields.dateDcFrom.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="vm.search.fields.activity.value"
                    ng-click='vm.search.fields.activity.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-picker>

            <mdp-date-picker
                class="date-field-picker"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                mdp-open-on-click
                mdp-placeholder="{{ 'to date' | translate }}"
                ng-model="vm.search.fields.dateDcTo.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="vm.search.fields.activity.value"
                    ng-click='vm.search.fields.activity.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-picker>
        </div>
    </div>

    <div ng-if="vm.search.fields.responsible.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'responsible' | translate }}</h5>
        <select-multi-virtualized
            ng-if="vm.responsibleOptionsGrouped"
            placeholder="'responsible'"
            options="vm.responsibleOptionsGrouped"
            selected-values="vm.search.fields.responsible.selectedResponsibles"
            on-change="(vm.onChangeResponsible)"
            is-grouped="true"
            translate-options="false"
            path-to-key="'userId'"
            position="'fixed'"
            select-all-text="'Select all-v3' | translate"
        ></select-multi-virtualized>
    </div>

    <div ng-if="vm.search.fields.candidateGroup.state.isSelected" class="field candidateGroups-field">
        <h5 class="adv-search-filters_filter-label">{{ 'add tags' | translate }}</h5>
        <select-multi-virtualized
            current-lang="$root.currentLang"
            placeholder="'add tags'"
            options="vm.candidateGroupsOptions"
            path-to-label="'name'"
            path-to-value="'name'"
            path-to-key="'candidateGroupId'"
            selected-values="vm.search.fields.candidateGroup.value"
            on-change="(vm.onChangeTagsField)"
            translate-options="false"
            disabled="vm.search.fields.candidateGroup.noValue"
            position="'fixed'"
            select-all-text="'Select all-v2' | translate"
        ></select-multi-virtualized>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.candidateGroup)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.candidateGroup.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.vacancyId.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'participation_in_vacancies' | translate }}</h5>
        <select-single-async
            placeholder="'participation_in_vacancies'"
            fetch-options="(vm.getVacanciesForCandidateAdvSearch)"
            on-change="(vm.onSelectVacancy)"
            selected-value="vm.search.fields.vacancyId.value"
            position="'fixed'"
        ></select-single-async>
    </div>

    <div ng-if="vm.search.fields.stages.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'search_by_stage' | translate }}</h5>
        <select-multi-virtualized
            placeholder="'search_by_stage'"
            select-all-text="'Select all-v2' | translate"
            options="vm.allAccountSages"
            translate-options="false"
            selected-values="vm.search.fields.stages.value"
            on-change="(vm.onChangeStages)"
            path-to-key="'key'"
            path-to-value="'key'"
            position="'fixed'"
        ></select-multi-virtualized>
    </div>

    <!--Custom fields-->
    <div
        ng-if="vm.customFieldsSearch.getFieldsSelectedState({ some: true }) && customField.state.isSelected"
        ng-repeat="customField in vm.customFieldsSearch.fields track by $index"
        ng-switch="customField.type"
    >
        <h5 ng-if="customField.state.isSelected" class="adv-search-filters_filter-label">{{ customField.placeholder }}</h5>

        <input-component
            ng-switch-when="string"
            placeholder="customField.placeholder"
            on-change="(vm.onChangeCustomString)"
            value="customField.value || ''"
            additional-arg="customField"
            disabled="customField.noValue"
        ></input-component>

        <select-single-virtualized
            ng-switch-when="select"
            is-searchable="customField.data.length > 10"
            placeholder="customField.placeholder"
            options="customField.data"
            path-to-label="'value'"
            path-to-value="'value'"
            on-change="(vm.onChangeCustomField)"
            additional-arg="customField"
            selected-value="customField.value"
            disabled="customField.noValue"
            position="'fixed'"
        ></select-single-virtualized>

        <div
            class="adv-search-filters__date-field-picker"
            ng-class="{ 'adv-search-filters__date-field-picker_disabled': customField.noValue }"
            ng-switch-when="date"
        >
            <mdp-date-picker
                class="date-field-picker"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY' : 'DD/MM/YYYY' }}"
                mdp-open-on-click
                mdp-placeholder="{{ 'date' | translate }}"
                ng-model="customField.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="customField.value"
                    ng-click='customField.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-picker>
        </div>

        <div
            class="adv-search-filters_date-field"
            ng-class="{ 'adv-search-filters__date-field-picker_disabled': customField.noValue }"
            ng-switch-when="datetime"
        >
            <mdp-date-time-picker
                class="date-field-picker"
                mdp-auto-switch="true"
                mdp-format="{{ $root.currentLang === 'en' ? 'M/DD/YYYY h:mm A' : 'DD/MM/YYYY HH:mm' }}"
                mdp-open-on-click
                mdp-placeholder="{{ customField.placeholder }}"
                name="{{ customField.fieldId }}"
                ng-model="customField.value"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
                <img
                    ng-if="customField.value"
                    ng-click='customField.value = ""'
                    class="date-field-picker__clear-icon"
                    alt=""
                    src="/images/redesign/svg-icons/close.svg"
                />
            </mdp-date-time-picker>
        </div>

        <div ng-click="vm.onToggleNoValue($event, customField)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" meta="customField" is-checked="customField.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>
</div>
