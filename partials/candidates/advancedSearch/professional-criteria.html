<h4 class="adv-search-filters_column-head">{{ 'Professional br Information' | translate }}</h4>

<div ng-if="vm.search.getFieldsStateByType('professional', false) && vm.model.criteria.professional" class="adv-search-filters_column-body">
    <div ng-if="vm.search.fields.position.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'position' | translate }}</h5>
        <select-single-async-creatable
            placeholder="'position' | translate"
            fetch-options="(vm.getPositionsOptions)"
            on-change="(vm.onChangePosition)"
            selected-value="vm.search.fields.position.value"
            position="'fixed'"
        ></select-single-async-creatable>
    </div>

    <div ng-if="vm.search.fields.roleLevels.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ vm.search.fields.roleLevels.placeholder | translate }}</h5>
        <select-multi-virtualized
            placeholder="vm.search.fields.roleLevels.placeholder"
            options="vm.allRolesList"
            translate-options="true"
            is-searchable="false"
            selected-values="vm.search.fields.roleLevels.value"
            on-change="(vm.onChangeSelectField)"
            additional-arg="vm.search.fields.roleLevels"
            disabled="vm.search.fields.roleLevels.noValue"
            position="'fixed'"
            path-to-key="'label'"
            max-menu-height="200"
            select-all-text="'Select all-v2' | translate"
        ></select-multi-virtualized>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.roleLevels)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.roleLevels.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.scorecards.state.isSelected" class="field industry-field">
        <h5 class="adv-search-filters_filter-label">{{ vm.search.fields.scorecards.placeholder | translate }}</h5>
        <select-single-virtualized
            placeholder="vm.search.fields.scorecards.placeholder"
            options="vm.scorecardsOptions"
            selected-value="vm.search.fields.scorecards.value"
            on-change="(vm.onChangeSelectField)"
            is-searchable="true"
            additional-arg="vm.search.fields.scorecards"
            path-to-label="'name'"
            path-to-value="'scoreCardId'"
            position="'fixed'"
        ></select-single-virtualized>
    </div>

    <div ng-if="vm.experience && vm.search.fields.experience.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ vm.search.fields.experience.placeholder | translate }}</h5>
        <select-single
            placeholder="vm.search.fields.experience.placeholder"
            options="vm.experience"
            on-change="(vm.onChangeSelectField)"
            selected-value="vm.search.fields.experience.value"
            additional-arg="vm.search.fields.experience"
            translate-options="true"
            position="'fixed'"
        ></select-single>
    </div>

    <div ng-if="vm.search.fields.languages.state.isSelected">
        <h5 class="adv-search-filters_filter-label">{{ 'Languages' | translate }}</h5>
        <languages-filter
            translate-func="$root.translate"
            languages="vm.languagesOptions"
            on-change="(vm.onChangeLanguage)"
            selected-languages="vm.search.fields.languages.value"
        ></languages-filter>
    </div>

    <div ng-if="vm.search.fields.skills.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'Skills' | translate }}</h5>
        <ng-include src="'partials/candidates/advancedSearch/skills-block.html'"></ng-include>
    </div>

    <div ng-if="vm.search.fields.salaryTo.state.isSelected && $root.me.recrutRole !== 'client'" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'salary' | translate }}</h5>
        <div class="salary-block">
            <input-component
                class="salary-block__input"
                placeholder="vm.search.fields.salaryTo.placeholder"
                value="vm.search.fields.salaryTo.value || ''"
                on-change="(vm.onChangeInputField)"
                additional-arg="vm.search.fields.salaryTo"
                disabled="vm.search.fields.salaryTo.noValue"
            ></input-component>

            <select-single
                class="salary-block__currency"
                options="vm.currency"
                translate-options="true"
                selected-value="vm.search.fields.salaryTo.salaryCurrency.value"
                path-to-label="'value'"
                path-to-value="'value'"
                on-change="(vm.onChangeSelectField)"
                is-clearable="false"
                additional-arg="vm.search.fields.salaryTo.salaryCurrency"
                disabled="vm.search.fields.salaryTo.noValue"
                position="'fixed'"
            ></select-single>
        </div>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.salaryTo)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.salaryTo.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.employmentType.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'employment_type' | translate }}</h5>
        <select-single
            placeholder="vm.search.fields.employmentType.placeholder"
            options="vm.employmentType"
            translate-options="true"
            selected-value="vm.search.fields.employmentType.value"
            on-change="(vm.onChangeSelectField)"
            additional-arg="vm.search.fields.employmentType"
            disabled="vm.search.fields.employmentType.noValue"
            position="'fixed'"
        ></select-single>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.employmentType)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.employmentType.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.industry.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'industry' | translate }}</h5>
        <select-single-virtualized
            placeholder="'industry'"
            options="vm.industryOptions"
            translate-options="true"
            on-change="(vm.onChangeSelectField)"
            selected-value="vm.search.fields.industry.value"
            is-searchable="true"
            path-to-label="'value'"
            additional-arg="vm.search.fields.industry"
            disabled="vm.search.fields.industry.noValue"
            position="'fixed'"
        ></select-single-virtualized>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.industry)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.industry.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>

    <div ng-if="vm.search.fields.currentWorkingPlace.state.isSelected" class="field">
        <h5 class="adv-search-filters_filter-label">{{ 'By_currentWorkingPlace' | translate }}</h5>
        <input-component
            placeholder="'By_currentWorkingPlace'"
            value="vm.search.fields.currentWorkingPlace.value || ''"
            on-change="(vm.onChangeInputField)"
            additional-arg="vm.search.fields.currentWorkingPlace"
            disabled="vm.search.fields.currentWorkingPlace.noValue"
        ></input-component>

        <div ng-click="vm.onToggleNoValue($event, vm.search.fields.currentWorkingPlace)" class="adv-search-filters_no-value">
            <checkbox-component class="adv-search-filters_no-value__checkbox" is-checked="vm.search.fields.currentWorkingPlace.noValue"></checkbox-component>
            <label class="adv-search-filters_no-value__label">
                {{ 'No value specified' | translate }}
            </label>
        </div>
    </div>
</div>
