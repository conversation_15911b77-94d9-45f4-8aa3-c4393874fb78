<table id="mainTable" ng-show="candidateFound" class="table" ng-table="tableParams" template-pagination="custom/pager">
    <thead>
        <tr>
            <th style="width: 40px"></th>
            <th></th>
            <th style="min-width: 200px">
                {{ 'Candidate' | translate }}
            </th>
            <th ng-if="$root.scorecardInSearch() || false">
                {{ 'Scores' | translate }}
            </th>
            <th style="min-width: 150px">
                {{ 'added_by_male' | translate }}
            </th>
            <th style="width: 44%">
                {{ 'The last important action' | translate }}
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        <tr
            class=""
            ng-class="{ checked_candidate: user.added }"
            hglocation-of-one-object="'/candidates/'"
            hgobject="user"
            hgtablecollspan="10"
            highlights
            ng-repeat="user in candidates"
        >
            <td class="checkbox-cell">
                <checkbox-component meta="user" on-change="(pushCandidateToVacancy)" is-checked="user.added"></checkbox-component>
            </td>

            <td ng-context-menu="menuOptions">
                <ng-include src="'partials/candidates/table/short-info-cell.html'"></ng-include>
            </td>

            <td
                ng-click=" pushCandidateToVacancy({target: {checked: !user.added}}, user);"
                class="name"
                data-title="'Name'"
                ng-context-menu="menuOptions"
                sortable="fullName"
            >
                <div class="avatar-with-name">
                    <person-avatar-component
                        user-id="user.localId"
                        avatar-id="user.photo"
                        full-name="$root.getFullNameDueToStyle(user)"
                        link-to="'candidates'"
                    ></person-avatar-component>

                    <div>
                        <a
                            ng-if="(user.firstName || user.middleName || user.lastName) && $root.useAmericanNameStyle"
                            ng-click="tofullinformation($event, user); $event.stopPropagation();"
                            class="candidate-name"
                            ng-class="{ openStatus: user.openStatus === 'Y' }"
                            ng-href="#/candidates/slide/{{ sliderId }}/{{ user.localId }}"
                            ng-middle-click
                        >
                            {{:: user.firstName + ' ' + user.middleName + ' ' + user.lastName}}
                        </a>

                        <a
                            ng-if="(user.firstName || user.middleName || user.lastName) && !$root.useAmericanNameStyle"
                            ng-click="tofullinformation($event,user); $event.stopPropagation();"
                            class="candidate-name"
                            ng-class="{ openStatus: user.openStatus === 'Y' }"
                            ng-href="#/candidates/slide/{{ sliderId }}/{{ user.localId }}"
                            ng-middle-click
                        >
                            {{:: user.lastName + ' ' + user.firstName + ' ' + user.middleName}}
                        </a>

                        <img
                            ng-if="user.isOpenToWork || user.candidateExternalIntegration.isOpenToWork"
                            class="flame-icon"
                            src="/images/sprite/candidate-flame.svg"
                            tooltip-placement="bottom-left"
                            tooltip-class="tooltip-info-black"
                            uib-tooltip="{{ 'This candidate is now looking for a job' | translate }}"
                            tooltip-append-to-body="true"
                            aria-hidden="true"
                        />

                        <br />
                        <span ng-if="user.position" class="candidate-position" bo-html="::user.position" data-title="'Position'" sortable="position"></span>
                    </div>
                </div>
            </td>
            <td ng-if="$root.scorecardInSearch() || false">
                <div class="scorecard">
                    <span class="scorecard__value" ng-hide="user.avgScore === 0">
                        {{ user.avgScore.toString().substring(0, 3) }}
                    </span>
                    <img ng-if="!user.avgScore" class="scorecard__star-icon" src="/images/redesign/svg-icons/star-outline.svg" alt="" />
                    <img
                        ng-if="user.avgScore"
                        class="scorecard__star-icon scorecard__star-icon_green"
                        src="/images/redesign/svg-icons/star-filled.svg"
                        alt=""
                    />
                </div>
            </td>
            <td
                ng-click="pushCandidateToVacancy({target: {checked: !user.added}}, user)"
                class="responsible"
                data-title="'Responsible'"
                ng-context-menu="menuOptions"
                sortable="responsible.lastName"
            >
                <div style="display: flex; justify-content: flex-start; align-items: center">
                    <responsible-person
                        ng-if="!$root.useAmericanNameStyle"
                        style="width: 36px"
                        avatar-id="user.responsible.avatarId"
                        full-name="user.responsible.fullName"
                        user-id="user.responsible.userId"
                    ></responsible-person>
                    <responsible-person
                        ng-if="$root.useAmericanNameStyle"
                        style="width: 36px"
                        avatar-id="user.responsible.avatarId"
                        full-name="user.responsible.fullNameEn"
                        user-id="user.responsible.userId"
                    ></responsible-person>
                    <div style="margin-left: 10px; display: flex; flex-direction: column">
                        <div data-title="'data'">
                            <date-format-base
                                class-name="'date-added'"
                                current-lang="$root.currentLang"
                                date="user.dc"
                                with-hours="false"
                                full-month="false"
                            ></date-format-base>
                        </div>
                        <span style="color: #666" ng-switch="user.source">
                            <span ng-switch-when="add_from_email">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/Mail.svg"
                                    title="{{ 'This candidate was added from resume from email' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_jobcannon">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/job-cannon-logo.png"
                                    title="{{ 'This candidate was added from JobCannon' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_wandify">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px"
                                    src="/images/wandify/wandify-logo.png"
                                    title="{{ 'This candidate was added from Wandify' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_pracuj">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/pracuj_logo.svg"
                                    title="{{ 'This candidate was added from resume from pracuj' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_pracuj">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/pracuj_logo.svg"
                                    title="{{ 'This candidate was updated from resume from pracuj' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_email">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/Mail.svg"
                                    title="{{ 'This candidate was updated from resume from email' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_olx">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/olx_logo.svg"
                                    title="{{ 'This candidate was added from resume from olx' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_olx">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/olx_logo.svg"
                                    title="{{ 'This candidate was updated from resume from olx' | translate }}"
                                />
                            </span>

                            <span ng-switch-when="add_from_linkedin">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/linkedinLogo.svg"
                                    title="{{ 'This candidate was added from resume from linkedin' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_archive">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/archive-logo.svg"
                                    title="{{ 'This candidate was added from resume from archive' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_excel">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/excel-logo.svg"
                                    title="{{ 'This candidate was added from resume from excel' | translate }}"
                                />
                            </span>

                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was added from resume from linkedin' | translate }}"
                                ng-switch-when="add_from_linkedinNew"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/linkedinLogo.svg"
                                    title="{{ 'This candidate was added from resume from linkedin' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_dou">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/dou-logo.svg"
                                    title="{{ 'This candidate was added from resume from dou' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                ng-switch-when="update_from_linkedin"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/linkedinLogo.svg"
                                    title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                ng-switch-when="update_from_linkedinNew"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/linkedinLogo.svg"
                                    title="{{ 'This candidate was updated from resume from linkedin' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_hh">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/hhLogo.svg"
                                    title="{{ 'This candidate was added from resume from headhunter' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_hh">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/hhLogo.svg"
                                    title="{{ 'This candidate was updated from resume from headhunter' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_workua">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/workuaLogo.svg"
                                    title="{{ 'This candidate was added from resume from work.ua' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_djinni">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/djinni-logo.svg"
                                    title="{{ 'This candidate was added from resume from djinni.co' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_workua">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/workuaLogo.svg"
                                    title="{{ 'This candidate was updated from resume from work.ua' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was added from resume from rabota.ua' | translate }}"
                                ng-switch-when="add_from_rabotaua"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/Rabota_ua_logo.svg"
                                    title="{{ 'This candidate was added from resume from rabota.ua' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was added from resume from delucru.md' | translate }}"
                                ng-switch-when="add_from_delucru"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/delucruLogo.png"
                                    title="{{ 'This candidate was added from resume from delucru.md' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was added from resume from delucru.md' | translate }}"
                                ng-switch-when="add_from_rabotamd"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/rabotaMdLogo.png"
                                    title="{{ 'This candidate was added from resume from rabota.md' | translate }}"
                                />
                            </span>
                            <span
                                class="linkedin-logo"
                                title="{{ 'This candidate was updated from resume from rabota.ua' | translate }}"
                                ng-switch-when="update_from_rabotaua"
                            >
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/Rabota_ua_logo.svg"
                                    title="{{ 'This candidate was updated from resume from rabota.ua' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_superjob">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/superJob.svg"
                                    title="{{ 'This candidate was added from resume from super job' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_superjob">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/superJob.svg"
                                    title="{{ 'This candidate was updated from resume from super job' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_cvlv">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/favicon_cvlv.svg"
                                    title="{{ 'This candidate was added from resume from cvlv' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_jobstutby">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobs_tut_by.svg"
                                    title="{{ 'This candidate was added from resume from jobsTutBy' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_jobstutby">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobs_tut_by.svg"
                                    title="{{ 'This candidate was updated from resume from jobsTutBy' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_cvlv">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/favicon_cvlv.svg"
                                    title="{{ 'This candidate was updated from resume from cvlv' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_jobkg">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobkg.svg"
                                    title="{{ 'This candidate was added from resume from jobkg' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_jobkg">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobkg.svg"
                                    title="{{ 'This candidate was updated from resume from jobkg' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_rabotauz">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobkg.svg"
                                    title="{{ 'This candidate was added from resume from rabotauz' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="update_from_rabotauz">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/jobkg.svg"
                                    title="{{ 'This candidate was updated from resume from rabotauz' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_manually">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/manually.svg"
                                    title="{{ 'This candidate was added manually' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_grc">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/grc.svg"
                                    title="{{ 'This candidate was added from resume from GRC' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_recall">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/response.svg"
                                    title="{{ 'This candidate was added from recall' | translate }}"
                                />
                            </span>
                            <span ng-switch-when="add_from_recommendation">
                                <span class="hide-on-mobile">{{ 'from_2' | translate }}</span>
                                <img
                                    class="workuaSmallLogo"
                                    style="width: 20px; height: 20px"
                                    src="/images/sprite/recommend.svg"
                                    title="{{ 'This candidate was added from recall' | translate }}"
                                />
                            </span>
                        </span>
                    </div>
                </div>
            </td>

            <td class="candidate-action">
                <candidate-last-action
                    user="user"
                    to-sent-preview="(toSentPreview)"
                    show-file-preview="(showFilePreview)"
                    to-scorecard="(toScorecard)"
                    translate-func="$root.translate"
                    $root="$root"
                    capitalised-date="'true'"
                ></candidate-last-action>
            </td>

            <td class="delete-candidate-cell">
                <div
                    ng-if="user.status != 'archived' && $root.me.recrutRole !== 'client'"
                    ng-click="deleteCandidate(user);$event.stopPropagation();"
                    class="delete-candidate-cell__icon"
                    title="{{ 'Remove the candidate' | translate }}"
                ></div>
            </td>
        </tr>
    </tbody>
</table>
