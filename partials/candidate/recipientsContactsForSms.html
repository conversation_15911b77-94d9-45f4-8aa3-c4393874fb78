<div class="recipients-component">
    <div ng-show="!vm.isManyCandidates" class="recipients-component-one">
        <span style="color: #55575a">{{ 'alphasms6' | translate }}</span>
        <select-multi-creatable-contacts
            placeholder="'phone'"
            is-phone="true"
            options="$parent.vm.candidatePhoneNumbers"
            path-to-label="'value'"
            path-to-key="'value'"
            on-change="($parent.vm.addCandidatePhoneNumber)"
            selected-values="$parent.vm.selectedCandidatePhoneNumbers"
            position="'fixed'"
        ></select-multi-creatable-contacts>
    </div>
    <div ng-show="vm.isManyCandidates" class="recipients-container">
        <div ng-show="vm.candidatesWithoutPhones > 0" class="item">
            <button-with-icon
                ng-click="vm.removeAllRecipientsWithoutPhones();"
                type="'secondary'"
                text="('Delete all recipients without number' | translate) + '&nbsp;' + '(' + vm.candidatesWithoutPhones + ')'"
                icon-name="'trash'"
            ></button-with-icon>
        </div>
        <div class="item">
            <ul class="candidates-container">
                <span ng-show="vm.candidates.length" class="item-title">{{ 'Candidates' | translate }}</span>
                <li class="candidates-container__item" ng-repeat="candidate in vm.candidates">
                    <div ng-show="vm.editableCandidate.candidateId !== candidate.candidateId || !vm.editableCandidate" class="candidates-container__item__info">
                        <span class="item-text item-name">{{ candidate.fullName }}</span>
                        <span
                            ng-if="!vm.hasCandidateAtLeastPhone(candidate)"
                            class="item-text"
                            ng-class="{ 'red-text': vm.showRemoveAllRecipientsWithoutPhones }"
                        >
                            ({{ 'phone number is not specified' | translate }})
                        </span>
                        <span ng-if="vm.hasCandidateAtLeastPhone(candidate)" class="item-text">({{ vm.getDefaultPhoneValue(candidate) }})</span>
                    </div>
                    <div ng-show="vm.editableCandidate && vm.editableCandidate.candidateId === candidate.candidateId" class="edit-candidate">
                        <div class="edit-candidate-title item" ng-bind="vm.editableCandidate.fullName"></div>
                        <div ng-show="vm.editableCandidate.phoneNumbers.length > 0" class="item">
                            <div class="item-radio" ng-repeat="phone in vm.editableCandidate.phoneNumbers">
                                <radio-component
                                    ng-click="vm.setDefaultPhone(phone.value, phone)"
                                    style="height: 20px"
                                    value="phone.value"
                                    name="stage-emails"
                                    checked="phone.default"
                                    additional-arg="phone"
                                ></radio-component>
                                <span
                                    ng-click="vm.setDefaultPhone(phone.value, phone); $event.preventDefault()"
                                    ng-class="{ checked: vm.defaultEmail === phone.value }"
                                >
                                    {{ phone.value }}
                                </span>
                            </div>
                        </div>
                        <div ng-show="vm.editableCandidate.phoneNumbers.length < 3 && !vm.isAddPhoneForCandidateOpened" class="item">
                            <button-with-icon
                                ng-click="vm.addPhoneForCandidate()"
                                type="'secondary'"
                                text="'alphasms8'  | translate"
                                icon-name="'plus'"
                            ></button-with-icon>
                        </div>

                        <div ng-show="vm.isAddPhoneForCandidateOpened" class="item">
                            <input-component
                                current-lang="$root.currentLang"
                                placeholder="'enter_phone'"
                                value="vm.addedPhone"
                                is-error="vm.phoneError"
                                on-change="(vm.changeCandidatePhone)"
                            ></input-component>
                            <span ng-show="vm.phoneError" class="error">{{ 'Incorrect phone number' | translate }}</span>
                        </div>

                        <div class="item-row">
                            <button-component
                                ng-click="vm.cancelEditingCandidate();vm.clearField('addedPhone')"
                                class="dropdown-width"
                                type="'secondary'"
                                text="'cancel' | translate"
                                current-lang="$root.currentLang"
                            ></button-component>
                            <button-component
                                ng-click="vm.addPhone(true); vm.saveEditableCandidate()"
                                class="dropdown-width"
                                text="'save' | translate"
                                current-lang="$root.currentLang"
                                disabled="vm.isAddPhoneForCandidateOpened && !vm.addedPhone"
                            ></button-component>
                        </div>
                    </div>
                    <div ng-show="vm.editableCandidate.candidateId !== candidate.candidateId || !vm.editableCandidate" class="candidates-container__item-btn">
                        <img
                            ng-click="vm.editCandidate(candidate, $event)"
                            class="control-icon"
                            src="/images/redesign/svg-icons/pencil.svg"
                            alt=""
                            title="{{ 'edit' | translate }}"
                            role="button"
                        />
                        <img
                            ng-click="vm.removeCandidate(candidate)"
                            class="control-icon"
                            src="/images/redesign/svg-icons/close.svg"
                            alt=""
                            title="{{ 'delete' | translate }}"
                            role="button"
                        />
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
