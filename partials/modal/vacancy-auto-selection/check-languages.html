<div class="vacancy-suggestion-fields">
    <div class="modal-header">
        <div ng-click="vm.closeModal()" class="close-btn"></div>
        <h4>{{ 'Fill in the information necessary for the selection of suitable candidates' | translate }}</h4>
    </div>
    <div class="modal-body">
        <form class="required-fields" name="requiredFieldsForm">
            <span class="required-fields__title" translate="effective selection languages"></span>
            <div class="language-item item" ng-class="{ error: vm.languagesError }">
                <p class="language-item-header">{{ 'languages' | translate }}</p>
                <div class="skills-block" ng-repeat="languageField in vm.setLanguages track by $index">
                    <div class="field-block">
                        <p ng-if="$index === 0" class="language-item-title">{{ 'Language' | translate }}</p>
                        <select-single
                            current-lang="$root.currentLang"
                            placeholder="'Select language'"
                            options="vm.languagesOptions"
                            selected-value="languageField.name"
                            on-change="(vm.onChangeLanguage)"
                            is-clearable="false"
                            additional-arg="$index"
                            position="'fixed'"
                        ></select-single>
                    </div>
                    <div class="field-block">
                        <p ng-if="$index === 0" class="language-item-title">{{ 'level_lang' | translate }}</p>
                        <select-single
                            current-lang="$root.currentLang"
                            placeholder="'selectLevel'"
                            translate-options="true"
                            is-clearable="false"
                            selected-value="languageField.level"
                            options="vm.languagesLevel"
                            on-change="(vm.onChangeLanguageLevel)"
                            additional-arg="$index"
                            position="'fixed'"
                        ></select-single>
                    </div>
                    <img
                        ng-click="vm.removeLanguageField($index)"
                        class="skills-block__remove-icon"
                        ng-style="$index === 0 && { 'margin-top': '24px' }"
                        src="/images/redesign/svg-icons/close.svg"
                        alt=""
                    />
                </div>

                <button-with-icon
                    on-click="(vm.addNewLanguageField)"
                    type="'secondary'"
                    text="'add_new_lang' | translate"
                    icon-name="'plus'"
                ></button-with-icon>
            </div>
            <span class="required-fields__note" translate="note that the specified languages"></span>
        </form>
    </div>
    <div class="modal-footer">
        <button-component ng-click="vm.closeModal()" type="'secondary'" text="'cancel_2' | translate"></button-component>
        <button-component ng-click="vm.saveLanguagesFromSuggestion()" text="'Finish_1' | translate"></button-component>
    </div>
</div>
