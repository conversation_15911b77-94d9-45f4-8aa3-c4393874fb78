<div class="candidate-add-in-vacancy">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">
            {{ 'Change the desired position' | translate }}
        </h4>
    </div>
    <div class="modal-body modal-body">
        <div class="item mrgBottom">
            <select-single-async-creatable
                placeholder="'position' | translate"
                fetch-options="(getPositionsOptions)"
                on-change="(onChangePosition)"
                selected-value="newCandidatesDesiredPosition"
                position="'fixed'"
                input-search-threshold="inputSearchThreshold"
                no-options-message="'Enter the desired position of the candidate'"
            ></select-single-async-creatable>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeModal()" type="'secondary'" text="'cancel' | translate "></button-component>
        <button-component ng-click="$parent.candidatesChangeDesiredPosition(newCandidatesDesiredPosition)" text="'save' | translate "></button-component>
    </div>
</div>
