<div class="candidate-add-in-vacancy">
    <div class="modal-header">
        <div ng-click="$root.closeModal()" class="close-btn"></div>
        <h4 class="modal-header-title">{{ 'adding_candidate_to_vacancy' | translate }}</h4>
    </div>
    <div class="modal-body">
        <div class="item">
            <span class="item-title">{{ 'Vacancy' | translate }}</span>
            <select-single-async
                placeholder="'enter job title' | translate"
                fetch-options="(onFetchVacancies)"
                on-change="(onChangeVacancy)"
                selected-value="vacancyToAddCandidate"
                isClearable="false"
                path-to-label="'label'"
                path-to-value="'vacancyId'"
                position="'fixed'"
                input-search-threshold="2"
            ></select-single-async>
        </div>
        <!--        <div ng-show="$root.addCandidateInInterview.addedInVacancy" class="alert alert-danger">-->
        <!--            {{ 'candidate' | translate }}-->
        <!--            <b>{{ $root.addCandidateInInterview.select2Obj.name }}</b>-->
        <!--            {{ 'has already been added to this vacancy and he have status' | translate }}-->
        <!--            <b>{{ $root.addCandidateInInterview.select2Obj.status | translate }}</b>-->
        <!--            <br />-->
        <!--            {{ 'You can transfer him to' | translate }}:-->
        <!--        </div>-->
        <div ng-show="vacancyToAddCandidate.vacancyId" class="item">
            <span class="item-title">{{ 'interview_status' | translate }}</span>
            <select-single
                current-lang="$root.currentLang"
                on-change="(onChangeVacancyStatus)"
                options="$root.VacancyStatusFiltered"
                translate-options="true"
                selected-value="$root.status2"
                path-to-label="'value'"
                path-to-value="'value'"
                is-clearable="false"
                position="'fixed'"
                additional-arg="true"
            ></select-single>
        </div>

        <div
            id="modalDateElement"
            ng-if="
                ($root.addCandidateInVacancy.status.value != 'accept_offer' && $root.addCandidateInVacancy.status.withDate) ||
                $root.addCandidateInVacancy.status.type == 'interview'
            "
            class="item"
        >
            <span class="item-title">{{ 'date_of_interview' | translate }}</span>
            <mdp-date-time-picker
                class="date-field-picker"
                ng-class="{ empty: vm.emptyDateField }"
                mdp-auto-switch="true"
                mdp-format="DD/MM/YYYY HH:mm"
                mdp-open-on-click
                mdp-placeholder="{{ 'Choose date and time' | translate }}"
                ng-change="$root.checkDateTimeCandidate(addCandidateInvacancyPicker, 'addCandidateInvacancyPicker')"
                ng-model="addCandidateInvacancyPicker"
            >
                <img class="date-field-picker__calendar-icon" src="/images/redesign/svg-icons/calendar.svg" alt="" />
            </mdp-date-time-picker>
        </div>

        <div
            ng-show="$root.addCandidateInVacancy.status.value.includes('interview') || $root.addCandidateInVacancy.status.type === 'interview'"
            class="item google-meet"
        >
            <div class="google-meet-checkbox">
                <custom-popover
                    turn-off-hover-state="true"
                    classes="default-hint"
                    placement="'top-left'"
                    popover-html="{{
                        !googleCalendar
                            ? ('withoutGoogleCalendar' | translate)
                            : googleCalendar && !addCandidateInvacancyPicker
                            ? ('Select interview time' | translate)
                            : ''
                    }}"
                >
                    <checkbox-component
                        ng-click="toggleMeetType('googleMeet'); changeTemplateForGoogleMeet(addCandidateInvacancyPicker, candidate, $root.VacancyAddedInCandidate);$event.preventDefault()"
                        ng-class="{ disabled: !addCandidateInvacancyPicker || !googleCalendar }"
                        disabled="!$root.addCandidateInVacancy.date || !googleCalendar"
                        is-checked="$root.googleMeet"
                    ></checkbox-component>
                    <span
                        ng-click="toggleMeetType('googleMeet'); changeTemplateForGoogleMeet(addCandidateInvacancyPicker, candidate, $root.VacancyAddedInCandidate);$event.preventDefault()"
                        ng-class="{ disabled: !addCandidateInvacancyPicker || !googleCalendar }"
                    >
                        {{ 'Create google-meet' | translate }}
                    </span>
                </custom-popover>
                <custom-popover
                    turn-off-hover-state="true"
                    classes="default-hint"
                    placement="'top-left'"
                    popover-html="{{
                        !outlookCalendar
                            ? ('withoutOutlookCalendar' | translate)
                            : outlookCalendar && !addCandidateInvacancyPicker
                            ? ('Select interview time' | translate)
                            : (outlookTooltipText | translate)
                    }}"
                >
                    <checkbox-component
                        ng-click="toggleMeetType('outlookMeet');$event.preventDefault()"
                        ng-class="{ disabled: !addCandidateInvacancyPicker || !outlookCalendar || availableOutlookMeetingType === 'UNKNOWN' }"
                        disabled="!addCandidateInvacancyPicker || !outlookCalendar || availableOutlookMeetingType === 'UNKNOWN'"
                        is-checked="$root.outlookMeet"
                    ></checkbox-component>
                    <span
                        ng-click="toggleMeetType('outlookMeet');$event.preventDefault();"
                        ng-class="{ disabled: !addCandidateInvacancyPicker || !outlookCalendar || availableOutlookMeetingType === 'UNKNOWN' }"
                    >
                        {{ outlookLabelText | translate }}
                    </span>
                </custom-popover>
            </div>
            <div ng-show="$root.googleMeet || $root.outlookMeet" class="google-meet-wrapper">
                <div class="item-title">
                    <span>{{ 'Add guests using the list or manually' | translate }}</span>
                    <custom-popover
                        turn-off-hover-state="true"
                        popover-html="{{ ($root.googleMeet ? 'meet-info' : 'meet-info-outlook') | translate }}"
                        classes="default-hint"
                        placement="'top-left'"
                    >
                        <i class="hint-info-icon__grey"></i>
                    </custom-popover>
                </div>
                <select-multi-creatable-contacts
                    ng-if="$root.googleMeet || $root.outlookMeet"
                    options="meetUsers"
                    placeholder="'Add guests'"
                    selected-values="$root.showMeetusers"
                    on-change="(addToMeetArray)"
                    path-to-value="'key'"
                    path-to-label="'fullName'"
                    path-to-key="'key'"
                    path-to-header-label="'email'"
                    show-select-all="false"
                    no-options-message="'No matches found. Add email'"
                    position="'fixed'"
                ></select-multi-creatable-contacts>
            </div>
            <div ng-show="$root.googleMeet || $root.outlookMeet" class="google-meet-wrapper">
                <div class="item-title">
                    <span id="calendarTitle">
                        {{ 'Title for the calendar' | translate }}
                        <span class="form-title-star">*</span>
                    </span>
                </div>
                <input-component
                    placeholder="'Enter the calendar event title' | translate"
                    value="$root.calendarTitle"
                    is-error="emptyRequiredInputs.includes('calendarTitle')"
                    on-change="($root.onChangeCalendarTitle)"
                ></input-component>
            </div>
            <div ng-show="$root.googleMeet || $root.outlookMeet" class="google-meet-wrapper">
                <div class="item-title">
                    <span>{{ 'Length of the interview' | translate }}</span>
                </div>
                <select-single-virtualized
                    current-lang="$root.currentLang"
                    translate-options="true"
                    is-clearable="false"
                    options="$root.meetTimeValues"
                    path-to-label="'name'"
                    selected-value="$root.meetDuration"
                    on-change="($root.onChangeMeetDuration)"
                ></select-single-virtualized>
            </div>
            <div ng-if="$root.googleMeet || $root.outlookMeet" class="item google-meet-comment">
                <span class="item-title">{{ 'Post for the meeting' | translate }}</span>
                <textarea
                    class="meetComment"
                    ng-model="changeStatusOfInterviewInVacancy.meetComment"
                    placeholder="{{ 'Descr for meeting' | translate }}"
                ></textarea>
            </div>
        </div>
        <div class="item">
            <textarea id="ckEditorCandidateAddVacancy" class="customTextarea" ckeditor options="$root.ckEditorCandidateAddVacancy"></textarea>
            <!--            <span class="item-note">{{ 'Press CTRL+ENTER to send messages' | translate }}</span>-->
        </div>
        <div ng-show="emailAutoActionForStage && !testAutoActionForStage">
            {{ 'For this stage sending a letter is set' | translate }}
            <span ng-if="emailAutoActionForStage.mailTemplate.type">
                {{ emailAutoActionForStage.mailTemplate.type | translate }}
            </span>
            <span ng-if="!emailAutoActionForStage.mailTemplate.type">
                {{ emailAutoActionForStage.mailTemplate.name }}
            </span>
        </div>
        <div ng-show="!emailAutoActionForStage && testAutoActionForStage">
            {{ 'For this stage sending a test is set' | translate }}
            {{ testAutoActionForStage.test.testName | translate }}
        </div>
        <div ng-show="emailAutoActionForStage && testAutoActionForStage">
            <span>{{ 'For this stage sending a letter is set' | translate }} "{{ emailAutoActionForStage.mailTemplate.name | translate }}"</span>
            <span>{{ 'and test' | translate }} "{{ testAutoActionForStage.test.testName | translate }}"</span>
        </div>

        <div ng-show="showNotifyEmailArrowAddToVacancy()" class="item item-actions">
            <div ng-click="openNotifyEmail('addCandidateInVacancyMCE')" class="item-actions-arrow">
                <span>{{ 'Notify the candidate' | translate }}</span>
                <img ng-class="{ arrowDown: $root.candnotify.show }" src="images/redesign/svg-icons/chevron-down.svg" alt="" />
            </div>
            <div ng-show="$root.candnotify.show" class="item-actions-wrapper">
                <div ng-show="$root.me.emails.length === 0 && !noAllowedMails" class="alert-danger">
                    <img src="images/redesign/svg-icons/info.svg" alt="" />
                    <div>
                        <span>{{ 'To send a letter from your mailbox, you need to' | translate }}</span>
                        <a href="#/email-integration" target="_blank">{{ 'add integration with email' | translate }}</a>
                    </div>
                </div>
                <div ng-show="$root.me.emails.length === 0 && noAllowedMails" class="alert-danger">
                    <span translate="To send a letter from your mailbox,allow sending mails from the email integration page"></span>
                </div>
                <div class="item">
                    <div class="item-title">
                        <span>{{ 'Email template' | translate }}</span>
                        <a class="other-btn" href="#/company/email-templates" target="_blank">
                            <img src="/images/redesign/svg-icons/plus.svg" alt="" />
                            <span>{{ 'Create a letter template' | translate }}</span>
                        </a>
                    </div>
                    <select-single-virtualized
                        title="{{ $root.googleMeet && 'You cant set another template for a Google Meet interview' | translate }}"
                        current-lang="$root.currentLang"
                        placeholder="'Choose a letter template'"
                        options="templatesGrouped"
                        selected-value="$root.emailTemplateModel"
                        on-change="($root.changeTemplate)"
                        translate-options="true"
                        is-grouped="true"
                        disabled="$root.googleMeet"
                        is-searchable="true"
                        path-to-value="'label'"
                        path-to-label="'label'"
                        position="'fixed'"
                    ></select-single-virtualized>
                </div>
                <div id="candidateEmail" class="item">
                    <div class="item-title">
                        <span>
                            {{ "Candidate's email" | translate }}
                            <span class="form-title-star">*</span>
                        </span>
                        <span ng-show="$root.me.emails.length == 1" class="send-from">{{ 'Send from' | translate }} {{ $root.me.emails[0].email }}</span>
                    </div>
                    <input-component
                        current-lang="$root.currentLang"
                        placeholder="'enter_email'"
                        value="$root.candnotify.sendMail"
                        on-change="($root.changeCandidateEmail)"
                        is-error="emptyRequiredInputs.includes('candidateEmail')"
                    ></input-component>
                </div>
                <div ng-show="$root.me.emails.length > 1" class="item">
                    <span class="item-title">{{ 'Select email' | translate }}</span>
                    <select-single
                        current-lang="$root.currentLang"
                        on-change="(vm.addEmailFromWhatSend)"
                        options="$root.me.emails"
                        selected-value="vm.selectedEmail"
                        placeholder="'Select email'"
                        path-to-label="'email'"
                        path-to-value="'email'"
                        is-clearable="false"
                        position="'fixed'"
                    ></select-single>
                </div>
                <div class="item">
                    <span class="item-title">{{ 'Letter subject' | translate }}</span>
                    <input-component
                        current-lang="$root.currentLang"
                        placeholder="'Letter subject'"
                        value="$root.emailTemplateInModal.title"
                        on-change="($root.changeLetterSubject)"
                    ></input-component>
                </div>
                <div class="item">
                    <textarea
                        id="addCandidateInVacancyMCE"
                        ng-show="$root.candnotify.show"
                        ckeditor
                        name="modalMCECandidate"
                        ng-model="$root.emailTemplateInModal.text"
                    ></textarea>
                    <!--                    <mail-to-->
                    <!--                        ng-if="$root.me.orgParams.mailto === 'true'"-->
                    <!--                        editor-id="addCandidateInVacancyMCE"-->
                    <!--                        email="$root.candnotify.sendMail"-->
                    <!--                        subject="$root.emailTemplateInModal.title"-->
                    <!--                    ></mail-to>-->
                    <template-file-attach
                        ng-if="$root.addCandidateInVacancy.status.value === 'accept_offer'"
                        files="$root.fileForSave"
                        io-file="$root.optionsTemplate"
                        max-files-count="5"
                        remove-file="removeFileAttach(fileId)"
                    ></template-file-attach>
                    <template-file-attach
                        ng-if="$root.addCandidateInVacancy.status.value !== 'accept_offer'"
                        files="$root.fileForSave"
                        io-file="$root.optionsTemplate"
                        max-files-count="3"
                        remove-file="removeFileAttach(fileId)"
                    ></template-file-attach>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button-component ng-click="$root.closeModal()" type="'secondary'" text="'cancel' | translate"></button-component>
        <button-component ng-click="$root.addVacancyInCandidate()" text="'add' | translate"></button-component>
        <button-component
            ng-if="$root.candnotify.show && !$root.outlookMeet"
            ng-click="$root.me.emails.length > 0 && $root.candnotify.show ? $root.addVacancyInCandidate(true, true) : false"
            text="'Add and notify' | translate"
        ></button-component>
    </div>
</div>
