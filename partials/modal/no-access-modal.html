<div class="no-access" ng-class="{ 'is-sam': isSAMLLoginActivated }">
    <div class="login-modal-container" ng-class="{ hideLoginModal: showResetPassword }">
        <h1 class="modal-title text-center" translate="Log in or refresh the page if you are logged in on a different tab"></h1>
        <form class="no-access-form" method="post">
            <div class="login-fields-wrapper" ng-hide="$root.authWithExternalService">
                <div>
                    <label class="access-label">
                        <span class="access-label-title">{{ 'auth_enter_email' | translate }}</span>
                        <input-component
                            class="login-form-input"
                            placeholder="'Email'"
                            current-lang="$root.currentLang"
                            value="$root.loginForm.login"
                            on-change="(updateLogin)"
                        ></input-component>
                    </label>
                    <p ng-if="$root.errorSignin.type === 'email'" class="mistake" translate="{{ $root.errorSignin.mistake }}"></p>
                </div>

                <div>
                    <label ng-if="!isSAMLLoginActivated" class="access-label">
                        <span class="access-label-title">{{ 'auth_enter_psw' | translate }}</span>
                        <input-component
                            class="login-form-input"
                            type="'password'"
                            placeholder="'password' | translate"
                            current-lang="$root.currentLang"
                            value="$root.loginForm.password"
                            on-change="(updatePassword)"
                        ></input-component>
                    </label>
                    <p ng-if="$root.errorSignin.type === 'password'" class="mistake" translate="{{ $root.errorSignin.mistake }}"></p>
                </div>
            </div>

            <div ng-show="$root.showAuthorizationCodeInput" class="access-container">
                <span class="access-hint">
                    {{
                        'You are asked to ask for a 6-digit login code every time you login to CleverStaff. This is an additional measure to protect your account. Enter the 6-digit code from the app you chose when activating 2-Step Verification (Google Authenticator/FreeOTP Authenticator)'
                            | translate
                    }}
                </span>

                <label ng-if="!isSAMLLoginActivated" class="access-label">
                    <span class="access-label-title">{{ 'authorization_code' | translate }}</span>
                    <input-component
                        class="login-form-input"
                        placeholder="'input_authorization_code' | translate"
                        current-lang="$root.currentLang"
                        value="$root.loginForm.authorizationCode"
                        on-change="(updateAuthorizationCode)"
                    ></input-component>
                </label>
            </div>
            <p
                class="mistake hide"
                ng-class="{
                    show: $root.errorSignin.type === 'incorrect' || $root.errorSignin.type === 'incorrect_code' || $root.errorSignin.type === 'manyAuth'
                }"
                translate="{{ $root.errorSignin.mistake }}"
                translate-values="{time: $root.timeOfBlock}"
            ></p>
            <p id="no-access-server_message" class="mistake"></p>
            <div ng-show="$root.showAuthorizationCodeInput" class="canNotGetCode-container">
                <span ng-click="toogleCanNotGetCode(true)" class="canNotGetCode-button">
                    {{ "Can't get your code?" | translate }}
                </span>
                <div ng-if="showCanNotGetCode" class="canNotGetCode-message-wrapper">
                    <img ng-click="toogleCanNotGetCode(false)" class="canNotGetCode-close" src="images/sprite/deleteComment.svg" />
                    <span
                        class="canNotGetCode-text"
                        ng-bind-html="'If you cannot get a login code, then contact your Account Administrator, he may turn off two-step authentication for you. Or contact CleverStaff support:' | translate"
                    ></span>
                </div>
            </div>
            <div class="modal-submit-wrapper">
                <button-component
                    ng-click="submitHandler()"
                    container="'button'"
                    type="'primary'"
                    text="'login' | translate"
                    size="'medium'"
                ></button-component>
            </div>
        </form>
        <div class="socials-fields-wrapper" ng-hide="$root.authWithExternalService">
            <div class="forgot-password-block-wrapper">
                <span ng-click="toggleResetBlock()" class="forgot-password-title">{{ 'forgot password' | translate }}</span>
            </div>
            <p class="socials socials_header">{{ 'Sign in with' | translate }}:</p>
            <div class="socials">
                <button-with-icon
                    ng-if="!isSAMLLoginActivated"
                    on-click="(onClickGoogle)"
                    icon-name="'btn_google'"
                    colorful-icon="'true'"
                    type="'secondary'"
                    text="'Google'"
                    size="'medium'"
                ></button-with-icon>

                <button-with-icon
                    ng-if="!isSAMLLoginActivated"
                    on-click="(onClickFb)"
                    icon-name="'facebook-logo'"
                    colorful-icon="'true'"
                    type="'secondary'"
                    text="'Facebook'"
                    size="'medium'"
                ></button-with-icon>

                <button-with-icon
                    on-click="(activateSAMLLogin)"
                    icon-name="'saml-lock'"
                    colorful-icon="'true'"
                    type="'secondary'"
                    text="'SAML'"
                    size="'medium'"
                ></button-with-icon>
            </div>
        </div>
    </div>
    <reset-password-component show-reset-password="showResetPassword"></reset-password-component>
</div>
