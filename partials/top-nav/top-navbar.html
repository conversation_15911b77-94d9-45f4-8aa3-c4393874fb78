<consent-banner ng-if="showConsentBanner" root-me="$root.me" translate-func="$root.translate"></consent-banner>

<div ng-if="$root.me" class="top-navbar-wrapper">
    <div class="subtab-sidebar-toggle">
        <div ng-click="onToggleSidebar()" class="icon-base mobile-sidebar-toggle"></div>
    </div>

    <div id="top-navbar-subtabs" ng-if="$root.navName" ng-include="'/partials/top-nav/second-navs-redesign/' + $root.navName + '.html'"></div>

    <div id="right-controls" class="right-block">
        <button-with-icon
            ng-if="['Activity Dashboard', 'Activity', 'Activity Calendar'].includes($root.activePage) && $root.me.recrutRole === 'admin'"
            class="invite-button__button-with-icon"
            icon-name="'user-add'"
            text="'Invite User / Client' | translate"
            on-click="(inviteUserClientModal)"
        ></button-with-icon>

        <icon-button
            ng-if="['Activity Dashboard', 'Activity', 'Activity Calendar'].includes($root.activePage) && $root.me.recrutRole === 'admin'"
            class="invite-button__icon-button hidden_mobile"
            type="'primary'"
            on-click="(inviteUserClientModal)"
            icon-name="'user-add'"
        ></icon-button>

        <ul class="nav information flex-ul right-block_ul">
            <li id="integration-block" class="dropdown integration-block hidden_laptop" uib-dropdown is-open="navDropdownStatuses.puzzle">
                <icon-button
                    ng-click="checkIntegration(); toggleNavDropdown('puzzle', $event)"
                    is-open="navDropdownStatuses.puzzle"
                    icon-name="'pazzle'"
                ></icon-button>
                <ul class="dropdown-hamburger" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                    <li role="menuitem">
                        <a class="dropdown-title" href="#/integration-page" ui-sref-opts="{reload: true}" ui-sref="integration-page">
                            <img src="images/redesign/svg-icons/globe.svg" alt="" />
                            <span>{{ 'Integrations' | translate }}</span>
                        </a>
                        <img
                            ng-class="{ check: workSiteIntegration, closer: !workSiteIntegration }"
                            ng-src="images/redesign/svg-icons/{{ workSiteIntegration ? 'check' : 'close' }}.svg"
                            alt=""
                        />
                    </li>
                    <li role="menuitem">
                        <a class="dropdown-title" href="#/email-integration">
                            <img src="images/redesign/svg-icons/mail.svg" alt="" />
                            <span>{{ 'email integration' | translate }}</span>
                        </a>
                        <img
                            ng-class="{ check: emailIntegration, closer: !emailIntegration }"
                            ng-src="images/redesign/svg-icons/{{ emailIntegration ? 'check' : 'close' }}.svg"
                            alt=""
                        />
                    </li>
                    <li role="menuitem">
                        <a class="dropdown-title" ng-href="{{ getPluginDownloadingLink() }}" target="_blank">
                            <img src="images/redesign/svg-icons/pazzle.svg" alt="" />
                            <span>{{ 'Extension for Chrome' | translate }}</span>
                        </a>
                        <img
                            ng-class="{ check: pluginIntegration, closer: !pluginIntegration }"
                            ng-src="images/redesign/svg-icons/{{ pluginIntegration ? 'check' : 'close' }}.svg"
                            alt=""
                        />
                    </li>
                    <li role="menuitem">
                        <a class="dropdown-title" href="#/organizer/calendar">
                            <img src="images/redesign/svg-icons/calendar.svg" alt="" />
                            <span>{{ 'calendar integration' | translate }}</span>
                        </a>
                        <img
                            ng-class="{ check: calendarIntegration, closer: !calendarIntegration }"
                            ng-src="images/redesign/svg-icons/{{ calendarIntegration ? 'check' : 'close' }}.svg"
                            alt=""
                        />
                    </li>
                </ul>
            </li>
            <li>
                <icon-button ng-click="openNoticeMenu()" icon-name="'bell'" is-open="$root.showNoticeMenu"></icon-button>
                <span ng-if="$root.currentNoticeCount > 0" ng-click="openNoticeMenu()" class="notifications-count-label" ng-bind="$root.newNoticeCount"></span>

                <notices-dir></notices-dir>
            </li>
            <li class="settings">
                <div class="btn-group" uib-dropdown is-open="navDropdownStatuses.gear">
                    <icon-button
                        ng-click="toggleNavDropdown('gear', $event)"
                        is-open="navDropdownStatuses.gear"
                        icon-name="'settings'"
                        disabled="disabled"
                    ></icon-button>
                    <ul
                        class="drodown-setting-menu dropdown-menu"
                        ng-style="
                            $root.me.typeOfClient === 'Top' &&
                            !$root.isAccountTrial &&
                            ($root.me.org.accountManager == '<EMAIL>' ||
                                $root.me.org.accountManager == 'a.kolpakova@cleverstaff' ||
                                $root.me.org.accountManager == '<EMAIL>' ||
                                $root.me.org.accountManager == 'yakovenko@cleverstaff') &&
                            $root.me.recrutRole !== 'client'
                                ? { width: '280px' }
                                : { width: '260px' }
                        "
                        uib-dropdown-menu
                        role="menu"
                        aria-labelledby="single-button"
                    >
                        <li
                            ng-if="
                                $root.me.typeOfClient === 'Top' &&
                                !$root.isAccountTrial &&
                                ($root.me.org.accountManager == '<EMAIL>' || $root.me.org.accountManager == 'a.kolpakova@cleverstaff') &&
                                $root.me.recrutRole !== 'client'
                            "
                            class="account-manager"
                            role="menuitem"
                        >
                            <a ng-click="$event.stopPropagation()" class="account-manager__block">
                                <div class="account-manager__photo">
                                    <img src="/images/sprite/account-manager/kolpakova-photo.png" alt="" />
                                </div>
                                <div class="account-manager__text">
                                    <span class="account-manager__name">{{ 'Anastasiya Kolpakova' | translate }}</span>
                                    <span ng-click="$event.stopPropagation();clickCopy($event)" class="account-manager__email">
                                        <EMAIL>
                                    </span>
                                    <span class="account-manager__descr">{{ 'Your personal account manager' | translate }}</span>
                                    <div class="textCopied" ng-style="showText ? { opacity: '1' } : { opacity: '0' }">{{ 'Text copied' | translate }}</div>
                                </div>
                            </a>
                        </li>

                        <li
                            ng-if="
                                $root.me.typeOfClient === 'Top' &&
                                !$root.isAccountTrial &&
                                ($root.me.org.accountManager == '<EMAIL>' || $root.me.org.accountManager == 'yakovenko@cleverstaff') &&
                                $root.me.recrutRole !== 'client'
                            "
                            class="account-manager"
                            role="menuitem"
                        >
                            <a ng-click="$event.stopPropagation()" class="account-manager__block">
                                <div class="account-manager__photo">
                                    <img src="/images/sprite/account-manager/yakovenko-photo.png" alt="" />
                                </div>
                                <div class="account-manager__text">
                                    <span class="account-manager__name">{{ 'Anastasia Yakovenko' | translate }}</span>
                                    <span ng-click="$event.stopPropagation();clickCopy($event)" class="account-manager__email"><EMAIL></span>
                                    <span class="account-manager__descr">{{ 'Your personal account manager' | translate }}</span>
                                    <div class="textCopied" ng-style="showText ? { opacity: '1' } : { opacity: '0' }">{{ 'Text copied' | translate }}</div>
                                </div>
                            </a>
                        </li>

                        <li role="menuitem">
                            <div ng-click="$event.stopPropagation()" class="dropdown-title">
                                <img src="images/redesign/svg-icons/globe.svg" alt="" />
                                <a ng-click="$event.stopPropagation()" class="lang-wrapper">
                                    <span
                                        ng-click="$event.stopPropagation();changeLanguage('en')"
                                        class="lang_option"
                                        ng-class="{ currentLang: $root.currentLangInMenu === 'en' }"
                                    >
                                        EN
                                    </span>
                                    <span
                                        ng-click="$event.stopPropagation();changeLanguage('ua')"
                                        class="lang_option"
                                        ng-class="{ currentLang: $root.currentLangInMenu === 'ua' }"
                                    >
                                        UA
                                    </span>
                                    <span
                                        ng-click="$event.stopPropagation();changeLanguage('pl')"
                                        class="lang_option"
                                        ng-class="{ currentLang: $root.currentLangInMenu === 'pl' }"
                                    >
                                        PL
                                    </span>
                                    <span
                                        ng-if="showLanguageByOrgParam['ru']"
                                        ng-click="$event.stopPropagation();changeLanguage('ru')"
                                        class="lang_option"
                                        ng-class="{ currentLang: $root.currentLangInMenu === 'ru' }"
                                    >
                                        RU
                                    </span>
                                    <span ng-click="$event.stopPropagation()" class="lang_plug"></span>
                                </a>
                            </div>
                        </li>

                        <li
                            ng-hide="$root.me.recrutRole == 'client' || $root.me.recrutRole == 'freelancer' || $root.me.recrutRole == 'researcher'"
                            role="menuitem"
                        >
                            <a class="dropdown-title" href="#/company/settings">
                                <img src="images/redesign/svg-icons/settings.svg" alt="" />
                                <span>{{ 'Account settings' | translate }}</span>
                            </a>
                        </li>

                        <li
                            ng-show="$root.me.recrutRole === 'client' || $root.me.recrutRole === 'freelancer' || $root.me.recrutRole === 'researcher'"
                            role="menuitem"
                        >
                            <a class="dropdown-title" href="#/company/scorecards">
                                <img src="images/redesign/svg-icons/settings.svg" alt="" />
                                <span>{{ 'Account settings' | translate }}</span>
                            </a>
                        </li>

                        <li class="menuitem hidden_laptop" role="menuitem">
                            <a class="dropdown-title" href="#/settings">
                                <img src="images/redesign/svg-icons/eye.svg" alt="" />
                                <span>{{ 'Account visibility' | translate }}</span>
                            </a>
                        </li>

                        <li class="menuitem hidden_laptop" role="menuitem">
                            <a ng-click="toNotification()" class="dropdown-title">
                                <img src="images/redesign/svg-icons/bell.svg" alt="" />
                                <span>{{ 'Email_notifications' | translate }}</span>
                            </a>
                        </li>

                        <li ng-show="$root.me.personParams.domainAdmin" role="menuitem">
                            <a class="dropdown-title" href="#/cloud-admin">
                                <span class="menuitem-icon"></span>
                                <span>cloud admin</span>
                            </a>
                        </li>

                        <li role="menuitem">
                            <a ng-click="signOut()" class="dropdown-title">
                                <img class="logout-img" src="images/redesign/svg-icons/logout.svg" alt="" />
                                <span class="logout">{{ 'Exit' | translate }}</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="responsive-nav hide">
    <ul class="nav">
        <li>
            <a ui-sref="organizer" ui-sref-opts="{reload:true}" href="#/organizer">
                <i class="sprite sprite-calendar">
                    <svg
                        id="calendar"
                        style="enable-background: new 0 0 20.1 20"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        width="18px"
                        height="18px"
                        viewBox="0 0 20.1 20"
                        xml:space="preserve"
                    >
                        <path
                            class="st0"
                            d="M19.2,0L19.2,0H0.8C0.3,0,0,0.3,0,0.8v18.5C0,19.7,0.3,20,0.8,20h18.5c0.4,0,0.8-0.3,0.8-0.8V0.8
                                        C20,0.3,19.7,0,19.2,0L19.2,0z M18.5,18.5L18.5,18.5h-17V6h16.9v12.5H18.5z M18.5,5.1L18.5,5.1h-17V1.5H3v1.1C3,3,3.3,3.4,3.8,3.4
                                        s0.8-0.3,0.8-0.8V1.5h2.6v1.1C7.2,3,7.5,3.4,8,3.4c0.4,0,0.8-0.3,0.8-0.8V1.5h2.6v1.1c0,0.4,0.3,0.8,0.8,0.8c0.4,0,0.8-0.3,0.8-0.8
                                        V1.5h2.6v1.1c0,0.4,0.3,0.8,0.8,0.8c0.4,0,0.8-0.3,0.8-0.8V1.5h1.5v3.6H18.5z"
                        />
                        <path
                            class="st1"
                            d="M7.3,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9c0-0.4-0.1-0.7-0.4-0.9l0,0
                                        C8.9,7.6,8.6,7.5,8.2,7.5c-0.4,0-0.7,0.1-0.9,0.4l0,0C7.1,8.1,6.9,8.4,6.9,8.8C6.9,9.1,7.1,9.4,7.3,9.7L7.3,9.7L7.3,9.7z"
                        />
                        <path
                            class="st1"
                            d="M8,8.5L8,8.5L8,8.5c0-0.1,0.1-0.1,0.2-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        c-0.1,0-0.2,0-0.3,0C8.1,9.1,8,9.1,8,9C7.9,9,7.8,8.9,7.8,8.8C7.8,8.7,7.9,8.6,8,8.5L8,8.5z"
                        />
                        <path
                            class="st1"
                            d="M7.3,13.2L7.3,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0C8.9,11.2,8.5,11,8.2,11c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C6.9,12.6,7.1,12.9,7.3,13.2L7.3,13.2L7.3,13.2z"
                        />
                        <path
                            class="st1"
                            d="M8,12L8,12L8,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3l0,0
                                        c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C7.8,12.1,7.9,12,8,12L8,12z"
                        />
                        <path
                            class="st1"
                            d="M14.4,9.7L14.4,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4s-0.7,0.1-0.9,0.4l0,0C14.2,8.1,14,8.4,14,8.8C14,9.1,14.2,9.4,14.4,9.7
                                        L14.4,9.7L14.4,9.7z"
                        />
                        <path
                            class="st1"
                            d="M15.1,8.5L15.1,8.5L15.1,8.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        s-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1s-0.2-0.2-0.2-0.3C14.9,8.7,15,8.6,15.1,8.5L15.1,8.5z"
                        />
                        <path
                            class="st1"
                            d="M10.9,9.7L10.9,9.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9S10.6,9.4,10.9,9.7
                                        L10.9,9.7L10.9,9.7z"
                        />
                        <path
                            class="st1"
                            d="M11.5,8.5L11.5,8.5L11.5,8.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        s-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1s-0.1-0.2-0.1-0.3S11.4,8.6,11.5,8.5L11.5,8.5z"
                        />
                        <path
                            class="st1"
                            d="M7.3,16.7L7.3,16.7C7.5,16.9,7.9,17,8.2,17c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C6.9,16.1,7.1,16.4,7.3,16.7L7.3,16.7L7.3,16.7z"
                        />
                        <path
                            class="st1"
                            d="M8,15.5L8,15.5L8,15.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3l0,0
                                        c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C7.8,15.6,7.9,15.5,8,15.5L8,15.5z"
                        />
                        <path
                            class="st1"
                            d="M10.9,16.7L10.9,16.7c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9S10.6,16.4,10.9,16.7
                                        L10.9,16.7L10.9,16.7z"
                        />
                        <path
                            class="st1"
                            d="M11.5,15.5L11.5,15.5L11.5,15.5c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3
                                        s0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C11.4,15.6,11.4,15.5,11.5,15.5L11.5,15.5
                                        z"
                        />
                        <path
                            class="st1"
                            d="M3.8,13.2L3.8,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.3,0.4-0.6,0.4-1s-0.1-0.7-0.4-0.9
                                        l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9C3.4,12.6,3.5,12.9,3.8,13.2L3.8,13.2L3.8,13.2z
                                        "
                        />
                        <path
                            class="st1"
                            d="M4.4,12L4.4,12L4.4,12c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0,0,0.1,0.1,0.1,0.2
                                        c0,0.1,0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C4.3,12.1,4.3,12,4.4,12
                                        L4.4,12z"
                        />
                        <path
                            class="st1"
                            d="M3.8,16.7L3.8,16.7C4,16.9,4.3,17,4.7,17c0.4,0,0.7-0.1,0.9-0.4l0,0C5.8,16.4,6,16.1,6,15.7
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C3.4,16.1,3.5,16.4,3.8,16.7L3.8,16.7L3.8,16.7z"
                        />
                        <path
                            class="st1"
                            d="M4.4,15.5L4.4,15.5L4.4,15.5c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3
                                        s0,0.2-0.1,0.3l0,0c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C4.3,15.6,4.3,15.5,4.4,15.5
                                        L4.4,15.5z"
                        />
                        <path
                            class="st1"
                            d="M10.9,13.2L10.9,13.2c0.2,0.2,0.6,0.4,0.9,0.4c0.4,0,0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0c-0.2-0.2-0.6-0.4-0.9-0.4c-0.4,0-0.7,0.1-0.9,0.4l0,0c-0.2,0.2-0.4,0.6-0.4,0.9
                                        C10.5,12.6,10.6,12.9,10.9,13.2L10.9,13.2L10.9,13.2z"
                        />
                        <path
                            class="st1"
                            d="M11.5,12L11.5,12L11.5,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0c-0.1-0.1-0.1-0.2-0.1-0.3C11.4,12.1,11.4,12,11.5,12L11.5,12z"
                        />
                        <path
                            class="st1"
                            d="M14.4,13.2L14.4,13.2c0.2,0.2,0.6,0.4,0.9,0.4s0.7-0.1,0.9-0.4l0,0c0.2-0.2,0.4-0.6,0.4-0.9
                                        c0-0.4-0.1-0.7-0.4-0.9l0,0C16,11.2,15.6,11,15.3,11s-0.7,0.1-0.9,0.4l0,0C14.2,11.6,14,12,14,12.3S14.2,12.9,14.4,13.2L14.4,13.2
                                        L14.4,13.2z"
                        />
                        <path
                            class="st1"
                            d="M15.1,12L15.1,12L15.1,12c0.1-0.1,0.2-0.1,0.3-0.1s0.2,0,0.3,0.1l0,0c0.1,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
                                        l0,0c-0.1,0.1-0.2,0.1-0.3,0.1s-0.2,0-0.3-0.1l0,0C15,12.5,15,12.4,15,12.3C14.9,12.1,15,12,15.1,12L15.1,12z"
                        />
                    </svg>
                </i>
                <span translate="activity"></span>
            </a>
        </li>
        <li>
            <a ng-click="$root.navClickHandler('sideNavBar')" ui-sref="vacancies" ui-sref-opts="{reload:true}" href="#/vacancies">
                <i class="sprite sprite-vacancy">
                    <svg
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        height="22px"
                        width="24px"
                        viewBox="0 0 25.9 20"
                        xml:space="preserve"
                    >
                        <path
                            class="st0"
                            d="M24.7,0H1C0.4,0,0,0.5,0,1v18c0,0.5,0.4,1,1,1h3.2c0.5,0,0.9-0.4,0.9-1v-1.6h3.2V19c0,0.5,0.5,1,1,1h7.2
                                        c0.5,0,0.8-0.5,0.8-1.1v-1.5h3.3v1.7c0,0.5,0.6,0.9,1.1,0.9h3.2c0.5,0,1-0.5,1-1.1V1C25.7,0.5,25.2,0,24.7,0z M3.9,16.5v2.3H1.3V1.2
                                        h23.1v17.5h-2.7v-2.3h-5.5l0,0l0,0v2.4H9.5v-2.3l0,0"
                        />
                        <path
                            class="st1"
                            d="M14.7,11.8h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,11.5,14.4,11.8,14.7,11.8L14.7,11.8z"
                        />
                        <path
                            class="st1"
                            d="M14.7,8.7L14.7,8.7h6.1c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,8.4,14.4,8.7,14.7,8.7L14.7,8.7z"
                        />
                        <path
                            class="st1"
                            d="M14.7,5.6L14.7,5.6h6.1c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6h-6.1c-0.3,0-0.6,0.3-0.6,0.6
                                        C14.1,5.3,14.4,5.6,14.7,5.6L14.7,5.6z"
                        />
                        <path
                            class="st1"
                            d="M4.9,5.6L4.9,5.6H11c0.3,0,0.6-0.3,0.6-0.6c0-0.3-0.3-0.6-0.6-0.6H4.9C4.6,4.4,4.3,4.7,4.3,5S4.6,5.6,4.9,5.6
                                        L4.9,5.6z"
                        />
                        <path
                            class="st1"
                            d="M4.9,11.8L4.9,11.8H11c0.3,0,0.6-0.3,0.6-0.6s-0.3-0.6-0.6-0.6H4.9c-0.3,0-0.6,0.3-0.6,0.6S4.6,11.8,4.9,11.8
                                        L4.9,11.8z"
                        />
                        <path
                            class="st1"
                            d="M4.3,5.1L4.3,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6c0.3,0,0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C4.6,4.5,4.3,4.8,4.3,5.1L4.3,5.1z"
                        />
                        <path
                            class="st1"
                            d="M10.4,5.1L10.4,5.1v6.1c0,0.3,0.3,0.6,0.6,0.6s0.6-0.3,0.6-0.6V5c0-0.3-0.3-0.6-0.6-0.6
                                        C10.7,4.5,10.4,4.8,10.4,5.1L10.4,5.1z"
                        />
                    </svg>
                </i>
                <span translate="vacancies"></span>
            </a>
        </li>
        <li>
            <a ng-click="$root.navClickHandler('sideNavBar')" ui-sref="candidates" ui-sref-opts="{reload:true}" href="#/candidates">
                <i class="sprite sprite-candidates">
                    <svg
                        style="enable-background: new 0 0 24.4 20.079"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        width="22px"
                        height="20px"
                        viewBox="0 0 24.4 20.079"
                        xml:space="preserve"
                    >
                        <path
                            class="st0"
                            d="M24.1,14.2c-0.2-0.6-0.6-1.1-1-1.5l-1-1c0.7-0.8,1.2-1.8,1.2-2.9c0-1.2-0.5-2.3-1.3-3.1s-1.9-1.3-3.1-1.3
                                            c-0.4,0-0.9,0.1-1.3,0.2C17.2,2,14.9,0,12.2,0s-5,2-5.4,4.6C6.4,4.5,6,4.4,5.6,4.4c-1.2,0-2.3,0.5-3.1,1.3c-0.8,0.7-1.3,1.8-1.3,3
                                            c0,1.1,0.4,2.2,1.2,2.9l-1,1c-0.5,0.5-0.8,1-1,1.5l0,0C0.1,14.8,0,15.4,0,16v1.3c0,0.3,0.1,0.7,0.3,1s0.5,0.5,0.9,0.6h0.1h1v0.4
                                            c0,0.4,0.4,0.7,0.8,0.7h18.2c0.4,0,0.8-0.3,0.8-0.7v-0.4h1h0.1c0.4-0.1,0.7-0.3,0.9-0.6c0.2-0.3,0.3-0.6,0.3-1V16
                                            C24.4,15.4,24.3,14.8,24.1,14.2z M22.9,17.3h-1.4v-1.4c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v2.5h-2.8c0.2-0.1,0.2-0.4,0.4-0.5
                                            c-0.8,0.4-1.3,0.5-1.8-0.2c-0.3,0-0.6-0.1-0.9-0.2c1.1,0.5-2.4,2.2-3,2.2c-1.9,0-2.9-1.5-4.6-1.7c-0.5,0.3-0.7,0.4-1.3,0.2
                                            c0.1,0,0.1,0.1,0.2,0.2H3.9v-2.5c0-0.3-0.2-0.5-0.5-0.5s-0.5,0.2-0.5,0.5v1.4H1.6V16c0-0.4,0.1-0.8,0.2-1.2l0,0l0,0
                                            c0.2-0.4,0.4-0.7,0.7-1l1.2-1.2C4.2,12.9,4.8,13,5.5,13v0.1c0.4-0.3,0.8-0.4,1.3-0.4C6.9,12.5,7,12.2,7.1,12
                                            c0.2-0.3,0.5-0.5,0.8-0.8c-0.4,0.1-0.8,0.1-1.3-0.1c0,0.1-0.1,0.1-0.1,0.1c-0.1,0-0.2,0.1-0.3,0.1l0,0c-0.2,0-0.3,0-0.5,0
                                            c-0.8,0-1.4-0.3-1.9-0.8l0,0C3.3,10,3,9.3,3,8.6c0-0.8,0.3-1.4,0.8-1.9C4.1,6.3,4.8,6,5.6,6c0.3,0,0.6,0,0.8,0.1
                                            c0.2,0,0.3,0.1,0.5,0.2l0,0c0.4,0,0.8,0.1,1.2,0.3C7.9,5.7,7.9,4.8,7.9,3.9c0-1.2,0.9-1.8,1.8-2c0.6-0.7,1.4-1,2.2-0.9
                                            c0.4-0.1,0.9-0.1,1.4,0.1c2,0.9,3.3,2,3.4,4.4c0,0.2,0,0.4,0,0.6c0.3,0.1,0.6,0.2,0.8,0.4l0,0l0,0l0,0l0,0V6.4
                                            c0.1-0.1,0.3-0.1,0.5-0.2C18.3,6,18.6,6,18.9,6c0.8,0,1.4,0.3,1.9,0.8s0.8,1.2,0.8,1.9c0,0.8-0.3,1.4-0.8,1.9l0,0
                                            c-0.5,0.5-1.2,0.8-1.9,0.8c-0.2,0-0.4,0-0.5,0l0,0c-0.1,0-0.2,0-0.3-0.1c0-0.1-0.1-0.1-0.1-0.2c-0.4,0.2-0.8,0.3-1.2,0.3
                                            c0.3,0.4,0.5,0.9,0.8,1.4c0.6-0.1,1.1,0,1.6,0.4c0-0.1,0-0.1,0-0.2c0.6,0,1.2-0.2,1.8-0.4l1.2,1.2c0.3,0.3,0.5,0.7,0.7,1l0,0l0,0
                                            c0.2,0.4,0.2,0.8,0.2,1.2v1.3H22.9z"
                        />
                        <path
                            class="st1"
                            d="M12.2,0c3,0,5.4,2.4,5.4,5.4c0,1.5-0.6,2.9-1.6,3.8l0,0l0,0l0,0l0,0c1.1,1,2.3,2,2.8,3.5l0,0
                                            c0.3,0.7,0.4,1.5,0.4,2.3v1.7c0,0.4-0.1,0.8-0.4,1.1c-0.2,0.3-0.6,0.6-1,0.6h-0.1h-1.6v0.7c0,0.4-0.4,0.9-0.8,0.9l0,0H9
                                            c-0.4,0-0.8-0.5-0.8-0.9l0,0v-0.7H6.6c-0.1,0-0.1,0-0.2,0c-0.4-0.1-0.8-0.3-1-0.6C5.2,17.5,5,17.1,5,16.7V15c0-0.8,0.1-1.6,0.4-2.3
                                            c0.6-1.4,1.8-2.4,2.8-3.5l0,0l0,0c-1-1-1.6-2.4-1.6-3.8C6.8,2.4,9.2,0,12.2,0L12.2,0z M8.8,16.9L8.8,16.9v-2.1
                                            c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.8v0.1v0.7h4.9v-0.7l0,0v-0.1v-2.8c0-0.3,0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5v2.1h2
                                            c0,0,0-0.1,0-0.2V15c0-0.6-0.1-1.2-0.3-1.7l0,0l0,0c-0.5-1.2-1.8-2.2-2.7-3.1c-0.8,0.4-1.6,0.6-2.5,0.6s-1.8-0.2-2.5-0.6
                                            c-0.9,0.9-2.2,1.9-2.7,3.1c-0.2,0.5-0.3,1.1-0.3,1.7v1.7c0,0.1,0,0.1,0,0.2L8.8,16.9L8.8,16.9z M12.2,1.6L12.2,1.6
                                            c-2.1,0-3.8,1.7-3.8,3.9c0,2.1,1.7,3.8,3.8,3.8S16,7.6,16,5.5C16.1,3.3,14.3,1.6,12.2,1.6L12.2,1.6z"
                        />
                    </svg>
                </i>
                <span translate="candidates"></span>
            </a>
        </li>
        <li class="dropdown reports" ng-hide="$root.me.recrutRole === 'researcher'">
            <a
                ng-class="{
                    active:
                        $root.activePage == 'Pipeline' ||
                        $root.activePage == 'Statistics client' ||
                        $root.activePage == 'Report all' ||
                        $root.activePage == 'Vacancy report' ||
                        $root.activePage == 'Reports' ||
                        $root.activePage == 'Reports constructor' ||
                        $root.activePage == 'Custom Reports' ||
                        $root.activePage == 'Edit Reports'
                }"
                ui-sref="reports"
                ui-sref-opts="{reload:true}"
                href="#/reports"
            >
                <i class="sprite sprite-reports">
                    <svg
                        style="enable-background: new 0 0 20 20"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        width="21px"
                        height="21px"
                        viewBox="0 0 20 20"
                        xml:space="preserve"
                    >
                        <path
                            class="st0"
                            d="M19.5,19H1V0.5C1,0.2,0.7,0,0.5,0C0.2,0,0,0.2,0,0.5v19l0,0C0,19.8,0.2,20,0.5,20l0,0h19c0.3,0,0.5-0.2,0.5-0.5
                                        C20,19.3,19.8,19,19.5,19L19.5,19z"
                        />
                        <path
                            class="st0"
                            d="M9.7,10.8H7.5C7.2,10.8,7,11,7,11.3v6c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5v-6
                                        C10.2,11,10,10.8,9.7,10.8z M9.3,16.8H8v-5h1.3V16.8z"
                        />
                        <path
                            class="st0"
                            d="M19.1,13.8h-2.2c-0.3,0-0.5,0.2-0.5,0.5v3c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5v-3
                                        C19.5,14,19.3,13.8,19.1,13.8z M18.6,16.8h-1.3v-2h1.3V16.8z"
                        />
                        <path
                            class="st1"
                            d="M5,2.3H2.7c-0.3,0-0.5,0.2-0.5,0.5v14.5c0,0.3,0.2,0.5,0.5,0.5H5c0.3,0,0.5-0.2,0.5-0.5V2.8
                                        C5.5,2.5,5.2,2.3,5,2.3z M4.5,16.8H3.2V3.2h1.3V16.8z"
                        />
                        <path
                            class="st1"
                            d="M14.5,4.9h-2.2c-0.3,0-0.5,0.2-0.5,0.5v11.8c0,0.3,0.2,0.5,0.5,0.5h2.2c0.3,0,0.5-0.2,0.5-0.5V5.4
                                        C15,5.1,14.8,4.9,14.5,4.9z M14,16.8h-1.3V5.9H14V16.8z"
                        />
                    </svg>
                </i>
                <span translate="Reports"></span>
            </a>
        </li>
        <li>
            <a ng-click="$root.navClickHandler('sideNavBar')" href="#/clients">
                <i class="sprite sprite-reports">
                    <svg width="21" height="21" viewBox="0 0 29 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M13.5 24C15 24.0795 12.8324 22.8297 12.5 21.5C12.1676 20.1703 12 18.5 13.2806 16.3741C13.2806 16.3741 14.6157 14.5119 14.5 14.5C14.3843 14.4881 13.4816 14.5 13 14.5C8.98173 14.5 1 17.0663 1 21.0696V24.0795C1 24.0795 12 23.9204 13.5 24Z"
                            stroke="#5EBB52"
                            stroke-width="2"
                        />
                        <path
                            d="M21 7.5C21 11.0915 18.0915 14 14.5 14C10.9085 14 8 11.0915 8 7.5C8 3.90853 10.9085 1 14.5 1C18.0915 1 21 3.90853 21 7.5Z"
                            stroke="#5EBB52"
                            stroke-width="2"
                        />
                        <path
                            d="M17 19.5L19.7946 22.2946C19.9091 22.4091 20.0938 22.4121 20.2119 22.3013L28 15"
                            stroke="#F8991E"
                            stroke-width="1.5"
                            stroke-linecap="round"
                        />
                    </svg>
                </i>
                <span translate="Clients"></span>
            </a>
        </li>
        <li>
            <a href="#/company/users">
                <i class="sprite sprite-reports">
                    <svg width="29" height="20" viewBox="0 0 29 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M21 12C23.21 12 25 10.21 25 8C25 5.79 23.21 4 21 4C18.79 4 17 5.79 17 8C17 10.21 18.79 12 21 12Z"
                            stroke="#F8991E"
                            stroke-width="1.5"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M17.9775 19.1694H27.0746C27.9978 19.1694 28.75 18.4171 28.75 17.494V16.5686C28.75 15.6777 28.2978 14.9529 27.6994 14.4006C27.106 13.853 26.3206 13.4256 25.5038 13.0984C23.8715 12.4446 21.9259 12.1169 20.5968 12.1169C19.2676 12.1169 17.322 12.4446 15.6897 13.0984C15.6857 13.1001 15.6816 13.1017 15.6776 13.1033L17 13.5L17.1182 14.1856C18.356 13.8071 19.6761 13.6169 20.5968 13.6169C21.7384 13.6169 23.4944 13.9094 24.946 14.4909C25.6712 14.7814 26.2733 15.1256 26.682 15.5028C27.0856 15.8754 27.25 16.2286 27.25 16.5686V17.494C27.25 17.5887 27.1693 17.6694 27.0746 17.6694H17.7188L17.9775 19.1694Z"
                            fill="#F8991E"
                        />
                        <path
                            d="M9.70969 11.8871C12.7173 11.8871 15.1532 9.45111 15.1532 6.44355C15.1532 3.43599 12.7173 1 9.70969 1C6.70213 1 4.26614 3.43599 4.26614 6.44355C4.26614 9.45111 6.70213 11.8871 9.70969 11.8871Z"
                            stroke="#5EBB52"
                            stroke-width="2"
                        />
                        <path
                            d="M9.70968 11.8871C6.80282 11.8871 1 13.346 1 16.2419V17.3306C1 17.9294 1.48992 18.4193 2.08871 18.4193H17.3306C17.9294 18.4193 18.4194 17.9294 18.4194 17.3306V16.2419C18.4194 13.346 12.6165 11.8871 9.70968 11.8871Z"
                            stroke="#5EBB52"
                            stroke-width="2"
                        />
                    </svg>
                </i>
                <span translate="Users"></span>
            </a>
        </li>
        <li ng-show="$root.me.personParams.enableEmployee == 'Y' && $root.me.orgParams.enableEmployee == 'Y'">
            <a ng-click="$root.changeEmployeesFlag()" href="#/company/employees">
                <i class="sprite sprite-reports">
                    <svg width="21" height="21" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M12.6008 17.5C12.1401 17.5 11.8513 17.9977 12.0799 18.3977L12.7053 19.4923C12.7808 19.6244 12.8701 19.7458 12.9708 19.8551C12.8868 19.9547 12.8213 20.0718 12.7807 20.2017L10.5 27.5H18.5L16.2193 20.2017C16.1781 20.0699 16.1113 19.9513 16.0255 19.8508C16.1209 19.7467 16.2059 19.6317 16.2787 19.5069L16.9224 18.4021C17.1555 18.0021 16.8669 17.5 16.404 17.5H12.6008Z"
                            fill="#F8991E"
                        />
                        <circle cx="14.5" cy="8" r="6.5" stroke="#5EBB52" stroke-width="2" />
                        <path
                            d="M9 17.5L6.25579 17.843C3.25322 18.2183 1 20.7715 1 23.7974C1 24.4204 1 25.0087 1 25.502C1 26.0542 1.44772 26.5 2 26.5H26.9998C27.5521 26.5 27.9998 26.0523 27.9998 25.5V23.7967C27.9998 20.7707 25.7466 18.2183 22.744 17.843L20 17.5"
                            stroke="#5EBB52"
                            stroke-width="2"
                            stroke-linecap="round"
                        />
                    </svg>
                </i>
                <span translate="employees"></span>
            </a>
        </li>
        <li>
            <a ng-href="#/users/{{ $root.me.userId }}">
                <i class="sprite sprite-reports">
                    <svg width="21" height="21" viewBox="0 0 29 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14.5 14.875C9.99437 14.875 1 17.1362 1 21.625V23.3125C1 24.2406 1.75937 25 2.6875 25H26.3125C27.2406 25 28 24.2406 28 23.3125V21.625C28 17.1362 19.0056 14.875 14.5 14.875Z"
                            stroke="#5EBB52"
                            stroke-width="2"
                        />
                        <path
                            d="M21.25 7.5C21.25 11.2295 18.2295 14.25 14.5 14.25C10.7705 14.25 7.75 11.2295 7.75 7.5C7.75 3.77046 10.7705 0.75 14.5 0.75C18.2295 0.75 21.25 3.77046 21.25 7.5Z"
                            stroke="#F8991E"
                            stroke-width="1.5"
                        />
                    </svg>
                </i>
                <span translate="My Account"></span>
            </a>
        </li>
        <li class="hideGamification">
            <a
                ng-if="$root.me.orgParams.gamificationEnabled == 'true' || $root.me.orgParams.gamificationEnabled === undefined"
                style="position: relative; left: -3px"
                ui-sref-opts="{reload:true}"
                href="#/achievements/results"
            >
                <i class="sprite sprite-reports">
                    <svg
                        style="position: relative; left: -1px"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="25"
                        height="22"
                        viewBox="0 0 29 35"
                    >
                        <metadata>
                            <?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
                            <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c138 79.159824, 2016/09/14-01:09:01">
                                <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
                                    <rdf:Description rdf:about="" />
                                </rdf:RDF>
                            </x:xmpmeta>
                            <?xpacket end="w"?>
                        </metadata>
                        <image
                            id="Vector_Smart_Object_copy_42"
                            data-name="Vector Smart Object copy 42"
                            width="35"
                            height="40"
                            xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAjCAMAAABfPfHgAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAACLlBMVEUAAADxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmznxmzlvxmHXpEHao0BvxmHqnTvxmznpnjtvxmFvxmHxmznxmznxmzlvxmFvxmGhtVHxmznxmzmmtFBvxmFvxmFvxmHhoD7snTvxmznsnTrioD5vxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmGhtVHxmzmitVFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmFvxmHxmznQpkPRpkPGqUbIqEZvxmGYuVTwmzmdt1MAAACOU9qJAAAAsHRSTlMAEScSU/3+VTtiNQEc7efyIjQ8T+Lg2tHfV6rjLCrx5K++WBZabx0bblsYUsBQyIT1Pjf3ggYDd91EAsKtZM5wCWZsz2dt2wTTchm7e/Rl+BWZa+pD7/qAftgF1mMHCMtqqMfGrIE2hQzx8o5dWVU0/S4o6wTPy9TVzXfzHiH55voiI/Zue3xvvNkvOTEm+Ih8ibteCQHAxQLsWGP5KgU2KR0QjMTvgdcHPD2YmSLmDxaVMy4AAAABYktHRACIBR1IAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAB3RJTUUH5QQPFB0SMESA/wAAAiRJREFUOMt1kmdDE0EQhicgGAiIIFgSgiWiqBQ1AnKKiogo9q6I2BVFBTVYCDYwKhCw0DuEagFfy4g/z83mEu4i7Ie7Z+aZvZ3bXSLtMISE0pxjXhjC588mjBGRpqhoLEDMQlNsRJxeLgIQn4DFhiVYGi94mc6aYQkDohPJmgSEL8cKnV2JVbbVyWsErU1Zt34DUnU2FWmaKA3pmigkYyM2aeLNsG9JVDnTLNpIz5KcvTVHPJVtIrM9U2ZygR07dwnI85blyAm784E90hZgb6H3nQf4LVHhPuyXUBSDDGkPZJsD9iAORfnoMI4c9dExvw2147jalnICJ4OsEacU8s89bZtzrlj3DAVZOutftwDniv+zWefVnkuA/JQLOluafBG4JFPFl8VvXlE0VrkqMtdK1Q9ar6v7rNobsN+0zmx72a2gM7pdpgnLf+CO7a483yJxvvfws3xGVlT+kncj6T49eCjuhgW/Kyv80lHFj0QblgQ8Njzx3aunXOVQbTU7a4y5kcqz53iBl69MsSVxNU6u9snaOn4twfVnGtN/30h+y3W1Et5xfUOjm6ipmd9/+MjNTUTuxoZ6/uSVLewdzta2du7opM4Obm9rdcpci2ipSxJ393Bvnyju6+Webl+qy0H9A4NDHhrmkVEekwuN8egID5NnaHCgX23bLUrHJyROjAt26247idRnFb+IQr0kF38N8Dd2BdnJKU+APVOTQZa+z8b/AJW13oc8i/aDAAAAAElFTkSuQmCC"
                        />
                    </svg>
                </i>
                <span style="position: relative; left: -1px" translate="Achievements"></span>
            </a>
        </li>
    </ul>
</div>
<payments-notifications></payments-notifications>
<new-privacy-shield-notification></new-privacy-shield-notification>
