<div class="controller-view">
    <div class="block-vacancy-reports">
        <div class="breadcrumbs">
            <ol class="breadcrumb">
                <li ng-repeat="breadcrumb in breadcrumbs">
                    <a ng-if="!$last" href="{{ breadcrumb.href }}" translate="{{ breadcrumb.text }}"></a>
                    <span ng-if="$last" translate="{{ breadcrumb.text }}"></span>
                </li>
            </ol>
        </div>
        <h3 class="main-page-title-report">
            <span class="main-page-title-report__first">
                {{ 'Vacancy report' | translate }}
            </span>
            <span class="main-page-title-report__second">
                <a class="line-height-21" href="#/vacancies/{{ vacancy.localId }}">{{ vacancy.position | limitToEllipse : 300 }}</a>
                <span ng-show="vacancy.clientId.name && $root.me.personParams.clientAccessLevel !== 'hide'" class="client">({{ vacancy.clientId.name }})</span>
            </span>
        </h3>
        <div class="row">
            <div class="col-xs-12 responsible" style="padding-top: 0px">
                <h5>{{ 'Responsible' | translate }}:</h5>
                <!-- prettier-ignore -->
                <div class="resp-preson" ng-repeat="respPerson in vacancy.responsiblesPerson">
                    <a ng-if="$root.useAmericanNameStyle" href="#/users/{{ respPerson.responsible.userId }}">
                        {{ respPerson.responsible.fullNameEn }}<span ng-hide="$last">,&nbsp</span>
                    </a>
                    <a ng-if="!$root.useAmericanNameStyle" href="#/users/{{ respPerson.responsible.userId }}">
                        {{ respPerson.responsible.fullName }}<span ng-hide="$last">,&nbsp</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12 report-info">
                <div class="clearfix"></div>
                <div class="pull-left dateFrom">
                    <div class="field pull-left">
                        <div class="pull-left form-title" style="position: relative; left: 10px">
                            {{ 'Date from' | translate }}
                        </div>
                        <mdp-date-picker
                            class="mdp-date-picker"
                            mdp-format="DD/MM/YYYY"
                            mdp-max-date="maxDate"
                            mdp-min-date="minDate"
                            mdp-open-on-click
                            mdp-placeholder="{{ dateFrom | dateFormat3 }}"
                            ng-change="checkDateTime(dateFrom, 'dateFrom')"
                            ng-model="dateFrom"
                        ></mdp-date-picker>
                        <div class="clearfix"></div>
                    </div>
                    <div class="field pull-left" style="margin-left: 10px">
                        <div class="pull-left form-title" style="position: relative; left: 10px">
                            {{ 'date to' | translate }}
                        </div>
                        <mdp-date-picker
                            class="mdp-date-picker"
                            mdp-format="DD/MM/YYYY"
                            mdp-max-date="maxDate"
                            mdp-min-date="minDate"
                            mdp-open-on-click
                            mdp-placeholder="{{ dateTo | dateFormat3 }}"
                            ng-change="checkDateTime(dateTo, 'dateTo')"
                            ng-model="dateTo"
                        ></mdp-date-picker>
                        <div class="clearfix"></div>
                    </div>
                    <div ng-show="vacancy.dateFinish" class="field pull-left">
                        <div class="deadline">
                            <div class="form-title" style="text-align: center" translate="Deadline"></div>
                            <span ng-class="{ 'deadline-finish': deadline <= $root.nowDate }">
                                {{ vacancy.dateFinish | dateFormat7 | translate }}
                            </span>
                        </div>
                    </div>
                </div>
                <div ng-show="vacancyFunnelMap.length" class="filter">
                    <div class="form-title" style="margin-right: 10px; text-align: center">{{ 'Show reports by users' | translate }}</div>
                    <div class="switcher">
                        <custom-switcher switcher-id="'report'" off-on="true" checked="showUsersReports" method="toggleUsersReports"></custom-switcher>
                    </div>
                    <div class="responsible-list" style="margin-right: 65px; align-items: center">
                        <custom-select-vacancy-reports
                            id="custom-select-vacancy-reports"
                            disabled="!showUsersReports"
                            method="addUserInFunnelUsersList(userIndex, $event);"
                            model="userIndex"
                            options="actionUsers"
                            placeholder="'Select user'"
                        ></custom-select-vacancy-reports>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
            <div class="col-lg-12 report-info">
                <div class="field update">
                    <button ng-click="updateData()" class="btn_success btn_default pull-right">
                        {{ 'Update data' | translate }}
                    </button>
                </div>
            </div>
            <div ng-show="vacancyFunnelMap.length" style="padding-bottom: 10px">
                <a ng-click="openModalWithText()" class="description-link" href="">
                    {{ 'Show the report description' | translate }}
                </a>
            </div>
            <div ng-if="vacancyFunnelMap.length && funnelActionUsers.length > 0" class="col-xs-12 no-padding">
                <div class="funnel-types">
                    <div
                        ng-if="vacancyFunnelMap"
                        ng-click="setStatistics('default'); setStatistics('default'); setStatistics('default')"
                        class="funnel-type general"
                        ng-class="{ active: statistics.type === 'default', 'full-width': !funnelActionUsers.length }"
                    >
                        <h5 ng-style="statistics.type === 'default' ? { 'font-weight': '500' } : { 'font-weight': '400' }">
                            {{ 'General Statistics' | translate }}
                        </h5>
                    </div>
                    <div
                        ng-if="$root.actionUsersTemp.length > 1"
                        ng-click="setStatistics('userFunnel', user); setStatistics('userFunnel', user)"
                        class="funnel-type"
                        ng-class="{ active: statistics.user === user }"
                        style="padding-right: 13px"
                        ng-repeat="user in funnelActionUsers track by $index"
                    >
                        <h5
                            style="word-break: break-all; padding-right: 13px"
                            ng-style="statistics.user === user ? { 'font-weight': '500' } : { 'font-weight': '400' }"
                        >
                            {{ user.cutName }}
                        </h5>
                        <span ng-click="removeUserInFunnelUsersList(user);$event.stopPropagation();" class="close">✖</span>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>

            <div ng-show="vacancyFunnelMap.length" class="col-lg-12 funnelPanel" ng-class="{ 'single-border': !funnelActionUsers.length }">
                <div id="funnel">
                    <div style="font-weight: 500; font-size: 14px; color: #838287">
                        {{ 'Opening date' | translate }}: {{ vacancy.dc | dateFormatSimple | translate }}
                        <span title="{{ 'Days in work' | translate }}">({{ vacancy.daysInWork }})</span>
                    </div>
                    <div style="min-height: 150px">
                        <div id="funnel-wrapper" class="funnel" ng-hide="!userFunnelData.userSeriesFunnel.length && statistics.type === 'userFunnel'">
                            <div id="funnel-inner" class="funnel-inner" style="flex-wrap: wrap; position: relative; align-items: center">
                                <table class="funnel-table no-caption no-border" style="height: min-content; margin-top: 71px">
                                    <tbody>
                                        <tr>
                                            <th style="border: none; color: rgba(49, 46, 55, 0.5)" translate="Stages"></th>
                                        </tr>
                                        <!--                                    <tr class="abs">-->
                                        <!--                                        <td>-->
                                        <!--                                            <div class="line"></div>-->
                                        <!--                                        </td>-->
                                        <!--                                    </tr>-->
                                        <tr class="hover" ng-repeat="stage in vacancyFunnelMap track by $index" title="{{ stage.key | translate }}">
                                            <td style="border: none">
                                                <span ng-if="stage.isCustom" class="vertical-align">{{ stage.key }}</span>
                                                <span ng-if="!stage.isCustom" class="vertical-align">{{ stage.key | translate }}</span>
                                                <div class="line" ng-class="{ last: $last }"></div>
                                            </td>
                                            <!--                                        <td class="hover-stripe"></td>-->
                                        </tr>
                                    </tbody>
                                </table>
                                <div
                                    id="wrapper"
                                    class="funnel-chart"
                                    style="height: fit-content; position: relative"
                                    ng-style="statistics.type === 'userFunnel' ? { 'margin-bottom': '25px' } : {}"
                                >
                                    <span style="position: absolute; border-top: 0.1em dashed #ccc; top: 118px; left: -210px; width: 620px"></span>
                                    <h5 ng-style="$root.isUserSelected ? { top: '-3px' } : { top: '15px' }" translate="Recruiting funnel">
                                        {{ $root.isUserSelected }}
                                    </h5>
                                    <p>{{ vacancies }}</p>
                                    <canvas id="mainFunnel" ng-show="statistics.type === 'default'"></canvas>
                                    <canvas id="userFunnel" ng-show="statistics.type === 'userFunnel'" class="abs"></canvas>
                                    <canvas id="buffer"></canvas>
                                </div>
                                <div class="conversion-table-wrapper" style="position: relative">
                                    <table
                                        id="table_with_bottom"
                                        ng-show="statistics.type === 'default' && hasFunnelChart"
                                        class="funnel-table general-conversion"
                                        style="height: fit-content; margin-top: 10px"
                                    >
                                        <caption class="conversion" style="border: none">
                                            <span>{{ 'Conversion for vacancy' | translate }}</span>
                                            <span
                                                style="position: absolute; padding-left: 3px; top: 10px"
                                                ng-init="popoverOpened=false"
                                                ng-mouseleave="popoverOpened=false"
                                                ng-mouseover="popoverOpened=true"
                                            >
                                                <i
                                                    class="info-icon"
                                                    popover-class="tooltip-outer vacancy-report vacancy-report-mobile center-xs auto-width first"
                                                    popover-is-open="popoverOpened"
                                                    popover-placement="bottom"
                                                    popover-trigger="none"
                                                    uib-popover-html="$root.tooltips.modalWithTextOnFunnelReport"
                                                ></i>
                                            </span>
                                        </caption>
                                        <tbody>
                                            <tr>
                                                <th style="background: #fff; color: rgba(49, 46, 55, 0.5)" translate="Candidates"></th>
                                                <th style="background: #fff; color: rgba(49, 46, 55, 0.5)" translate="Relative conversion"></th>
                                                <th
                                                    style="background: #fff; border-right: 1px solid #ccc; color: rgba(49, 46, 55, 0.5)"
                                                    translate="Absolute conversion"
                                                ></th>
                                            </tr>
                                            <tr
                                                ng-if="mainFunnel.data.stages[$index] !== 'longlist'"
                                                class="hover"
                                                ng-repeat="candidateSeries in mainFunnel.data.candidateSeries track by $index"
                                            >
                                                <td style="background: #fff">
                                                    <span>{{ candidateSeries }}</span>
                                                </td>
                                                <td>
                                                    <span>{{ mainFunnel.data.RelConversion[$index] }}</span>
                                                </td>
                                                <td style="background: #fff">
                                                    <span>{{ mainFunnel.data.AbsConversion[$index] }}</span>
                                                </td>
                                                <!--                                        <td class="hover-stripe"></td>-->
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div
                                        ng-if="actionUsers.length === 0 && $root.actionUsersTemp.length === 1"
                                        class="performed-by-user"
                                        style="width: fit-content; align-self: center; text-align: center; margin-top: 80px; margin-left: 30px"
                                    >
                                        <p style="margin: 0">{{ 'All actions for' | translate }}</p>
                                        <p style="margin: 0">{{ 'vacancies filled' | translate }}</p>
                                        <p style="margin: 0">{{ 'by_user' | translate }} -</p>
                                        <p style="margin: 0">{{ usersColumn.users[0].fullName }}</p>
                                    </div>
                                </div>
                                <table
                                    ng-show="$root.actionUsersTemp.length > 1 && usersColumn.users.length && statistics.type === 'default' && hasFunnelChart"
                                    class="funnel-table users-table"
                                    style="height: fit-content; position: relative"
                                >
                                    <caption
                                        style="height: 70px; border: none; border-bottom: 1px solid #ccc; padding-top: 25px"
                                        translate="Candidates by users"
                                    ></caption>
                                    <tr>
                                        <td style="border: none" ng-repeat="user in usersColumn.users track by $index">
                                            <table class="user-column-table">
                                                <tbody>
                                                    <tr>
                                                        <th style="height: 48px; border-top: none; border-right: none">
                                                            <span
                                                                class="user-name"
                                                                style="color: rgba(49, 46, 55, 0.5)"
                                                                ng-bind-html="user.cutName | breakOnMiddleString"
                                                            ></span>
                                                        </th>
                                                    </tr>
                                                    <tr class="hover" ng-repeat="series in usersColumn.dataArray[$index] track by $index">
                                                        <td>{{ series || '0' }}</td>
                                                        <!--                                                    <td class="hover-stripe"></td>-->
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                        <td style="border: none">
                                            <table class="user-column-table">
                                                <tbody>
                                                    <tr>
                                                        <th style="border-top: none">
                                                            <span class="total-wrapper" style="color: rgba(49, 46, 55, 0.5); font-size: 13px">
                                                                {{ 'Total in vacancy' | translate }}
                                                                <i
                                                                    class="info-icon"
                                                                    aria-hidden="true"
                                                                    tooltip-class="tooltip-outer center-xs"
                                                                    tooltip-placement="left"
                                                                    uib-tooltip="{{
                                                                        'Only unique candidates counts. A unique candidate - is a candidate , who was at the stage on this vacancy only once'
                                                                            | translate
                                                                    }}"
                                                                ></i>
                                                            </span>
                                                        </th>
                                                    </tr>
                                                    <tr class="hover" ng-repeat="candidateSeries in mainFunnel.data.candidateSeries track by $index">
                                                        <td>{{ candidateSeries }}</td>
                                                        <!--                                                    <td class="hover-stripe"></td>-->
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                <table
                                    ng-show="statistics.type === 'userFunnel'"
                                    class="funnel-table"
                                    style="height: fit-content; position: relative; bottom: 9.5px"
                                >
                                    <caption class="conversion" style="border: none" translate="Conversion"></caption>
                                    <tr>
                                        <th style="padding: 15px 8px; color: rgba(49, 46, 55, 0.5)" translate="Relative conversion"></th>
                                        <th style="color: rgba(49, 46, 55, 0.5)" translate="Absolute conversion"></th>
                                    </tr>
                                    <tr class="hover" ng-repeat="rel in userFunnelData.relConversion track by $index">
                                        <td>
                                            <span>{{ rel }}</span>
                                        </td>
                                        <td>
                                            <span>{{ userFunnelData.absConversion[$index] }}</span>
                                        </td>
                                        <!--                                        <td class="hover-stripe"></td>-->
                                    </tr>
                                </table>
                                <table
                                    ng-show="statistics.type === 'userFunnel'"
                                    class="funnel-table user-table"
                                    style="height: fit-content; position: relative; bottom: 10px"
                                >
                                    <caption style="border: none" translate="Candidates"></caption>
                                    <tr>
                                        <td style="border: none">
                                            <table>
                                                <tr>
                                                    <th
                                                        style="height: 70px; color: rgba(49, 46, 55, 0.5)"
                                                        translate="Amount by user"
                                                        translate-values="{ name: statistics.user.cutName}"
                                                    ></th>
                                                </tr>
                                                <tr class="hover" ng-repeat="userSeries in userFunnelData.userSeriesFunnel track by $index">
                                                    <td>{{ userSeries }}</td>
                                                    <!--                                                    <td class="hover-stripe"></td>-->
                                                </tr>
                                            </table>
                                        </td>
                                        <td style="border: none">
                                            <table>
                                                <tr>
                                                    <th style="height: 70px; color: rgba(49, 46, 55, 0.5)" translate="Total in vacancy"></th>
                                                </tr>
                                                <tr class="hover" ng-repeat="vacancySeries in userFunnelData.vacancySeries track by $index">
                                                    <td>{{ vacancySeries }}</td>
                                                    <!--                                                    <td class="hover-stripe"></td>-->
                                                </tr>
                                            </table>
                                        </td>
                                        <td style="border: none">
                                            <table>
                                                <tr>
                                                    <th style="height: 70px; color: rgba(49, 46, 55, 0.5)" translate="Percent of total amount"></th>
                                                </tr>
                                                <tr class="hover" ng-repeat="percent in userFunnelData.userPercentSeries() track by $index">
                                                    <td>{{ percent }}%</td>
                                                    <!--                                                    <td class="hover-stripe"></td>-->
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                <div
                                    class="funnel-inner-right funnel-center"
                                    ng-class="{
                                        'mini-funnel':
                                            (usersColumn.users.length && statistics.type === 'default') || (statistics.type === 'userFunnel' && hasFunnelChart),
                                        'funnel-inner-hide': vacancy_resp.length <= 0
                                    }"
                                    style="width: 310px"
                                    ng-style="statistics.type === 'userFunnel' ? { 'margin-left': '30px' } : { 'margin-left': '40px' }"
                                >
                                    <h5 ng-style="statistics.type === 'userFunnel' ? { margin: '6px 0 28px 0' } : { margin: '27px 0 26px 0' }">
                                        {{ 'refusals' | translate }} - {{ allCandidatesinRefuseSteps }}
                                    </h5>
                                    <div id="myChart"></div>
                                </div>
                            </div>
                            <div style="width: 97%; margin: 20px auto; border-bottom: 1px dashed silver; position: relative"></div>
                            <div id="wrapperColumnStages" style="display: flex; width: 2900px; justify-content: center">
                                <div id="columnStages" style="width: 100%; min-height: 300px; height: 100%"></div>
                            </div>
                        </div>
                        <div ng-show="!userFunnelData.userSeriesFunnel.length && statistics.type === 'userFunnel'" class="error">
                            <div class="msg" translate="User did not add/change stages for candidates in the selected period for this vacancy"></div>
                        </div>
                    </div>
                </div>
                <div
                    ng-hide="
                        !mainFunnel.data.candidateSeries.length ||
                        $root.me.recrutRole === 'freelancer' ||
                        (statistics.type === 'userFunnel' && !userFunnelData.userSeries.length)
                    "
                >
                    <a id="downloadPDF" style="display: none"></a>
                    <a id="downloadExcel" style="display: none"></a>
                    <div class="btn_wrapper">
                        <a
                            ng-click="downloadPDF()"
                            class="btn_default btn_success"
                            ng-hide="
                                ($root['me']['recrutRole'] == 'recruter' &&
                                    ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)) ||
                                ($root['me']['recrutRole'] == 'admin' && $root.me.personParams.enableDownloadToExcel == 'N')
                            "
                            type="button"
                        >
                            <span>{{ 'Download PDF' | translate }}</span>
                        </a>
                        <a
                            ng-click="downloadExcel()"
                            class="btn_default btn_success custom-margin"
                            ng-hide="
                                ($root['me']['recrutRole'] == 'recruter' &&
                                    ($root.me.personParams.enableDownloadToExcel == 'N' || !$root.me.personParams.enableDownloadToExcel)) ||
                                ($root['me']['recrutRole'] == 'admin' && $root.me.personParams.enableDownloadToExcel == 'N')
                            "
                            type="button"
                        >
                            <span>{{ 'Download Excel' | translate }}</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-12 funnel-info">
                <div class="ui vertically divided grid">
                    <div class="sixteen wide column" style="padding-left: 0px; padding-right: 0px">
                        <div ng-show="!hasFunnelChart || !vacancyFunnelMap.length" style="padding: 6px; text-align: center; font-size: 16px">
                            {{ 'No data for display' | translate }}
                        </div>
                        <p>
                            {{ 'The recruiting funnel is based on the number of candidates on vacancy and on transitions between statuses' | translate }}
                        </p>
                        <p>
                            {{ 'The recruiting is funnel calculated considering the fact that all vacancy stages are consistent' | translate }}
                        </p>
                        <p>
                            {{
                                'You can add/remove, swap, and customize all your vacancy stages, except Long list and Hired, in the Stages block on a vacancy page'
                                    | translate
                            }}
                        </p>
                        <p>
                            {{
                                'Recruiting funnel shows the conversion between the stages. Vacancy stages with the lowest conversion are the zone of growth. Thus recruiting funnel helps to identify bottlenecks and to focus on them'
                                    | translate
                            }}
                        </p>
                        <p>
                            {{ 'It is useful to compare the recruiting funnel different recruiters to funnel recruiting of the company' | translate }}
                        </p>
                        <p>
                            {{
                                'The chart displays the actual progression of candidates through the stages and does not account for any stages that a candidate may have skipped. Additionally, the chart shows the average time a candidate spends at each stage.'
                                    | translate
                            }}
                        </p>
                        <p>
                            {{ 'The histogram of stages differs from the funnel because' | translate }}
                        </p>
                    </div>
                </div>
            </div>
            <div
                ng-show="(statistics.type === 'default' && vacancyFunnelMap.length) || (statistics.type === 'userFunnel' && userFunnelData.userSeries.length)"
                class="show-list-of-candidates-wrapper flex-justify-center"
            >
                <button ng-click="showListOfCandidatesToogle()" class="btn_default btn_success">
                    {{ 'List of candidates' | translate }}
                </button>
            </div>
            <div id="listOfCandidates" ng-if="showListOfCandidates" class="col-lg-12 tableUpdate" ng-repeat="item in vacancyHistory">
                <table ng-if="item.key !== 'longlist'" class="table">
                    <thead>
                        <tr ng-class="{ hidden: $index != 0 }">
                            <th>{{ 'Candidate' | translate }}</th>
                            <th>{{ 'salary' | translate }}</th>
                            <th>{{ 'Current company' | translate }}</th>
                            <th>{{ 'added_by_male' | translate }}</th>
                            <th>{{ 'The amount of time in the stage' | translate }}</th>
                        </tr>
                        <tr>
                            <th class="no-background" ng-class="{ red_back_refuse: item.value[0].isRefusalState === true }" style="text-align: left">
                                {{ 'Were on the stage' | translate }}
                                <span ng-if="item.isCustom">{{ item.key }}</span>
                                <span ng-if="!item.isCustom">{{ item.key | translate }}</span>
                                - {{ item.value.length }}
                            </th>
                            <th class="no-background"></th>
                            <th class="no-background"></th>
                            <th class="no-background"></th>
                            <th class="no-background"></th>
                        </tr>
                    </thead>
                    <tbody ng-if="!isHiddenStage">
                        <tr style="border-bottom: none" ng-repeat-start="candidate in item.value">
                            <td>
                                <span style="display: inline-flex">
                                    <a href="#/candidates/{{ candidate.localId }}">
                                        <span ng-if="$root.useAmericanNameStyle">{{ candidate.fullNameEn }},</span>
                                        <span ng-if="!$root.useAmericanNameStyle">{{ candidate.fullName }},</span>
                                        <span ng-if="candidate.db">
                                            {{ candidate.db | YearsSinceDate }}&nbsp;{{ candidate.db | YearsEndingForm | translate }},
                                        </span>
                                        <span ng-if="candidate.position && candidate.position !== ' '">{{ candidate.position }}</span>
                                    </a>
                                </span>
                            </td>
                            <td>
                                <span ng-show="candidate.salary > 0 && $root.me.recrutRole !== 'client'">{{ candidate.salary }} {{ candidate.currency }}</span>
                            </td>
                            <td>{{ candidate.currentWorkPlace }}</td>
                            <td>{{ (candidate.dm | dateFormatSimple2 : true).trim() }}</td>
                            <td ng-if="candidate.timeOnInterviewState">
                                {{ timeDeclination(candidate.timeOnInterviewState) }}
                            </td>
                            <td ng-if="!candidate.timeOnInterviewState">
                                <label>-</label>
                                <i
                                    class="info-icon"
                                    style="display: inline-block; position: relative; top: 3px; margin-right: 5px"
                                    aria-hidden="true"
                                    tooltip-class="tooltip-outer"
                                    tooltip-placement="left-bottom"
                                    uib-tooltip-html="$root.tooltips.no_time_on_stage"
                                ></i>
                            </td>
                        </tr>
                        <tr ng-if="candidate.descr">
                            <td class="description-row" colspan="5">
                                <span>↵</span>
                                {{ (candidate.descr | translateDescr).trim() }}
                            </td>
                        </tr>
                        <tr class="table-space" ng-hide="$last" ng-repeat-end=""></tr>
                    </tbody>
                    <tbody ng-if="item.isHiddenStage">
                        <tr>
                            <td colspan="5">
                                {{
                                    'You have no access to candidate list on this stage. Please contact the user with an Admin role to access the candidate list.'
                                        | translate
                                }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
