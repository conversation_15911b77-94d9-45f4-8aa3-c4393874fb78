<div class="controller-view email-templates-block">
    <div class="email-templates-wrapper">
        <div class="template-selection">
            <div
                ng-click='$root.selectTemplate("My templates")'
                class="template-selection__name"
                ng-class="{ 'border-templates': $root.selectTemplateName === 'My templates' }"
                ng-style="$root.selectTemplateName === 'My templates' ? { opacity: '1' } : null"
            >
                {{ 'My templates' | translate }}
            </div>
            <div
                ng-click='$root.selectTemplate("General templates")'
                class="template-selection__name"
                ng-class="{ 'border-templates': $root.selectTemplateName === 'General templates' }"
                ng-style="$root.selectTemplateName === 'General templates' ? { opacity: '1' } : null"
                ui-sref-opts="{reload:true}"
            >
                {{ 'General templates' | translate }}
            </div>
        </div>
        <div class="border-wrapper">
            <hr class="border-main" />
        </div>
        <div
            ng-if="$root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'N' && $root.selectTemplateName === 'My templates'"
            class="add-template"
        >
            <span
                ng-show="
                    $root.selectTemplateName === 'General templates' || ($root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length > 0)
                "
                class="select-template-title"
            >
                {{ 'Choose a letter template' | translate }}
            </span>
            <span ng-show="$root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length === 0" class="select-template-title">
                {{ 'You have not created any template yet' | translate }}
            </span>
            <button ng-click="$root.createEmptyTemplate()" class="btn_default btn_success btn_thin" style="font-size: 14px; min-width: 239px">
                <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="6.5" y1="0.5" x2="6.5" y2="13.5" stroke="white" stroke-width="2" />
                    <line y1="7" x2="13" y2="7" stroke="white" stroke-width="2" />
                </svg>
                <span style="margin-left: 8px">{{ 'Create a letter template' | translate }}</span>
            </button>
        </div>
        <div ng-if="$root.me.recrutRole != 'recruter' && $root.me.recrutRole != 'admin' && $root.selectTemplateName === 'My templates'" class="add-template">
            <span
                ng-show="
                    $root.selectTemplateName === 'General templates' || ($root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length > 0)
                "
                class="select-template-title"
            >
                {{ 'Choose a letter template' | translate }}
            </span>
            <span ng-show="$root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length === 0" class="select-template-title">
                {{ 'You have not created any template yet' | translate }}
            </span>
            <button ng-click="$root.createEmptyTemplate()" class="btn_default btn_success btn_thin" style="font-size: 14px; min-width: 239px">
                <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="6.5" y1="0.5" x2="6.5" y2="13.5" stroke="white" stroke-width="2" />
                    <line y1="7" x2="13" y2="7" stroke="white" stroke-width="2" />
                </svg>
                <span style="margin-left: 8px">{{ 'Create a letter template' | translate }}</span>
            </button>
        </div>
        <div
            ng-if="$root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'N' && $root.selectTemplateName === 'General templates'"
            class="add-template"
        >
            <span
                ng-show="
                    $root.selectTemplateName === 'General templates' || ($root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length > 0)
                "
                class="select-template-title"
            >
                {{ 'Choose a letter template' | translate }}
            </span>
            <span ng-show="$root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length === 0" class="select-template-title">
                {{ 'You have not created any template yet' | translate }}
            </span>
        </div>
        <div ng-if="$root.me.recrutRole == 'recruter' && $root.editEmailTemplatesValue == 'Y'" class="add-template">
            <span
                ng-show="
                    $root.selectTemplateName === 'General templates' || ($root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length > 0)
                "
                class="select-template-title"
            >
                {{ 'Choose a letter template' | translate }}
            </span>
            <span ng-show="$root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length === 0" class="select-template-title">
                {{ 'You have not created any template yet' | translate }}
            </span>
            <button ng-click="$root.createEmptyTemplate()" class="btn_default btn_success btn_thin" style="font-size: 14px; min-width: 239px">
                <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="6.5" y1="0.5" x2="6.5" y2="13.5" stroke="white" stroke-width="2" />
                    <line y1="7" x2="13" y2="7" stroke="white" stroke-width="2" />
                </svg>
                <span style="margin-left: 8px">{{ 'Create a letter template' | translate }}</span>
            </button>
        </div>
        <div ng-if="$root.me.recrutRole === 'admin'" class="add-template">
            <span
                ng-show="
                    $root.selectTemplateName === 'General templates' || ($root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length > 0)
                "
                class="select-template-title"
            >
                {{ 'Choose a letter template' | translate }}
            </span>
            <span ng-show="$root.selectTemplateName === 'My templates' && $root.isEmailTemplate.custom.length === 0" class="select-template-title">
                {{ 'You have not created any template yet' | translate }}
            </span>
            <button
                ng-click="$root.createEmptyTemplate()"
                class="btn_default btn_success btn_thin"
                style="font-size: 14px; min-width: 239px"
                ng-hide="$root.me.recrutRole != 'admin'"
            >
                <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="6.5" y1="0.5" x2="6.5" y2="13.5" stroke="white" stroke-width="2" />
                    <line y1="7" x2="13" y2="7" stroke="white" stroke-width="2" />
                </svg>
                <span style="margin-left: 8px">{{ 'Create a letter template' | translate }}</span>
            </button>
        </div>
    </div>
    <div>
        <email-templates></email-templates>
    </div>
    <div style="float: left; width: 100%; opacity: 0; height: 20px"></div>
</div>
