<div class="navbar navbar-static-top" ng-class="{ overModal: $root.me.orgs.length > 1 && !$root.hideTariff }">
    <div class="container-fluid">
        <div class="navbar-header pull-left">
            <a class="navbar-brand">
                <span class="logo"></span>
            </a>
        </div>
        <div id="navbar-main" class="">
            <ul ng-if="$root.me" class="nav navbar-nav navigation navbar-collapse collapse">
                <li class="dropdown organizer" ng-class="{ 'ru-width': $root.currentLang !== 'en' }">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Activity' ||
                                $root.$state.current.data.pageName == 'Activity Calendar' ||
                                $root.$state.current.data.pageName == 'Activity Dashboard'
                        }"
                        href="#/organizer"
                    >
                        <i class="sprite sprite-calendar"></i>
                        <span translate="Organizer"></span>
                    </a>
                </li>
                <li class="vacancies" ng-class="{ 'ru-width': $root.currentLang !== 'en' }">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Vacancies' ||
                                $root.$state.current.data.pageName == 'Vacancy add' ||
                                $root.$state.current.data.pageName == 'Vacancy edit'
                        }"
                        href="#/vacancies"
                    >
                        <i class="sprite sprite-vacancy"></i>
                        <span translate="Vacancies"></span>
                    </a>
                </li>
                <li class="candidates" ng-class="{ 'ru-width': $root.currentLang !== 'en' }">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Candidates' ||
                                $root.$state.current.data.pageName == 'Candidate add' ||
                                $root.$state.current.data.pageName == 'Candidates add from email' ||
                                $root.$state.current.data.pageName == 'Zip' ||
                                $root.$state.current.data.pageName == 'Excel History' ||
                                $root.$state.current.data.pageName == 'Candidate edit' ||
                                $root.$state.current.data.pageName == 'Tests and forms' ||
                                $root.$state.current.data.pageName == 'Send test candidate to email' ||
                                $root.$state.current.data.pageName == 'Test results' ||
                                $root.$state.current.data.pageName == 'Test page' ||
                                $root.$state.current.data.pageName == 'Candidate'
                        }"
                        href="#/candidates"
                    >
                        <i class="sprite sprite-candidates"></i>
                        <span translate="candidates"></span>
                    </a>
                </li>
                <li class="dropdown clients" ng-class="{ 'ru-width': $root.currentLang !== 'en' }">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Clients' ||
                                $root.$state.current.data.pageName == 'Client add' ||
                                $root.$state.current.data.pageName == 'Client edit'
                        }"
                        href="#/clients"
                        ng-hide="$root.me.personParams.clientAccessLevel === 'hide'"
                    >
                        <i class="sprite sprite-clients"></i>
                        <span ng-show="$root.me != undefined" translate="Clients"></span>
                    </a>
                </li>
                <li class="dropdown users" ng-class="{ 'ru-width': $root.currentLang !== 'en' }" ng-hide="$root.me.recrutRole === 'client'">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Users' ||
                                $root.$state.current.data.pageName == 'Company History' ||
                                $root.$state.current.data.pageName == 'Company settings' ||
                                $root.$state.current.data.pageName == 'Company users' ||
                                $root.$state.current.data.pageName == 'Company employees' ||
                                $root.$state.current.data.pageName == 'Employee add' ||
                                $root.$state.current.data.pageName == 'Employee User' ||
                                $root.$state.current.data.pageName == 'Department Catalog' ||
                                $root.$state.current.data.pageName == 'Employee Edit User' ||
                                $root.$state.current.data.pageName == 'Custom fields'
                        }"
                        href="#/company/users"
                    >
                        <i class="sprite sprite-company"></i>
                        <span translate="Account"></span>
                    </a>
                </li>
                <li class="dropdown reports" ng-class="{ 'ru-width': $root.currentLang !== 'en' }" ng-hide="$root.me.recrutRole === 'researcher'">
                    <a
                        ng-class="{
                            active:
                                $root.$state.current.data.pageName == 'Pipeline' ||
                                $root.$state.current.data.pageName == 'Statistics client' ||
                                $root.$state.current.data.pageName == 'Report all' ||
                                $root.$state.current.data.pageName == 'Vacancy report' ||
                                $root.$state.current.data.pageName == 'Reports' ||
                                $root.$state.current.data.pageName == 'Reports constructor' ||
                                $root.$state.current.data.pageName == 'Custom Reports' ||
                                $root.$state.current.data.pageName == 'Edit Reports'
                        }"
                        href="#/reports"
                    >
                        <i class="fa fa-bar-chart"></i>
                        <span translate="Reports"></span>
                    </a>
                </li>
            </ul>
            <span class="active-page">{{ $root.notFormatedTitle }}</span>
            <div ng-if="digit > 0 && digit <= 14" class="trial">
                <span ng-show="digit != 1">{{ 'Only' | translate }}</span>
                <span ng-show="digit == 1">{{ 'Only_1' | translate }}</span>
                <span class="digit" ng-bind="digit"></span>
                <span>
                    {{ digit >= 5 && digit <= 14 ? 'days_1Trial' : digit < 5 && digit > 1 ? 'daysTrial' : digit === 0 ? 'dayTrial' : ('dayTrial' | translate) }}
                </span>
                <i class="tooltip-icon" ng-mouseover="HoverBlockTrialShow()" ng-mouseleave="HoverBlockTrialHidden()" aria-hidden="true">
                    <div ng-show="hoverBlockTrial" class="hoverExtension" style="font-family: 'Helvetica-Normal', Arial, Verdana, sans-serif">
                        <p>
                            {{ 'All features are unlimited within your trial. You could invite unlimited number of users to test the system.' | translate }}
                        </p>
                        <p>{{ 'If your account will not be paid until trial end date' | translate }}</p>
                        <ul>
                            <li>
                                {{ 'it will be automatically changed to ‘1 RECRUITER’ plan with limited features;' | translate }}
                            </li>
                            <li>{{ 'all invited users will be blocked until account is paid.' | translate }}</li>
                        </ul>
                    </div>
                </i>
            </div>
            <ul class="nav information navbar-nav navbar-right pull-right margin-bot">
                <li ng-show="$root.me.recrutRole == 'admin'" class="payMoney hidden-xs hidden-sm">
                    <a
                        class="payment"
                        ng-class="{
                            'low-low-balance':
                                paidFor !== undefined &&
                                paidFor <= 4 &&
                                !(digit > 0 && digit <= 14) &&
                                !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free'),
                            'low-balance': paidFor !== undefined && paidFor <= 7
                        }"
                        href="#/pay"
                    >
                        <i class="fa fa-usd"></i>
                        <span ng-show="!$root.paidTillDateBilling && $root.companyParams.paidTillDate && paidFor > 7">
                            {{ 'up to' | translate }}&nbsp;{{ $root.companyParams.paidTillDate }}
                        </span>
                        <span ng-show="$root.paidTillDateBilling && paidFor > 7 && !(digit > 0 && digit <= 14)">
                            {{ 'up to' | translate }}&nbsp;{{ $root.paidTillDateBilling }}
                        </span>
                        <span
                            ng-show="
                                paidFor > 4 && paidFor <= 7 && !(digit > 0 && digit <= 14) && !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free')
                            "
                            translate="Days left"
                            translate-values="{days_left: paidFor}"
                        ></span>
                        <span
                            ng-show="
                                paidFor <= 4 && paidFor > 1 && !(digit > 0 && digit <= 14) && !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free')
                            "
                            translate="Days left!"
                            translate-values="{days_left: paidFor}"
                        ></span>
                        <span
                            ng-show="paidFor == 1 && !(digit > 0 && digit <= 14) && !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free')"
                            translate="Day left!"
                            translate-values="{days_left: paidFor}"
                        ></span>
                        <span
                            ng-show="paidFor == 0 && !(digit > 0 && digit <= 14) && !(getMeObj['orgParams']['tarif'] === 'free' || tarif == 'free')"
                            translate="No Day left!"
                            translate-values="{days_left: paidFor}"
                        ></span>
                    </a>
                </li>
                <li class="showFeedbackMenu hidden-xs hidden-sm">
                    <div class="btn-group" uib-dropdown>
                        <button uib-dropdown-toggle>
                            <i class="fa fa-question" aria-hidden="true"></i>
                            {{ 'support' | translate }}
                        </button>
                        <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                            <li role="menuitem">
                                <a class="boldText" href="#/faq">{{ 'Read FAQ first' | translate }}</a>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a href="#/ask_question">{{ 'Ask question' | translate }}</a>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a href="#/report_problem_on_this_page">
                                    {{ 'Report problem on this page' | translate }}
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a href="#/suggest_improvement_or_request_feature">
                                    {{ 'Suggest improvement or request feature' | translate }}
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a class="boldText" href="skype:cleverstaff?chat">
                                    {{ 'Instant support at Skype' | translate }}
                                </a>
                            </li>
                        </ul>
                        <div id="arrowFeedback"></div>
                    </div>
                </li>
                <li>
                    <a id="open-notice-button" ng-click="openNoticeMenu();" class="hidden-xs" href="">
                        <i class="fa fa-bell-o"></i>
                        <span ng-if="$root.currentNoticeCount > 0" class="label label-danger">
                            {{ $root.newNoticeCount }}
                        </span>
                    </a>
                    <notices-dir></notices-dir>
                    <a class="mobile-notices hidden-lg hidden-md hidden-sm" href="#/notices">
                        <i class="fa fa-bell-o"></i>
                        <span ng-if="$root.currentNoticeCount > 0" class="label label-danger">
                            {{ $root.newNoticeCount }}
                        </span>
                    </a>
                </li>
                <li>
                    <div class="btn-group" uib-dropdown>
                        <button class="btn btn-primary profile" type="button" uib-dropdown-toggle ng-disabled="disabled">
                            <i class="fa fa-cog" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                            <li role="menuitem">
                                <a ng-click="toUser()">
                                    <i class="fa fa-user" aria-hidden="true"></i>
                                    {{ 'My Account' | translate }}
                                    <img
                                        ng-if="g_info"
                                        ng-click="toDetailUserInfo()"
                                        class="img-circle hidden-xs margin-left"
                                        width="30"
                                        height="30"
                                        ng-src="{{ getImgSrc(g_info) }}"
                                    />
                                </a>
                            </li>
                            <li class="divider hidden-xs hidden-sm"></li>
                            <li class="hidden-xs hidden-sm" role="menuitem">
                                <a ng-click="toAddEmail()">
                                    <i class="fa fa-envelope" aria-hidden="true"></i>
                                    {{ 'Integration with email' | translate }}
                                    <i
                                        class="tooltip-icon extension full-size"
                                        ng-mouseover="integrationEmailPrompt()"
                                        ng-mouseleave="integrationEmailPromptHide()"
                                        aria-hidden="true"
                                    ></i>
                                </a>
                                <div ng-show="showHoverEmailPrompt" class="hoverExtension">
                                    <p>
                                        {{
                                            'Once you integrate your email, candidates CVs sent to your email inbox will be added the candidates database of your company'
                                                | translate
                                        }}
                                    </p>
                                    <p>{{ 'The synchronization runs 1 time per hour' | translate }}</p>
                                    <p>
                                        {{
                                            'To set the integration click on Integration with email and follow the instructions. You will have to type the SMTP Host and SMTP Port of your email. You could request this data from your system administrator'
                                                | translate
                                        }}
                                    </p>
                                    <p>
                                        {{
                                            'Please note that the Mass mailing feature will be available only when integrated with your corporate email account. Corporate mail is mail with a format ...@your domain.'
                                                | translate
                                        }}
                                    </p>
                                </div>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a>
                                    <select
                                        id="selectLN"
                                        ng-click="$event.stopPropagation()"
                                        class="form-control"
                                        ng-model="$root.currentLang"
                                        ng-change="changeLanguage($root.currentLang)"
                                    >
                                        <option selected value="en">ENG</option>
                                        <option id="russianLN" value="ru">RUS</option>
                                    </select>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li class="menuitem">
                                <a ng-click="toNotification()" href>
                                    {{ 'Email_notifications' | translate }}
                                </a>
                            </li>
                            <li class="divider hidden-xs hidden-sm"></li>
                            <li class="menuitem hidden-xs hidden-sm">
                                <a ng-href="{{ getPluginDownloadingLink() }}" target="_blank">
                                    {{ 'Extension for' | translate }}
                                    Chrome
                                    <i class="tooltip-icon extension full-size" ng-mouseover="inHover()" ng-mouseleave="outHover()" aria-hidden="true"></i>
                                </a>
                                <div ng-show="showHover" class="hoverExtension">
                                    <p>
                                        {{ 'CleverStaff browser extension to integrate & save candidates from LinkedIn and job boards in 1 click' | translate }}
                                        <a href="work.ua">work.ua</a>
                                        ,
                                        <a href="robota.ua">robota.ua</a>
                                        ,
                                        <a href="grc.ua">grc.ua</a>
                                        ,
                                        <a href="kiev.grc.ua">kiev.grc.ua</a>
                                        ,
                                        <a href="rabota.by">rabota.by</a>
                                        ,
                                        <a href="cv.lv">cv.lv</a>
                                        ,
                                        <a href="job.kg">job.kg</a>
                                        ,
                                        <a href="dou.ua">dou.ua</a>
                                        .
                                    </p>
                                </div>
                            </li>
                            <li ng-show="$root.me.personParams.domainAdmin" class="divider"></li>
                            <li ng-show="$root.me.personParams.domainAdmin">
                                <a href="#/cloud-admin">cloud admin</a>
                            </li>
                            <li class="divider"></li>
                            <li role="menuitem">
                                <a ng-click="signOut()" href="">
                                    <i class="fa fa-sign-out" aria-hidden="true"></i>
                                    {{ 'Exit' | translate }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <button ng-click="showResponsiveNav()" class="navbar-toggle" type="button">
                    <span class="sr-only">Toggle navigation</span>
                    <i id="" class="fa fa-bars" aria-hidden="true"></i>
                </button>
            </ul>
        </div>
    </div>
    <div class="responsive-nav hide">
        <ul class="nav">
            <li>
                <a href="#/organizer">
                    <i class="sprite sprite-calendar"></i>
                    {{ 'activity' | translate }}
                </a>
            </li>
            <li>
                <a href="#/vacancies">
                    <i class="sprite sprite-vacancy"></i>
                    {{ 'vacancies' | translate }}
                </a>
            </li>
            <li>
                <a href="#/candidates">
                    <i class="sprite sprite-candidates"></i>
                    {{ 'candidates' | translate }}
                </a>
            </li>
            <li ng-if="$root.me.personParams.clientAccessLevel != 'hide'">
                <a href="#/clients">
                    <i class="sprite sprite-clients"></i>
                    {{ 'clients' | translate }}
                </a>
            </li>
            <li>
                <a href="#/company/users">
                    <i class="sprite sprite-company"></i>
                    <span ng-if="$root.me != undefined && $root.me.recrutRole != 'client'">
                        {{ 'Account' | translate }}
                    </span>
                    <span ng-if="$root.me.recrutRole == 'client'">{{ 'Recruiters' | translate }}</span>
                </a>
            </li>
            <li>
                <div class="btn-group" uib-dropdown>
                    <a id="tech-dropdown" uib-dropdown-toggle>
                        {{ 'support' | translate }}
                    </a>
                    <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                        <li role="menuitem">
                            <a class="boldText" href="#/faq">{{ 'FAQ' | translate }}</a>
                        </li>
                        <li class="divider"></li>
                        <li role="menuitem">
                            <a href="#/ask_question">{{ 'Ask question' | translate }}</a>
                        </li>
                        <li class="divider"></li>
                        <li role="menuitem">
                            <a href="#/report_problem_on_this_page">{{ 'Report problem on this page' | translate }}</a>
                        </li>
                        <li class="divider"></li>
                        <li role="menuitem">
                            <a href="#/suggest_improvement_or_request_feature">
                                {{ 'Suggest improvement or request feature' | translate }}
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
    </div>
    <!--Navbar for future-->
    <div
        ng-show="
            $root.$state.current.data.pageName == 'Activity' ||
            $root.$state.current.data.pageName == 'Activity Calendar' ||
            $root.$state.current.data.pageName == 'Activity Dashboard'
        "
        class="container-fluid second-navbar hidden-sm hidden-xs"
    >
        <div class="second-navbar">
            <ul class="nav navbar-nav col-lg-8 col-md-9 col-sm-12 col-xs-12">
                <li>
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Activity Dashboard' }" href="#/organizer/dashboard">
                        {{ 'Dashboard' | translate }}
                    </a>
                </li>
                <li class="dropdown">
                    <a class="no-margin" ng-class="{ selected: $root.$state.current.data.pageName == 'Activity' }" href="#/organizer">
                        {{ 'activity' | translate }}
                    </a>
                </li>
                <li>
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Activity Calendar' }" href="#/organizer/calendar">
                        {{ 'calendar' | translate }}
                    </a>
                </li>
                <li class="dropdown">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'News' }" href="#/news">
                        {{ 'User news' | translate }}
                    </a>
                </li>
                <li ng-show="otherDate >= nowDate && $root.me.recrutRole == 'admin'" class="dropdown">
                    <a ng-show="$root.blockUser != true" ng-click="inviteUserClientModal();" class="btn btn-primary inviteUserClientHideTariff" href>
                        {{ 'Invite User / Client' | translate }}
                    </a>
                </li>
                <li ng-show="otherDate < nowDate && $root.me.recrutRole == 'admin' && $root.blockUser != true" class="dropdown">
                    <a ng-click="inviteUserClientModal();" class="btn btn-primary inviteUserClient" href>
                        {{ 'Invite User / Client' | translate }}
                    </a>
                </li>
            </ul>

            <ul class="nav navbar-nav col-lg-4 col-md-3 hidden-sm hidden-xs text-right" style="justify-content: flex-end">
                <li
                    id="drop_down_scope"
                    ng-click="showScopeFunc();"
                    class="no-padding pull-right"
                    ng-class="{
                        darkOrangeShadowForScope: $root.scopeActiveObject.name != 'company' && $root.scopeActiveObject.check
                    }"
                    uib-dropdown
                    is-open="status.isopen"
                >
                    <a href="" uib-dropdown-toggle title="{{ 'scope' | translate }}">
                        {{ 'scope' | translate }}:
                        <span ng-show="$root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check">
                            <span>{{ 'entire account' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check">
                            <span>{{ 'only_me1' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check">
                            {{ 'only' | translate }}
                            <span>{{ $root.scopeActiveObject.value.name }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <i
                            ng-click="$event.stopPropagation(); $event.preventDefault();"
                            class="margin-left tooltip-icon"
                            title=""
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-outer"
                            uib-tooltip-html="$root.tooltips.scopeTooltip"
                            aria-hidden="true"
                        ></i>
                    </a>
                    <ul class="dropdown-menu showScopeMenu" uib-dropdown-menu role="menu" scope-directive aria-labelledby="single-button">
                        <h4 class="text-center">{{ 'scope' | translate }}</h4>
                        <p>{{ 'scope_message' | translate }}</p>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkOrg" ng-click="changeScope('company')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">{{ 'entire account' | translate }}</span>
                            <span ng-show="orgs.length > 1" class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12"
                                    ng-model="orgId"
                                    name="region"
                                    ng-options="org.orgId as org.orgName for org in orgs"
                                    ng-change="changeScope('company')"
                                    required=""
                                ></select>
                            </span>
                            <span ng-show="orgs.length < 2" class="col-lg-6 no-margin">
                                <div ng-repeat="org in orgs|filter:{ orgId: orgId }">
                                    {{ org.orgName }}
                                </div>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkRegion" ng-click="changeScope('region')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">
                                <label style="font-size: 14px">{{ 'only_region' | translate }}</label>
                            </span>
                            <span class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12 cs-region-filter-select-scope2"
                                    ng-style="regionListStyle"
                                    ng-model="regionId"
                                    ng-change="changeScopeForRegionSelect('region')"
                                ></select>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkMe" ng-click="changeScope('onlyMy')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-11 no-margin">
                                <span ng-style="$root.currentLang !== 'ru' ? { 'margin-right': '0px' } : null">
                                    {{ 'only_me1' | translate }}
                                </span>
                                <span>
                                    {{ 'only_me2' | translate }}
                                </span>
                            </span>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for vacancy-->
    <div
        ng-show="
            $root.$state.current.data.pageName == 'Vacancies' ||
            $root.$state.current.data.pageName == 'Vacancy add' ||
            $root.$state.current.data.pageName == 'Vacancy edit'
        "
        class="container-fluid second-navbar"
    >
        <div class="second-navbar">
            <ul class="nav vacancy-second-header navbar-nav col-lg-9 col-md-12">
                <li class="dropdown">
                    <a
                        class="no-margin"
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Vacancies' }"
                        href="#/vacancies"
                        ng-hide="!searchParam.words && !$root.objectSize && $root.me.recrutRole === 'client'"
                    >
                        {{ 'Active vacancies' | translate }}
                    </a>
                </li>
                <li>
                    <a
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Vacancy add' }"
                        href="#/vacancy/add"
                        ng-hide="!searchParam.words && !$root.objectSize && $root.me.recrutRole === 'client'"
                    >
                        {{ 'New vacancy' | translate }}
                    </a>
                </li>
                <li>
                    <a target="_blank" ng-href="{{ $root.publicLink }}">
                        {{ 'Open_vacancies_for_company' | translate }}
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav col-lg-3 hidden-md hidden-sm hidden-xs text-right">
                <li
                    id="drop_down_scope"
                    ng-click="showScopeFunc();"
                    class="no-padding pull-right"
                    ng-class="{
                        darkOrangeShadowForScope: $root.scopeActiveObject.name != 'company' && $root.scopeActiveObject.check
                    }"
                    uib-dropdown
                    is-open="status.isopen"
                >
                    <a href="" uib-dropdown-toggle title="{{ 'scope' | translate }}">
                        {{ 'scope' | translate }}:
                        <span ng-show="$root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check">
                            <span>{{ 'entire account' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check">
                            <span>{{ 'only_me1' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check">
                            {{ 'only' | translate }}
                            <span>{{ $root.scopeActiveObject.value.name }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <i
                            ng-click="$event.stopPropagation(); $event.preventDefault();"
                            class="margin-left tooltip-icon"
                            title=""
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-outer"
                            uib-tooltip-html="$root.tooltips.scopeTooltip"
                            aria-hidden="true"
                        ></i>
                    </a>
                    <ul class="dropdown-menu showScopeMenu" uib-dropdown-menu role="menu" scope-directive aria-labelledby="single-button">
                        <h4 class="text-center">{{ 'scope' | translate }}</h4>
                        <p>{{ 'scope_message' | translate }}</p>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkOrg" ng-click="changeScope('company')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">{{ 'entire account' | translate }}</span>
                            <span ng-show="orgs.length > 1" class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12"
                                    ng-model="orgId"
                                    name="region"
                                    ng-options="org.orgId as org.orgName for org in orgs"
                                    ng-change="changeScope('company')"
                                    required=""
                                ></select>
                            </span>
                            <span ng-show="orgs.length < 2" class="col-lg-6 no-margin">
                                <div ng-repeat="org in orgs|filter:{ orgId: orgId }">
                                    {{ org.orgName }}
                                </div>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkRegion" ng-click="changeScope('region')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">
                                <label style="font-size: 14px">{{ 'only_region' | translate }}</label>
                            </span>
                            <span class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12 cs-region-filter-select-scope2"
                                    ng-style="regionListStyle"
                                    ng-model="regionId"
                                    ng-change="changeScopeForRegionSelect('region')"
                                ></select>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkMe" ng-click="changeScope('onlyMy')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-11 no-margin">
                                <span ng-style="$root.currentLang !== 'ru' ? { 'margin-right': '0px' } : null">
                                    {{ 'only_me1' | translate }}
                                </span>
                                <span>
                                    {{ 'only_me2' | translate }}
                                </span>
                            </span>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for candidates and candidate-->
    <div
        ng-show="
            $root.hideContainer &&
            ($root.$state.current.data.pageName == 'Candidates' ||
                $root.$state.current.data.pageName == 'Candidate' ||
                $root.$state.current.data.pageName == 'Candidate add' ||
                $root.$state.current.data.pageName == 'Candidate edit' ||
                $root.$state.current.data.pageName == 'Excel History' ||
                $root.$state.current.data.pageName == 'Tests and forms' ||
                $root.$state.current.data.pageName == 'Send test candidate to email' ||
                $root.$state.current.data.pageName == 'Detailed test results' ||
                $root.$state.current.data.pageName == 'Detailed test results' ||
                $root.$state.current.data.pageName == 'Tests results' ||
                $root.$state.current.data.pageName == 'Test results' ||
                $root.$state.current.data.pageName == 'Test page' ||
                $root.$state.current.data.pageName == 'Send test candidate to email from vacancy' ||
                $root.$state.current.data.pageName == 'Send test candidate to email from candidate' ||
                $root.$state.current.data.pageName == 'Mailings')
        "
        class="container-fluid second-navbar"
    >
        <div class="second-navbar">
            <ul class="nav navbar-nav col-lg-7 col-md-8 col-sm-12 col-xs-12 col-sm-offset-3 col-xs-offset-1 text-center">
                <li class="dropdown">
                    <a class="no-margin" ng-class="{ selected: $root.$state.current.data.pageName == 'Candidates' }" href="#/candidates">
                        {{ 'our_base' | translate }}
                        <span class="counter">{{ $root.objectSizeCand }}</span>
                    </a>
                </li>
                <li class="hidden-sm hidden-xs">
                    <a
                        ng-show="$root.me.recrutRole != 'client'"
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Candidate add' }"
                        href="#/candidate/add"
                    >
                        {{ 'new' | translate }}
                        <span style="text-transform: lowercase">{{ 'candidate' | translate }}</span>
                    </a>
                </li>
                <li class="hidden-sm hidden-xs">
                    <a
                        ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'"
                        ng-click="$root.changeSearchType('add')"
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Tests and forms' }"
                        href="#/candidate/tests"
                    >
                        {{ 'Tests and forms' | translate }}
                    </a>
                </li>
                <li class="hidden-sm hidden-xs">
                    <a
                        ng-show="
                            $root.me.orgParams.enableMailing === 'true' &&
                            $root.me.personParams.enableMailing === 'true' &&
                            ($root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter')
                        "
                        class="mailing-tab"
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Mailings' }"
                        href="#/mailings"
                    >
                        {{ 'Mass mailings' | translate }}
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav col-lg-5 col-md-4 hidden-sm hidden-xs text-right text-right" style="justify-content: flex-end">
                <li
                    id="drop_down_scope"
                    ng-click="showScopeFunc();"
                    class="no-padding pull-right"
                    ng-class="{
                        darkOrangeShadowForScope: $root.scopeActiveObject.name != 'company' && $root.scopeActiveObject.check
                    }"
                    uib-dropdown
                    is-open="status.isopen"
                >
                    <a href="" uib-dropdown-toggle title="{{ 'scope' | translate }}">
                        {{ 'scope' | translate }}:
                        <span ng-show="$root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check">
                            <span>{{ 'entire account' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check">
                            <span>{{ 'only_me1' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check">
                            {{ 'only' | translate }}
                            <span>{{ $root.scopeActiveObject.value.name }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <i
                            ng-click="$event.stopPropagation(); $event.preventDefault();"
                            class="margin-left tooltip-icon"
                            title=""
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-outer"
                            uib-tooltip-html="$root.tooltips.scopeTooltip"
                            aria-hidden="true"
                        ></i>
                    </a>
                    <ul class="dropdown-menu showScopeMenu" uib-dropdown-menu role="menu" scope-directive aria-labelledby="single-button">
                        <h4 class="text-center">{{ 'scope' | translate }}</h4>
                        <p>{{ 'scope_message' | translate }}</p>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkOrg" ng-click="changeScope('company')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">{{ 'entire account' | translate }}</span>
                            <span ng-show="orgs.length > 1" class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12"
                                    ng-model="orgId"
                                    name="region"
                                    ng-options="org.orgId as org.orgName for org in orgs"
                                    ng-change="changeScope('company')"
                                    required=""
                                ></select>
                            </span>
                            <span ng-show="orgs.length < 2" class="col-lg-6 no-margin">
                                <div ng-repeat="org in orgs|filter:{ orgId: orgId }">
                                    {{ org.orgName }}
                                </div>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkRegion" ng-click="changeScope('region')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">
                                <label style="font-size: 14px">{{ 'only_region' | translate }}</label>
                            </span>
                            <span class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12 cs-region-filter-select-scope2"
                                    ng-style="regionListStyle"
                                    ng-model="regionId"
                                    ng-change="changeScopeForRegionSelect('region')"
                                ></select>
                            </span>
                        </div>
                        <div
                            ng-show="$root.me.recrutRole != 'freelancer'"
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkMe" ng-click="changeScope('onlyMy')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-11 no-margin">
                                <span ng-style="$root.currentLang !== 'ru' ? { 'margin-right': '0px' } : null">
                                    {{ 'only_me1' | translate }}
                                </span>
                                <span>
                                    {{ 'only_me2' | translate }}
                                </span>
                            </span>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for clients-->
    <div
        ng-show="
            $root.$state.current.data.pageName == 'Clients' ||
            $root.$state.current.data.pageName == 'Client add' ||
            $root.$state.current.data.pageName == 'Client edit'
        "
        class="container-fluid second-navbar"
    >
        <div class="second-navbar client-second-header">
            <ul class="nav navbar-nav col-lg-6 col-md-6">
                <li class="dropdown">
                    <a class="no-margin" ng-class="{ selected: $root.$state.current.data.pageName == 'Clients' }" href="#/clients">
                        {{ 'Clients' | translate }}
                        <span class="counter">{{ $root.objectSize | clientsCountFormat }}</span>
                    </a>
                </li>
                <li class="dropdown">
                    <a
                        ng-show="$root.me.recrutRole != 'researcher' && $root.me.recrutRole != 'freelancer' && $root.me.recrutRole != 'client'"
                        ng-click="$root.toAdd()"
                        ng-class="{ selected: $root.$state.current.data.pageName == 'Client add' }"
                        href=""
                    >
                        {{ 'New client' | translate }}
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav col-lg-6 col-md-6 hidden-xs hidden-sm text-right" style="justify-content: flex-end">
                <li
                    id="drop_down_scope"
                    ng-click="showScopeFunc();"
                    class="no-padding pull-right"
                    ng-class="{
                        darkOrangeShadowForScope: $root.scopeActiveObject.name != 'company' && $root.scopeActiveObject.check
                    }"
                    uib-dropdown
                    is-open="status.isopen"
                >
                    <a href="" uib-dropdown-toggle title="{{ 'scope' | translate }}">
                        {{ 'scope' | translate }}:
                        <span ng-show="$root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check">
                            <span>{{ 'entire account' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check">
                            <span>{{ 'only_me1' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check">
                            {{ 'only' | translate }}
                            <span>{{ $root.scopeActiveObject.value.name }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <i
                            ng-click="$event.stopPropagation(); $event.preventDefault();"
                            class="margin-left tooltip-icon"
                            title=""
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-outer"
                            uib-tooltip-html="$root.tooltips.scopeTooltip"
                            aria-hidden="true"
                        ></i>
                    </a>
                    <ul class="dropdown-menu showScopeMenu" uib-dropdown-menu role="menu" scope-directive aria-labelledby="single-button">
                        <h4 class="text-center">{{ 'scope' | translate }}</h4>
                        <p>{{ 'scope_message' | translate }}</p>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkOrg" ng-click="changeScope('company')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">{{ 'entire account' | translate }}</span>
                            <span ng-show="orgs.length > 1" class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12"
                                    ng-model="orgId"
                                    name="region"
                                    ng-options="org.orgId as org.orgName for org in orgs"
                                    ng-change="changeScope('company')"
                                    required=""
                                ></select>
                            </span>
                            <span ng-show="orgs.length < 2" class="col-lg-6 no-margin">
                                <div ng-repeat="org in orgs|filter:{ orgId: orgId }">
                                    {{ org.orgName }}
                                </div>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkRegion" ng-click="changeScope('region')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">
                                <label style="font-size: 14px">{{ 'only_region' | translate }}</label>
                            </span>
                            <span class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12 cs-region-filter-select-scope2"
                                    ng-style="regionListStyle"
                                    ng-model="regionId"
                                    ng-change="changeScopeForRegionSelect('region')"
                                ></select>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkMe" ng-click="changeScope('onlyMy')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-11 no-margin">
                                <span ng-style="$root.currentLang !== 'ru' ? { 'margin-right': '0px' } : null">
                                    {{ 'only_me1' | translate }}
                                </span>
                                <span>
                                    {{ 'only_me2' | translate }}
                                </span>
                            </span>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for company-->
    <div
        ng-show="
            $root.$state.current.data.pageName == 'Users' ||
            $root.$state.current.data.pageName == 'Company History' ||
            $root.$state.current.data.pageName == 'Company settings' ||
            $root.$state.current.data.pageName == 'Company users' ||
            $root.$state.current.data.pageName == 'Company employees' ||
            $root.$state.current.data.pageName == 'Employee add' ||
            $root.$state.current.data.pageName == 'Department Catalog' ||
            $root.$state.current.data.pageName == 'Employee User' ||
            $root.$state.current.data.pageName == 'Employee Edit User' ||
            $root.$state.current.data.pageName == 'Custom fields'
        "
        class="container-fluid second-navbar"
    >
        <div class="second-navbar">
            <ul class="nav navbar-nav col-lg-9">
                <li>
                    <a class="no-margin" ng-class="{ selected: $root.$state.current.data.pageName == 'Company users' }" href="#/company/users">
                        {{ 'users' | translate }}
                    </a>
                </li>
                <li class="hidden-sm hidden-xs">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Company History' }" href="#/company/history">
                        {{ 'history_of_action' | translate }}
                    </a>
                </li>
                <li ng-show="$root.me.personParams.enableEmployee == 'Y' && $root.me.orgParams.enableEmployee == 'Y' && $root.hideTariff">
                    <a
                        ng-class="{
                            selected:
                                $root.$state.current.data.pageName == 'Company employees' ||
                                $root.$state.current.data.pageName == 'Employee add' ||
                                $root.$state.current.data.pageName == 'Employee User' ||
                                $root.$state.current.data.pageName == 'Employee Edit User' ||
                                $root.$state.current.data.pageName == 'Department Catalog'
                        }"
                        href="#/company/employees"
                    >
                        {{ 'employees' | translate }}
                    </a>
                </li>
                <li ng-show="$root.me.orgParams.enableEmployee == 'Y' && !$root.hideTariff && $root.me.recrutRole == 'admin'">
                    <a
                        class="disabled"
                        ng-class="{
                            selected:
                                $root.$state.current.data.pageName == 'Company employees' ||
                                $root.$state.current.data.pageName == 'Employee add' ||
                                $root.$state.current.data.pageName == 'Employee User' ||
                                $root.$state.current.data.pageName == 'Employee Edit User' ||
                                $root.$state.current.data.pageName == 'Department Catalog'
                        }"
                        href="#/hr-module-info"
                    >
                        {{ 'hr-module' | translate }}
                    </a>
                </li>
                <li ng-show="$root.me.recrutRole == 'admin' || $root.me.recrutRole == 'recruter'" class="hidden-sm hidden-xs">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Company settings' }" href="#/company/settings">
                        {{ 'Account settings' | translate }}
                    </a>
                </li>
                <li ng-show="$root.me.recrutRole == 'admin'">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Custom fields' }" href="#/company/custom-fields">
                        {{ 'Custom fields' | translate }}
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav col-lg-3 text-right hidden-md hidden-sm hidden-xs">
                <li
                    id="drop_down_scope"
                    ng-click="showScopeFunc();"
                    class="no-padding pull-right"
                    ng-class="{
                        darkOrangeShadowForScope: $root.scopeActiveObject.name != 'company' && $root.scopeActiveObject.check
                    }"
                    uib-dropdown
                    is-open="status.isopen"
                >
                    <a href="" uib-dropdown-toggle title="{{ 'scope' | translate }}">
                        {{ 'scope' | translate }}:
                        <span ng-show="$root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check">
                            <span>{{ 'entire account' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check">
                            <span>{{ 'only_me1' | translate }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <span ng-show="$root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check">
                            {{ 'only' | translate }}
                            <span>{{ $root.scopeActiveObject.value.name }}</span>
                            <i class="fa fa-chevron-down" aria-hidden="true"></i>
                        </span>
                        <i
                            ng-click="$event.stopPropagation(); $event.preventDefault();"
                            class="margin-left tooltip-icon"
                            title=""
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-outer"
                            uib-tooltip-html="$root.tooltips.scopeTooltip"
                            aria-hidden="true"
                        ></i>
                    </a>
                    <ul class="dropdown-menu showScopeMenu" uib-dropdown-menu role="menu" scope-directive aria-labelledby="single-button">
                        <h4 class="text-center">{{ 'scope' | translate }}</h4>
                        <p>{{ 'scope_message' | translate }}</p>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkOrg" ng-click="changeScope('company')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'company' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">{{ 'entire account' | translate }}</span>
                            <span ng-show="orgs.length > 1" class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12"
                                    ng-model="orgId"
                                    name="region"
                                    ng-options="org.orgId as org.orgName for org in orgs"
                                    ng-change="changeScope('company')"
                                    required=""
                                ></select>
                            </span>
                            <span ng-show="orgs.length < 2" class="col-lg-6 no-margin">
                                <div ng-repeat="org in orgs|filter:{ orgId: orgId }">
                                    {{ org.orgName }}
                                </div>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkRegion" ng-click="changeScope('region')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'region' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-5 no-margin">
                                <label style="font-size: 14px">{{ 'only_region' | translate }}</label>
                            </span>
                            <span class="col-lg-6 no-margin">
                                <select
                                    ng-click="$event.stopPropagation();"
                                    class="select2-container form-control input-lg select2 select12 cs-region-filter-select-scope2"
                                    ng-style="regionListStyle"
                                    ng-model="regionId"
                                    ng-change="changeScopeForRegionSelect('region')"
                                ></select>
                            </span>
                        </div>
                        <div
                            class="col-lg-12"
                            ng-class="{
                                scope_background_color: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                            }"
                        >
                            <span class="col-lg-1 no-margin no-padding">
                                <button id="scopeCheckmarkMe" ng-click="changeScope('onlyMy')" class="ui icon button">
                                    <i
                                        class="fa fa-check-square"
                                        ng-class="{
                                            checkmark: $root.scopeActiveObject.name == 'onlyMy' && $root.scopeActiveObject.check
                                        }"
                                        ng-checked="true"
                                        aria-hidden="true"
                                    ></i>
                                </button>
                            </span>
                            <span class="col-lg-11 no-margin">
                                <span ng-style="$root.currentLang !== 'ru' ? { 'margin-right': '0px' } : null">
                                    {{ 'only_me1' | translate }}
                                </span>
                                <span>
                                    {{ 'only_me2' | translate }}
                                </span>
                            </span>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for mailing-->
    <div ng-show="$root.$state.current.data.pageName == 'Mailing'" class="container-fluid second-navbar">
        <div class="second-navbar">
            <ul class="nav navbar-nav col-lg-9">
                <li class="tab hidden-sm hidden-xs">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Mailing' }" href="#/mailing" translate="Mailing create"></a>
                </li>
            </ul>
        </div>
    </div>
    <!--Navbar for mail integration-->
    <div ng-show="$root.$state.current.data.pageName == 'Integration with email'" class="container-fluid second-navbar">
        <div class="second-navbar">
            <ul class="nav navbar-nav col-lg-9">
                <li class="tab hidden-sm hidden-xs">
                    <a ng-class="{ selected: $root.$state.current.data.pageName == 'Integration with email' }" href="#/email-integration">
                        {{ 'Integration with email' | translate }}
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<ui-view ng-if="$root.blockUser != true"></ui-view>
<div ng-show="$root.blockUser">
    <div class="ui vertically devided grid" style="margin-top: 2%">
        <div class="three wide column"></div>
        <div class="ten wide column" style="padding: 0px">
            <div
                class="col-lg-6 col-lg-offset-3 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2 col-xs-10 col-xs-offset-1 clever-item"
                style="padding: 1.6em; border: 3px solid red !important; box-shadow: none !important"
            >
                <div class="padding:1em;line-height: 1.4em;">
                    <div id="blockMessgae" style="font-size: 16px"></div>
                </div>
            </div>
            <div class="clearfix"></div>
        </div>
        <div class="two wide column"></div>
    </div>
    <div class="block-pay">
        <div class="row">
            <div class="col-lg-12">
                <h2 class="text-center">{{ 'Pay for using CleverStaff' | translate }}</h2>
            </div>
            <div class="col-lg-5 leftBar blockPay">
                <div ng-show="errorMessage" class="col-lg-12">
                    <div>{{ errorMessage }}</div>
                </div>
                <div class="col-lg-12 payForMonth">
                    <div id="bilDisabledText" class="hidden">
                        <p>
                            {{ 'CleverStaff is paid for' | translate }} 25
                            {{ 'USD per month for each active user' | translate }}
                        </p>
                        <p>{{ 'Pay1 discount' | translate }}</p>
                    </div>
                    <div id="bilEnabledText" class="hidden">
                        <p>
                            {{ 'CleverStaff is paid for-2' | translate }}
                            <span id="dailyRate"></span>
                            {{ 'USD per day' | translate }}
                            <span id="monthRate"></span>
                            {{ 'USD per month for each active user-2' | translate }}
                        </p>
                        <p>{{ 'Pay2 discount' | translate }}</p>
                    </div>
                    <div class="payFor">
                        <div class="checkoutSelectOuter">
                            <div class="checkoutInner">
                                <h4 class="payFor floatLeft">{{ 'Pay for' | translate }}</h4>
                            </div>
                            <div class="checkoutInner word floatRight">
                                <div class="paySelectOuter pull-left">
                                    <span class="ammount floatLeft">{{ 'Users2' | translate }}</span>
                                    <br />
                                    <select id="countPeople" class="paySelect" select-with-scroll active-Class="'active'" scroll-size="10">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                        <option>4</option>
                                        <option>5</option>
                                        <option>6</option>
                                        <option>7</option>
                                        <option>8</option>
                                        <option>9</option>
                                        <option>10</option>
                                        <option>11</option>
                                        <option>12</option>
                                        <option>13</option>
                                        <option>14</option>
                                        <option>15</option>
                                        <option>16</option>
                                        <option>17</option>
                                        <option>18</option>
                                        <option>19</option>
                                        <option>20</option>
                                        <option>21</option>
                                        <option>22</option>
                                        <option>23</option>
                                        <option>24</option>
                                        <option>25</option>
                                    </select>
                                </div>
                                <div class="paySelectOuter pull-left">
                                    <span class="ammount ammountMonth floatLeft">{{ 'Months2' | translate }}</span>
                                    <br />
                                    <select id="countMonth" class="paySelect" select-with-scroll active-Class="'active'" scroll-size="12">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                        <option>4</option>
                                        <option>5</option>
                                        <option>6</option>
                                        <option>7</option>
                                        <option>8</option>
                                        <option>9</option>
                                        <option>10</option>
                                        <option>11</option>
                                        <option>12</option>
                                    </select>
                                </div>
                            </div>
                            <div class="payButtonOuter pull-right">
                                <span id="priceWOrd" class="payPrice">{{ 'Amount' | translate }}:</span>
                                <span id="price" class="payPrice">{{ price }} USD</span>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                    <div class="creditCards">
                        <img class="pull-left" src="../images/sprite/visa-mastercard.png" width="140" height="40" alt="" />
                        <div class="text-center pull-right">
                            <p id="bonuce" class="hidden">
                                {{ 'The balance of your company account is' | translate }}
                                <span id="amountBonus"></span>
                                {{ 'including' | translate }}
                                {{ $root.bonuce + '%' }}
                                {{ 'of the bonus' | translate }}
                            </p>
                            <a id="payButton" ng-click="payClick()" class="btn btn-primary accept ng-binding" type="button">
                                {{ 'Go to payment service' | translate }}
                            </a>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</div>
