<div class="status-block">
    <div class="status-block-wrapper">
        <div ng-if="status === 'authorized'" class="status-block-main">
            <div class="status-block-title">{{ 'Congratulations' | translate }}!</div>
            <div class="logoCs">
                <img src="/external/images/assets/party-popper.svg" alt="" />
            </div>
            <p>{{ 'Linkedin account is integrated' | translate }}!</p>
            <div style="text-align: center">
                <p>{{ 'In the future, publication will occur automatically' | translate }}</p>
                <p>{{ "by clicking 'Post to LinkedIn' button on the vacancy page" | translate }}.</p>
            </div>
        </div>
        <div ng-if="status === 'vacancyPosted'" class="status-block-main">
            <div class="status-block-title">{{ 'Congratulations' | translate }}!</div>
            <div class="logoCs">
                <img src="/external/images/assets/party-popper.svg" alt="" />
            </div>
            <p>{{ 'Vacancy posted to your Linkedin account' | translate }}</p>
            <div style="text-align: center">
                <p>{{ 'In the future, publication will occur automatically' | translate }}</p>
                <p>{{ "by clicking 'Post to LinkedIn' button on the vacancy page" | translate }}.</p>
            </div>
        </div>

        <div ng-if="status === 'error'" class="status-block-main">
            <div class="status-block-title">{{ 'That is an error' | translate }}!</div>
            <svg xmlns="http://www.w3.org/2000/svg" height="32" viewBox="0 0 24 24" width="32">
                <path d="M0 0h24v24H0z" fill="none" />
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
            </svg>
            <p>{{ 'Please try again' | translate }}.</p>
        </div>
    </div>
</div>
