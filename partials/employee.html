<div class="block-employee">
    <div class="breadcrumbs hidden-xs hidden-sm">
        <ol class="breadcrumb">
            <li>
                <a href="#/company/employees">{{ 'employees' | translate }}</a>
            </li>
            <li class="active">{{ pageObject.employee.candidateId.fullName }}</li>
        </ol>
    </div>
    <div class="row employee-information">
        <div class="row employee-profile-rezume">
            <div class="col-lg-3 leftBar with-box-shadow">
                <div ng-if="pageObject.employee.candidateId.photo" class="col-lg-12 employee-foto block-user">
                    <div class="photo-block">
                        <img alt="" ng-src="{{ serverAddress }}/getapp?id={{ pageObject.employee.candidateId.photo }}&d={{ $root.me.personId }}" />
                    </div>
                </div>

                <div class="employee-info" style="margin-top: 10px">
                    <span class="employee-info-name">
                        {{
                            pageObject.employee.candidateId.lastName +
                                ' ' +
                                pageObject.employee.candidateId.firstName +
                                ' ' +
                                pageObject.employee.candidateId.middleName
                        }}
                    </span>
                    <p class="employee-info-position">{{ pageObject.employee.position }}</p>
                </div>

                <div class="col-lg-12 employee-control">
                    <div class="form-title-main">{{ 'Status' | translate }}</div>
                    <form class="form-inline-position">
                        <div class="select">
                            <custom-select-new-status
                                hide-placeholder-from-options="true"
                                custom-status-styles="true"
                                method="onChangeEmployeeStatus"
                                model="employeeStatus"
                                options="pageObject.employeeStatus"
                                placeholder="'status'"
                            ></custom-select-new-status>
                        </div>
                    </form>
                </div>

                <div class="flex-row flex-block flex-column attachments not-for-print" uib-dropdown is-open="status.isopen">
                    <div class="form-title-main">{{ 'Attachments' | translate }}</div>
                    <div
                        ng-show="pageObject.employee.candidateId.files"
                        class="flex-align-center flex-space-between flex-item-block file-item attachments__item"
                        ng-repeat="file in pageObject.employee.candidateId.files | orderBy:'-url'"
                    >
                        <div ng-show="file.showEditFileName" class="editFileName">
                            <h5 ng-show="file.showEditFileName" class="form-title">{{ 'Change file name' | translate }}</h5>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <div style="display: flex; align-items: center">
                                    <img ng-show="!file.url" style="margin-right: 5px" src="images/sprite/clip.svg?b2" />
                                    <input
                                        id="showEditFile"
                                        ng-show="file.showEditFileName"
                                        class="form-control"
                                        ng-model="file.fileName"
                                        type="text"
                                        name="name"
                                        value="{{ file.fileName | limitTo : 15 }}"
                                    />
                                </div>
                                <div style="margin-left: 10px">
                                    <button ng-show="file.showEditFileName" ng-click="editFileName(file)" class="btn_default btn_thin btn_success">
                                        {{ 'save' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div ng-show="file.fileId" class="fileName">
                            <a ng-show="!file.url && !file.showEditFileName" ng-click="showModalResume(file)" title="{{ file.fileName }}">
                                <img style="cursor: pointer" src="images/sprite/clip.svg?b2" />
                                <span style="color: #666666; cursor: pointer; word-break: break-word">{{ file.fileName | fileNameCut : 0 : 25 }}</span>
                            </a>
                            <a ng-show="file.url && !file.showEditFileName" href="{{ file.url }}" target="_blank" title="{{ file.fileName }}">
                                <img style="height: 13px; width: 13px; cursor: pointer" src="images/sprite/link.svg" />
                                <span style="color: #666666; cursor: pointer; word-break: break-word">{{ file.fileName | limitTo : 20 }}</span>
                                <span ng-show="file.fileName.length > 20">...</span>
                            </a>
                        </div>
                        <i
                            ng-show="!file.showEditFileName"
                            ng-click="MenuEdDelFile(file)"
                            class="fa fa-chevron-down pull-right"
                            style="font-size: 11px; color: black"
                            aria-hidden="true"
                        >
                            <div ng-show="file.showMenuEdDelFile" class="flex-item-block editFileMenu">
                                <div class="ui middle aligned selection list">
                                    <a
                                        ng-show="!file.url"
                                        ng-click="$root.downloadFile(file.fileId, file.fileName)"
                                        class="text-decoration-none"
                                        style="cursor: pointer"
                                    >
                                        <svg
                                            id="Capa_1"
                                            style="enable-background: new 0 0 477.867 477.867"
                                            fill="#00B549"
                                            height="13px"
                                            width="13px"
                                            version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px"
                                            y="0px"
                                            viewBox="0 0 477.867 477.867"
                                            xml:space="preserve"
                                        >
                                            <g>
                                                <g>
                                                    <path
                                                        d="M443.733,307.2c-9.426,0-17.067,7.641-17.067,17.067v102.4c0,9.426-7.641,17.067-17.067,17.067H68.267
                                                                    c-9.426,0-17.067-7.641-17.067-17.067v-102.4c0-9.426-7.641-17.067-17.067-17.067s-17.067,7.641-17.067,17.067v102.4
                                                                    c0,28.277,22.923,51.2,51.2,51.2H409.6c28.277,0,51.2-22.923,51.2-51.2v-102.4C460.8,314.841,453.159,307.2,443.733,307.2z"
                                                    />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <path
                                                        d="M335.947,295.134c-6.614-6.387-17.099-6.387-23.712,0L256,351.334V17.067C256,7.641,248.359,0,238.933,0
                                                                    s-17.067,7.641-17.067,17.067v334.268l-56.201-56.201c-6.78-6.548-17.584-6.36-24.132,0.419c-6.388,6.614-6.388,17.099,0,23.713
                                                                    l85.333,85.333c6.657,6.673,17.463,6.687,24.136,0.031c0.01-0.01,0.02-0.02,0.031-0.031l85.333-85.333
                                                                    C342.915,312.486,342.727,301.682,335.947,295.134z"
                                                    />
                                                </g>
                                            </g>
                                        </svg>
                                        <span class="editFileMenuTitle">{{ 'Download file' | translate }}</span>
                                    </a>
                                    <div ng-click="showEditFileNameFunc(file)" class="item" style="border-radius: 5px; padding: 1px 0px">
                                        <div class="content">
                                            <div ng-show="!file.url" ng-click="editFile(file.fileId)" class="header pencilHover">
                                                <svg width="13" height="12" fill="#00B549" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Change file name' | translate }}</span>
                                            </div>
                                            <div ng-show="file.url" ng-click="editFile(file.fileId)" class="header pencilHover">
                                                <svg width="13" fill="#00B549" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Change link name' | translate }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-click="removeFile(file.fileId)" class="item" style="border-radius: 5px; padding: 1px 0px">
                                        <div class="content">
                                            <div ng-show="!file.url" class="header">
                                                <svg width="12" height="12" viewBox="0 0 12 12" fill="#00B549" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M11.25 1.8075L10.1925 0.75L6 4.9425L1.8075 0.75L0.75 1.8075L4.9425 6L0.75 10.1925L1.8075 11.25L6 7.0575L10.1925 11.25L11.25 10.1925L7.0575 6L11.25 1.8075Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Delete file' | translate }}</span>
                                            </div>
                                            <div ng-show="file.url" class="header">
                                                <svg width="12" height="12" viewBox="0 0 12 12" fill="#00B549" xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M11.25 1.8075L10.1925 0.75L6 4.9425L1.8075 0.75L0.75 1.8075L4.9425 6L0.75 10.1925L1.8075 11.25L6 7.0575L10.1925 11.25L11.25 10.1925L7.0575 6L11.25 1.8075Z"
                                                        fill="#00B549"
                                                    />
                                                </svg>
                                                <span class="editFileMenuTitle">{{ 'Delete link' | translate }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </i>
                    </div>
                    <div class="text-center" style="display: flex; justify-content: flex-start; align-items: center">
                        <img style="margin-right: 5px" src="images/sprite/clip.svg?b2" alt="" />
                        <a class="text-center" style="color: #c0c0c0" href="" uib-dropdown-toggle>
                            {{ 'Attach file and link' | translate }}
                        </a>
                    </div>
                    <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                        <input id="file" class="hidden" type="file" oi-file="options" />
                        <label class="dropdown-menu__item" style="margin: 0" role="menuitem" for="file">
                            <img style="cursor: pointer; padding-left: 9px; vertical-align: middle" src="images/sprite/clip.svg?b2" alt="" />
                            <span class="dropdown-menu__title" style="margin-left: 2px">{{ 'Attach file' | translate }}</span>
                        </label>
                        <li class="divider"></li>
                        <label ng-click="showAddLinkFunc()" class="dropdown-menu__item" style="margin: 0" role="menuitem">
                            <img style="height: 13px; width: 13px; margin-left: 9px" src="images/sprite/link.svg" alt="" />
                            <span class="dropdown-menu__title" style="margin-left: 2px">{{ 'Attach link' | translate }}</span>
                        </label>
                    </ul>
                    <div ng-show="showAddLink" class="col-lg-12 addLinkFormForLink" style="margin-top: 10px">
                        <div style="margin-bottom: 10px">
                            <form name="addLinkForm">
                                <label for="nameLink">
                                    {{ 'Title' | translate }}
                                    <span class="form-title-star">*</span>
                                </label>
                                <br />
                                <input
                                    id="nameLink"
                                    class="form-control"
                                    ng-class="{ errorInput: addLinkErrorShow && addLinkToCandidate.name.length == 0 }"
                                    ng-model="addLinkToCandidate.name"
                                    name="name"
                                    type="text"
                                />
                                <br />
                                <label style="margin-top: 5px" for="urlLink">
                                    URL
                                    <span class="form-title-star">*</span>
                                </label>
                                <br />
                                <input
                                    id="urlLink"
                                    class="form-control"
                                    ng-class="{ errorInput: (addLinkErrorShow && addLinkToCandidate.url.length == 0) || addLinkForm.url.$error.url }"
                                    ng-model="addLinkToCandidate.url"
                                    name="url"
                                    type="text"
                                />
                            </form>
                        </div>
                        <div style="margin-top: 10px; display: flex; justify-content: space-between; width: 100%">
                            <button ng-click="closeAddLinkFunc()" class="btn_empty btn_default btn_thin" style="min-width: 100px">
                                {{ 'cancel' | translate }}
                            </button>
                            <button ng-click="addLinkInCandidate()" class="btn_success btn_default btn_thin" style="min-width: 100px">
                                {{ 'add' | translate }}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="block-remove-employee text-center">
                    <div class="remove-employee" style="display: flex; justify-content: center">
                        <span ng-click="showModalRemoveEmployee()" class="btn_default btn_thin btn_error">
                            {{ 'Remove employee' | translate }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-lg-9 centerBar" style="padding-left: 10px">
                <div class="main-info col-lg-12 with-box-shadow without-bottom-divider block-buttons" style="margin-bottom: 10px">
                    <span style="margin-right: 10px">
                        <img
                            ng-click="toEdit()"
                            class="actions-block-image"
                            alt="{{ 'edit_profile' | translate }}"
                            src="../images/sprite/edit.svg"
                            title="{{ 'edit_profile' | translate }}"
                        />
                    </span>
                    <span ng-click="showModalAddCommentToEmployee()" href="">
                        <img
                            class="actions-block-image"
                            style="position: relative; bottom: 1px; width: auto; height: 30px"
                            alt="{{ 'add_comment' | translate }}"
                            title="{{ 'add_comment' | translate }}"
                            src="../images/sprite/addMessage.svg"
                        />
                    </span>
                </div>

                <div
                    class="main-info col-lg-12 with-box-shadow without-bottom-divider"
                    ng-class="{ 'without-bottom-divider': !pageObject.employee.candidateId.descr && !pageObject.employee.candidateId.coreSkills }"
                    style="margin-bottom: 10px; border-bottom: none; border-radius: 5px"
                >
                    <div class="col-lg-3" style="padding-left: 23px">
                        <div ng-show="pageObject.employee.localId" class="dateOfWork">
                            {{ 'Employee id' | translate }}:
                            <span class="dateOfWorkBold">{{ pageObject.employee.localId }}</span>
                        </div>
                        <div
                            ng-show="pageObject.employee.position"
                            class="dateOfWork info phone"
                            style="height: auto; display: flex; align-items: flex-start; gap: 3px; margin-bottom: 5px"
                            href=""
                        >
                            <img class="main-info--icon" width="16px" alt="" src="images/sprite/currentPosition.svg" />
                            {{ pageObject.employee.position }}
                        </div>
                        <div ng-show="pageObject.employee.candidateId.region" class="dateOfWork info">
                            <img class="main-info--icon" style="width: 16px" src="images/sprite/location-grey.svg" alt="" />
                            {{ pageObject.employee.candidateId.region.googlePlaceId | translateLocation }}
                        </div>
                        <div
                            ng-show="pageObject.employee.candidateId != undefined && pageObject.employee.candidateId.db"
                            class="dateOfWork info phone"
                            class="phone"
                            href=""
                            title="{{ 'date_of_birth' | translate }}"
                        >
                            <img class="main-info--icon" width="16px" alt="" src="images/sprite/candidate/db.svg" />
                            <span>{{ pageObject.employee.candidateId.db | dateFormatSimpleWithOutUTC | translate }}</span>
                            <span style="color: #828282">
                                ({{ pageObject.employee.candidateId.db | YearsSinceDate }}&nbsp;{{
                                    pageObject.employee.candidateId.db | YearsEndingForm | translate
                                }})
                            </span>
                        </div>
                        <div ng-show="pageObject.employee.salary" class="dateOfWork info phone" class="phone" href="">
                            <img class="main-info--icon" src="images/sprite/candidate/salaryCand.svg" alt="" />
                            {{ pageObject.employee.salary }} {{ pageObject.employee.currency }}
                        </div>
                        <div ng-show="pageObject.employee.employeeDepartment" class="dateOfWork info phone" class="phone" href="">
                            <img class="main-info--icon" src="images/sprite/currentWorkPlace.svg" alt="" />
                            {{ pageObject.employee.employeeDepartment.name }}
                        </div>
                        <div
                            ng-show="pageObject.employee.candidateId.industry"
                            class="dateOfWork info phone"
                            class="phone"
                            style="height: auto; display: flex; align-items: flex-start; gap: 3px; margin-bottom: 5px"
                            href=""
                        >
                            <img class="main-info--icon" src="images/sprite/candidate/industryCand.svg" alt="" />
                            {{ pageObject.employee.candidateId.industry | translate }}
                        </div>
                        <div ng-show="pageObject.employee.candidateId.expirence" class="dateOfWork info phone" class="phone" href="">
                            <img class="main-info--icon" style="margin-right: 12px" src="images/sprite/candidate/experienceCand.svg" alt="" />
                            {{ pageObject.employee.candidateId.expirence | translate }}
                        </div>
                        <div ng-show="pageObject.employee.candidateId.education" class="dateOfWork info phone" class="phone" href="">
                            <svg
                                id="Capa_1"
                                style="enable-background: new 0 0 398.97 398.97"
                                height="16px"
                                width="16px"
                                fill="#838286"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                                x="0px"
                                y="0px"
                                viewBox="0 0 398.97 398.97"
                                xml:space="preserve"
                            >
                                <g>
                                    <g id="Layer_5_59_">
                                        <g>
                                            <path
                                                d="M225.092,276.586c-7.101,4.355-16.195,6.754-25.607,6.754c-9.412,0-18.506-2.398-25.607-6.754L57.946,205.484
                                                c0,0-10.464-6.414-10.464,8.116c0,16.5,0,66,0,66c0,0.66,0,3.973,0,5c0,37.402,68.053,77.723,152,77.723s152-40.32,152-77.723
                                                c0-1.027,0-4.34,0-5c0,0,0-52.502,0-70.003c0-11.663-7.439-5.964-7.439-5.964L225.092,276.586z"
                                            />
                                            <path
                                                d="M392.289,148.927c8.908-5.463,8.908-14.403,0-19.867L215.681,40.745c-8.908-5.463-23.485-5.463-32.393,0L6.681,129.06
                                                c-8.908,5.463-8.908,14.403,0,19.867l176.607,108.315c8.908,5.463,23.485,5.463,32.393,0"
                                            />
                                            <path
                                                d="M384.985,309.475c0-29.906,0-119.625,0-119.625s0.083-5.666-3.279-3.795c-2.697,1.501-9.308,5.186-11.637,7.212
                                                c-2.689,2.337-2.083,7.583-2.083,7.583s0,81.469,0,108.625c0,1.542-1.325,2.278-1.957,2.65
                                                c-6.105,3.589-10.21,10.214-10.21,17.809c0,11.414,9.252,20.667,20.667,20.667c11.414,0,20.666-9.253,20.666-20.667
                                                c0-7.624-4.135-14.27-10.279-17.85C386.262,311.728,384.985,311.016,384.985,309.475z"
                                            />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <span style="margin-left: 9px">{{ pageObject.employee.candidateId.education | translate }}</span>
                        </div>
                        <div
                            ng-show="pageObject.employee.candidateId.languages.length"
                            class="dateOfWork info phone"
                            class="phone"
                            style="height: auto; display: flex; align-items: flex-start; gap: 3px; margin-bottom: 5px"
                            href=""
                        >
                            <img class="main-info--icon" src="images/sprite/candidate/langsCand.svg" alt="" />
                            <div class="flex-column">
                                <div ng-repeat="lang in pageObject.employee.candidateId.languages">
                                    <span>{{ lang.name | translate }}</span>
                                    -
                                    <span>{{ lang.level | translate }}</span>
                                </div>
                            </div>
                        </div>

                        <div
                            ng-show="pageObject.employee.candidateId.employmentType"
                            class="dateOfWork info phone"
                            class="phone"
                            style="height: auto; display: flex; align-items: flex-start; gap: 5px; margin-top: 3px"
                            href=""
                        >
                            <img class="main-info--icon" style="margin-left: 2px" src="images/sprite/candidate/employmentTypeCand.svg" alt="" />
                            <div>
                                <div
                                    style="display: flex; flex-direction: column"
                                    ng-repeat="emplType in pageObject.employee.candidateId.employmentType.split(',') track by $index"
                                >
                                    <span>{{ emplType.trim() | translate }}{{ $last ? '' : ',' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 copyable-contacts">
                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'mphone').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'mphone'"
                            title="{{ contact.value }}"
                        >
                            <a class="phone dateOfWork">
                                <img class="main-info--icon" src="images/sprite/contact-phone.svg" alt="" />
                                {{ contact.value }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>

                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'skype').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'Skype'"
                        >
                            <a class="dateOfWork" style="cursor: pointer" title="{{ contact.value }}">
                                <img class="main-info--icon" width="16px" src="images/sprite/contact-skype.svg" alt="" />
                                {{ contact.value }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>

                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'email').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'email'"
                        >
                            <a class="dateOfWork" ng-href="mailto:{{ contact.value }}" title="{{ contact.value }}">
                                <img class="main-info--icon" src="images/sprite/contact-email.svg" alt="" />
                                {{ contact.value }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>

                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'linkedin').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'linkedin'"
                        >
                            <a class="dateOfWork" target="_blank" ng-href="{{ contact.value | makeLink }}" title="{{ contact.value }}">
                                <img class="main-info--icon" src="images/sprite/contact-linkedin.svg" alt="" />
                                {{ contact.value | cutName : true : 30 }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>

                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'facebook').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'facebook'"
                        >
                            <a class="dateOfWork" target="_blank" ng-href="{{ contact.value | makeLink }}" title="{{ contact.value }}">
                                <img class="main-info--icon" width="16px" src="images/sprite/contact-facebook.svg" alt="" />
                                {{ contact.value | cutName : true : 30 }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>

                        <div
                            ng-show="(pageObject.employee.candidateId.contacts|filter:type = 'homepage').length > 0"
                            class="contact-item-wrapper"
                            ng-repeat="contact in pageObject.employee.candidateId.contacts|filter:type = 'homepage'"
                        >
                            <a class="dateOfWork" target="_blank" ng-href="{{ contact.value | makeLink }}" title="{{ contact.value }}">
                                <img class="main-info--icon" width="16px" src="images/sprite/contact-homepage.svg" alt="" />
                                {{ contact.value | cutName : true : 30 }}
                            </a>
                            <copy-to-clipboard text="contact.value"></copy-to-clipboard>
                        </div>
                    </div>
                    <div class="col-lg-5">
                        <div ng-show="pageObject.employee" class="dateOfWork">
                            <span>{{ 'Date of the work in the department' | translate }}</span>
                            <span class="dateOfWorkBold">{{ pageObject.employee.dateDepartment | dateFormat }}</span>
                            <br />
                        </div>
                        <div ng-show="pageObject.employee" class="dateOfWork">
                            <span>{{ 'Date of employment' | translate }}</span>
                            <span class="dateOfWorkBold">{{ pageObject.employee.dateEmployee | dateFormat }}</span>
                            <br />
                        </div>
                        <div ng-show="pageObject.employee" class="dateOfWork">
                            <span>{{ 'Date of admission to the post' | translate }}</span>
                            <span class="dateOfWorkBold">{{ pageObject.employee.datePosition | dateFormat }}</span>
                            <br />
                        </div>
                        <div ng-show="pageObject.employee && pageObject.employee.state != 'dismiss'" class="dateOfWork">
                            <span>{{ 'Salary date' | translate }}</span>
                            <span class="dateOfWorkBold">{{ pageObject.employee.dateSalary | dateFormat }}</span>
                            <br />
                        </div>
                        <div ng-show="pageObject.employee && pageObject.employee.state == 'dismiss'" class="dateOfWork">
                            <span>{{ 'dismiss' | translate }}</span>
                            <span class="dateOfWorkBold">{{ pageObject.employee.dateDismiss | dateFormat }}</span>
                        </div>
                    </div>
                </div>
                <div
                    ng-if="pageObject.employee.candidateId.coreSkills || pageObject.employee.candidateId.descr"
                    class="col-lg-12 skills-block"
                    style="margin-bottom: 10px"
                >
                    <div ng-if="pageObject.employee.candidateId.coreSkills" style="margin-bottom: 30px">
                        <div class="form-title-main">{{ 'core_skills' | translate }}</div>
                        <p ng-bind-html="pageObject.employee.candidateId.coreSkills"></p>
                    </div>
                    <div ng-if="pageObject.employee.candidateId.descr" class="">
                        <div class="form-title-main">{{ 'description' | translate }}</div>
                        <p ng-bind-html="pageObject.employee.candidateId.descr"></p>
                    </div>
                </div>
                <div class="col-lg-12 history-outer" style="margin-top: 0">
                    <table id="mainTable" class="table with-box-shadow" ng-table="tableParamsEmployeeHistory" template-pagination="custom/pager">
                        <thead>
                            <tr class="last-actions-header grey-header">
                                <th class="title-header border border-radius-top" ng-class="{ 'border-radius-bottom': !onlyComments }">
                                    <div style="display: flex; justify-content: space-between; align-items: center">
                                        <div>
                                            <span class="pull-left">{{ 'Activity_Log' | translate }}</span>
                                            <div class="switch pull-left">
                                                <custom-switcher
                                                    switcher-id="'employee'"
                                                    first-label="'Show comments only'"
                                                    second-label="'Show details'"
                                                    background="true"
                                                    checked="onlyComments"
                                                    method="showCommentsSwitch"
                                                ></custom-switcher>
                                            </div>
                                        </div>

                                        <span ng-click="showModalAddCommentToEmployee()" href="">
                                            <img
                                                class="actions-block-image"
                                                style="position: relative; bottom: 1px; width: auto; height: 30px"
                                                src="../images/sprite/addMessage.svg"
                                                alt="{{ 'add_comment' | translate }}"
                                                title="{{ 'add_comment' | translate }}"
                                            />
                                        </span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                class="event last-action border"
                                ng-class="{ 'not-for-print': history.type != 'candidate_message' }"
                                ng-repeat-start="history in history"
                            >
                                <td class="border" ng-switch="history.type">
                                    <div class="col-lg-12 padding-left" ng-switch-when="setSalary">
                                        <ng-include src="'partials/history/employee/setSalary.html'"></ng-include>
                                    </div>
                                    <div class="col-lg-12 padding-left" ng-switch-when="setPosition">
                                        <ng-include src="'partials/history/employee/setPosition.html'"></ng-include>
                                    </div>
                                    <div class="col-lg-12 padding-left" ng-switch-when="setDepartment">
                                        <ng-include src="'partials/history/employee/setDepartment.html'"></ng-include>
                                    </div>
                                    <div class="col-lg-12 padding-left" ng-switch-when="setState">
                                        <ng-include src="'partials/history/employee/setState.html'"></ng-include>
                                    </div>
                                    <div class="col-lg-12 padding-left" ng-switch-when="comment">
                                        <ng-include src="'partials/history/employee/comment.html'"></ng-include>
                                    </div>
                                </td>
                            </tr>
                            <tr class="table-space" ng-hide="$last" ng-repeat-end=""></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div id="scrollup"><img alt="Прокрутить вверх" src="/images/sprite/up_racket.png" /></div>
</div>
