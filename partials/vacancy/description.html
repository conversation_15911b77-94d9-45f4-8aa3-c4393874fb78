<div class="row main-info block-vacancy-descr">
    <div class="block-vacancy-descr-wrapper">
        <div class="vacancy-description">
            <div class="vacancy-description-main-info" style="margin-top: 0">
                <div class="vacancy-description-main-info-row" style="margin-top: 0">
                    <div class="vacancy-description-main-info-block">
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'employment_type' | translate }}</span>
                            <div ng-if="vm.vacancy.employmentType" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame.svg" alt="" />
                                <div ng-if="vm.vacancy.employmentType">
                                    {{ 'employment_type_assoc.' + vm.vacancy.employmentType | translate }}
                                </div>
                            </div>
                            <div ng-if="!vm.vacancy.employmentType" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div
                            ng-if="vm.vacancy.region.displayFullName"
                            ng-click="vm.showRegion2Map = !vm.showRegion2Map"
                            class="vacancy-description-main-info-row-item"
                        >
                            <span class="second-title">{{ 'Location' | translate }}</span>
                            <div class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-4.svg" alt="" />
                                <span>{{ vm.vacancy.region.googlePlaceId | translateLocation }}</span>
                            </div>
                            <div ng-if="!vm.vacancy.region.displayFullName && vm.vacancy.employmentType !== 'remote'" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-4.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'salary' | translate }}</span>
                            <div
                                ng-show="
                                    (vm.vacancy.salaryFrom || vm.vacancy.salaryTo) &&
                                    ($root.me.personParams.hideSalariesVacancy == undefined || $root.me.personParams.hideSalariesVacancy == 'N') &&
                                    ($root.me.recrutRole !== 'client' || $root.me.personParams.showSalaryHm === 'Y')
                                "
                                class="main-info-item-wrapper"
                            >
                                <img src="images/redesign/vacancy-description/Frame-8.svg" alt="" />
                                <span style="width: max-content">{{ vm.vacancy.salaryFrom | salaryFormat : vm.vacancy.salaryTo | numberSpacing }}</span>
                                <div ng-show="vm.vacancy.salaryFrom || vm.vacancy.salaryTo" class="double-eyes-wrapper" style="position: relative">
                                    {{ vm.vacancy.currency }}
                                </div>
                                <div
                                    class="double-eyes hint-info-icon__grey"
                                    style="display: block"
                                    ng-style="!vm.hideSalary ? { 'background-position': '-20px 0px' } : null"
                                    tooltip-placement="top-left"
                                    tooltip-class="tooltip-info-black"
                                    uib-tooltip="{{
                                        vm.hideSalary
                                            ? ('Salary isn`t displayed on the public vacancy page' | translate)
                                            : ('Salary is displayed on the public vacancy page' | translate)
                                    }}"
                                ></div>
                            </div>
                            <div
                                ng-show="
                                    !(
                                        (vm.vacancy.salaryFrom || vm.vacancy.salaryTo) &&
                                        ($root.me.personParams.hideSalariesVacancy == undefined || $root.me.personParams.hideSalariesVacancy == 'N') &&
                                        ($root.me.recrutRole !== 'client' || $root.me.personParams.showSalaryHm === 'Y')
                                    )
                                "
                                class="main-info-item-wrapper"
                            >
                                <img src="images/redesign/vacancy-description/Frame-8.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="vacancy-description-main-info-block">
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'Number of positions' | translate }}</span>
                            <div ng-if="vm.vacancy.numberOfPositions" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-1.svg" alt="" />
                                <div>{{ vm.vacancy.numberOfPositions }}</div>
                            </div>
                            <div ng-if="!vm.vacancy.numberOfPositions" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-1.svg" alt="" />
                                <div ng-if="!vm.vacancy.numberOfPositions">{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'deadline' | translate }}</span>
                            <div ng-show="vm.vacancy.dateFinish" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-5.svg" alt="" />
                                <div>{{ vm.vacancy.dateFinish | dateFormatSimpleExcelHistory }}</div>
                            </div>
                            <div ng-show="!vm.vacancy.dateFinish" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-5.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'Recall-template' | translate }}</span>
                            <div class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/text.svg" />
                                <span ng-if="!vm.vacancy.recallTemplate.default">{{ vm.vacancy.recallTemplate.templateName }}</span>
                                <span ng-if="vm.vacancy.recallTemplate.default">{{ 'Default-recall-template' | translate }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="vacancy-description-main-info-block">
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'industry' | translate }}</span>
                            <div ng-if="vm.vacancy.category" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-2.svg" alt="" />
                                <div>IT,</div>
                                <div style="margin-left: 5px">{{ vm.vacancy.category | bigFirstLetterCategory }}</div>
                            </div>
                            <div ng-if="!vm.vacancy.category" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-2.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item" style="margin-bottom: 20px">
                            <span class="second-title">{{ 'Score card' | translate }}</span>
                            <div class="main-info-item-wrapper" style="align-items: start">
                                <img src="images/redesign/vacancy-description/Frame-6.svg" alt="" />
                                <div class="score-card-block">
                                    <div
                                        ng-click="vm.showScoreCardPreviewPopup(card)"
                                        class="cardName"
                                        ng-repeat="card in vm.vacancy.scoreCards track by $index"
                                    >
                                        <span></span>
                                        <span ng-hide="card.name === 'Default' || card.name === 'Old Default'">
                                            {{ card.name }}
                                        </span>
                                        <span ng-show="card.name === 'Default' || card.name === 'Old Default'">
                                            {{ card.name | translate }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="vacancy-description-main-info-block">
                        <div ng-if="vm.vacancy.creator.userId" class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'Created_vacancy' | translate }}</span>
                            <div class="main-info-item-wrapper" style="align-items: start">
                                <img src="images/redesign/vacancy-description/Frame-3.svg" alt="" />
                                <div class="dateCreator">
                                    <span>{{ (vm.vacancy.dc | dateFormatSimple).trim() }}</span>
                                    <a class="creator_link" href="#/users/{{ vm.vacancy.creator.userId }}">
                                        {{ $root.useAmericanNameStyle ? vm.vacancy.creator.cutFullNameEn : vm.vacancy.creator.cutFullName }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'budget' | translate }}</span>
                            <div ng-if="vm.vacancy.budget" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-7.svg" alt="" />
                                <div>{{ vm.vacancy.budget }}</div>
                            </div>
                            <div ng-if="!vm.vacancy.budget" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-7.svg" alt="" />
                                <div ng-if="!vm.vacancy.budget">{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                        <div class="vacancy-description-main-info-row-item">
                            <span class="second-title">{{ 'payment' | translate }}</span>
                            <div ng-if="vm.vacancy.datePayment" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-9.svg" alt="" />
                                <div>{{ vm.vacancy.datePayment | dateFormatSimpleExcelHistory }}</div>
                            </div>
                            <div ng-if="!vm.vacancy.datePayment" class="main-info-item-wrapper">
                                <img src="images/redesign/vacancy-description/Frame-9.svg" alt="" />
                                <div>{{ 'emptyValue' | translate }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="vacancy-description-content-wrapper">
                <div class="vacancy-description-content-left" style="width: 100%; padding-right: 0">
                    <div
                        ng-if="vm.vacancy.experience || vm.vacancy.skills.length || vm.vacancy.languages.length || vm.vacancy.role"
                        class="vacancy-description-main-info"
                    >
                        <span class="form-title-main">{{ 'Requirements for a candidate' | translate }}</span>
                        <div class="vacancy-description-main-info-row">
                            <div class="vacancy-description-main-info-row-item">
                                <span class="second-title">{{ 'requiredSkills' | translate }}</span>
                                <div ng-if="vm.vacancy.requiredSkills.length" style="display: flex; flex-direction: column">
                                    <span style="margin-bottom: 5px" ng-repeat="skill in vm.vacancy.requiredSkills track by $index">
                                        <span ng-if="skill.experience !== 'e00_no_experience'">
                                            {{ skill.skillName | bigFirstLetter }} - {{ skill.experience | translate }}
                                        </span>
                                        <span ng-if="skill.experience === 'e00_no_experience'">
                                            <span style="margin-right: 3px">{{ 'Experience in' | translate }}</span>
                                            {{ skill.skillName | bigFirstLetter }}
                                        </span>
                                    </span>
                                </div>
                                <span ng-if="!vm.vacancy.requiredSkills.length">{{ 'emptyValue' | translate }}</span>
                            </div>
                            <div class="vacancy-description-main-info-row-item">
                                <span class="second-title">{{ 'Will be a plus' | translate }}</span>
                                <div ng-if="vm.vacancy.niceToHaveSkills.length" style="display: flex; flex-direction: column">
                                    <span style="margin-bottom: 5px" ng-repeat="skill in vm.vacancy.niceToHaveSkills track by $index">
                                        <span ng-if="skill.experience !== 'e00_no_experience'">
                                            {{ skill.skillName | bigFirstLetter }} - {{ skill.experience | translate }}
                                        </span>
                                        <span ng-if="skill.experience === 'e00_no_experience'">
                                            <span style="margin-right: 3px">{{ 'Experience in' | translate }}</span>
                                            {{ skill.skillName | bigFirstLetter }}
                                        </span>
                                    </span>
                                </div>
                                <span ng-if="!vm.vacancy.niceToHaveSkills.length">{{ 'emptyValue' | translate }}</span>
                            </div>
                            <div class="vacancy-description-main-info-row-item">
                                <span class="second-title">{{ 'languages' | translate }}</span>
                                <div ng-show="vm.vacancy.languages.length > 0" style="display: flex; flex-direction: column">
                                    <span style="margin-bottom: 5px" ng-repeat="lang in vm.vacancy.languages track by $index">
                                        {{ lang.translatedName }} -
                                        <span>{{ lang.level | translate }}</span>
                                    </span>
                                </div>
                                <span ng-if="!vm.vacancy.languages.length">{{ 'emptyValue' | translate }}</span>
                            </div>
                            <div class="vacancy-description-main-info-row-item">
                                <div class="vacancy-description-main-info-row-item">
                                    <span class="second-title">{{ 'experience' | translate }}</span>

                                    <span ng-if="!vm.vacancy.experience">{{ 'emptyValue' | translate }}</span>
                                    <span ng-if="vm.vacancy.experience == 'e00_no_experience'">
                                        {{ 'experience_assoc.can_be_e00_no_experience' | translate }}
                                    </span>
                                    <span ng-if="vm.vacancy.experience !== 'e00_no_experience'">
                                        {{ 'experience_assoc.' + vm.vacancy.experience | translate }}
                                    </span>
                                </div>
                            </div>
                            <div ng-if="vm.vacancy.industry === 'IT'" class="vacancy-description-main-info-row-item">
                                <span class="second-title">{{ 'role' | translate }}</span>
                                <span ng-if="vm.vacancy.role">{{ vm.vacancy.role }}</span>
                                <span ng-if="!vm.vacancy.role">{{ 'emptyValue' | translate }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="vacancy-nav-button-wrapper vacancy-nav-button-wrapper_adaptive">
                        <div class="info-buttons">
                            <button-with-icon
                                ng-click="vm.SendEmailTemplateModal();"
                                type="'secondary'"
                                text="'send_to_email' | translate"
                                icon-name="'mail'"
                            ></button-with-icon>
                        </div>
                        <div ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="info-buttons">
                            <button-with-icon
                                ng-show="vm.vacancy.descr.length > 30"
                                ng-click="vm.copyVacancyModal();"
                                type="'secondary'"
                                text="'Duplicate the vacancy' | translate"
                                icon-name="'copy'"
                            ></button-with-icon>
                        </div>
                    </div>

                    <div ng-show="$root.me.recrutRole != 'researcher'" class="vacancy-description-main-info adaptive-version" style="margin-top: 16px">
                        <div class="vacancy-responsible">
                            <div class="vacancy-responsible__titles">
                                <div>
                                    <span class="third-title">
                                        {{ 'choose_user2' | translate }}
                                        <i
                                            class="hint-info-icon__grey responsible-hint-icon"
                                            tooltip-placement="center"
                                            tooltip-class="tooltip-info-black"
                                            uib-tooltip-html="$root.tooltips.selectResponsible"
                                            aria-hidden="true"
                                        ></i>
                                    </span>
                                </div>
                                <span class="third-title" style="width: 55%">{{ 'Responsible types' | translate }}</span>
                            </div>
                            <div class="vacancy-responsible__dropdowns" ng-repeat="responsible in vm.vacancy.responsiblesPerson track by $index">
                                <div class="vacancy-responsible__dropdowns-first">
                                    <select-single-virtualized
                                        current-lang="$root.currentLang"
                                        placeholder="'responsible'"
                                        options="vm.responsibleOptions"
                                        selected-value="responsible.responsible"
                                        on-change="(vm.showAddResponsibleUser)"
                                        is-clearable="false"
                                        is-searchable="true"
                                        path-to-label="'fullName'"
                                        path-to-value="'personId'"
                                        additional-arg="responsible"
                                    ></select-single-virtualized>
                                </div>
                                <div class="vacancy-responsible__dropdowns-second">
                                    <select-single
                                        current-lang="$root.currentLang"
                                        placeholder="'Responsible types'"
                                        on-change="(vm.changeResponsibleRole)"
                                        options="vm.typesOfRecruitersForHM"
                                        translate-options="true"
                                        selected-value="responsible"
                                        path-to-label="'type'"
                                        path-to-value="'type'"
                                        is-clearable="false"
                                        additional-arg="responsible"
                                        ng-hide="$root.me.recrutRole === 'client' || $root.me.recrutRole === 'researcher'"
                                    ></select-single>
                                </div>
                                <div ng-show="$root.me.recrutRole != 'client'" class="vacancy-responsible__dropdowns-remove" title="{{ 'Remove' | translate }}">
                                    <img ng-click="vm.showRemoveResponsibleModal(responsible)" alt="" src="images/redesign/svg-icons/close.svg" />
                                </div>
                            </div>
                            <div class="vacancy-responsible__add">
                                <button-with-icon
                                    ng-click="vm.onAddNewResonsible();"
                                    ng-class="{
                                        disabledResponsibleAddButton:
                                            vm.vacancy.responsiblesPerson.length !== 0 &&
                                            !vm.vacancy.responsiblesPerson[vm.vacancy.responsiblesPerson.length - 1].responsible.cutFullName
                                    }"
                                    type="'secondary'"
                                    text="'add new responsible' | translate"
                                    icon-name="'plus'"
                                ></button-with-icon>
                            </div>
                        </div>
                    </div>
                    <div class="vacancy-description-main-info adaptive-version" style="margin-bottom: 15px">
                        <div>
                            <div class="third-title">{{ 'Status of vacancy' | translate }}</div>
                            <select-single
                                ng-class="{ 'disable-style': $root['me']['recrutRole'] === 'client' }"
                                current-lang="$root.currentLang"
                                placeholder="'status'"
                                on-change="(vm.showChangeStatusOfVacancy)"
                                options="vm.status"
                                translate-options="true"
                                selected-value="vm.statusForChange"
                                on-change="(vm.showChangeStatusOfVacancy)"
                                is-clearable="false"
                            ></select-single>
                        </div>
                        <div style="margin-top: 10px">
                            <div class="third-title">{{ 'Priority of vacancy' | translate }}</div>
                            <select-single
                                current-lang="$root.currentLang"
                                placeholder="'Select priority of vacancy'"
                                on-change="(vm.onChangePriority)"
                                options="vm.priorities"
                                translate-options="true"
                                selected-value="vm.priorityModel"
                                on-change="(vm.showChangeStatusOfVacancy)"
                                is-clearable="false"
                            ></select-single>
                        </div>
                        <div ng-if="$root.me.recrutRole === 'admin' && $root.me.orgParams.enableHideVacancy === 'true'" class="hide-vacancy-wrapper">
                            <checkbox-component on-change="(vm.changeVacancyVisibility)" is-checked="vm.vacancyHidden"></checkbox-component>
                            <div ng-click="vm.changeVacancyVisibility({target: {checked: !vm.vacancyHidden}})" class="hide-vacancy-wrapper__label">
                                <span style="margin-right: 5px">
                                    {{ 'Hide the vacancy from other users' | translate }}
                                </span>
                                <i
                                    class="hint-info-icon__grey"
                                    tooltip-placement="bottom-right"
                                    tooltip-class="tooltip-info-black"
                                    uib-tooltip-html="$root.tooltips.hideVacancy"
                                ></i>
                            </div>
                        </div>
                    </div>
                    <div ng-if="vm.showCustomFields" class="vacancy-description-main-info">
                        <span class="form-title-main">{{ 'Custom fields' | translate }}</span>
                        <div class="vacancy-description-main-info-row" style="flex-direction: column">
                            <div
                                ng-if="fieldValue.fieldValue != undefined"
                                class="vacancy-description-main-info-row-item"
                                ng-repeat="fieldValue in vm.vacancy.customFields track by $index"
                            >
                                <span ng-if="fieldValue.state == 'A'" class="second-title">{{ fieldValue.title }}:</span>

                                <div ng-if="fieldValue.type == 'string' && fieldValue.state == 'A'" class="text-container">
                                    <div
                                        class="text-container__text"
                                        ng-attr-id="text-{{ $index }}"
                                        ng-bind-html="fieldValue.fieldValue.plainTextValue | linkify3"
                                    >
                                        {{ fieldValue.fieldValue.plainTextValue }}
                                    </div>
                                    <div class="more-info">
                                        <div
                                            ng-if="vm.checkOverflow($index)"
                                            ng-click="vm.showCustomFieldFullText(fieldValue.fieldValue.value)"
                                            class="more-info"
                                        >
                                            {{ 'More info' | translate }}
                                        </div>
                                    </div>
                                </div>

                                <div ng-if="fieldValue.type == 'select' && fieldValue.state == 'A'" class="text-container">
                                    <div class="text-container__text" ng-attr-id="text-{{ $index }}">
                                        {{ fieldValue.fieldValue.value }}
                                    </div>
                                    <div ng-if="vm.checkOverflow($index)" ng-click="vm.showCustomFieldFullText(fieldValue.fieldValue.value)" class="more-info">
                                        {{ 'More info' | translate }}
                                    </div>
                                </div>

                                <date-format-base
                                    ng-if="fieldValue.type == 'date' && fieldValue.state == 'A'"
                                    class-name="'date-added'"
                                    current-lang="$root.currentLang"
                                    date="fieldValue.fieldValue.dateTimeValue"
                                    with-hours="false"
                                    full-month="true"
                                ></date-format-base>

                                <date-format-base
                                    ng-if="fieldValue.type == 'datetime' && fieldValue.state == 'A'"
                                    class-name="'date-added'"
                                    current-lang="$root.currentLang"
                                    date="fieldValue.fieldValue.dateTimeValue"
                                    with-hours="true"
                                    full-month="true"
                                ></date-format-base>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div
                ng-if="
                    vm.vacancy.descr &&
                    vm.vacancy.serviceInformation &&
                    ($root.me.recrutRole === 'admin' || vm.isResponsiblePerson) &&
                    $root.me.recrutRole !== 'client'
                "
                class="desription-service"
            >
                <div ng-if="vm.vacancy.descr" class="desription-service__item" style="width: 70%">
                    <div>
                        <span class="form-title-main">
                            {{ 'description' | translate }}
                        </span>
                        <div
                            class="content-description"
                            style="max-width: 100%; word-break: break-word; margin-top: 15px"
                            description="vm.vacancy.descr"
                            description-treatment
                        ></div>
                    </div>
                </div>
                <div
                    ng-if="vm.vacancy.serviceInformation && ($root.me.recrutRole === 'admin' || vm.isResponsiblePerson) && $root.me.recrutRole !== 'client'"
                    class="desription-service__item"
                    style="width: 38%"
                >
                    <div style="display: flex; justify-content: space-between">
                        <span class="form-title-main">{{ 'Comments' | translate }}</span>
                        <img
                            class="hide-on-public-icon"
                            src="images/redesign/svg-icons/lock.svg"
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-info-black"
                            uib-tooltip="{{ 'hide-on-public-vacancy-page-and-clients' | translate }}"
                        />
                    </div>

                    <div
                        class="content-description"
                        style="max-width: 100%; word-break: break-word; margin-top: 15px"
                        description="vm.vacancy.serviceInformation"
                        description-treatment
                    ></div>
                </div>
            </div>
            <div ng-if="vm.vacancy.descr && vm.vacancy.serviceInformation && $root.me.recrutRole === 'client'" style="display: flex">
                <div style="width: 100%; padding-right: 5px">
                    <div ng-if="vm.vacancy.descr" class="vacancy-description-main-info">
                        <div>
                            <span class="form-title-main">
                                {{ 'description' | translate }}
                            </span>
                            <div
                                class="content-description"
                                style="max-width: 100%; word-break: break-word; margin-top: 15px"
                                description="vm.vacancy.descr"
                                description-treatment
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            <div
                ng-if="
                    (vm.vacancy.descr && !vm.vacancy.serviceInformation) ||
                    (vm.vacancy.descr && vm.vacancy.serviceInformation && $root.me.recrutRole !== 'admin' && !vm.isResponsiblePerson)
                "
            >
                <div class="vacancy-description-main-info">
                    <div>
                        <span class="form-title-main">{{ 'description' | translate }}</span>
                        <div
                            class="content-description"
                            style="max-width: 100%; word-break: break-word; margin-top: 15px"
                            description="vm.vacancy.descr"
                            description-treatment
                        ></div>
                    </div>
                </div>
            </div>

            <div
                ng-if="
                    !vm.vacancy.descr &&
                    vm.vacancy.serviceInformation &&
                    ((vm.isResponsiblePerson && $root.me.recrutRole !== 'client') || $root.me.recrutRole === 'admin')
                "
            >
                <div class="vacancy-description-main-info" style="position: relative">
                    <div style="display: flex; justify-content: space-between">
                        <span class="form-title-main">{{ 'Comments' | translate }}</span>
                        <img
                            class="hide-on-public-icon"
                            src="images/redesign/svg-icons/lock.svg"
                            tooltip-placement="bottom-right"
                            tooltip-class="tooltip-info-black"
                            uib-tooltip="{{ 'hide-on-public-vacancy-page-and-clients' | translate }}"
                        />
                    </div>
                    <div
                        class="content-description"
                        style="max-width: 100%; word-break: break-word; margin-top: 15px"
                        description="vm.vacancy.serviceInformation"
                        description-treatment
                    ></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-5 short-info vacancy-description-right">
        <div class="vacancy-nav-button-wrapper">
            <div class="info-buttons">
                <button-with-icon
                    ng-click="vm.SendEmailTemplateModal();"
                    type="'secondary'"
                    text="'send_to_email' | translate"
                    icon-name="'mail'"
                ></button-with-icon>
            </div>
            <div ng-if="$root.me.personParams.clientAccessLevel !== 'hide'" class="info-buttons">
                <button-with-icon
                    ng-show="vm.vacancy.descr.length > 30"
                    ng-click="vm.copyVacancyModal();"
                    type="'secondary'"
                    text="'Duplicate the vacancy' | translate"
                    icon-name="'copy'"
                ></button-with-icon>
            </div>
        </div>

        <div ng-show="$root.me.recrutRole != 'researcher'" class="vacancy-description-main-info large-version" style="margin-top: 0">
            <div class="vacancy-responsible">
                <div class="vacancy-responsible__titles">
                    <div>
                        <span class="third-title" style="margin-bottom: 6px">
                            {{ 'choose_user2' | translate }}
                            <i
                                class="hint-info-icon__grey responsible-hint-icon"
                                tooltip-placement="center"
                                tooltip-class="tooltip-info-black"
                                uib-tooltip-html="$root.tooltips.selectResponsible"
                                aria-hidden="true"
                            ></i>
                        </span>
                    </div>
                    <span class="third-title" style="width: 55%">{{ 'Responsible types' | translate }}</span>
                </div>
                <div class="vacancy-responsible__dropdowns" ng-repeat="responsible in vm.vacancy.responsiblesPerson track by $index">
                    <div class="vacancy-responsible__dropdowns-first">
                        <select-single-virtualized
                            current-lang="$root.currentLang"
                            placeholder="'responsible'"
                            options="vm.responsibleOptions"
                            selected-value="responsible.responsible"
                            on-change="(vm.showAddResponsibleUser)"
                            is-clearable="false"
                            is-searchable="true"
                            path-to-label="'fullName'"
                            path-to-value="'personId'"
                            additional-arg="responsible"
                        ></select-single-virtualized>
                    </div>
                    <div class="vacancy-responsible__dropdowns-second">
                        <select-single
                            current-lang="$root.currentLang"
                            placeholder="'Responsible types'"
                            on-change="(vm.changeResponsibleRole)"
                            options="vm.typesOfRecruitersForHM"
                            translate-options="true"
                            selected-value="responsible"
                            path-to-label="'type'"
                            path-to-value="'type'"
                            is-clearable="false"
                            additional-arg="responsible"
                            ng-hide="$root.me.recrutRole === 'client' || $root.me.recrutRole === 'researcher'"
                        ></select-single>
                    </div>
                    <div ng-show="$root.me.recrutRole != 'client'" class="vacancy-responsible__dropdowns-remove" title="{{ 'Remove' | translate }}">
                        <img ng-click="vm.showRemoveResponsibleModal(responsible)" alt="" src="images/redesign/svg-icons/close.svg" />
                    </div>
                </div>
                <div class="vacancy-responsible__add">
                    <button-with-icon
                        ng-click="vm.onAddNewResonsible();"
                        ng-class="{
                            disabledResponsibleAddButton:
                                vm.vacancy.responsiblesPerson.length !== 0 &&
                                !vm.vacancy.responsiblesPerson[vm.vacancy.responsiblesPerson.length - 1].responsible.cutFullName
                        }"
                        type="'secondary'"
                        text="'add new responsible' | translate"
                        icon-name="'plus'"
                    ></button-with-icon>
                </div>
            </div>
        </div>
        <div class="vacancy-description-main-info large-version" style="margin-bottom: 16px">
            <div>
                <div class="third-title">{{ 'Status of vacancy' | translate }}</div>
                <select-single
                    ng-class="{ 'disable-style': $root['me']['recrutRole'] === 'client' }"
                    current-lang="$root.currentLang"
                    placeholder="'status'"
                    on-change="(vm.showChangeStatusOfVacancy)"
                    options="vm.status"
                    translate-options="true"
                    selected-value="vm.statusForChange"
                    on-change="(vm.showChangeStatusOfVacancy)"
                    is-clearable="false"
                ></select-single>
            </div>
            <div style="margin-top: 10px">
                <div class="third-title">{{ 'Priority of vacancy' | translate }}</div>
                <select-single
                    current-lang="$root.currentLang"
                    placeholder="'Select priority of vacancy'"
                    on-change="(vm.onChangePriority)"
                    options="vm.priorities"
                    translate-options="true"
                    selected-value="vm.priorityModel"
                    on-change="(vm.showChangeStatusOfVacancy)"
                    is-clearable="false"
                ></select-single>
            </div>
            <div ng-if="$root.me.recrutRole === 'admin' && $root.me.orgParams.enableHideVacancy === 'true'" class="hide-vacancy-wrapper">
                <checkbox-component on-change="(vm.changeVacancyVisibility)" is-checked="vm.vacancyHidden"></checkbox-component>
                <div ng-click="vm.changeVacancyVisibility({target: {checked: !vm.vacancyHidden}})" class="hide-vacancy-wrapper__label">
                    <span style="margin-right: 5px">
                        {{ 'Hide the vacancy from other users' | translate }}
                    </span>
                    <i
                        class="hint-info-icon__grey"
                        tooltip-placement="bottom-right"
                        tooltip-class="tooltip-info-black"
                        uib-tooltip-html="$root.tooltips.hideVacancy"
                    ></i>
                </div>
            </div>
        </div>
        <div
            ng-if="vm.vacancy.descr.length > 30 && vm.vacancy.status != 'payment' && vm.vacancy.status != 'canceled' && vm.vacancy.status != 'completed'"
            class="social hidden-xs hidden-sm"
        >
            <div class="social-page-info-and-statistic">
                <div class="social-page-title">
                    <span class="social-title__text">{{ 'public_page' | translate }}</span>
                </div>
                <div class="social-page-navigation" style="display: flex; flex-direction: column">
                    <div class="social-page-navigation__open-vacancy">
                        <img src="/images/redesign/svg-icons/eye.svg" alt="" />
                        <a ng-show="vm.vacancy.descr.length > 30" class="open-vacancy-link" ng-href="{{ vm.publicLink }}" target="_blank">
                            {{ 'Job Preview' | translate }}
                        </a>
                    </div>
                    <div class="social-page-navigation__hide-vacancy vacancy-nav-wrapper">
                        <!--                        <input id="hidePublish" class="checkbox" ng-model="vm.accessVacancies" type="checkbox" />-->
                        <checkbox-component meta="'hidePublish'" on-change="(vm.hideOrShowPublicVacancy)" is-checked="vm.accessVacancies"></checkbox-component>
                        <span ng-click="vm.hideOrShowPublicVacancy()">
                            {{ 'Hide from job portal' | translate }}
                        </span>
                    </div>
                </div>
                <div class="social-page-statistic-table">
                    <div ng-click="vm.statistickSwitcher = !vm.statistickSwitcher" class="social-page-show-statistic">
                        <div class="social-page-show-statistic__title">{{ 'Statistic' | translate }}</div>
                        <div class="social-page-show-statistic__icon">
                            <img ng-if="!vm.statistickSwitcher" alt="" src="images/redesign/svg-icons/chevron-down.svg" />
                            <img ng-if="vm.statistickSwitcher" alt="" src="images/redesign/svg-icons/chevron-up.svg" />
                        </div>
                    </div>
                    <div ng-if="vm.statistickSwitcher" class="social-page-table">
                        <div class="table-wrapper">
                            <table class="table">
                                <tbody>
                                    <tr class="table-row">
                                        <td class="table-head__data">{{ 'date' | translate }}</td>
                                        <td class="table-head__data">{{ 'Feedback_integration' | translate }}</td>
                                        <td class="table-head__data unique-visits">{{ 'Unique visits' | translate }}</td>
                                    </tr>
                                    <tr class="table-row">
                                        <td class="table-data" translate="total"></td>
                                        <td class="table-data">{{ vm.vacancyStatistic.applyStatisticTotal }}</td>
                                        <td class="table-data">{{ vm.vacancyStatistic.viewStatisticTotal }}</td>
                                    </tr>
                                    <tr class="table-row tbody-row" ng-repeat="statisticItem in vm.vacancyStatistic.statistic">
                                        <td class="table-data">{{ statisticItem.statisticDate | dateFormat2 }}</td>
                                        <td class="table-data">{{ statisticItem.applyStatisticValue || 0 }}</td>
                                        <td class="table-data">{{ statisticItem.viewStatisticValue || 0 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div
            ng-if="vm.vacancy.descr.length > 30 && vm.vacancy.status != 'payment' && vm.vacancy.status != 'canceled' && vm.vacancy.status != 'completed'"
            class="social hidden-xs hidden-sm"
            style="margin-top: 15px"
        >
            <div class="social-networks-wrapper">
                <div class="social-title">
                    <span class="social-title__text">{{ 'Social Networks' | translate }}</span>
                </div>
                <div class="social-content" style="flex-direction: column; display: flex; align-items: center">
                    <div class="social-links_wrapper" style="width: 100%">
                        <div class="social-content__text">
                            {{ 'Help your colleague to find candidates for this vacancy. Share it on your' | translate }}
                        </div>
                        <div class="social-content__platforms">
                            <div id="shareButton1">
                                <button-with-icon
                                    ng-click="vm.share('linkedin')"
                                    type="'secondary'"
                                    text="'share' | translate"
                                    colorful-icon="true"
                                    icon-name="'linkedin_logo'"
                                ></button-with-icon>
                            </div>
                            <div id="shareButton2">
                                <button-with-icon
                                    ng-click="vm.showFbMessage()"
                                    type="'secondary'"
                                    text="'share' | translate"
                                    colorful-icon="true"
                                    icon-name="'facebook_logo'"
                                ></button-with-icon>
                            </div>
                            <div id="shareButton3">
                                <button-with-icon
                                    ng-click="vm.addPublishX()"
                                    type="'secondary'"
                                    text="'share' | translate"
                                    colorful-icon="true"
                                    icon-name="'x_logo'"
                                ></button-with-icon>
                            </div>
                        </div>
                        <div class="social-content__number_of_published">
                            <span class="number_of_published__title">{{ 'Posts in Social Networks' | translate }}:</span>
                            <span class="number_of_published__value">{{ vm.vacancy.publishingCount }}</span>
                        </div>
                    </div>
                    <div class="social-vacancy-img" style="width: 100%; align-items: flex-start">
                        <div class="social-vacancy-img-wrapper">
                            <div class="social-vacancy-img__title">{{ 'cover_picture' | translate }}</div>
                            <div
                                id="logo-button"
                                ng-if="!$root.promoLogo && ($root.me.recrutRole === 'admin' || $root.me.recrutRole === 'recruter')"
                                class="social-vacancy-img__addPhoto"
                                add-promo-logo
                            >
                                <span class="addPhoto-message">{{ 'max size is 5 mb' | translate }}</span>
                                <img class="addPromoLogo" alt="" src="/images/redesign/svg-icons/camera.svg" />
                            </div>
                            <div
                                id="owner_photo_wrap"
                                class="text-center"
                                ng-mouseleave="vm.unblurToRemovePromoLogo()"
                                ng-mouseover="vm.blurToRemovePromoLogo()"
                            >
                                <img
                                    ng-if="($root.me.recrutRole === 'admin' || $root.me.recrutRole === 'recruter') && $root.promoLogo"
                                    class="add-photo"
                                    add-promo-logo
                                    src="/images/redesign/svg-icons/camera.svg"
                                />
                                <img ng-if="$root.promoLogo" ng-click="vm.openPromoLogo()" class="open-photo" src="../images/vacancy/openPhoto.svg" />
                                <input id="the-file-promo-logo" style="display: none" accept="image/*" type="file" />
                                <img
                                    ng-if="$root.promoLogo != undefined"
                                    ng-click="vm.openPromoLogo();"
                                    class="imgForPromo"
                                    alt=""
                                    ng-src="{{ $root.serverAddress }}/getapp?id={{ $root.promoLogo }}&d="
                                />
                                <div class="blur-wrapper" ng-class="{ 'blur-to-remove': vm.isBlurToRemovePromoLogo && $root.promoLogo }">
                                    <i ng-click="vm.removePromoLogo()" class="fa fa-trash trash-position" title="{{ 'remove_picture' | translate }}"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="social-facebook-uploading">
                    <div class="social-facebook-uploading__title">
                        {{ 'Job_poste_on_the_page_in_Facebook' | translate }}
                    </div>
                    <div class="social-facebook-uploading__btn">
                        <button-component
                            ng-click="vm.showShareFbPages()"
                            type="'secondary'"
                            text="vm.vacancy.publish.length == 0 ? ('Share_on_the_company_page_in_Facebook' | translate) : ('Share_on_the_company_page_in_Facebook_1' | translate)"
                        ></button-component>
                    </div>
                </div>
            </div>
        </div>

        <div
            ng-if="
                ((vm.vacancy.descr.length > 30 &&
                    vm.vacancy.status != 'payment' &&
                    vm.vacancy.status != 'canceled' &&
                    vm.vacancy.status != 'completed' &&
                    $root.me.recrutRole === 'admin') ||
                    (vm.vacancy.descr.length > 30 &&
                        vm.vacancy.status != 'payment' &&
                        vm.vacancy.status != 'canceled' &&
                        vm.vacancy.status != 'completed' &&
                        $root.me.recrutRole === 'recruter')) &&
                ($root.me.personParams.showUaWorkSites === 'Y' ||
                    $root.me.personParams.registrationCountry == 'Ukraine' ||
                    $root.me.personParams.registrationCountry == 'Kazakhstan' ||
                    $root.me.personParams.registrationCountry == 'Belarus' ||
                    $root.me.personParams.registrationCountry == 'Moldova' ||
                    $root.me.personParams.registrationCountry == 'Uzbekistan' ||
                    $root.me.personParams.registrationCountry == 'Kyrgyzstan' ||
                    $root.me.personParams.registrationCountry == 'Russia' ||
                    !$root.me.personParams.registrationCountry ||
                    mphoneInCountry ||
                    phoneWorkInCountry ||
                    phoneMobInCountry)
            "
            class="social hidden-xs hidden-sm"
        >
            <job-sites-integration-component></job-sites-integration-component>
        </div>
        <div ng-if="vm.vacancy.descr == undefined || vm.vacancy.descr.length < 30" class="social hidden-xs hidden-sm">
            <p class="form-title-main">
                {{ 'Social Networks' | translate }}
            </p>
            <span class="text-info">{{ 'Please add more detailed job description for candidates before publishing in social networks' | translate }}.</span>
        </div>
        <div class="files">
            <h4 class="files__header">
                {{ 'files' | translate }}
            </h4>
            <div ng-show="vm.vacancy.files.length" class="files__wrapper">
                <div class="files__wrapper__items" ng-repeat="file in vm.vacancy.files track by $index">
                    <div ng-show="file.showEditFileName" style="width: 100%">
                        <p class="files__wrapper__title title-standard-size">
                            {{ 'Change file name' | translate }}
                        </p>
                        <input-component value="file.fileName" on-change="(vm.onChangeFileName)" additional-arg="file"></input-component>
                    </div>

                    <button-component
                        ng-show="file.showEditFileName"
                        ng-click="vm.editFileName(file)"
                        type="'secondary'"
                        text="'Ok' | translate"
                    ></button-component>

                    <img ng-show="!file.url && !file.showEditFileName" src="images/redesign/svg-icons/attach.svg" alt="" />

                    <a
                        ng-show="!file.url && !file.showEditFileName"
                        ng-click="vm.showModalResume(file)"
                        class="file-name-link"
                        title="{{ file.fileName | fileNameCut : 0 : 40 }}"
                    >
                        <filename-ellipsis file-name="file.fileName"></filename-ellipsis>
                    </a>
                    <img
                        ng-if="!file.showEditFileName"
                        ng-click="vm.MenuEdDelFile(file)"
                        ng-class="{ revert: file.showMenuEdDelFile }"
                        ng-style="file.showEditFileName && { 'margin-bottom': '10px' }"
                        src="images/redesign/svg-icons/chevron-down.svg"
                        alt=""
                    />
                    <div ng-if="file.showMenuEdDelFile" class="editFileMenu">
                        <a ng-show="!file.url" ng-click="vm.onDownloadFile(file)" class="editFileMenu__item">
                            <img src="images/redesign/svg-icons/upload.svg" alt="" />
                            <span>{{ 'Download file' | translate }}</span>
                        </a>
                        <a ng-click="vm.showEditFileNameFunc(file)" class="editFileMenu__item" href="">
                            <img src="images/redesign/svg-icons/pencil.svg" alt="" />
                            <span>{{ !file.url ? ('Change file name' | translate) : ('Change link name' | translate) }}</span>
                        </a>
                        <a ng-click="vm.removeFile(file.fileId)" class="editFileMenu__item" href="">
                            <img src="images/redesign/svg-icons/close.svg" alt="" />
                            <span>{{ !file.url ? ('Delete file' | translate) : ('Delete link' | translate) }}</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="files__add">
                <input id="file-vacancy" class="hidden" oi-file="vm.optionsForVacancy" type="file" />
                <label class="files__add__label" for="file-vacancy">
                    <img style="cursor: pointer" src="images/redesign/svg-icons/attach.svg" alt="" />
                    <span>
                        {{ 'Attach file' | translate }}
                    </span>
                </label>
            </div>
        </div>
    </div>

    <div id="cover-picture-modal" class="cover-picture-modal">
        <span ng-click="vm.closeModalImage()" class="close">&times;</span>
        <img ng-if="$root.promoLogo" class="modal-content" ng-src="{{ $root.serverAddress }}/getapp?id={{ $root.promoLogo }}&d=" />
    </div>
</div>
