<div id="block-vacancy" class="block-vacancy">
    <div class="breadcrumbs">
        <ol class="breadcrumb position-relative" style="margin-bottom: 0; margin-top: 0">
            <li>
                <a href="#/vacancies">{{ 'vacancies' | translate }}</a>
            </li>
            <li class="active">
                {{ vm.vacancy.position | limitToEllipse : 50 }}
            </li>
        </ol>
    </div>
    <h3 style="margin-top: 0">
        <!-- prettier-ignore -->
        <div style="text-align: center">
            <span class="main-page-title">{{ vm.vacancy.position | limitToEllipse : 300 }}</span><span ng-if="$root.me.personParams.clientAccessLevel !== 'hide'">,
            <a class="image-link link-style" ng-href="#/clients/{{ vm.vacancy.clientId.localId }}">
                <img
                    ng-show="vacancy.clientId.logoId"
                    ng-src="{{ vm.serverAddress }}/getapp?id={{ vm.vacancy.clientId.logoId }}&d={{ $root.me.personId }}"
                    alt=""
                />
            </a>
            <a class="link link-edit" ng-href="#/clients/{{ vm.vacancy.clientId.localId }}">
                {{ vm.vacancy.clientId.name }}
            </a>
            </span>
        </div>
        <div class="vacancy-nav vacancy-nav-mobile">
            <div class="vacancy-nav-block">
                <button-with-icon
                    ng-if="$root.me.personParams.clientAccessLevel !== 'hide'"
                    ng-click="vm.redirectToEdit(vm.vacancy.localId)"
                    class="hidden_laptop"
                    type="'secondary'"
                    text="'Edited_1' | translate"
                    icon-name="'pencil'"
                ></button-with-icon>

                <button-with-icon
                    ng-if="$root.getCounts && $root.vacancy.status !== 'completed' && $root.getCounts"
                    ng-click="vm.showChangeStatusOfVacancy('completed')"
                    type="'secondary'"
                    text="'Close vacancy' | translate"
                    icon-name="'check'"
                ></button-with-icon>

                <button-with-icon
                    ng-if="($root.$state.is('vacancy.stages') || $root.$state.is('vacancy.pipeline')) && $root.me.recrutRole !== 'client'"
                    ng-click="$root.$state.is('vacancy.stages') ? vm.toAddCandidateForm(null, false, false) : $root.addNewCandidate()"
                    type="'secondary'"
                    text="'add_candidate' | translate"
                    icon-name="'plus'"
                ></button-with-icon>

                <div
                    ng-if="
                        vm.vacancy && !vm.isITVacancyValid && (vm.vacancy.status === 'open' || vm.vacancy.status === 'onhold' || vm.vacancy.status === 'inwork')
                    "
                    class="vacancy-nav-block"
                >
                    <div ng-if="!$root.vacancy.attractive" class="improve-vacancy">
                        <a ng-click="vm.toValidateVacancy(vm.vacancy.localId)" class="improve-vacancy__main-block">
                            <span class="improve-vacancy__text">
                                {{ 'Please improve this vacancy to attract candidates' | translate }}
                            </span>
                            <img class="improve-vacancy__dancer-icon" src="images/emoji-dancer.png" alt="" />
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </h3>
    <div class="clearfix"></div>
    <div class="container-fluid top-navbar-wrapper">
        <div id="top-navbar-subtabs" class="second-navbar">
            <ul class="subtabs-list">
                <li class="subtab" ui-sref="vacancy.description" ui-sref-active="subtab__selected">
                    <span class="subtab_link" translate="information"></span>
                </li>
                <li ng-click="vm.setVacancyTab('stages')" class="subtab" ui-sref="vacancy.stages" ui-sref-active="subtab__selected">
                    <span class="subtab_link">
                        {{ 'candidates_2' | translate }}
                        <span class="counter">
                            {{ $root.countCandidates }}
                        </span>
                    </span>
                </li>
                <li
                    ng-click="vm.setVacancyTab('kanban')"
                    class="subtab hidden_laptop"
                    ng-hide="vm.mobileVers || $root.countCandidates > 400"
                    ui-sref="vacancy.pipeline"
                    ui-sref-active="subtab__selected"
                >
                    <span class="subtab_link">
                        {{ 'Pipeline' | translate }}
                    </span>
                </li>
                <li ng-show="$root.me.recrutRole !== 'client'" class="subtab" ui-sref="vacancy.suggestions" ui-sref-active="subtab__selected">
                    <span class="subtab_link">
                        {{ 'Suggested candidates' | translate }}
                        <span class="counter suggest" style="margin-left: 5px">
                            {{ vm.candidates.length }}
                        </span>
                        <div ng-if="vm.jobCannonCount" class="job-cannon-advice-count">
                            +{{ vm.jobCannonCount }}
                            <img class="job-cannon-advice-count__logo" src="images/sprite/job-cannon-logo.png" title="JobCannon" />
                        </div>
                    </span>
                </li>
                <li class="subtab hidden_laptop" ui-sref="vacancy.tasks" ui-sref-active="subtab__selected">
                    <span class="subtab_link">
                        {{ 'Tasks_v2' | translate }}
                        <span class="counter">{{ vm.tabsDataCount.tasks }}</span>
                    </span>
                </li>
                <li class="subtab hidden_laptop" ui-sref="vacancy.history" ui-sref-active="subtab__selected">
                    <span class="subtab_link" translate="history"></span>
                </li>
                <li class="subtab">
                    <button
                        id="disabledBtn-2"
                        ng-show="!$root.hideTariff && $root.me.recrutRole != 'client'"
                        ng-click="$root.disabledBtnFunc();"
                        class="btn btn-default margin-left disabled"
                        type="button"
                        translate="Email templates vacancy"
                    ></button>
                </li>
                <li
                    ng-if="
                        $root.me.orgParams.enableMailing === 'true' &&
                        $root.me.personParams.enableMailing === 'true' &&
                        vm.isSavedMailing &&
                        ($root.me.recrutRole == 'recruter' || $root.me.recrutRole == 'admin')
                    "
                    class="subtab hidden-xs hidden-sm"
                >
                    <span ng-click="toMyMailings()" class="hidden-xs pull-left">
                        {{ 'My mailings' | translate }}
                    </span>
                </li>
            </ul>
            <ul class="nav navbar-nav vacancy-report-btn hidden_laptop">
                <li ng-show="$root.hideTariff && $root.me.recrutRole != 'researcher'">
                    <button-component ng-click="vm.goToReportPage()" type="'secondary'" size="'medium'" text="'Vacancy report' | translate"></button-component>
                </li>
            </ul>
        </div>
    </div>

    <div ui-view></div>
</div>
<ng-include src="'partials/candidate-preview.html'"></ng-include>
