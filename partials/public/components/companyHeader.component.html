<section class="company-header-wrapper flex-column flex-justify-align-center">
    <div ng-if="vm.moveWallpaperModeActive" class="company-confirm-changes-block">
        <div class="company-confirm-changes-block__message">
            <img class="image" src="/images/sprite/icons/move-selector.svg" alt="" />
            <span class="text">{{ 'Drag_to_reposition' | translate }}</span>
        </div>
        <div class="company-confirm-changes-block__buttons">
            <button ng-click="vm.setPrevCompanyWallpaperPosition()" class="btn_default btn_error">
                {{ 'Cancel_1' | translate }}
            </button>
            <button ng-click="vm.saveCompanyWallpaperPosition()" class="btn_default btn_success">
                {{ 'Save changes' | translate }}
            </button>
        </div>
    </div>
    <div
        ng-show="$root.isActiveSession && $root.me.recrutRole == 'admin' && !vm.moveWallpaperModeActive && $root.me.orgId === $root.orgId"
        class="company-edit-view-block-icon"
    >
        <span ng-click="vm.openCustomizeView($event)" class="tags-main-item-icon pencilHover">
            <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                />
            </svg>
        </span>
    </div>
    <div
        ng-if="$root.isActiveSession && $root.me.recrutRole == 'admin'"
        ng-click="vm.closeCustomizeView($event)"
        class="company-edit-view-block-backdrop"
        ng-class="{ visible: vm.openedCustomizeView }"
    >
        <div ng-click="$event.stopPropagation()" class="company-edit-view-block-list">
            <div class="company-edit-view-block-list-item">
                <public-photo-cropper-component photo-id="$root.accountInformation.logoId" page-name="'company'"></public-photo-cropper-component>
            </div>
            <div class="company-edit-view-block-list-item">
                <input id="file-input" style="display: none" type="file" accept="image/x-png,image/jpeg" custom-on-change="vm.uploadCompanyCover" />
                <label for="file-input">
                    {{ 'Upload a cover' | translate }}
                </label>
            </div>
            <div
                ng-if="$root.accountInformation.companyWallpaper"
                ng-click="vm.moveWallpaperModeEnable()"
                class="company-edit-view-block-list-item hidden-sm-screen"
            >
                <label>{{ 'Move cover' | translate }}</label>
            </div>
            <div
                ng-if="$root.accountInformation.companyWallpaper"
                ng-click="vm.deleteCompanyCover()"
                class="company-edit-view-block-list-item hidden-sm-screen"
            >
                <label>{{ 'Delete cover' | translate }}</label>
            </div>
            <div class="company-edit-view-block-list-item">
                <label class="labelBackgroundColor" for="backgroundColor">
                    {{ 'Choose a background color' | translate }}
                </label>
                <input
                    id="backgroundColor"
                    class="inputBackgroundColor"
                    type="color"
                    ng-model="vm.backgroundColor"
                    ng-blur="vm.confirmChangeBackgroundColor()"
                    ng-model-options="{debounce: 25}"
                    ng-change="vm.changeBackgroundColor(vm.backgroundColor)"
                />
            </div>
        </div>
    </div>
    <div class="company-wallpaper-block"></div>
    <div class="company-info-block">
        <div class="header-company-block flex-block flex-justify-start">
            <div ng-show="$root.isActiveSession && $root.me.recrutRole == 'admin' && $root.me.orgId === $root.orgId" class="company-edit">
                <span ng-click="vm.goToAccount()" class="tags-main-item-icon pencilHover" style="border: none">
                    <svg width="13" height="12" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7.37333 4.01333L7.98667 4.62667L1.94667 10.6667H1.33333V10.0533L7.37333 4.01333ZM9.77333 0C9.60667 0 9.43333 0.0666666 9.30667 0.193333L8.08667 1.41333L10.5867 3.91333L11.8067 2.69333C12.0667 2.43333 12.0667 2.01333 11.8067 1.75333L10.2467 0.193333C10.1133 0.06 9.94667 0 9.77333 0ZM7.37333 2.12667L0 9.5V12H2.5L9.87333 4.62667L7.37333 2.12667Z"
                        />
                    </svg>
                </span>
            </div>
            <div class="header-company-main">
                <div class="company-logo">
                    <img ng-if="$root.accountInformation.logoId" src="{{ vm.serverAddress }}/getlogo?id={{ $root.accountInformation.logoId }}" alt="" />
                    <img ng-if="!$root.accountInformation.logoId" src="/images/sprite/icons/photo.svg" alt="" />
                </div>
            </div>
            <div class="header-company-details">
                <div ng-if="$root.accountInformation.companyName" class="company-name main-title">
                    {{ $root.accountInformation.companyName }}
                </div>
                <div
                    ng-if="$root.accountInformation.companyIndustry"
                    class="company-industry"
                    title="{{ 'industries_assoc.' + $root.accountInformation.companyIndustry | translate }}"
                >
                    {{ 'industries_assoc.' + $root.accountInformation.companyIndustry | translate }}
                </div>
                <div ng-if="$root.accountInformation.companyLocation" class="company-location">
                    <span class="company-location__text" title="{{ $root.accountInformation.companyLocation }}">
                        {{ $root.accountInformation.companyLocation }}
                    </span>
                </div>
                <div class="company-contacts">
                    <div ng-if="$root.accountInformation.companyPhone" class="contact-item">
                        <svg width="15" height="18" viewBox="0 0 15 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M0.833333 0H3.75C4.20833 0 4.58333 0.45 4.58333 1C4.58333 2.25 4.75 3.45 5.05833 4.57C5.15 4.92 5.08333 5.31 4.85 5.59L3.01667 7.79C4.21667 10.62 6.15 12.93 8.50833 14.38L10.3417 12.18C10.5083 11.99 10.7167 11.89 10.9333 11.89C11.0167 11.89 11.1083 11.9 11.1917 11.94C12.125 12.31 13.1333 12.51 14.1667 12.51C14.625 12.51 15 12.96 15 13.51V17C15 17.55 14.625 18 14.1667 18C6.34167 18 0 10.39 0 1C0 0.45 0.375 0 0.833333 0ZM2.95002 2C3.00002 2.89 3.12502 3.76 3.32502 4.59L2.32502 5.79C1.98336 4.59 1.76669 3.32 1.69169 2H2.95002ZM11.1667 14.02C11.875 14.26 12.6 14.41 13.3333 14.47V15.96C12.2333 15.87 11.175 15.61 10.1667 15.21L11.1667 14.02Z"
                                fill="#00B549"
                            />
                        </svg>
                        <div class="company__phone">{{ $root.accountInformation.companyPhone }}</div>
                    </div>
                    <div ng-if="$root.accountInformation.companyEmail" class="contact-item">
                        <svg width="16" height="16" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M1.6 0H14.4C15.28 0 16 0.675 16 1.5V10.5C16 11.325 15.28 12 14.4 12H1.6C0.712 12 0 11.325 0 10.5V1.5C0 0.6675 0.712 0 1.6 0ZM8 5.25L14.4 1.5H1.6L8 5.25ZM1.6 10.5H14.4V3.2775L8 7.02L1.6 3.2775V10.5Z"
                                fill="#00B549"
                            />
                        </svg>
                        <a
                            class="ellipsis link-style"
                            href="mailTo:{{ $root.accountInformation.companyEmail }}"
                            target="_blank"
                            title="{{ $root.accountInformation.companyEmail }}"
                        >
                            {{ $root.accountInformation.companyEmail }}
                        </a>
                    </div>
                    <div ng-if="$root.accountInformation.companyWebSite" class="contact-item">
                        <svg class="" xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 0 512 512" width="16px">
                            <g>
                                <path
                                    class="active-path"
                                    d="m437.019531 74.980469c-48.351562-48.**********.636719-74.**********.015625-74.980469h-.007812c-68.378906 0-132.664063 26.**********.015625 74.980469-48.351563 48.351562-74.**********.640625-74.**********.019531s26.**********.667969 74.**********.019531c48.351562 48.**********.640625 74.**********.019531 74.980469s132.667969-26.**********.019531-74.980469c48.351563-48.351562 74.**********.640625 74.**********.019531s-26.**********.667969-74.**********.019531zm16.**********.335937h-74.925782c10.039063-29.414062 16.085938-61.261718 17.496094-94.316406h85.175781c-2.238281 34.03125-12.042969 66.023438-27.746093 94.316406zm-197.**********.464844c-31.929688-19-58.410156-48.449219-77.457031-84.464844h154.914062c-19.046875 36.015625-45.527343 65.464844-77.457031 84.464844zm-91.015625-114.464844c-11.03125-28.941406-17.71875-60.898437-19.265625-94.316406h220.5625c-1.546875 33.417969-8.238281 65.375-19.269531 94.316406zm-134.472656-94.316406h85.175781c1.410156 33.054688 7.457031 64.902344 17.496094 94.316406h-74.925782c-15.703124-28.292968-25.507812-60.285156-27.746093-94.316406zm27.746093-124.316406h74.925782c-10.039063 29.414062-16.085938 61.261718-17.496094 94.316406h-85.175781c2.238281-34.03125 12.042969-66.023438 27.746093-94.316406zm197.742188-114.464844c31.929688 19 58.410156 48.449219 77.457031 84.464844h-154.914062c19.046875-36.015625 45.527343-65.464844 77.457031-84.464844zm91.015625 114.464844c11.03125 28.941406 17.71875 60.898437 19.265625 94.316406h-220.5625c1.546875-33.417969 8.238281-65.375 19.269531-94.316406zm134.472656 94.316406h-85.175781c-1.410156-33.054688-7.457031-64.902344-17.496094-94.316406h74.925782c15.703124 28.292968 25.507812 60.285156 27.746093 94.316406zm-114.683593-124.316406c-14.257813-30.796875-33.226563-58.011719-56-79.949219 49.429687 12.359375 92.472656 41.027344 123.03125 79.949219zm-165.609376-79.949219c-22.773437 21.9375-41.742187 49.152344-56 79.949219h-67.03125c30.558594-38.921875 73.601563-67.589844 123.03125-79.949219zm-56 358.582031c14.257813 30.796875 33.226563 58.011719 56 79.949219-49.429687-12.359375-92.472656-41.027344-123.03125-79.949219zm165.609376 79.949219c22.773437-21.9375 41.742187-49.15625 56-79.949219h67.03125c-30.558594 38.921875-73.601563 67.**********.03125 79.949219zm0 0"
                                    data-original="#000000"
                                    data-old_color="#000000"
                                    fill="#00B549"
                                />
                            </g>
                        </svg>
                        <a
                            class="ellipsis link-style"
                            href="{{ vm.addLinkPrefix($root.accountInformation.companyWebSite) }}"
                            target="_blank"
                            title="{{ $root.accountInformation.companyWebSite }}"
                        >
                            {{ vm.formatCompanyWebSiteLink($root.accountInformation.companyWebSite) }}
                        </a>
                    </div>
                    <div ng-if="$root.accountInformation.companyFacebookPage" class="contact-item link-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="-110 1 511 511.99996" width="16px" height="16px">
                            <g>
                                <path
                                    class="active-path"
                                    d="m180 512h-81.992188c-13.695312 0-24.835937-11.140625-24.835937-24.835938v-184.9375h-47.835937c-13.695313 0-24.835938-11.144531-24.835938-24.835937v-79.246094c0-13.695312 11.140625-24.835937 24.835938-24.835937h47.835937v-39.683594c0-39.347656 12.355469-72.824219 35.726563-96.804688 23.476562-24.089843 56.285156-36.820312 94.878906-36.820312l62.53125.101562c13.671875.023438 24.792968 11.164063 24.792968 24.835938v73.578125c0 13.695313-11.136718 24.835937-24.828124 24.835937l-42.101563.015626c-12.839844 0-16.109375 2.574218-16.808594 3.363281-1.152343 1.308593-2.523437 5.007812-2.523437 15.222656v31.351563h58.269531c4.386719 0 8.636719 1.082031 12.289063 3.121093 7.878906 4.402344 12.777343 12.726563 12.777343 21.722657l-.03125 79.246093c0 13.6875-11.140625 24.828125-24.835937 24.828125h-58.46875v184.941406c0 13.695313-11.144532 24.835938-24.839844 24.835938zm-76.8125-30.015625h71.632812v-193.195313c0-9.144531 7.441407-16.582031 16.582032-16.582031h66.726562l.027344-68.882812h-66.757812c-9.140626 0-16.578126-7.4375-16.578126-16.582031v-44.789063c0-11.726563 1.191407-25.0625 10.042969-35.085937 10.695313-12.117188 27.550781-13.515626 39.300781-13.515626l36.921876-.015624v-63.226563l-57.332032-.09375c-62.023437 0-100.566406 39.703125-100.566406 103.609375v53.117188c0 9.140624-7.4375 16.582031-16.578125 16.582031h-56.09375v68.882812h56.09375c9.140625 0 16.578125 7.4375 16.578125 16.582031zm163.0625-451.867187h.003906zm0 0"
                                    data-original="#000000"
                                    data-old_color="#000000"
                                    fill="#00B549"
                                />
                            </g>
                        </svg>
                        <a
                            class="ellipsis link-style"
                            href="{{ vm.addLinkPrefix($root.accountInformation.companyFacebookPage) }}"
                            target="_blank"
                            title="{{ $root.accountInformation.companyFacebookPage }}"
                        >
                            {{ vm.formatCompanyFacebookLink($root.accountInformation.companyFacebookPage) }}
                        </a>
                    </div>
                    <div ng-if="$root.accountInformation.companyLinkedIn" class="contact-item">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="#00B549" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M4.37181 4.42857L4.37174 4.4285L4.3687 4.4316C4.16481 4.63894 3.89186 4.76583 3.60634 4.74768L3.60635 4.74748H3.6C3.32482 4.74748 3.03116 4.63484 2.83131 4.4316L2.83137 4.43153L2.82819 4.42857C2.61154 4.22659 2.50001 3.92928 2.50001 3.62714C2.50001 3.32765 2.60991 3.04783 2.83131 2.82268C3.03116 2.61943 3.30483 2.50679 3.61999 2.50679C3.89336 2.50679 4.16761 2.61819 4.3687 2.82268C4.5901 3.04783 4.7 3.32765 4.7 3.62714C4.7 3.92928 4.58847 4.22659 4.37181 4.42857ZM3.61999 1.9C3.1544 1.9 2.72833 2.07264 2.4087 2.39769L3.6 5.25425L3.6 5.35425H3.6H3.60001H3.60001H3.60001H3.60002H3.60002H3.60002H3.60003H3.60003H3.60004H3.60005H3.60005H3.60006H3.60007H3.60007H3.60008H3.60009H3.6001H3.60011H3.60012H3.60013H3.60014H3.60015H3.60017H3.60018H3.60019H3.6002H3.60022H3.60023H3.60024H3.60026H3.60028H3.60029H3.60031H3.60032H3.60034H3.60036H3.60038H3.60039H3.60041H3.60043H3.60045H3.60047H3.60049H3.60051H3.60053H3.60055H3.60058H3.6006H3.60062H3.60064H3.60067H3.60069H3.60072H3.60074H3.60077H3.60079H3.60082H3.60085H3.60087H3.6009H3.60093H3.60096H3.60098H3.60101H3.60104H3.60107H3.6011H3.60113H3.60116H3.60119H3.60123H3.60126H3.60129H3.60132H3.60136H3.60139H3.60142H3.60146H3.60149H3.60153H3.60156H3.6016H3.60163H3.60167H3.60171H3.60175H3.60178H3.60182H3.60186H3.6019H3.60194H3.60198H3.60202H3.60206H3.6021H3.60214H3.60218H3.60222H3.60227H3.60231H3.60235H3.60239H3.60244H3.60248H3.60249H3.60253H3.60257H3.60262H3.60266H3.60271H3.60275H3.6028H3.60285H3.60289H3.60294H3.60299H3.60304H3.60309H3.60314H3.60319H3.60324H3.60329H3.60334H3.60339H3.60344H3.60349H3.60354H3.60359H3.60365H3.6037H3.60375H3.6038H3.60386H3.60391H3.60397H3.60402H3.60408H3.60413H3.60419H3.60424H3.6043H3.60436H3.60442H3.60447H3.60453H3.60459H3.60465H3.60471H3.60476H3.60482H3.60488H3.60494H3.605H3.60506H3.60513H3.60519H3.60525H3.60531H3.60537H3.60544H3.6055H3.60556H3.60563H3.60569H3.60575H3.60582H3.60588H3.60595H3.60601H3.60608H3.60614H3.60621H3.60628H3.60634H3.60641H3.60648H3.60655H3.60661H3.60668H3.60675H3.60682H3.60689H3.60696H3.60703H3.6071H3.60717H3.60724H3.60731H3.60738H3.60745H3.60753H3.6076H3.60767H3.60774H3.60782H3.60789H3.60796H3.60804H3.60811H3.60818H3.60826H3.60833H3.60841H3.60849H3.60856H3.60864H3.60871H3.60879H3.60887H3.60894H3.60902H3.6091H3.60918H3.60925H3.60933H3.60941H3.60949H3.60957H3.60965H3.60973H3.60981H3.60989H3.60997H3.61005H3.61013H3.61021H3.61029H3.61038H3.61046H3.61054H3.61062H3.61071H3.61079H3.61087H3.61096H3.61104H3.61112H3.61121H3.61129H3.61138H3.61146H3.61155H3.61163H3.61172H3.6118H3.61189H3.61198H3.61206H3.61215H3.61224H3.61232H3.61241H3.6125H3.61259H3.61268H3.61276H3.61285H3.61294H3.61303H3.61312H3.61321H3.6133H3.61339H3.61348H3.61357H3.61366H3.61375H3.61384H3.61393H3.61403H3.61412H3.61421H3.6143H3.61439H3.61449H3.61458H3.61467H3.61477H3.61486H3.61495H3.61505H3.61514H3.61523H3.61533H3.61542H3.61552H3.61561H3.61571H3.6158H3.6159H3.616H3.61609H3.61619H3.61628H3.61638H3.61648H3.61658H3.61667H3.61677H3.61687H3.61697H3.61706H3.61716H3.61726H3.61736H3.61746H3.61756H3.61765H3.61775H3.61785H3.61795H3.61805H3.61815H3.61825H3.61835H3.61845H3.61855H3.61866H3.61876H3.61886H3.61896H3.61906H3.61916H3.61926H3.61937H3.61947H3.61957H3.61967H3.61978H3.61988H3.61998H3.62008H3.62019H3.62029H3.62039H3.6205H3.6206H3.62071H3.62081H3.62091H3.62102H3.62112H3.62123H3.62133H3.62144H3.62154H3.62165H3.62175H3.62186H3.62197H3.62207H3.62218H3.62228H3.62239H3.6225H3.6226H3.62271H3.62282H3.62293H3.62303H3.62314H3.62325H3.62335H3.62346H3.62357H3.62368H3.62379H3.62389H3.624H3.62411H3.62422H3.62433H3.62444H3.62455H3.62466H3.62477H3.62487H3.62498H3.62509H3.6252H3.62531H3.62542H3.62553H3.62564H3.62575H3.62586H3.62598H3.62609H3.6262H3.62631H3.62642H3.62653H3.62664H3.62675H3.62686H3.62697H3.62709H3.6272H3.62731H3.62742H3.62753H3.62765H3.62776H3.62787H3.62798H3.62809H3.62821H3.62832H3.62843H3.62855H3.62866H3.62877H3.62888H3.629H3.62911H3.62922H3.62934H3.62945H3.62956H3.62968H3.62979H3.62991H3.63002H3.63013H3.63025H3.63036H3.63048H3.63059H3.6307H3.63082H3.63093H3.63105H3.63116H3.63128H3.63139H3.63151H3.63162H3.63174H3.63185H3.63197H3.63208H3.6322H3.63231H3.63243H3.63254H3.63266H3.63277H3.63289H3.63301H3.63312H3.63324H3.63335H3.63347H3.63358H3.6337H3.63382H3.63393H3.63405H3.63416H3.63428H3.6344H3.63451H3.63463H3.63475H3.63486H3.63498H3.63509H3.63521H3.63533H3.63544H3.63556H3.63568H3.63579H3.63591H3.63603H3.63614H3.63626H3.63638H3.63649H3.63661H3.63673H3.63685H3.63696H3.63708H3.6372H3.63731H3.63743H3.63755H3.63766H3.63778H3.6379H3.63802H3.63813H3.63825H3.63837H3.63848H3.6386H3.63872H3.63884H3.63895H3.63907H3.63919H3.63931H3.63942H3.63954H3.63966H3.63977H3.63989H3.64001C4.06837 5.35425 4.47361 5.17964 4.79131 4.85656C5.10812 4.53437 5.29937 4.10368 5.3 3.62945C5.32069 3.17206 5.12786 2.7405 4.81243 2.41915C4.49279 2.07309 4.06617 1.9 3.61999 1.9Z"
                                fill="#00B549"
                                stroke="#00B549"
                                stroke-width="0.2"
                            />
                            <path
                                d="M4.39976 5.56107H2.77977C2.40079 5.56107 2.09977 5.87408 2.09977 6.27124V13.3899C2.09977 13.769 2.42297 14.1001 2.79976 14.1001H4.39976C4.7744 14.1001 5.09976 13.7712 5.09976 13.4103V6.27124C5.09976 5.89216 4.77656 5.56107 4.39976 5.56107ZM4.49975 13.4103C4.49975 13.427 4.4915 13.447 4.47113 13.4649C4.45043 13.4832 4.42353 13.4933 4.39976 13.4933H2.79976C2.78289 13.4933 2.75721 13.4837 2.73357 13.4597C2.71004 13.4357 2.69977 13.4089 2.69977 13.3899V6.27124C2.69977 6.24563 2.71033 6.21746 2.72838 6.19628C2.74624 6.17533 2.76517 6.16786 2.77977 6.16786H4.39976C4.41664 6.16786 4.44232 6.17749 4.46596 6.20153C4.48949 6.22547 4.49975 6.25234 4.49975 6.27124V13.4103Z"
                                fill="#00B549"
                                stroke="#00B549"
                                stroke-width="0.2"
                            />
                            <path
                                d="M10.78 5.35758C10.0608 5.35758 9.37419 5.64081 8.87998 6.10511V6.06775C8.87998 5.93413 8.81525 5.80845 8.72628 5.71798C8.63742 5.62761 8.5131 5.56096 8.37997 5.56096H6.37999C6.2621 5.56096 6.1392 5.61304 6.04618 5.69543C5.95207 5.77879 5.87998 5.90105 5.87998 6.0474V13.6339C5.87998 13.7813 5.95316 13.8995 6.04907 13.9782C6.14357 14.0557 6.26575 14.1 6.37999 14.1H8.57999C8.69423 14.1 8.8164 14.0557 8.9109 13.9782C9.00681 13.8995 9.07999 13.7813 9.07999 13.6339V9.24063C9.07999 8.616 9.5235 8.14062 10.08 8.14062C10.3743 8.14062 10.6482 8.25265 10.8487 8.4565L10.8486 8.4566L10.8525 8.46015C11.022 8.6153 11.1 8.86544 11.1 9.2203V13.5932C11.1 13.7268 11.1647 13.8525 11.2537 13.943C11.3425 14.0334 11.4669 14.1 11.6 14.1H13.6C13.7331 14.1 13.8574 14.0334 13.9463 13.943C14.0352 13.8525 14.1 13.7269 14.1 13.5932V8.34573C14.1 6.66639 12.8382 5.35758 11.2 5.35758H10.78ZM10.1 7.53384L10.0976 7.5339C9.1998 7.55563 8.49998 8.29502 8.49998 9.24066V13.4932H6.49998V6.16775H8.29996V7.36945V7.61377L8.47127 7.43956L9.01126 6.89041L9.01126 6.89041L9.03125 6.87008L9.03704 6.86419L9.04178 6.85744C9.42258 6.3153 10.0917 5.96437 10.8 5.96437H11.22C12.5005 5.96437 13.4999 7.01356 13.4999 8.34575V13.4932H11.7195L11.7 9.22032H11.6L11.7 9.21987L11.7 9.22007C11.6999 8.7157 11.5755 8.32065 11.2913 8.03157C10.9721 7.70698 10.5462 7.53387 10.1 7.53384Z"
                                fill="#00B549"
                                stroke="#00B549"
                                stroke-width="0.2"
                            />
                        </svg>
                        <a
                            class="ellipsis link-style"
                            href="{{ $root.accountInformation.companyLinkedIn }}"
                            target="_blank"
                            title="{{ $root.accountInformation.companyLinkedIn }}"
                        >
                            {{ $root.accountInformation.companyLinkedIn }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
