<div class="our-vacancies-wrapper vertical-items">
    <div class="vacancies-nav">
        <div class="field-block">
            <p class="field-block__title">{{ 'Position' | translate }}</p>
            <select-single-virtualized
                placeholder="'Position'"
                options="vm.uniqueVacancyNames"
                selected-value="vm.selectedVacancyPosition"
                path-to-key="'value'"
                is-searchable="true"
                on-change="(vm.onSelectVacancy)"
            ></select-single-virtualized>
        </div>

        <div class="or-block">{{ 'or' | translate }}</div>

        <div class="field-block">
            <p class="field-block__title">{{ 'Location' | translate }}</p>
            <select-single-virtualized
                placeholder="'Location'"
                options="vm.vacanciesLocation"
                selected-value="vm.selectedCountry"
                path-to-value="'country'"
                path-to-label="'displayCountry'"
                path-to-key="'value'"
                is-searchable="true"
                on-change="(vm.selectCountry)"
            ></select-single-virtualized>
        </div>
    </div>

    <div ng-if="vm.citiesOfSelectCountry.length" class="locations-list">
        <label
            class="locations-list__item"
            ng-class="{ 'locations-list__item_selected': vm.selectedCity === location.city }"
            ng-repeat="location in vm.citiesOfSelectCountry track by $index"
        >
            <input ng-click="vm.selectCity(location.city)" type="radio" name="selectedCity" value="{{ location.city }}" ng-model="vm.selectedCity" />
            <span>{{ location.city | translate }}</span>
            <span>{{ location.count }}</span>
        </label>
    </div>

    <div class="vacancies-list">
        <div ng-if="!vm.showSearchInDescriptions" class="vacancy-card" ng-repeat="vacancy in vm.filteredVacancies track by $index">
            <div class="vacancy-card__header">
                <img ng-if="vacancy.priority == 'top'" class="vacancy-card__flame-icon" src="/images/public-flame.svg" alt="" />

                <a class="vacancy-card__position" ng-href="/i/vacancy-{{ vacancy.localId }}">{{ vacancy.position }}</a>

                <div ng-if="vacancy.industry == 'IT'" class="icon-laptop vacancy-card__laptop-icon" title="IT"></div>

                <span ng-if="vacancy.employmentType" class="vacancy-card__employment">{{ vacancy.employmentType | translate }}</span>

                <div ng-if="vacancy.salaryFrom || vacancy.salaryTo" class="vacancy-card__salary">
                    <div class="icon-coins"></div>
                    {{ vacancy.salaryFrom | salaryFormat : vacancy.salaryTo | numberSpacing }}
                    <span ng-if="vacancy.salaryFrom || vacancy.salaryTo" class="vacancy-wrappers__currency">
                        {{ vacancy.currency }}
                    </span>
                </div>

                <div ng-if="vacancy.dm">{{ 'Updated' | translate }} {{ (vacancy.dm | dateFormatShort).trim() }}</div>

                <div class="location-wrapper">
                    <div ng-show="$root.currentLang == 'ru'" class="vacancy-wrappers__location">
                        <span ng-if="vacancy.employmentType !== 'remote'">
                            <!-- prettier-ignore -->
                            <span>
                                {{ vacancy.region.googlePlaceId.cityRu }}<span ng-show="vacancy.region.country">,</span>
                            </span>
                            {{ vacancy.region.googlePlaceId.countryRu }}
                        </span>

                        <span ng-if="vacancy.employmentType === 'remote'">
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.cityRu">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.cityRu }}
                            </span>
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.countryRu && !vacancy.region.googlePlaceId.cityRu">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.countryRu }}
                            </span>
                        </span>
                    </div>
                    <div ng-show="$root.currentLang == 'en'" class="vacancy-wrappers__location">
                        <span ng-if="vacancy.employmentType !== 'remote'">
                            <!-- prettier-ignore -->
                            <span>
                                {{ vacancy.region.googlePlaceId.cityEn }}<span ng-show="vacancy.region.country">,</span>
                            </span>
                            {{ vacancy.region.googlePlaceId.countryEn }}
                        </span>

                        <span ng-if="vacancy.employmentType === 'remote'">
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.cityEn">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.cityEn }}
                            </span>
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.countryEn && !vacancy.region.googlePlaceId.cityEn">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.countryEn }}
                            </span>
                        </span>
                    </div>
                    <div ng-show="$root.currentLang == 'ua'" class="vacancy-wrappers__location">
                        <span ng-if="vacancy.employmentType !== 'remote'">
                            <!-- prettier-ignore -->
                            <span>
                                {{ vacancy.region.googlePlaceId.cityUa }}<span ng-show="vacancy.region.country">,</span>
                            </span>
                            {{ vacancy.region.googlePlaceId.countryUa }}
                        </span>

                        <span ng-if="vacancy.employmentType === 'remote'">
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.cityUa">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.cityUa }}
                            </span>
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.countryUa && !vacancy.region.googlePlaceId.cityUa">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.countryUa }}
                            </span>
                        </span>
                    </div>
                    <div ng-show="$root.currentLang == 'pl'" class="vacancy-wrappers__location">
                        <span ng-if="vacancy.employmentType !== 'remote'">
                            <!-- prettier-ignore -->
                            <span>
                                {{ vacancy.region.googlePlaceId.cityPl }}<span ng-show="vacancy.region.country">,</span>
                            </span>
                            {{ vacancy.region.googlePlaceId.countryPl }}
                        </span>

                        <span ng-if="vacancy.employmentType === 'remote'">
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.cityUa">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.cityPl }}
                            </span>
                            <span ng-if="vacancy.employmentType === 'remote' && vacancy.region.googlePlaceId.countryPl && !vacancy.region.googlePlaceId.cityPl">
                                {{ 'remote_1' | translate }}, {{ vacancy.region.googlePlaceId.countryPl }}
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="vacancy-card__content" ng-bind-html="vacancy.descr | trust"></div>
        </div>

        <div ng-if="vm.showSearchInDescriptions" class="">
            <div ng-if="vm.showSearchInDescriptionsLink">
                <h1>{{ 'There are no vacancies for the query' | translate }} «{{ vm.searchInDescriptionsValue }}»</h1>
                <a
                    ng-click="vm.searchInDescription($event)"
                    href="#"
                    translate="{{ 'Search for in job descriptions' | translate : { value: vm.searchInDescriptionsValue } }}"
                ></a>
            </div>
            <div ng-if="!vm.showSearchInDescriptionsLink">
                <h1>{{ 'The description searching did not return any results' | translate }}</h1>
            </div>
        </div>
    </div>
</div>
