<div id="calendar-page" class="controller-view">
    <!--Integration-->
    <div ng-show="$root.calendarShow === false && !$root.me.outlookCalendar" class="integration-section">
        <div class="integration-head">
            <div class="integration-head__header">{{ 'Integration_with_Calendar' | translate }}:</div>
            <div ng-show="showConnectButtom" class="integration-head__buttons">
                <button-with-icon
                    ng-click="addGoogleCalendarForCs()"
                    type="'secondary'"
                    text="'Google'"
                    icon-name="'google-icon'"
                    colorful-icon="'true'"
                ></button-with-icon>

                <button-with-icon
                    ng-click="addOutlookcalendar()"
                    type="'secondary'"
                    text="'Microsoft'"
                    icon-name="'microsoft-icon'"
                    colorful-icon="'true'"
                ></button-with-icon>
            </div>
        </div>

        <div class="integration-block">
            <div class="integration-block__title">{{ 'There_are' | translate }}:</div>
            <ul class="advantages-list">
                <li>{{ 'interviews_of_your_vacancies' | translate }}</li>
                <li>{{ 'your_tasks' | translate }}</li>
                <li>{{ 'target_dates_deadlines_of_your_vacancies' | translate }}</li>
            </ul>
            {{ 'You_will_be_notified_about' | translate }}
            {{ 'You_will_be_able_to_turn' | translate }}
            {{ 'You_will_be_able_to_create_meetings' | translate }}
        </div>

        <!--        <div class="integration-block">-->
        <!--            <p class="integration-block__title">-->
        <!--                {{ 'Please note!' | translate }}-->
        <!--            </p>-->
        <!--            {{ 'activate pop-up windows' | translate }}-->
        <!--            <img ng-click="enlargeImage('/images/sprite/allow-popUps.png')" class="gmail-reintegration__img-icon" src="..\images\sprite\allow-popUps.png" />-->
        <!--        </div>-->
    </div>

    <!--Calendar Google-->
    <div ng-if="($root.calendarShow || $root.me.outlookCalendar) && selectedCalendar && calendarsForGet" class="calendar-container">
        <div ng-show="($root.calendarShow || $root.me.outlookCalendar) && selectedCalendar && calendarsForGet" class="calendar">
            <iframe
                id="calendar_iframe"
                ng-show="selectedCalendar && calendarsForGet"
                style="border: 0"
                ng-src="{{ getCalendarFrame(selectedCalendar, calendarsForGet) }}"
                width="100%"
                height="600"
                frameborder="0"
                scrolling="no"
            ></iframe>
        </div>

        <div class="right-block">
            <div ng-show="$root.calendarShow && calendarsForCheck" class="calendar-settings">
                <div class="integration-block__title" style="padding-bottom: 12px">{{ 'Settings' | translate }}</div>

                <div class="switcher-wrapper">
                    <span>{{ 'All events' | translate }}</span>
                    <toggle-component
                        class="search-type-toggle"
                        style="display: flex"
                        checked="addOnlyCreatedByMeGoogle"
                        on-change="(addOnlyCreatedByMeGoogleHandler)"
                        always-checked-style="true"
                    ></toggle-component>
                    <span>{{ 'Which I created' | translate }}</span>
                </div>

                <hr />

                <div class="calendar-item" ng-repeat="calendar in calendarsForCheck track by $index">
                    <checkbox-component
                        meta="calendar"
                        on-change="(updateCalendar)"
                        is-checked="calendar.checked"
                        disabled="calendar.isCleverStaffEvent"
                    ></checkbox-component>
                    <div ng-click="updateCalendar(_, calendar)" ng-class="{ 'calendar-item_disabled-text': calendar.isCleverStaffEvent }">
                        {{ calendar.name }}
                    </div>
                </div>

                <div ng-show="selectedCalendar" class="calendar-settings__buttons">
                    <button-component
                        ng-click="deleteGoogleCalendarForCs()"
                        class-name="'calendar-settings__button'"
                        text="getDisconnectBtnText('Google Calendar')"
                        type="'danger'"
                        size="'small'"
                    ></button-component>

                    <button-component
                        ng-if="!$root.me.outlookCalendar"
                        ng-click="addOutlookcalendar()"
                        class-name="'calendar-settings__button'"
                        text="getIntegrationBtnText('Outlook Calendar')"
                        type="'secondary'"
                        size="'small'"
                    ></button-component>
                </div>
            </div>

            <!--Google & Outlook -->
            <div ng-show="$root.me.outlookCalendar && $root.calendarShow" class="integration-block">
                <div class="integration-block__title">
                    {{ 'You successfully integrated your Outlook Calendar with your CleverStaff account' | translate }}
                </div>
                <div>{{ 'Now in your Outlook Calendar will be displayed the following events' | translate }}:</div>
                <ul class="advantages-list">
                    <li>{{ 'interviews_of_your_vacancies' | translate }}</li>
                    <li>{{ 'your_tasks' | translate }}</li>
                    <li>{{ 'target_dates_deadlines_of_your_vacancies' | translate }}</li>
                </ul>
                <div>{{ 'All these events will be displayed only for vacancies you are responsible for' | translate }}.</div>
                <span>
                    {{ 'You_will_be_notified_about_outlook' | translate }}. {{ 'You_will_be_able_to_turn' | translate }}
                    {{ 'You_will_be_able_to_create_meetings' | translate }}
                </span>

                <div class="switcher-wrapper switcher-wrapper_outlook">
                    <span>{{ 'All events' | translate }}</span>
                    <toggle-component
                        class="search-type-toggle"
                        style="display: flex"
                        checked="addOnlyCreatedByMeOutlook"
                        on-change="(addOnlyCreatedByMeOutlookHandler)"
                        always-checked-style="true"
                    ></toggle-component>
                    <span>{{ 'Which I created' | translate }}</span>
                </div>

                <div ng-if="$root.me.outlookCalendar" class="outlook-block__buttons">
                    <button-component
                        ng-if="!$root.calendarShow"
                        ng-click="addGoogleCalendarForCs()"
                        ng-style="{ width: $root.calendarShow ? '100%' : 'auto' }"
                        class-name="'calendar-settings__button'"
                        text="getIntegrationBtnText('Google Calendar')"
                        type="'secondary'"
                        size="'small'"
                    ></button-component>

                    <button-component
                        ng-click="deleteOutLookCalendar()"
                        ng-style="{ width: $root.calendarShow ? '100%' : 'auto' }"
                        class-name="'calendar-settings__button'"
                        text="getDisconnectBtnText('Outlook Calendar')"
                        type="'danger'"
                        size="'small'"
                    ></button-component>
                </div>
            </div>
        </div>
    </div>

    <!--Calendar Outlook only-->
    <div ng-show="$root.me.outlookCalendar && !$root.calendarShow && !deleteOutlookv2" class="integration-section integration-block">
        <div class="integration-block__title">
            {{ 'You successfully integrated your Outlook Calendar with your CleverStaff account' | translate }}
        </div>

        <div>{{ 'Now in your Outlook Calendar will be displayed the following events' | translate }}:</div>
        <ul class="advantages-list">
            <li>{{ 'interviews_of_your_vacancies' | translate }}</li>
            <li>{{ 'your_tasks' | translate }}</li>
            <li>{{ 'target_dates_deadlines_of_your_vacancies' | translate }}</li>
        </ul>

        <div>{{ 'All these events will be displayed only for vacancies you are responsible for' | translate }}.</div>
        <span>
            {{ 'You_will_be_notified_about_outlook' | translate }}. {{ 'You_will_be_able_to_turn' | translate }}
            {{ 'You_will_be_able_to_create_meetings' | translate }}
        </span>

        <div class="switcher-wrapper switcher-wrapper_outlook">
            <span>{{ 'All events' | translate }}</span>
            <toggle-component
                class="search-type-toggle"
                style="display: flex"
                checked="addOnlyCreatedByMeOutlook"
                on-change="(addOnlyCreatedByMeOutlookHandler)"
                always-checked-style="true"
            ></toggle-component>
            <span>{{ 'Which I created' | translate }}</span>
        </div>

        <div ng-if="$root.me.outlookCalendar" class="outlook-block__buttons">
            <button-component
                ng-click="addGoogleCalendarForCs()"
                class-name="'calendar-settings__button'"
                text="getIntegrationBtnText('Google Calendar')"
                type="'secondary'"
                size="'small'"
            ></button-component>

            <button-component
                ng-click="deleteOutLookCalendar()"
                class-name="'calendar-settings__button'"
                text="getDisconnectBtnText('Outlook Calendar')"
                type="'danger'"
                size="'small'"
            ></button-component>
        </div>
    </div>
</div>

<script>
    function HandlePopupResult(code) {
        var $element = $('#calendar-page');
        var scope = angular.element($element).scope();
        scope.outLookWatch = code;
        scope.$apply();
    }
</script>
