<!DOCTYPE html>
<html class="notranslate" lang="en" ng-app="RecruitingApp" bindonce>
    <head>
        <!--     Google Tag Manager -->
        <script>
            (function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js',
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-5WBTBTS');
        </script>
        <!--     End Google Tag Manager -->
        <!--     Hotjar Tracking Code for cleverstaff.net -->
        <script>
            (function (h, o, t, j, a, r) {
                h.hj =
                    h.hj ||
                    function () {
                        (h.hj.q = h.hj.q || []).push(arguments);
                    };
                h._hjSettings = { hjid: 1484746, hjsv: 6 };
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
        <meta charset="utf-8" />
        <meta http-equiv="cache-control" content="no-cache" />
        <meta http-equiv="expires" content="0" />
        <meta http-equiv="pragma" content="no-cache" />
        <script>
            (function () {
                var w = window;
                var ic = w.Intercom;
                if (typeof ic === 'function') {
                    ic('reattach_activator');
                    ic('update', intercomSettings);
                } else {
                    var d = document;
                    var i = function () {
                        i.c(arguments);
                    };
                    i.q = [];
                    i.c = function (args) {
                        i.q.push(args);
                    };
                    w.Intercom = i;

                    function l() {
                        var s = d.createElement('script');
                        s.type = 'text/javascript';
                        s.async = true;
                        s.src = 'https://widget.intercom.io/widget/s7ks8wjn';
                        var x = d.getElementsByTagName('script')[0];
                        x.parentNode.insertBefore(s, x);
                    }

                    if (w.attachEvent) {
                        w.attachEvent('onload', l);
                    } else {
                        w.addEventListener('load', l, false);
                    }
                }
            })();
        </script>

        <script type="text/javascript">
            if (window.location.pathname.replace(/[A-Za-z$-]/g, '') != '/!') {
                window.location = window.location.protocol + '//' + window.location.host;
            }
            var ua = window.navigator.userAgent;
            var userLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;
            var msie = ua.indexOf('MSIE ');
            if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
                window.location.replace('/?q=ie');
            }

            let d, v;
            if (/iP(hone|od|ad)/.test(navigator.platform)) {
                v = navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);
                d = parseInt(v[1], 10) + '.' + parseInt(v[2], 10) + '.' + parseInt(v[3] || 0, 10);
                if (d === '12.4.0') {
                    if (userLang === 'ru') {
                        alert('Ваша версия IOS не поддерживается CleverStaff. Обновите пожалуйста до последней актульной версии');
                    } else if (userLang === 'ua') {
                        alert('Ваша версія IOS не підтримується CleverStaff. Обновіть будь ласка до останньої актульної версії');
                    } else {
                        alert('Your IOS version is not supported by CleverStaff. Please update to the latest current version');
                    }
                }
            }
        </script>
        <title translate="{{ $state.current.data.title }}">CleverStaff</title>
        <script src="https://apis.google.com/js/api.js"></script>
        <script src="https://apis.google.com/js/platform.js?onload=init"></script>
        <!--    <script src="https://apis.google.com/js/client.js?onload=onLoadCallback" async defer></script>-->
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,300,700" rel="stylesheet" type="text/css" />
        <link href="https://fonts.googleapis.com/css?family=Roboto:400,700,300" rel="stylesheet" type="text/css" />
        <LINK REL="SHORTCUT ICON" HREF="images/sprite/favicon.png?v=915" />
        <link rel="stylesheet" href="dist/css/lib-styles.min.css?v=915" />
        <link rel="stylesheet" href="dist/css/react-styles.css?v=915" />
        <link rel="stylesheet" href="dist/css/styles.min.css?v=915" />
        <LINK REL="SHORTCUT ICON" HREF="images/sprite/favicon.png?v=915" />
        <!--        <link rel="stylesheet" type="text/css" media="print" href="dist/css/print.css" />-->

        <!--<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>-->
        <script type="text/javascript" src="dist/js/mainLib.js?v=915"></script>

        <!--<link  href="http://fonts.googleapis.com/css?family=Reenie+Beanie:regular" rel="stylesheet" type="text/css">-->

        <style>
            html {
                /*height: 100%;*/
                background: none;
            }

            body {
                background-color: #f8f9fa;
                height: 100%;
                font-size: 14px;
            }
        </style>
        <script>
            $(document).click(function (event) {
                if ($(event.target).closest('openBox').length) return;
                $('openBox').fadeOut('slow');
                event.stopPropagation();
            });
        </script>
        <script type="text/javascript">
            function openbox(id) {
                display = document.getElementById(id).style.display;
                if (display == 'block') {
                    document.getElementById(id).style.display = 'none';
                } else {
                    document.getElementById(id).style.display = 'block';
                }
            }
        </script>
        <script>
            (function () {
                var w = window;
                var ic = w.Intercom;
                let setIntervalIntercom = setInterval(() => {
                    if (window.intercomSettings) {
                        clearInterval(setIntervalIntercom);
                        if (typeof ic === 'function') {
                            ic('reattach_activator');
                            ic('update', intercomSettings);
                        } else {
                            var d = document;
                            var i = function () {
                                i.c(arguments);
                            };
                            i.q = [];
                            i.c = function (args) {
                                i.q.push(args);
                            };
                            w.Intercom = i;

                            function l() {
                                var s = d.createElement('script');
                                s.type = 'text/javascript';
                                s.async = true;
                                s.src = 'https://widget.intercom.io/widget/fcijt4tn';
                                var x = d.getElementsByTagName('script')[0];
                                x.parentNode.insertBefore(s, x);
                            }

                            if (w.attachEvent) {
                                w.attachEvent('onload', l);
                            } else {
                                w.addEventListener('load', l, false);
                            }
                        }
                    }
                }, 300);
            })();
        </script>
    </head>
    <body ng-class="{ showVerticalScrollbar: $root.activePage == 'Report all' }" ng-controller="NavbarController">
        <div class="blurBackground" style="margin: auto" ng-cloak>
            <div ng-show="$root.loading" class="loader-container" ng-include="'partials/main-loader.html'"></div>
            <!--            <div ng-show="$root.loaderQueue.length" class="loader-container" ng-include="'partials/main-loader.html'"></div>-->
            <!--    <div  ng-show="$root.loading" class="loader-container" ng-include="'partials/new-year-loader.html'">-->
            <!--    <div  ng-show="$root.loaderQueue.length" class="loader-container" ng-include="'partials/new-year-loader.html'"></div>-->

            <div style="display: flex">
                <div
                    ng-style="$root.activePage == 'Activity Dashboard' && $root.loading && { 'z-index': '1100' }"
                    ng-include="'/partials/top-nav/left-navbar.html'"
                ></div>

                <div style="width: 100%; overflow: auto">
                    <div
                        ng-show="$root.activePage !== 'Invoice' && $root.activePage != 'onboarding'"
                        class="navbar"
                        ng-class="{ overModal: $root.me.orgs.length > 1 && !$root.hideTariff }"
                        style="margin: 0"
                        ng-style="$root.activePage == 'Activity Dashboard' && $root.loading && { 'z-index': '1100' }"
                        ng-include="'/partials/top-nav/top-navbar.html'"
                    ></div>

                    <div class="content-section">
                        <div id="crutchGoogleMap"></div>
                        <div
                            id="gamificationNotifyContainer"
                            class="gamification-notification-wrapper hideGamification"
                            ng-hide="$root.me.recrutRole === 'client'"
                        ></div>

                        <ui-view ng-if="!$root.accountDeletion.isActive || $root.activePage === 'Settings'"></ui-view>

                        <div
                            ng-if="$root.accountDeletion.isActive && $root.activePage !== 'Settings'"
                            class="account-deletion"
                            ng-include="'/partials/account-deleted.html'"
                        ></div>
                        <div id="fb-root"></div>
                    </div>
                </div>
            </div>

            <jira-service-desk-component></jira-service-desk-component>
            <news-feed-component
                ng-hide="$root.me.recrutRole === 'client' || $root.activePage === 'onboarding' || $root.topNavBarState === false"
            ></news-feed-component>
            <integration-lost-notification-component></integration-lost-notification-component>
        </div>
        <div class="backdrop-custom-fields"></div>

        <!-- SCRIPTS -->
        <script type="text/javascript" src="js/ApiKey.js?v=915"></script>
        <script
            type="text/javascript"
            src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCZul9dW5-r5WVV1479hAgVxM5Z75C-ykM&libraries=places&amp;language=en"
        ></script>
        <script type="text/javascript" src="lib/ckeditor/ckeditor.js"></script>
        <script type="text/javascript" src="lib/progressbar.js"></script>
        <script id="versionScript" type="text/javascript" src="dist/js/mainApp.js?v=915"></script>
        <script id="versionScript" type="text/javascript" src="dist/js/react-bundle.js?v=915"></script>
        <script>
            try {
                if (google) {
                }
            } catch (e) {
                window.google = undefined;
            }
        </script>

        <!--Pagination for ngTable-->
        <script id="custom/pager" type="text/ng-template">
            <div style='margin-bottom: 60px' class='pagination-block not-for-print' nav-pagination
                 ng-if='objectSize > 15 || vm.objectSize > 15'>
            </div>
        </script>

        <!-- Carrot quest BEGIN -->
        <script type="text/javascript">
            var userLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;
        </script>
        <!-- Carrot quest END -->
        <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 964011751;
            var google_custom_params = window.google_tag_params;
            var google_remarketing_only = true;
            /* ]]> */
        </script>
        <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js"></script>
        <noscript>
            <div style="display: inline">
                <img
                    style="border-style: none"
                    height="1"
                    width="1"
                    alt=""
                    src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/964011751/?guid=ON&amp;script=0"
                />
            </div>
        </noscript>

        <voitos-notice $root="$root" current-lang="$root.currentLang"></voitos-notice>
    </body>
</html>
