{"name": "complex-app-frontend", "version": "0.1.0", "private": true, "engines": {"node": "22.x.x", "pnpm": "10.x.x"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/material-nextjs": "^7.1.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@types/axios": "^0.14.4", "axios": "^1.9.0", "axios-auth-refresh": "^3.3.6", "clsx": "^2.1.1", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.2", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.89.0", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}