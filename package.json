{"name": "ng-extention", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "watch": "set NODE_ENV=dev && ng build --watch", "build-dev": "set NODE_ENV=dev && ng build && gulp dev", "build": "set NODE_ENV=prod && ng build --prod --output-hashing none && gulp prod", "build-dev-clear": "set NODE_ENV=dev gulp clear-prod && set NODE_ENV=dev && ng build && gulp dev", "build-clear": "set NODE_ENV=prod && gulp clear-dev && ng build --prod --output-hashing none && gulp prod", "build-prod-dev": "npm run build && npm run build-dev", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^8.2.14", "@angular/cdk": "~8.2.3", "@angular/common": "^8.2.14", "@angular/compiler": "^8.2.14", "@angular/core": "^8.2.14", "@angular/elements": "^8.2.14", "@angular/forms": "^8.2.14", "@angular/material": "^8.2.3", "@angular/platform-browser": "^8.2.14", "@angular/platform-browser-dynamic": "^8.2.14", "@angular/router": "^8.2.14", "@webcomponents/custom-elements": "^1.3.2", "@webcomponents/shadydom": "^1.7.1", "@webcomponents/webcomponentsjs": "^2.4.1", "del": "^5.1.0", "document-register-element": "^1.14.3", "elements-zone-strategy": "^8.0.0", "fs-extra": "^9.0.0", "hammerjs": "^2.0.8", "ngx-build-plus": "^9.0.2", "qs": "^6.9.1", "replace": "^1.1.5", "rxjs": "~6.4.0", "tslib": "^1.10.0", "webpack": "^4.41.5", "yargs": "^14.2.2", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-builders/custom-webpack": "^8.4.1", "@angular-devkit/build-angular": "^0.802.2", "@angular/cli": "^8.2.2", "@angular/compiler-cli": "^8.2.14", "@types/chrome": "0.0.93", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "^2.0.8", "@types/jest": "^25.1.0", "@types/node": "^8.9.5", "codelyzer": "^5.2.1", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.5.1", "minimist": "^1.2.0", "protractor": "~5.4.0", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "~3.5.3", "vinyl-sourcemaps-apply": "^0.2.1"}}