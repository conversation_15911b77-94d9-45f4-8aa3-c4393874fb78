import { type Locales, type LocalesFull } from 'common/enums/locales';
import { type CandidateType } from 'common/types/candidate';
import { Vacancy } from 'common/types/vacancy/vacancy';

interface Person {
    cutFullName: string;
    cutFullNameEn: string;
    firstName: string;
    fullName: string;
    fullNameEn: string;
    hideClients: boolean;
    login: string;
    middleName: string;
    orgs: string[];
    personId: string;
    recrutRole: string;
    sex: boolean;
    userId: string;
}

export interface AngularRootScope {
    indexForAdvice: any;
    vacancyShadow: any;
    me: any;
    vacancy: Vacancy;
    currentLang: Locales;
    useAmericanNameStyle: boolean;
    /**
     * Returns fullName or fullNameEn depending on $rootScope.useAmericanNameStyle
     */
    getFullNameDueToStyle: (personLikeObj: { fullName: string; fullNameEn: string }) => string;
    translate: (key: string, variable?: any) => string;
    allLanguages: Record<string, Record<LocalesFull, string>>;
    persons?: Person[];
    scorecardInSearch: () => { name: string; scoreCardId: string } | undefined;
    changeMoreAndLess: (user: CandidateType) => any;
    redirectToSuggestions?: boolean;
}
