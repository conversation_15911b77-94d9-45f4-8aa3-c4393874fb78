import { type ResponseStatuses } from 'common/enums/api';

interface BaseResponse<Status = string, Data = unknown> {
    status: ResponseStatuses | Status;
    message?: string;
    object?: Data;
    objects?: Data;
    code?: string;
}

interface APISuccess<Data = unknown> {
    status: ResponseStatuses.OK;
    message?: string;
    object?: Data;
    objects?: Data;
}

export type { APISuccess, BaseResponse };
