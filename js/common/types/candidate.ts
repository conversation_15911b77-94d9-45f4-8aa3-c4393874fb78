// All automatically parsed from json response
// TODO: decompose in the future

interface CandidateType {
    readMore: boolean;
    adviceIncreased: any;
    avgScore?: number;
    candidateId: string;
    localId: string;
    fullName: string;
    fullNameEn: string;
    firstName: string;
    lastName?: string;
    middleName?: string;
    added?: boolean;
    position: string;
    employmentType?: any;
    currency?: string;
    expirence?: Expirence | any;
    salary?: number;
    status: Stat;
    dc: number;
    dm: number;
    responsibleId: string;
    regionId?: string;
    source?: string;
    origin?: string;
    openUsers?: string;
    region?: Region;
    contacts: Contact[];
    candidateSkills?: CandidateSkill[];
    responsible: Responsible;
    files?: FileType[];
    lastAction: LastAction;
    existInBamboo: boolean;
    exactlyAppropriate: boolean;
    groups?: CandidateGroup[];
    readyRelocate?: boolean;
    relocatedRegionIds?: string;
    openStatus?: OpenStatus;
    db?: number;
    roleLevel?: string;
    photo?: string;
    education?: Education;
    employeeId?: string;
    countActiveVacancy?: number;
    languages?: Language[];
    scoreCardResultDto?: ScoreCardResultDto;
    currentPosition?: string;
    currentWorkPlace?: string;
    relatedRegions?: Region[];
}

interface CandidateSkill {
    id: number;
    candidateId: string;
    skillId: number;
    orgId: string;
    experience?: string;
    dm: number;
    skillName: string;
    type: string;
    fromGpt?: boolean;
}

interface Contact {
    contactId: number;
    personType: Type;
    personId: string;
    type: TypeEnum;
    value: string;
    orgId: string;
    dc?: number;
    default: boolean;
}

enum Type {
    Candidate = 'candidate',
}

enum TypeEnum {
    Djinni = 'djinni',
    Email = 'email',
    Facebook = 'facebook',
    Github = 'github',
    Homepage = 'homepage',
    Linkedin = 'linkedin',
    Mphone = 'mphone',
    Other = 'other',
    Skype = 'skype',
    Telegram = 'telegram',
    Viber = 'viber',
    WhatsApp = 'whatsApp',
}

enum Education {
    Associate = 'Associate',
    Bachelor = 'Bachelor',
    HighSchool = 'HighSchool',
    Master = 'Master',
}

enum Expirence {
    NoExperience = 'e00_no_experience',
    E01LessThan1Year = 'e01_less_than1year',
    E11Year = 'e1_1year',
    E22Years = 'e2_2years',
    E33Years = 'e3_3years',
    E44Years = 'e4_4years',
    E55Years = 'e5_5years',
    E610Years = 'e6_10years',
}

interface FileType {
    fileId: string;
    objId: string;
    dc: number;
    fileName: string;
    creatorId: string;
    showFiles?: boolean;
    showText?: boolean;
    showDocument?: boolean;
    url?: string;
    showImg?: boolean;
}

interface Language {
    languageId: string;
    name: string;
    level: Level | undefined;
    orgId: string;
    objId: string;
    objType: Type;
    dc: number;
    creatorId: string;
}

enum Level {
    Advanced = 'Advanced',
    Basic = 'Basic',
    Intermediate = 'Intermediate',
    Native = 'Native',
    PreIntermediate = 'Pre_Intermediate',
    Proficient = 'Proficient',
    Undefined = 'undefined',
    DontKnow = 'no_experience',
    UpperIntermediate = 'Upper_Intermediate',
}

interface CandidateGroup {
    candidateGroupId: string;
    orgId: string;
    name: string;
    creatorId: string;
    dc: number;
}

interface LastAction {
    probationPeriod?: string;
    dateEmployee?: any;
    itsCutDescr?: boolean;
    stageName?: any;
    compaignId?: any;
    test?: any;
    generalStates?: any;
    customInterviewStates?: any;
    mailTemplate?: any;
    fullName?: string;
    localId?: string;
    testId?: string;
    actionId: number;
    orgId: string;
    personId: string;
    status: ResponsibleStatus;
    type: string;
    descr: string;
    dm: number;
    candidateId: string;
    stateNew?: Stat;
    regionId?: string;
    dc?: number;
    person: Responsible;
    countCandidate: number;
    countCandidateToUpdate: number;
    creatorId: string;
    taskId?: string;
    targetDate?: number;
    vacancyId?: string;
    clientId?: string;
    stateOld?: Stat;
    vacancy?: Vacancy;
    candidate?: any;
    oldTargetDate?: number;
    descrLength?: number;
    cutDescr?: string;
    stateOldName?: string;
    stateNewName?: string;
    customStateOld?: string;
    customStateNew?: string;
    scoreCardId?: string;
    scoreCard?: ScoreCard;
    source?: Source;
    fileName?: string;
    tags?: boolean;
    targetPersonId?: string;
    targetPerson?: Responsible;
}

interface ScoreCardResultDto {
    isFull?: boolean;
    mainScore: number;
    vacancy: Vacancy;
    candidateIdAndVacancyId: string;
}

interface Responsible {
    personId?: string;
    userId?: string;
    login?: string;
    firstName?: string;
    lastName?: string;
    sex?: boolean;
    avatarId?: string;
    contacts: any[];
    orgs: any[];
    roles: any[];
    status?: ResponsibleStatus;
    recrutRole?: RecrutRole;
    countUnreadNotice: number;
    countAllNotice: number;
    otherSessionsRemoves: boolean;
    level: number;
    hideClients: boolean;
    fullNameEn: string;
    phone: string;
    cutFullNameEn: string;
    cutFullName: string;
    fullName: string;
    regionId?: string;
    photoLink?: string;
    middleName?: string;
}

enum RecrutRole {
    Admin = 'admin',
    Recruter = 'recruter',
    Researcher = 'researcher',
}

enum ResponsibleStatus {
    A = 'A',
    N = 'N',
}

interface ScoreCard {
    id: string;
    name: string;
    status: ResponsibleStatus;
    orgId: string;
    userId: string;
    schema: Schema;
    dc: number;
    dm: number;
    vacancyIdAndScoreCardId: string;
}

interface Schema {
    blocks: Block[];
}

interface Block {
    id: string;
    label: string;
    questions: Question[];
}

interface Question {
    id: string;
    label: string;
}

enum Source {
    RabotaUa = 'rabotaUa',
    WorkUa = 'workUa',
}

enum Stat {
    Archived = 'archived',
    AcceptOffer = 'accept_offer',
    ActiveSearch = 'active_search',
    AppliedPeople = 'applied_people',
    Approved = 'approved',
    Employed = 'employed',
    HrInterview = 'hr_interview',
    Interview = 'interview',
    InterviewWithTheBoss = 'interview_with_the_boss',
    Longlist = 'longlist',
    Notafit = 'notafit',
    Open = 'open',
    PassiveSearch = 'passive_search',
    SentOffer = 'sent_offer',
    Shortlist = 'shortlist',
    TechInterview = 'tech_interview',
    TestTask = 'test_task',
    Work = 'work',
    Confirmed = 'confirmed',
    UserConfirmed = 'user_confirmed',
    Rejected = 'rejected',
    EmailSent = 'email_sent',
    EmailExpired = 'email_expired',
    Expired = 'expired',
    Undefined = 'undefined',
}

interface Vacancy {
    vacancyId: string;
    localId: string;
    position: string;
    status: VacancyStatus;
    employmentType: string;
    clientId: ClientIDClass;
    interviewStatus: string;
    priority: Priority;
    hideSalary: boolean;
    attractive: boolean;
    crystals: number;
    numberOfPositions?: string;
}

interface ClientIDClass {
    clientId: string;
    orgId: string;
    localId: string;
    name: string;
    hidden: boolean;
    regionId?: string;
    logoId?: string;
}

enum Priority {
    Medium = 'medium',
    Top = 'top',
}

enum VacancyStatus {
    Canceled = 'canceled',
    Completed = 'completed',
    Inwork = 'inwork',
    Onhold = 'onhold',
    Open = 'open',
}

enum OpenStatus {
    Y = 'Y',
}

export interface Region {
    regionId: string;
    orgId: string;
    lang: Lang;
    country: string;
    area?: string;
    city?: string;
    lat: number;
    lng: number;
    fullName: string;
    googlePlaceId: GooglePlaceID;
    fromGoogle: boolean;
    displayCountry: string;
    displayCity?: string;
    displayFullName: string;
}

interface GooglePlaceID {
    googlePlaceId: string;
    countryEn: string;
    areaEn?: string;
    cityEn?: string;
    countryRu: string;
    areaRu?: string;
    cityRu?: string;
    countryUa: string;
    areaUa?: string;
    cityUa?: string;
    countryPl: string;
    areaPl?: string;
    cityPl?: string;
}

enum Lang {
    Ua = 'ua',
    En = 'en',
    Pl = 'pl',
    Ru = 'ru',
}

export type {
    CandidateGroup,
    CandidateSkill,
    CandidateType,
    Contact,
    FileType,
    Language,
    LastAction,
    TypeEnum,
    ScoreCardResultDto,
};

export enum CriterionTypes {
    position = 'position',
    salary = 'salary',
    employmentType = 'employmentType',
    experience = 'experience',
    role = 'role',
    location = 'location',
    language = 'language',
    skill = 'skill',
}

export interface Criterion {
    requiredValue?: string;
    requiredLevel?: string;
    value?: string;
    level?: string;
    type: CriterionTypes;
    noBlur?: boolean;
    key?: any;
}

export interface CandidateAdviceInfo {
    matches: Criterion[];
    notEnough: Criterion[];
    unknown: Criterion[];
}

export interface Advice {
    candidate: any;
    adviceIncreased: boolean;
    adviceAmountOfFieldsToEdit: number;
    status: string;
    candidateAdviceInfo: CandidateAdviceInfo;
    candidateId: CandidateType;
    candidateSkills: CandidateSkill[];
    exactlyAppropriate: boolean;
    existInBamboo: boolean;
    firstName: string;
    fullName: string;
    fullNameEn: string;
    localId: string;
    position: string;
    region: Region;
    regionId: string;
    scorePersent: number;
    advice: number;
}
