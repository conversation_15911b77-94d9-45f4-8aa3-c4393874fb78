import type { Locales } from 'common/enums/locales';
import { type AngularRootScope } from 'common/types/angular-types';

export const getLangLocaleByKey = (
    allLanguages: AngularRootScope['allLanguages'],
    currentLang: Locales,
    languageKey: string,
): string | undefined => {
    switch (currentLang) {
        case 'en':
            return allLanguages[languageKey]?.english;
        case 'ua':
            return allLanguages[languageKey]?.ukrainian;
        case 'pl':
            return allLanguages[languageKey]?.polish;
        case 'ru':
            return allLanguages[languageKey]?.russian;
    }
};
