controller.controller('PublicCompanyController', [
    '$scope',
    '$rootScope',
    'serverAddress',
    'Service',
    'Person',
    'Company',
    'notificationService',
    '$routeParams',
    '$window',
    '$filter',
    '$uibModal',
    '$location',
    '$state',
    '$translate',
    '$stateParams',
    function (
        $scope,
        $rootScope,
        serverAddress,
        Service,
        Person,
        Company,
        notificationService,
        $routeParams,
        $window,
        $filter,
        $uibModal,
        $location,
        $state,
        $translate,
        $stateParams,
    ) {
        $rootScope.activePublicController = 'PublicCompanyController';
        $scope.serverAddress = serverAddress;
        checkActiveSession();

        const removeLinkPrefix = function (link) {
            return link.replace(/^https?:\/\/(www\.)?/, '');
        };

        const formatCompanyFacebookLink = function (link) {
            this.headerCompanyDetails = document.querySelector('.header-company-details');

            const newLink = removeLinkPrefix(link);
            return newLink.length > Math.floor(this.headerCompanyDetails.offsetWidth / 10)
                ? `facebook ${$filter('translate')('page')}`.toLowerCase()
                : newLink;
        };

        const formatCompanyWebSiteLink = function (link) {
            this.headerCompanyDetails = document.querySelector('.header-company-details');

            const newLink = removeLinkPrefix(link);
            return newLink.length > Math.floor(this.headerCompanyDetails.offsetWidth / 10)
                ? `${newLink.split('/')[0]} ${$filter('translate')('page')}`.toLowerCase()
                : newLink;
        };

        Service.onGetCompanyInformation({ alias: getAliasName() })
            .then((resp) => {
                $rootScope.accountInformation = $rootScope.accountInformation
                    ? { ...$rootScope.accountInformation, ...resp.object }
                    : resp.object;

                if ($rootScope.accountInformation.companyFacebookPage) {
                    $rootScope.accountInformation.companyFacebookPageFormatted = formatCompanyFacebookLink(
                        $rootScope.accountInformation.companyFacebookPage,
                    );
                }
                if ($rootScope.accountInformation.companyWebSite) {
                    $rootScope.accountInformation.companyWebSiteFormatted = formatCompanyWebSiteLink(
                        $rootScope.accountInformation.companyWebSite,
                    );
                }

                if ($rootScope.accountInformation.companyLocation) {
                    const parsedLocation = JSON.parse($rootScope.accountInformation.companyLocation);
                    const { country, city, street } = parsedLocation;
                    $rootScope.accountInformation.companyLocation = Object.values({ country, city, street })
                        .filter((location) => location)
                        .join(', ');
                }
            })
            .catch((err) => {
                console.error(err);
                window.location.replace('/');
            });

        function getAliasName() {
            return $stateParams.nameAlias.replace('-vacancies', '');
        }

        function initPublicCompanyData() {
            let defaultLang;
            if (navigator.language == 'ru-RU' || navigator.language == 'ua-UA' || navigator.language == 'en-EN') {
                defaultLang = navigator.language.split('-')[1].toLowerCase();
            }
            if (defaultLang != $rootScope.currentLang) {
                defaultLang = $rootScope.currentLang;
            }

            $rootScope.loading = true;
            const isCompanyDescriptionPage = $location.$$path.includes('-vacancies/description');

            const aliasName = getAliasName();

            Company.getAllOpenVacancies({ alias: aliasName, lang: defaultLang })
                .then((companyData) => {
                    setCompanyData(companyData);
                })
                .catch((resp) => {
                    console.error(resp.message);
                })
                .finally(() => {
                    if (!isCompanyDescriptionPage) $state.go('publicCompany.ourVacancies');
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        }

        function setUserLang() {
            let defaultLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;

            if (defaultLang.length > 2) {
                defaultLang.substr(0, 2);
            }

            $rootScope.currentLang = ($rootScope.me && $rootScope.me.personParams.lang) || defaultLang;
            $translate.use($rootScope.currentLang);
        }

        function checkActiveSession() {
            Promise.all([Person.onAuthPing(), Person.onGetMe()])
                .then(([activeSession, getMeData]) => {
                    $rootScope.isActiveSession = activeSession.status === 'ok';
                    $rootScope.me = getMeData.object;
                })
                .catch((resp) => {
                    console.error(resp.message);
                })
                .finally(() => {
                    setUserLang();
                    initPublicCompanyData();
                });
        }

        function setCompanyData(companyData) {
            $rootScope.orgId = companyData.orgId;
            $scope.orgParams = companyData;
            $window.document.title = $scope.orgParams.orgName + ' ' + 'vacancies';
        }
    },
]);
