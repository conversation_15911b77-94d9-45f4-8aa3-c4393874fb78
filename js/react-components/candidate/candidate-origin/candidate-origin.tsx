import React, { type FC } from 'react';

interface PropsType {
    origin: string | undefined;
    translateFunc: (text: string) => string;
}

const CandidateOrigin: FC<PropsType> = ({ origin, translateFunc }) => {
    let originObj: { iconName: string; iconPath?: string; title: string } = { iconName: '', iconPath: '', title: '' };

    switch (origin) {
        case 'add_from_email':
            originObj = { iconName: 'Mail.svg', title: 'This candidate was added from resume from email' };
            break;

        case 'update_from_email':
            originObj = { iconName: 'Mail.svg', title: 'This candidate was updated from resume from email' };
            break;

        case 'add_from_pracuj':
            originObj = { iconName: 'pracuj_logo.svg', title: 'This candidate was added from resume from pracuj' };
            break;

        case 'update_from_pracuj':
            originObj = { iconName: 'pracuj_logo.svg', title: 'This candidate was updated from resume from pracuj' };
            break;

        case 'add_from_linkedin':
        case 'add_from_linkedinNew':
            originObj = {
                iconName: 'linkedinLogo.svg',
                title: 'This candidate was added from resume from linkedin',
            };
            break;

        case 'add_from_archive':
            originObj = {
                iconName: 'archive-logo.svg',
                title: 'This candidate was added from resume from archive',
            };
            break;

        case 'add_from_excel':
            originObj = {
                iconName: 'excel-logo.svg',
                title: 'This candidate was added from resume from excel',
            };
            break;

        case 'add_from_dou':
            originObj = {
                iconName: 'dou-logo.svg',
                title: 'This candidate was added from resume from dou',
            };
            break;

        case 'add_from_delucru':
            originObj = {
                iconName: 'delucruLogo.png',
                title: 'This candidate was added from resume from delucru.md',
            };
            break;

        case 'add_from_rabotamd':
            originObj = {
                iconName: 'rabotaMdLogo.png',
                title: 'This candidate was added from resume from rabota.md',
            };
            break;

        case 'update_from_linkedin':
        case 'update_from_linkedinNew':
            originObj = {
                iconName: 'linkedinLogo.svg',
                title: 'This candidate was updated from resume from linkedin',
            };
            break;

        case 'add_from_hh':
            originObj = {
                iconName: 'hhLogo.svg',
                title: 'This candidate was added from resume from headhunter',
            };
            break;

        case 'add_from_grc':
            originObj = {
                iconName: 'grc.svg',
                title: 'This candidate was added from resume from GRC',
            };
            break;

        case 'update_from_hh':
            originObj = {
                iconName: 'hhLogo.svg',
                title: 'This candidate was updated from resume from headhunter',
            };
            break;

        case 'add_from_workua':
            originObj = {
                iconName: 'workuaLogo.svg',
                title: 'This candidate was added from resume from work.ua',
            };
            break;

        case 'update_from_workua':
            originObj = {
                iconName: 'workuaLogo.svg',
                title: 'This candidate was updated from resume from work.ua',
            };
            break;

        case 'add_from_djinni':
            originObj = {
                iconName: 'djinni-logo.svg',
                title: 'This candidate was added from resume from djinni.co',
            };
            break;

        case 'add_from_rabotaua':
            originObj = {
                iconName: 'Rabota_ua_logo.svg',
                title: 'This candidate was added from resume from rabota.ua',
            };
            break;

        case 'update_from_rabotaua':
            originObj = {
                iconName: 'Rabota_ua_logo.svg',
                title: 'This candidate was updated from resume from rabota.ua',
            };
            break;

        case 'add_from_superjob':
            originObj = {
                iconName: 'superJob.svg',
                title: 'This candidate was added from resume from super job',
            };
            break;

        case 'update_from_superjob':
            originObj = {
                iconName: 'superJob.svg',
                title: 'This candidate was updated from resume from super job',
            };
            break;

        case 'add_from_cvlv':
            originObj = {
                iconName: 'favicon_cvlv.svg',
                title: 'This candidate was added from resume from cvlv',
            };
            break;

        case 'add_from_jobstutby':
            originObj = {
                iconName: 'jobs_tut_by.svg',
                title: 'This candidate was added from resume from jobsTutBy',
            };
            break;

        case 'update_from_jobstutby':
            originObj = {
                iconName: 'jobs_tut_by.svg',
                title: 'This candidate was updated from resume from jobsTutBy',
            };
            break;

        case 'update_from_cvlv':
            originObj = {
                iconName: 'favicon_cvlv.svg',
                title: 'This candidate was updated from resume from cvlv',
            };
            break;

        case 'add_from_jobkg':
            originObj = {
                iconName: 'jobkg.svg',
                title: 'This candidate was added from resume from jobkg',
            };
            break;

        case 'update_from_jobkg':
            originObj = {
                iconName: 'jobkg.svg',
                title: 'This candidate was updated from resume from jobkg',
            };
            break;

        case 'add_from_rabotauz':
            originObj = {
                iconName: 'jobkg.svg',
                title: 'This candidate was added from resume from rabotauz',
            };
            break;

        case 'update_from_rabotauz':
            originObj = {
                iconName: 'jobkg.svg',
                title: 'This candidate was updated from resume from rabotauz',
            };
            break;

        case 'add_manually':
            originObj = {
                iconName: 'manually.svg',
                title: 'This candidate was added manually',
            };
            break;

        case 'add_from_recall':
            originObj = {
                iconName: 'response.svg',
                title: 'This candidate was added from recall',
            };
            break;

        case 'add_from_recommendation':
            originObj = {
                iconName: 'recommend.svg',
                title: 'This candidate was added from recall',
            };
            break;

        case 'add_from_jobcannon':
            originObj = {
                iconName: 'job-cannon-logo.png',
                title: 'This candidate was added from JobCannon',
            };
            break;
        case 'add_from_wandify':
            originObj = {
                iconName: 'wandify-logo.png',
                iconPath: '/images/wandify/wandify-logo.png',
                title: 'This candidate was added from Wandify',
            };
            break;
    }

    return origin ? (
        <img
            style={{ width: 20 }}
            src={originObj.iconPath || `/images/sprite/${originObj.iconName}`}
            title={translateFunc(originObj.title)}
            alt=""
        />
    ) : null;
};

export { CandidateOrigin };
