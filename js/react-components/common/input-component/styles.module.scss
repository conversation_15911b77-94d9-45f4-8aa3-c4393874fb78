@import 'common/styles/global-variables.module';
@import 'common/styles/mixins';

.inputWrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 42px;
    padding: 12px;
    background-color: $main-white;
    transition: all 0.2s ease;
    @include border-small;

    &:hover {
        border-color: $dark-grey;

        .clearButton {
            opacity: 1;
        }
    }

    &_focused {
        background-color: $pale-grey;
        border-color: $semi-black;

        &:hover {
            border-color: $semi-black;
        }
    }

    &_error {
        @include border-error;
    }
}

.icon {
    @include control-icon-size;
    margin-right: 8px;
    filter: $filter-dark-grey;
}

.input {
    width: 100%;
    margin: 0 auto 0 0;
    font-family: $main-font-family, serif;
    font-size: $main-font-size;
    color: $main-black;
    background-color: transparent;
    border: none;

    &::placeholder {
        color: $semi-black;
    }
}

.clearButton {
    flex-shrink: 0;
    margin-left: 5px;
    cursor: pointer;
    opacity: 0;

    &:hover {
        fill: $main-black;
    }
}

.disabled {
    pointer-events: none;
    cursor: pointer;
    background-color: $background-grey;
}
