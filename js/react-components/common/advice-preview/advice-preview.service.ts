import axios from 'axios';

export const editAdvice = (props: any): any => {
    const { payload } = props;
    return axios.post('/hr/candidate/editForAdvice', payload).then((r) => r.data.object);
};

export const getAllCountries = (currentLang: string): any => {
    return axios.get('/hr/region/getAllCountryForRelocate', { params: { lang: currentLang } }).then((r) => r.data);
};

export const getAllCities = (country: string): any => {
    return axios.get('/hr/region/autocompleteCity', { params: { country: country } }).then((r) => r.data);
};

export const getLanguages = (): any => {
    return axios.get('/hr/candidate/getLanguages').then((r) => r.data);
};

export const getCustomSkills = (): any => {
    return axios.get('/hr/vacancy/getSkillsByCategory').then((r) => r.data);
};

export const getSkillsByCategory = (category: string): any => {
    return axios.get('/hr/vacancy/getSkillsByCategory', { params: { category: category } }).then((r) => r.data);
};

export const getFullCandidate = (id: string): any => {
    return axios.get('/hr/candidate/get', { params: { localId: id } }).then((r) => r.data.object);
};

export const editCandidateAdvice = (props: any): any => {
    return axios.post('/hr/vacancy/interview/editCandidateAdvice', props).then((r) => r.data.object);
};

export const setDataClarified = (props: any): any => {
    return axios.post('/hr/vacancy/interview/advice/dataClarified', props).then((r) => r.data.object);
};

export const sendAdviceStatistic = (props: any): any => {
    return axios.post('/hr/candidate/saveAdvicePopupClick', props).then((r) => r.data.object);
};
