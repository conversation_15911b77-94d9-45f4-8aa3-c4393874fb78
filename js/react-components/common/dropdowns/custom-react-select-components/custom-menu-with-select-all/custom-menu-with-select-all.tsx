import clsx from 'clsx';
import CheckIcon from 'images/redesign/svg-icons/check.svg';
import React, { useLayoutEffect, useState } from 'react';
import { getService } from 'react-in-angularjs';
import { components, type GroupBase, type MenuProps } from 'react-select';

import classes from './styles.module.scss';

const CustomMenuWithSelectAll = (props: MenuProps<any, false, GroupBase<any>>): React.ReactElement => {
    const { selectProps, isMulti } = props;
    const translate = getService('$translate').instant;

    const [isNothingFound, setIsNothingFound] = useState(false);

    useLayoutEffect(() => {
        if (props.innerRef && typeof props.innerRef !== 'function' && props.innerRef.current) {
            setIsNothingFound(props.innerRef.current.innerHTML.includes('-NoOptionsMessage'));
        }
    }, [props.selectProps.inputValue, props.innerRef]);

    const isAllSelected = (): boolean => {
        if (props.options[1]?.options) {
            return props.options.flatMap((item) => item.options).length === props.getValue().length;
        }
        return props.options.length === props.getValue().length;
    };

    const onClickSelectAll = (): void => {
        props.selectOption({ label: translate('Select all') as string, value: 'select-*-all' });
    };

    const onClickSelectFound = (): void => {
        props.selectOption({ label: translate('Select found') as string, value: 'select-*-found' });
    };

    return (
        <>
            {!!(isMulti && !isNothingFound && props.options?.length && !selectProps.inputValue) && (
                <div
                    className={clsx(
                        classes.selectAllWrapper,
                        props.selectProps.isSearchable && classes.selectAllWrapper_withSearch,
                    )}
                    onClick={onClickSelectAll}
                >
                    <span
                        className={clsx(classes.styledCheckbox, isAllSelected() && classes.styledCheckbox__checked)}
                        aria-hidden={true}
                    >
                        <CheckIcon width={14} height={14} fill={'#fff'} />
                    </span>
                    {props.selectProps.selectAllText || translate('Select all')}
                </div>
            )}
            {isMulti && !isNothingFound && props.options?.length && selectProps.inputValue && !isAllSelected() ? (
                <div
                    className={clsx(
                        classes.selectAllWrapper,
                        props.selectProps.isSearchable && classes.selectAllWrapper_withSearch,
                    )}
                    onClick={onClickSelectFound}
                >
                    {translate('Select all found')}
                </div>
            ) : null}
            <components.Menu {...props}>{props.children}</components.Menu>
        </>
    );
};

export { CustomMenuWithSelectAll };
