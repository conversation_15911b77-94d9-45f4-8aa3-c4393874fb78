import { InputProps, components } from 'react-select';
import classes from './styles.module.scss';
import ClearIcon from 'images/redesign/svg-icons/close.svg';
import React from 'react';
import clsx from 'clsx';

const CustomInput = (props: InputProps<any, true>) => {
    return (
        <div className={classes.inputWrapper}>
            <components.Input
                {...props}
                aria-autocomplete={'none'}
                aria-expanded={false}
                aria-haspopup={false}
                className={classes.input}
            />
            <ClearIcon
                className={clsx(classes.clearIndicator, props.value && classes.clearIndicator__visible)}
                width={18}
                height={18}
                onClick={() => props.selectProps.onInputChange('', { prevInputValue: '', action: 'input-change' })}
            />
        </div>
    );
};

export { CustomInput };
