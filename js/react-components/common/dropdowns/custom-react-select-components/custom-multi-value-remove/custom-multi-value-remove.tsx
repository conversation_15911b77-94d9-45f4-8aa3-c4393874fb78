import ClearIcon from 'images/redesign/svg-icons/close.svg';
import React from 'react';
import { components, type MultiValueRemoveProps } from 'react-select';

export const CustomMultiValueRemove = (props: MultiValueRemoveProps<any, true>): React.ReactNode => {
    return (
        <components.MultiValueRemove {...props}>
            <ClearIcon width={16} height={16} />
        </components.MultiValueRemove>
    );
};
