@import 'common/styles/global-variables.module';

.skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    color: $secondary-black;

    .restSkillsTooltip {
        z-index: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        max-width: 300px;
        padding: 8px;
        font-size: $main-font-size;
        color: inherit;
        background-color: $main-white;
        border: 1px solid $border-grey;
        border-radius: 4px;
        box-shadow: $main-box-shadow;
    }
}

.skillTag {
    padding: 4px 8px;
    background-color: $pale-grey;
    border-radius: 4px;
}

.restSkillsButton {
    &:hover {
        background-color: $border-grey;
        outline: 1px solid $dark-grey;
    }
}
