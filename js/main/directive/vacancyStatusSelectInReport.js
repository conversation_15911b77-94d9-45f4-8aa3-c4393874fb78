(function () {
    directives.directive('vacancyStatusSelectInReport', [
        'Service',
        '$filter',
        '$timeout',
        function (Service, $filter, $timeout) {
            return {
                restrict: 'E',
                scope: {
                    options: '=options',
                    model: '=model',
                    path: '=path',
                    countPath: '=countPath',
                    placeholder: '=placeholder',
                    disabled: '=disabled',
                    method: '=method',
                    isLoading: '=isLoading',
                    displayMultSelection: '=displayMultSelection',
                    restriction: '=?restriction',
                    translateAll: '=?',
                },
                link: function (scope, element, attrs) {
                    if (!Array.isArray(scope.model))
                        throw 'custom-select-with-checkboxes | typeof scope.model must be "array"';
                    if (!Array.isArray(scope.options))
                        throw 'custom-select-with-checkboxes | typeof scope.options must be "array"';
                    if (!attrs.id) throw 'custom-select-with-checkboxes | element should have ID attr';

                    scope.multSelection = scope.displayMultSelection === undefined ? true : scope.displayMultSelection;
                    scope.translateAll = !scope.translateAll ? 'Select all' : scope.translateAll;
                    scope.id = attrs.id;
                    scope.isAllSelected = false;
                    scope.isOpenOpt = true;
                    scope.getPropertyValue = Service.getObjectPropertyValueByPath;
                    scope.getCountValue = Service.getObjectPropertyCountPath;

                    scope.checkForRestriction = function (opt, e) {
                        const checkedOptions = scope.options.filter((option) => scope.isChecked(option)).length;
                        if (!scope.isChecked(opt) && checkedOptions < scope.restriction) {
                            opt.checked = e.target.checked = true;
                            return true;
                        } else {
                            opt.checked = e.target.checked = false;
                            return false;
                        }
                    };

                    scope.selectOpt = function (opt, e) {
                        if (scope.method) scope.method(opt, e);
                        if (scope.restriction) {
                            if (scope.checkForRestriction(opt, e)) {
                                if (e.target.checked) scope.model.push(opt);
                                else {
                                    for (let i = 0; i < scope.model.length; i++) {
                                        opt.checked = scope.model[i].checked;

                                        if (angular.equals(scope.model[i], opt)) {
                                            scope.model.splice(i, 1);
                                            i--;
                                        }
                                    }
                                }

                                opt.checked = e.target.checked;
                            } else {
                                for (let i = 0; i < scope.model.length; i++) {
                                    opt.checked = scope.model[i].checked;

                                    if (angular.equals(scope.model[i], opt)) {
                                        scope.model.splice(i, 1);
                                        i--;
                                    }
                                }
                            }
                        } else {
                            if (e.target.checked) {
                                scope.model.push(opt);
                            } else {
                                for (let i = 0; i < scope.model.length; i++) {
                                    opt.checked = scope.model[i].checked;

                                    if (angular.equals(scope.model[i], opt)) {
                                        scope.model.splice(i, 1);
                                        i--;
                                    }
                                }
                            }

                            opt.checked = e.target.checked;
                        }

                        const elementById = document.getElementById(scope.id);

                        const placeholder = elementById.querySelector('.placeholder-checkboxes');
                        const inputText = elementById.querySelector('.select-text');
                        const oneItemText = elementById.querySelector('.one-item-text');
                        const arrow = elementById.querySelector('.arrow-checkboxes');

                        inputText.style.borderBottomLeftRadius = '0px';
                        arrow.style.borderBottomRightRadius = '0px';
                        placeholder.style.borderBottomLeftRadius = '0px';
                        oneItemText.style.borderBottomLeftRadius = '0px';
                    };

                    scope.selectAllOptions = function (e) {
                        scope.isAllSelected = !scope.isAllSelected;
                        scope.method(e);
                        setOptionsState(e.target.checked);
                        if (e.target.checked) {
                            scope.model = angular.copy(scope.options);
                        } else {
                            scope.model = [];
                        }

                        const elementById = document.getElementById(scope.id);

                        const placeholder = elementById.querySelector('.placeholder-checkboxes');
                        const inputText = elementById.querySelector('.select-text');
                        const arrow = elementById.querySelector('.arrow-checkboxes');
                        inputText.style.borderBottomLeftRadius = '0px';
                        arrow.style.borderBottomRightRadius = '0px';
                        placeholder.style.borderBottomLeftRadius = '0px';
                    };

                    scope.isEverySelected = function () {
                        return scope.options?.every((option) => scope.isChecked(option));
                    };

                    scope.isChecked = function (opt) {
                        let isExist = false,
                            isChecked = opt.hasOwnProperty('checked') ? opt.checked : true;

                        scope.model.forEach((selectedOpt, index) => {
                            if (selectedOpt.hasOwnProperty('checked')) {
                                opt.checked = opt.checked || selectedOpt.checked;
                            }
                            if (selectedOpt.id && opt.id && selectedOpt.id === opt.id)
                                opt.checked = selectedOpt.checked;
                            if (
                                angular.equals(selectedOpt, opt) ||
                                (selectedOpt.id && opt.id && selectedOpt.id === opt.id)
                            ) {
                                isExist = true;
                            } else if (
                                selectedOpt.localId &&
                                opt.localId &&
                                selectedOpt.localId === opt.localId &&
                                !selectedOpt.hasOwnProperty('checked')
                            ) {
                                selectedOpt.checked = true;
                                isExist = true;
                            }
                        });

                        return isExist;
                    };

                    function setOptionsState(state) {
                        scope.options.forEach((option) => {
                            option.checked = state;
                        });
                    }
                    var labelDom = element.find('.select-label'),
                        optionsDom = element.find('.select-ops'),
                        backdrop = element.find('.select-backdrop');

                    const elementById = document.getElementById(scope.id);

                    labelDom.on('click', function () {
                        open();
                    });
                    backdrop.on('click', function () {
                        close();
                    });

                    function open() {
                        if (!scope.disabled && !scope.isLoading) {
                            optionsDom.toggleClass('active');
                            backdrop.toggleClass('active');
                        } else {
                            labelDom.addClass('disabled');
                        }

                        const elementById = document.getElementById(scope.id);

                        const placeholder = elementById.querySelector('.placeholder-checkboxes');
                        const inputText = elementById.querySelector('.select-text');
                        const oneItemText = elementById.querySelector('.one-item-text');
                        const arrow = elementById.querySelector('.arrow-checkboxes');

                        inputText.style.borderBottomLeftRadius = '0px';
                        arrow.style.borderBottomRightRadius = '0px';
                        placeholder.style.borderBottomLeftRadius = '0px';
                        oneItemText.style.borderBottomLeftRadius = '0px';

                        scope.isOpenOpt = !scope.isOpenOpt;
                    }

                    function close() {
                        optionsDom.removeClass('active');
                        backdrop.removeClass('active');
                        labelDom.removeClass('disabled');
                        const elementById = document.getElementById(scope.id);

                        if (elementById) {
                            const placeholder = elementById.querySelector('.placeholder-checkboxes');
                            const inputText = elementById.querySelector('.select-text');
                            const oneItemText = elementById.querySelector('.one-item-text');
                            const arrow = elementById.querySelector('.arrow-checkboxes');

                            inputText.style.borderBottomLeftRadius = '5px';
                            arrow.style.borderBottomRightRadius = '5px';
                            placeholder.style.borderBottomLeftRadius = '5px';
                            oneItemText.style.borderBottomLeftRadius = '5px';
                        }
                    }

                    $(element).bind('open', open);
                    $(element).bind('close', close);

                    scope.$watch('disabled', () => {
                        if (scope.disabled) labelDom.addClass('disabled');
                        else labelDom.removeClass('disabled');
                    });

                    scope.$watch('isLoading', () => {
                        if (scope.isLoading) {
                            labelDom.addClass('disabled');
                        } else {
                            if (!scope.disabled) labelDom.removeClass('disabled');
                        }
                    });

                    scope.getPropertyFromValueForOneItem = function () {
                        if (!scope.path || scope.model.length === 0 || !scope.model) return scope.model[0];

                        let prevProp = null;

                        scope.path.split('.').forEach((prop) => {
                            prevProp = prevProp ? prevProp[prop] : scope.model[0][prop];
                        });

                        return prevProp || prevProp === false ? prevProp : scope.model;
                    };

                    scope.checkForInactiveItem = function (opt) {
                        const checkedOptions = scope.options.filter((option) => scope.isChecked(option)).length;
                        const isExistInModel = scope.model.find((option) => option.id === opt.id);

                        return !isExistInModel && checkedOptions >= scope.restriction;
                    };
                },
                template: `<div class="select-label custom-new" tabindex="0">
                            <span class="select-label-text placeholder placeholder-checkboxes" ng-show="!model.length">{{ placeholder | translate }}</span>
                            <span style="font-family: 'Helvetica-Medium'" class="select-label-text select-text" ng-show="model.length >= 2">{{'Selected:' | translate}} {{model.length}}</span>
                            <span class="select-label-text one-item-text" ng-show="model.length === 1">{{getPropertyFromValueForOneItem() | translate}}</span>
                            <span class="select-caret arrow-checkboxes">
                                <span></span>
                            </span>
                        </div>
                        <div class="select-backdrop custom-new"></div>
                        <div class="select-ops custom-new" ng-class="isAllSelected ? 'all-selected' : null">
                            <div ng-if="!options.length">
                                <label class="empty-data" title="{{'No data for display' | translate}}">
                                    <span class="label-text" translate="No data for display"></span>
                                </label>
                            </div>
                            <div class='custom-new-checkboxes grey-hover' ng-show="options.length && multSelection && !restriction">
                                <input type="checkbox" ng-checked="isEverySelected()" ng-click="selectAllOptions($event);$root.checkAllFields();" class="custom default" id="{{id}}-all">
                                <label for="{{id}}-all">
                                    <span class="label-text" >{{translateAll | translate}}</span>
                                </label>
                            </div>
                            <div ng-repeat="o in options track by $index" class='custom-new-checkboxes grey-hover' ng-class="{'is-not-active-item': restriction && checkForInactiveItem(o)}">
                                <input type="checkbox" ng-checked="isChecked(o)" ng-click="selectOpt(o, $event);" class="custom default" id="{{id}}-{{$index}}">
                                <label for="{{id}}-{{$index}}">
                                     <span>{{getPropertyValue(o, path) | translate}}</span><span ng-if="countPath">({{getCountValue(o, countPath)}})</span>
                                </label>
                            </div>
                        </div>`,
            };
        },
    ]);
})();
