var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('testCandidateAutocompleter', [
        '$filter',
        'serverAddress',
        '$rootScope',
        '$localStorage',
        function ($filter, serverAddress, $rootScope, $localStorage) {
            return {
                restrict: 'EA',
                replace: true,
                link: function ($scope, element, attrs) {
                    function formData(data) {
                        markup =
                            "<span style='font-weight: 600;color: #ccc'>" +
                            data.fullName +
                            '</span>' +
                            "<span style='color: #ccc'>" +
                            `<span>${data.position && data.position !== ' ' ? `, ${data.position}` : ''}</span>` +
                            '</span>';
                        return markup;
                    }
                    $scope.groupNameList = [];
                    $(element[0])
                        .select2({
                            placeholder: $filter('translate')('Select one or more candidates'),
                            tags: $scope.groupNameList,
                            tokenSeparators: [','],
                            ajax: {
                                url: serverAddress + '/candidate/autocomplete',
                                dataType: 'json',
                                crossDomain: true,
                                type: 'GET',
                                data: function (term, page) {
                                    return {
                                        name: term.trim(),
                                        withPersonalContacts: true,
                                    };
                                },
                                results: function (data, page) {
                                    var results = [];
                                    if (data['objects'] !== undefined) {
                                        angular.forEach(data['objects'], function (item) {
                                            if (item.contacts !== undefined) {
                                                var contacts = item.contacts;
                                                if (
                                                    contacts[0].value.length > 0 &&
                                                    contacts[0].value.search(/ /) !== -1
                                                ) {
                                                    contacts[0].value = contacts[0].value.split(/ /)[0];
                                                }
                                            }
                                            results.push({
                                                id: item.candidateId,
                                                text: $rootScope.useAmericanNameStyle
                                                    ? item.fullNameEn
                                                    : item.fullName + item.position && item.position !== ' '
                                                    ? `${item.fullName}, ${item.position}`
                                                    : item.fullName,
                                                fullName: $rootScope.useAmericanNameStyle
                                                    ? item.fullNameEn
                                                    : item.fullName,
                                                position: item.position,
                                                localId: item.localId,
                                                email: contacts,
                                            });
                                        });
                                    }
                                    return {
                                        results: results,
                                    };
                                },
                            },
                            formatResult: formData,
                            dropdownCssClass: 'bigdrop',
                        })
                        .on('change', function (e) {
                            if (e.added) {
                                $scope.groupNameList.push(e.added);
                                $rootScope.$broadcast('groupNameList', $scope.groupNameList);
                            } else if (e.removed) {
                                angular.forEach($scope.groupNameList, function (nval) {
                                    if (e.removed.id == nval.id) {
                                        var deleteFromArray = $scope.groupNameList.indexOf(nval);
                                        if (deleteFromArray > -1) {
                                            $scope.groupNameList.splice(deleteFromArray, 1);
                                        }
                                        for (var i = $scope.groupNameList.length - 1; i >= 0; i--) {
                                            if ($scope.groupNameList[i] == undefined) {
                                                $scope.groupNameList.splice(i, 1);
                                            }
                                        }
                                        $rootScope.$broadcast('groupNameList', $scope.groupNameList);
                                        return $scope.groupNameList;
                                    }
                                });
                            }
                            $rootScope.$apply();
                        });
                    $rootScope.$watch('currentLang', function (newLang, oldLang) {
                        if (newLang !== oldLang) {
                            $(element[0]).data('select2').opts.placeholder =
                                $rootScope.currentLang === 'en'
                                    ? 'Select one or more candidates'
                                    : $rootScope.currentLang === 'ua'
                                    ? 'Виберіть одного або кількох кандидатів'
                                    : $rootScope.currentLang === 'pl'
                                    ? 'Proszę wybrać jednego lub więcej kandydatów'
                                    : 'Выберите одного или нескольких кандидатов';
                            $(element[0]).data('select2').focus();
                            $(element[0]).data('select2').blur();
                        }
                    });
                },
            };
        },
    ]);
})();
