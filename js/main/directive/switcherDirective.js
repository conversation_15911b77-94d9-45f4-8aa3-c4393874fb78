var directives = directives || angular.module('RecruitingApp.directives', []);
(function () {
    directives.directive('customSwitcher', [
        function () {
            return {
                restrict: 'E',
                scope: {
                    switcherId: '=?',
                    firstLabel: '=?',
                    secondLabel: '=?',
                    method: '=method',
                    beforeClickPromise: '=beforeClickPromise',
                    checked: '=checked',
                    background: '=?',
                    fio: '=?',
                    offOn: '=?',
                    gdpr: '=?',
                    firstHint: '=?',
                    secondHint: '=?',
                },
                link: function (scope) {
                    scope.id = `cmn-toggle-${scope.switcherId}`;

                    scope.changeModel = async function (flag, event) {
                        try {
                            scope.beforeClickPromise && (await scope.beforeClickPromise(flag, event));
                        } catch (e) {
                            return;
                        }

                        if (scope.gdpr) {
                            let gdprItem =
                                event.target.parentElement.parentElement.parentElement.parentElement.parentElement;
                            gdprItem.style.boxShadow = 'none';
                            setTimeout(() => {
                                gdprItem.style.boxShadow =
                                    '0 0 1.6px rgba(0, 0, 0, 0.14), 0 3.32px 6.65px rgba(0, 0, 0, 0.2)';
                            }, 0);
                        }
                        scope.checked = flag;
                        if (!scope.method) return;
                        scope.method(flag, scope.switcherId);
                    };
                },
                template: `
                    <div class="switcher-main-wrapper">
                        <label class="switcher-label left" ng-class="{'chosen': !checked}" ng-click="changeModel(false, $event)">
                            <span ng-if="firstLabel">{{firstLabel | translate}}</span>
                            <span ng-if="fio"><br>{{'FIO v2' | translate}}</span>
                            <span ng-if="offOn">{{'Off' | translate}}</span>
                            
                            <i
                                ng-if='firstHint'
                                class="tooltip-icon info"
                                tooltip-class="tooltip-outer"
                                tooltip-placement="bottom"
                                uib-tooltip-html="firstHint"
                            />
                        </label>
                        
                        <input class="switch-toggle switch-toggle-round"
                               ng-class="{'background': background}"
                               id="{{id}}"
                               ng-checked="checked === true"
                               type="checkbox">
                        <label ng-click="changeModel(!checked, $event)"></label>
                        
                        <label class="switcher-label right" ng-class="{'chosen': checked}" ng-click="changeModel(true, $event)">
                            <span ng-if="secondLabel">{{secondLabel | translate}}</span>
                            <span ng-if="fio"><br>{{'IOF v2' | translate}}</span>
                            <span ng-if="offOn">{{'On' | translate}}</span>
                            
                            <i
                                ng-if='secondHint'
                                class="tooltip-icon info"
                                tooltip-class="tooltip-outer"
                                tooltip-placement="bottom-right"
                                uib-tooltip-html="secondHint"
                            />
                        </label>
                    </div>
                `,
            };
        },
    ]);
})();
