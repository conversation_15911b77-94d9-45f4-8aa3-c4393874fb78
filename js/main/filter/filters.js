'use strict';

/* Filters */
var mass = {
    0: 'today',
    1: 'tomorrow',
};

const enMonthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const ruMonthNames = ['янв', 'фев', 'мар', 'апр', 'мая', 'июня', 'июля', 'авг', 'сент', 'окт', 'нояб', 'дек'];
const uaMonthNames = ['cіч', 'лют', 'бер', 'квіт', 'трав', 'черв', 'липн', 'серп', 'вер', 'жовт', 'лист', 'груд'];
const plMonthNames = ['st', 'lut', 'mrz', 'kw', 'maj', 'cz', 'lip', 'sier', 'wrz', 'paź', 'lis', 'gr'];
const ro<PERSON><PERSON>h<PERSON><PERSON>s = ['ian', 'feb', 'mar', 'apr', 'mai', 'iun', 'iul', 'aug', 'sep', 'oct', 'nov', 'dec'];

const enMonthNamesFull = [
    'ianuarie',
    'februarie',
    'martie',
    'aprilie',
    'mai',
    'iunie',
    'iulie',
    'august',
    'septembrie',
    'octombrie',
    'noiembrie',
    'decembrie',
];

const roMonthNamesFull = [
    'Ianuarie',
    'Februarie',
    'Martie',
    'Aprilie',
    'Mai',
    'Iunie',
    'Iulie',
    'August',
    'Septembrie',
    'Octombrie',
    'Noiembrie',
    'Decembrie',
];

const ruMonthNamesFull = [
    'января',
    'февраля',
    'марта',
    'апреля',
    'мая',
    'июня',
    'июля',
    'августа',
    'сентября',
    'октября',
    'ноября',
    'декабря',
];
const uaMonthNamesFull = [
    'січня',
    'лютого',
    'березня',
    'квітня',
    'травня',
    'червня',
    'липня',
    'серпня',
    'вересня',
    'жовтня',
    'листопада',
    'грудня',
];
const plMonthNamesFull = [
    'stycznia',
    'lutego',
    'marca',
    'kwietnia',
    'maja',
    'czerwca',
    'lipca',
    'sierpnia',
    'września',
    'października',
    'listopada',
    'grudnia',
];

const getMonthNames = (lang) => {
    switch (lang) {
        case 'ua':
            return uaMonthNames;
        case 'pl':
            return plMonthNames;
        case 'ro':
            return roMonthNames;
        case 'ru':
            return ruMonthNames;
        case 'en':
        default:
            return enMonthNames;
    }
};

const getFullMonthNames = (lang) => {
    switch (lang) {
        case 'ua':
            return uaMonthNamesFull;
        case 'pl':
            return plMonthNamesFull;
        case 'ro':
            return roMonthNamesFull;
        case 'ru':
            return ruMonthNamesFull;
        case 'en':
        default:
            return enMonthNamesFull;
    }
};

angular
    .module('RecruitingApp.filters', ['ngSanitize'])
    .filter('fileNameCut', [
        function () {
            return function (fileName, start, end) {
                if (fileName != undefined) {
                    start = start != undefined ? start : 0;
                    end = end != undefined ? end : 50;
                    var filenameArr = fileName.split('.');
                    if (fileName.length >= end) {
                        return filenameArr[0].substring(start, end - 6) + '...' + filenameArr.pop();
                    } else if (filenameArr.length == 1) {
                        return filenameArr[0].substring(start, end);
                    } else {
                        return fileName.substring(start, end);
                    }
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('roundUp', [
        function () {
            return function (number) {
                if (number == undefined) return 0;
                return Math.ceil(number);
            };
        },
    ])
    .filter('calculateTimeOnStep', [
        '$filter',
        function ($filter) {
            return function (date) {
                if (date) {
                    const day = Math.round((new Date().getTime() - date) / (1000 * 60 * 60 * 24));
                    const translate = $filter('translate'),
                        cases = [2, 0, 1, 1, 1, 2];
                    let res = '';
                    if (day === 0) {
                        return translate('today');
                    }
                    res +=
                        day +
                        ' ' +
                        [translate('day'), translate('days'), translate('days_1')][
                            day % 100 > 4 && day % 100 < 20 ? 2 : cases[day % 10 < 5 ? day % 10 : 5]
                        ];
                    return res;
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('datefunction', [
        '$filter',
        function ($filter) {
            return function (date) {
                var vlDate = new Date($filter('date')(date, 'MM/dd/yyyy', 'UTC'));
                var now = new Date();
                var vlM = vlDate.getMonth();
                if (vlDate.getMonth() == now.getMonth()) {
                    if (vlDate.getDate() >= now.getDate()) {
                        var dayDiff = vlDate.getDate() - now.getDate();
                        if (dayDiff > 1) {
                        } else {
                            return $filter('translate')(mass[dayDiff]) + ' ' + $filter('translate')('birthday_2');
                        }
                    } else {
                        return (
                            $filter('date')(vlDate, 'dd MMM') +
                            ' ' +
                            $filter('translate')('was') +
                            ' ' +
                            $filter('translate')('birthday_2')
                        );
                    }
                } else {
                    return (
                        $filter('date')(vlDate, 'dd MMM') +
                        ' ' +
                        $filter('translate')('was') +
                        ' ' +
                        $filter('translate')('birthday_2')
                    );
                }
            };
        },
    ])
    .filter('findTypeOfVacancy', [
        '$filter',
        'Vacancy',
        function ($filter, Vacancy) {
            return function (vacancyStatus) {
                var array = vacancyStatus.split(',');
                var vacancyType;
                angular.forEach(Vacancy.interviewStatusNew(), function (vStatus) {
                    var check = [];
                    var statusDef = $filter('filter')(vStatus.status, {
                        defaultS: true,
                    });
                    angular.forEach(statusDef, function (statusD) {
                        angular.forEach(array, function (statA) {
                            if (statusD.value == statA) {
                                check.push(true);
                            }
                        });
                    });
                    if (check.length == statusDef.length) {
                        vacancyType = vStatus.vacancyType;
                    } else {
                    }
                });
                return vacancyType;
            };
        },
    ])
    .filter('vacancyStatusForHistory', [
        '$filter',
        'Vacancy',
        function ($filter, Vacancy) {
            return function (vacancyStatus) {
                var array = vacancyStatus.split(',');
                var forReturn = [];
                angular.forEach(Vacancy.interviewStatusNew(), function (vStatus) {
                    var check = [];
                    var statusDef = $filter('filter')(vStatus.status, {
                        defaultS: true,
                    });
                    angular.forEach(statusDef, function (statusD) {
                        angular.forEach(array, function (statA) {
                            if (statusD.value == statA) {
                                check.push(true);
                            }
                        });
                    });
                    if (check.length == statusDef.length) {
                        angular.forEach(vStatus.status, function (vStatusV) {
                            if (vStatusV.forHistory) {
                                angular.forEach(array, function (val) {
                                    if (val == vStatusV.value) {
                                        forReturn.push(vStatusV.value);
                                    }
                                });
                            }
                        });
                    }
                });
                return forReturn;
            };
        },
    ])
    .filter('vacancyStatusInSelectFilter', [
        '$filter',
        function ($filter) {
            return function (vacancyStatus) {
                if (vacancyStatus != undefined) {
                    var value = $filter('filter')(vacancyStatus, {
                        used: true,
                    });
                    if (value != undefined && value.length == 1) {
                        return $filter('filter')(value[0].status, {
                            added: true,
                        });
                    }
                }
                return [];
            };
        },
    ])
    .filter('vacancyStatusInCheckFilter', [
        '$filter',
        function ($filter) {
            return function (vacancyStatus, vacancyType) {
                if (vacancyStatus != undefined) {
                    var value = $filter('filter')(vacancyStatus, {
                        vacancyType: vacancyType,
                    });
                    if (value != undefined && value.length == 1) {
                        return $filter('filter')(value[0].status, {
                            defaultS: false,
                        });
                    }
                }
            };
        },
    ])
    .filter('dateFormat', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, withHour, withUTC) {
                function createDateAsUTC(datLong) {
                    if (datLong != undefined) {
                        var date = new Date(datLong);
                        return new Date(
                            date.getUTCFullYear(),
                            date.getUTCMonth(),
                            date.getUTCDate(),
                            date.getUTCHours(),
                            date.getUTCMinutes(),
                            date.getUTCSeconds(),
                        );
                    }
                }

                if (withUTC == true) {
                    date = createDateAsUTC(date);
                }
                var dateToday = new Date().getTime();
                let hour = '';
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';

                let dateFormat = '';
                let hourFormat = '';

                if (lang === 'pl') {
                    dateFormat =
                        new Date().getFullYear() === new Date(date).getFullYear()
                            ? `${new Date(date).getDate()} ${plMonthNames[new Date(date).getMonth()]}`
                            : `${new Date(date).getDate()} ${plMonthNames[new Date(date).getMonth()]} ${new Date(
                                  date,
                              ).getFullYear()}`;

                    if (withHour) {
                        const hour = new Date(date).getHours().toString().padStart(2, '0');
                        const minutes = new Date(date).getMinutes().toString().padStart(2, '0');
                        dateFormat += ` ${hour}:${minutes}`;
                    }
                } else {
                    if (lang === 'ua') {
                        dateMD = 'd ' + uaMonthNames[new Date(date).getMonth()] + ' ';
                        dateMDY = 'd ' + uaMonthNames[new Date(date).getMonth()] + ' y ';
                    } else if (lang === 'ru') {
                        dateMD = 'd ' + ruMonthNames[new Date(date).getMonth()] + ' ';
                        dateMDY = 'd ' + ruMonthNames[new Date(date).getMonth()] + ' y ';
                    } else if (lang === 'ro') {
                        dateMD = 'd ' + roMonthNames[new Date(date).getMonth()] + ' ';
                        dateMDY = 'd ' + roMonthNames[new Date(date).getMonth()] + ' y ';
                    } else if (lang === 'en') {
                        dateMD = 'MMM d, ';
                        dateMDY = 'MMM d, y ';
                    }

                    if (withHour) {
                        hourFormat = lang === 'en' ? 'h:mm a' : 'H:mm';
                    }

                    if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                        dateFormat = dateMD + hourFormat;
                    } else {
                        dateFormat = dateMDY + hourFormat;
                    }

                    dateFormat = $filter('date')(date, dateFormat);
                }

                return dateFormat;
            };
        },
    ])
    .filter('dateFormatRedesign', [
        function () {
            return function (date, lang) {
                if (lang === 'en') {
                    return new Date(date).toLocaleDateString('en-US');
                }
                return new Date(date).toLocaleDateString('uk-UA').replaceAll('.', '/');
            };
        },
    ])
    .filter('dateTimeFormatRedesign', [
        function () {
            return function (date, lang) {
                const dateObj = new Date(date);

                if (lang === 'en') {
                    return `${dateObj.toLocaleDateString('en-US')} ${dateObj.toLocaleTimeString('en-US', {
                        timeStyle: 'short',
                    })}`;
                }
                return `${dateObj.toLocaleDateString('uk-UA').replaceAll('.', '/')} ${dateObj.toLocaleTimeString(
                    'uk-UA',
                    { timeStyle: 'short' },
                )}`;
            };
        },
    ])
    .filter('dateFormatPayment', [
        function () {
            return function (date, lang) {
                if (lang === 'en') {
                    return new Date(date).toLocaleDateString('en-US', { dateStyle: 'short' });
                }
                return new Date(date).toLocaleDateString('uk-UA', { dateStyle: 'short' });
            };
        },
    ])
    .filter('dateTimeFrom', [
        '$filter',
        '$translate',
        function ($filter) {
            return function (date) {
                let dateToday = new Date().getTime();
                let diff = dateToday - date;
                if (diff < 3420000) {
                    let minutes = Math.round(diff / 300000) * 5;
                    return minutes > 0
                        ? minutes + $filter('translate')('minutes ago')
                        : $filter('translate')('just now');
                } else if (diff >= 3420000) {
                    let hours = Math.round(diff / 3600000);
                    if (hours < 4) {
                        return hours === 1
                            ? hours + $filter('translate')('hour ago')
                            : hours + $filter('translate')('hours ago');
                    } else {
                        return $filter('dateFormatSimpleExcelHistory')(new Date(date), true);
                    }
                }
            };
        },
    ])
    .filter('dateFormatInterview', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour) {
                const d = new Date(date);

                const day = d.getDate();
                const month = d.getMonth() + 1;
                const hours = d.getHours();
                const minutes = d.getMinutes();

                const formattedDate = `${day < 10 ? '0' : ''}${day}.${month < 10 ? '0' : ''}${month} ${
                    hours < 10 ? '0' : ''
                }${hours}:${minutes < 10 ? '0' : ''}${minutes}`;
                return formattedDate;
            };
        },
    ])
    .filter('dateFormat2', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua' || lang === 'pl') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = 'MMM d, y ';
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm a';
                    } else {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = $filter('translate')('today');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        var res = $filter('translate')('tomorrow');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else {
                        return $filter('date')(date, dateMD + hour);
                    }
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    .filter('dateFormat2OnlyEnLang', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = 'MMM d, y ';
                }
                if (withHour === true) {
                    hour = 'h:mm a';
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = 'today';
                        if (withHour) {
                            res += ' ' + 'at' + ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        var res = 'tomorrow';
                        if (withHour) {
                            res += ' ' + 'at' + ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else {
                        return $filter('date')(date, dateMD + hour);
                    }
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    .filter('dateFormat3', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour, withUTC) {
                function createDateAsUTC(datLong) {
                    if (datLong != undefined) {
                        var date = new Date(datLong);
                        return new Date(
                            date.getUTCFullYear(),
                            date.getUTCMonth(),
                            date.getUTCDate(),
                            date.getUTCHours(),
                            date.getUTCMinutes(),
                            date.getUTCSeconds(),
                        );
                    }
                }

                if (withUTC == true) {
                    date = createDateAsUTC(date);
                }
                var hour = '';
                var dateToday = new Date().getTime();
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = 'dd-MM-yyyy ';
                    dateMDY = 'dd-MM-yyyy ';
                } else if (lang == 'en') {
                    dateMD = 'dd-MM-yyyy ';
                    dateMDY = 'dd-MM-yyyy ';
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm:ss a';
                    } else {
                        hour = 'H:mm:ss';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    return $filter('date')(date, dateMD + hour);
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    .filter('dateFormat4', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour, withUTC) {
                function createDateAsUTC(datLong) {
                    if (datLong != undefined) {
                        var date = new Date(datLong);
                        return new Date(
                            date.getUTCFullYear(),
                            date.getUTCMonth(),
                            date.getUTCDate(),
                            date.getUTCHours(),
                            date.getUTCMinutes(),
                            date.getUTCSeconds(),
                        );
                    }
                }

                if (withUTC == true) {
                    date = createDateAsUTC(date);
                }
                var hour = '';
                var dateToday = new Date().getTime();
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = 'MMM d, y ';
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm a';
                    } else {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    return (
                        $filter('date')(date, dateMD) + $filter('translate')('at') + ' ' + $filter('date')(date, hour)
                    );
                } else {
                    return (
                        $filter('date')(date, dateMDY) + $filter('translate')('at') + ' ' + $filter('date')(date, hour)
                    );
                }
            };
        },
    ])
    .filter('dateFormat5', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour) {
                var hour = '';
                var dateforToday = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var dateYesterday = new Date().setDate(new Date().getDate() - 1);
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = 'MMM d, y ';
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm a';
                        dateforToday = 'h:mm:ss a';
                    } else {
                        hour = 'H:mm';
                        dateforToday = 'H:mm:ss';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = $filter('translate')('today');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, dateforToday);
                        }
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateYesterday, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        var res = $filter('translate')('yesterday');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, dateforToday);
                        }
                        return res;
                    } else {
                        return $filter('date')(date, dateMD);
                    }
                } else {
                    return $filter('date')(date, dateMDY);
                }
            };
        },
    ])
    .filter('dateFormat6', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $rootScope.currentLang || 'ru';
                var dateMD = '';
                var dateMDY = '';

                if (lang == 'ua' || lang == 'pl' || lang == 'ro' || lang == 'ru') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = "MMM d '<br/>' y ";
                }

                if (withHour === true) {
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else if (lang === 'ua' || lang === 'pl' || lang === 'ro' || lang === 'ru') {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = $filter('translate')('Today');

                        if (lang === 'ru') {
                            var res = 'Сегодня в';
                        } else if (lang === 'ua') {
                            var res = 'Сьогодні о';
                        } else if (lang === 'ro') {
                            var res = 'Astăzi la ora';
                        } else if (lang === 'pl') {
                            var res = 'Dziś o';
                        } else var res = 'Today at';
                        if (withHour) {
                            res += '<br/>' + $filter('date')(date, hour);
                        }
                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }
                        res = res.replace('пп', '');
                        res = res.replace('дп', '');
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        if (lang === 'ru') {
                            var res = 'Завтра в';
                        } else if (lang === 'ua' || lang === 'pl') {
                            var res = 'Завтра о';
                        } else if (lang === 'pl') {
                            var res = 'Jutro o';
                        } else if (lang === 'ro') {
                            var res = 'Mâine la ora';
                        } else var res = 'Tomorrow at';

                        if (withHour) {
                            res += '<br/>' + $filter('date')(date, hour);
                        }
                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }
                        res = res.replace('пп', 'PM');
                        res = res.replace('дп', 'AM');
                        return res;
                    } else {
                        let res = $filter('dateFormatSimple3')(date, dateMD + '<br/>' + hour);

                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }

                        res = res.replace('пп', 'PM');
                        res = res.replace('дп', 'AM');
                        return res;
                    }
                } else {
                    let res = $filter('dateFormatSimple3')(date, dateMD + '<br/>' + hour);

                    if (res.indexOf('до полудня') !== -1) {
                        return res.split(' ').slice(0, 2).join(' ') + ' AM';
                    } else if (res.indexOf('после полудня') !== -1) {
                        return res.split(' ').slice(0, 2).join(' ') + ' PM';
                    }

                    res = res.replace('пп', 'PM');
                    res = res.replace('дп', 'AM');
                    return res;
                }
            };
        },
    ])
    .filter('dateFormat6_Organizer', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $rootScope.currentLang || 'ru';
                var dateMD = '';
                var dateMDY = '';

                if (lang == 'ru' || lang == 'ua' || lang === 'ro' || lang === 'pl') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = "MMM d '<br/>' y ";
                }

                if (withHour === true) {
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else if (lang === 'ua' || lang === 'pl' || lang === 'ru') {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = $filter('translate')('Today');
                        if (lang === 'ru') {
                            var res = 'Сегодня в';
                        } else if (lang === 'ua') {
                            var res = 'Сьогодні о';
                        } else if (lang === 'ro') {
                            var res = 'Astăzi la ora';
                        } else if (lang === 'pl') {
                            var res = 'Dziś o';
                        } else var res = 'Today at';
                        if (withHour) {
                            res += '<br/>' + $filter('date')(date, hour);
                        }
                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }
                        res = res.replace('пп', '');
                        res = res.replace('дп', '');
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        if (lang === 'ru') {
                            var res = 'Завтра в';
                        } else if (lang === 'ua') {
                            var res = 'Завтра о';
                        } else if (lang === 'pl') {
                            var res = 'Jutro o';
                        } else if (lang === 'ro') {
                            var res = 'Mâine la ora';
                        } else var res = 'Tomorrow at';
                        if (withHour) {
                            res += '<br/>' + $filter('date')(date, hour);
                        }
                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }
                        res = res.replace('пп', 'PM');
                        res = res.replace('дп', 'AM');
                        return res;
                    } else {
                        let res = $filter('dateFormatSimple3_Organizer')(date, dateMD + '<br/>' + hour);

                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }

                        res = res.replace('пп', 'PM');
                        res = res.replace('дп', 'AM');
                        return res;
                    }
                } else {
                    let res = $filter('dateFormatSimple3_Organizer')(date, dateMD + '<br/>' + hour);

                    if (res.indexOf('до полудня') !== -1) {
                        return res.split(' ').slice(0, 2).join(' ') + ' AM';
                    } else if (res.indexOf('после полудня') !== -1) {
                        return res.split(' ').slice(0, 2).join(' ') + ' PM';
                    }

                    res = res.replace('пп', 'PM');
                    res = res.replace('дп', 'AM');
                    return res;
                }
            };
        },
    ])
    .filter('dateFormat6Bold', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $rootScope) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $translate.use() || $rootScope.currentLang || 'ru';
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = "'<b>'d MMM '</b>'";
                    dateMDY = "'<b>'d MMM y '</b>'";
                } else if (lang == 'en') {
                    dateMD = "'<b>'MMM d '</b>'";
                    dateMDY = "'<b>'MMM d, y '</b>'";
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm a';
                    } else {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = '<b>' + $filter('translate')('Today');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + '</b>&nbsp;' + $filter('date')(date, hour);
                        }

                        if (res.indexOf('до полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' AM';
                        } else if (res.indexOf('после полудня') !== -1) {
                            return res.split(' ').slice(0, 2).join(' ') + ' PM';
                        }

                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        var res = '<b>' + $filter('translate')('tomorrow');
                        if (withHour) {
                            res += ' ' + $filter('translate')('at') + '</b>&nbsp;' + $filter('date')(date, hour);
                        }
                        return res;
                    } else {
                        return $filter('date')(date, '<b>' + dateMD + '</b>' + hour);
                    }
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    //dateFormat6BoldAndBr - like dateFormat6Bold, but have bold date and br
    .filter('dateFormat6BoldAndBr', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $translate.use() || $rootScope.currentLang || 'ru';
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = "'<b>'d MMM '</b>'";
                    dateMDY = "'<b>'d MMM y'</b>'";
                } else if (lang == 'en') {
                    dateMD = "'<b>'MMM d '</b>'";
                    dateMDY = "'<b>'MMM d, y '</b>'";
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = "'<br><span class=\"decoratiton\">'h:mm a'</span>'";
                    } else {
                        hour = "'<br><span class=\"decoratiton\">'H:mm'</span>'";
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                        var res = '<b>' + $filter('translate')('Today') + '</b>';
                        if (withHour) {
                            res += ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else if (
                        angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))
                    ) {
                        var res = '<b>' + $filter('translate')('tomorrow') + '</b>';
                        if (withHour) {
                            res += ' ' + $filter('date')(date, hour);
                        }
                        return res;
                    } else {
                        return $filter('date')(date, dateMD) + $filter('date')(date, hour);
                    }
                } else {
                    return $filter('date')(date, dateMDY) + $filter('date')(date, hour);
                }
            };
        },
    ])
    .filter('dateFormat7', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour, withUTC) {
                function createDateAsUTC(datLong) {
                    if (datLong != undefined) {
                        var date = new Date(datLong);
                        return new Date(
                            date.getUTCFullYear(),
                            date.getUTCMonth(),
                            date.getUTCDate(),
                            date.getUTCHours(),
                            date.getUTCMinutes(),
                            date.getUTCSeconds(),
                        );
                    }
                }

                if (withUTC == true) {
                    date = createDateAsUTC(date);
                }
                var hour = '';
                var dateToday = new Date().getTime();
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';

                if (lang === 'ru' || lang === 'ua' || lang === 'pl' || lang === 'ro') {
                    dateMD = 'dd-MM-yyyy ';
                    dateMDY = 'dd-MM-yyyy ';
                } else if (lang === 'en') {
                    dateMD = 'MM-dd-yyyy ';
                    dateMDY = 'MM-dd-yyyy ';
                }

                if (withHour === true) {
                    if (lang === 'en') {
                        hour = 'h:mm:ss a';
                    } else {
                        hour = 'H:mm:ss';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    return $filter('date')(date, dateMD + hour);
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    .filter('dateFormatSlash', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, withHour, withUTC) {
                function createDateAsUTC(datLong) {
                    if (datLong != undefined) {
                        var date = new Date(datLong);
                        return new Date(
                            date.getUTCFullYear(),
                            date.getUTCMonth(),
                            date.getUTCDate(),
                            date.getUTCHours(),
                            date.getUTCMinutes(),
                            date.getUTCSeconds(),
                        );
                    }
                }

                if (withUTC == true) {
                    date = createDateAsUTC(date);
                }
                var hour = '';
                var dateToday = new Date().getTime();
                var lang = $translate.use();
                var dateMD = '';
                var dateMDY = '';

                if (lang === 'ru' || lang === 'ua' || lang === 'pl' || lang === 'ro') {
                    dateMD = 'dd/MM/yyyy ';
                    dateMDY = 'dd/MM/yyyy ';
                } else if (lang === 'en') {
                    dateMD = 'MM/dd/yyyy ';
                    dateMDY = 'MM/dd/yyyy ';
                }
                if (withHour === true) {
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'))) {
                    return $filter('date')(date, dateMD + hour);
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    //dateFormat7 - like dateFormat2, but always show year
    .filter('dateFormat8', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, withHour) {
                var hour = '';
                var dateToday = new Date().getTime();
                var dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                var lang = $translate.use() || $rootScope.currentLang || 'ru';
                var dateMD = '';
                var dateMDY = '';
                if (lang == 'ru' || lang == 'ua') {
                    dateMD = 'd MMM ';
                    dateMDY = 'd MMM y ';
                } else if (lang == 'en') {
                    dateMD = 'MMM d ';
                    dateMDY = 'MMM d, y ';
                }
                if (withHour === true) {
                    if (lang == 'en') {
                        hour = 'h:mm a';
                    } else {
                        hour = 'H:mm';
                    }
                }
                if (angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                    var res = $filter('translate')('today');
                    if (withHour) {
                        res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, hour);
                    }
                    return res;
                } else if (angular.equals($filter('date')(dateTomorrow, 'y MMM d'), $filter('date')(date, 'y MMM d'))) {
                    var res = $filter('translate')('tomorrow');
                    if (withHour) {
                        res += ' ' + $filter('translate')('at') + ' ' + $filter('date')(date, hour);
                    }
                    return res;
                } else {
                    return $filter('date')(date, dateMDY + hour);
                }
            };
        },
    ])
    .filter('dateFormat9', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, isWithoutBr) {
                if (!date) return;

                const dateObj = new Date(date);
                const dateToday = new Date().getTime();
                const dateTomorrow = new Date().setDate(new Date().getDate() + 1);
                const lang = $rootScope.currentLang;
                const monthNames = getMonthNames(lang);

                const isThisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));
                const isToday = angular.equals($filter('date')(dateToday, 'y MMM d'), $filter('date')(date, 'y MMM d'));
                const isTomorrow = angular.equals(
                    $filter('date')(dateTomorrow, 'y MMM d'),
                    $filter('date')(date, 'y MMM d'),
                );
                const today =
                    lang === 'ru'
                        ? 'Сегодня в'
                        : lang === 'en'
                        ? 'Today at'
                        : lang === 'pl'
                        ? 'Dziś o'
                        : lang === 'ro'
                        ? 'Astăzi la ora'
                        : 'Сьогодні о';

                const tomorrow =
                    lang === 'ru'
                        ? 'Завтра в'
                        : lang === 'en'
                        ? 'Tomorrow at'
                        : lang === 'pl'
                        ? 'Jutro o'
                        : lang === 'ro'
                        ? 'Mâine la ora'
                        : 'Завтра о';

                let time = $filter('date')(date, lang === 'en' ? 'h:mm a' : 'H:mm')
                    .replace(/дп|до полудня/, 'AM')
                    .replace(/пп|после полудня/, 'PM');

                if (isThisYear) {
                    if (isToday) return `${today}${isWithoutBr ? ' ' : '<br/>'}${time}`;
                    if (isTomorrow) return `${tomorrow}${isWithoutBr ? ' ' : '<br/>'}${time}`;
                }

                return `${
                    lang === 'en'
                        ? `${monthNames[dateObj.getMonth()]} ${dateObj.getDate()},`
                        : `${dateObj.getDate()} ${monthNames[dateObj.getMonth()]}`
                } ${!isThisYear ? dateObj.getFullYear() : ''}${isWithoutBr ? ' ' : '<br/>'}${time}`;
            };
        },
    ])
    .filter('dateFormatHours', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date, isWithoutBr) {
                if (!date) return;
                const lang = $rootScope.currentLang;
                let time = $filter('date')(date, lang === 'en' ? 'h:mm a' : 'H:mm')
                    .replace(/дп|до полудня/, 'AM')
                    .replace(/пп|после полудня/, 'PM');

                return `${time}`;
            };
        },
    ])
    .filter('dateFormatNotices', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date) {
                var dateToday = new Date().getDate(),
                    dateYesterday = new Date().setDate(new Date().getDate() - 1),
                    date_Today = null,
                    date_Yesterday = null,
                    date_old = null,
                    noticeDays = new Date(date),
                    noticeDateYesterday = new Date(dateYesterday);

                if (dateToday == noticeDays.getDate()) {
                    date_Today =
                        '<b>' +
                        $filter('translate')('Today') +
                        ' ' +
                        $filter('translate')('in') +
                        ' ' +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_Today;
                } else if (noticeDateYesterday.getDate() == noticeDays.getDate()) {
                    date_Yesterday =
                        '<b>' +
                        $filter('translate')('Yesterday') +
                        ' ' +
                        $filter('translate')('in') +
                        ' ' +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_Yesterday;
                } else {
                    date_old =
                        '<b>' +
                        $filter('date')(date, 'd MMM ' + $filter('translate')('in') + ' ') +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_old;
                }
            };
        },
    ])
    .filter('dateFormatNoticesBr', [
        '$filter',
        '$translate',
        '$rootScope',
        function ($filter, $translate, $rootScope) {
            return function (date) {
                var dateToday = new Date().getDate(),
                    dateYesterday = new Date().setDate(new Date().getDate() - 1),
                    date_Today = null,
                    date_Yesterday = null,
                    date_old = null,
                    noticeDays = new Date(date),
                    noticeDateYesterday = new Date(dateYesterday);

                if (dateToday == noticeDays.getDate()) {
                    date_Today =
                        '<b>' +
                        $filter('translate')('Today') +
                        '<br>' +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_Today;
                } else if (noticeDateYesterday.getDate() == noticeDays.getDate()) {
                    date_Yesterday =
                        '<b>' +
                        $filter('translate')('Yesterday') +
                        '<br>' +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_Yesterday;
                } else {
                    date_old =
                        '<b>' +
                        $filter('date')(date, 'd MMM ' + '<br>' + ' ') +
                        "</b><span class='decoration'>" +
                        $filter('date')(date, 'HH:mm') +
                        '</span>';
                    return date_old;
                }
            };
        },
    ])
    .filter('salaryFormat', [
        '$filter',
        function ($filter) {
            return function (salaryFrom, salaryTo) {
                var res = '';
                if (salaryFrom && salaryTo) {
                    res = salaryFrom + '-' + salaryTo;
                } else if (salaryFrom && !salaryTo) {
                    res = salaryFrom + '+';
                } else if (!salaryFrom && salaryTo) {
                    res = $filter('translate')('up to') + ' ' + salaryTo;
                } else if (!salaryFrom && !salaryTo) {
                    res = $filter('translate')('on the interview results');
                }
                return res;
            };
        },
    ])
    .filter('secondsToHhMm', [
        '$filter',
        '$rootScope',
        function ($filter, $rootScope) {
            return function (seconds) {
                let lang = $rootScope.currentLang;
                if (seconds != undefined && seconds != null && !isNaN(seconds) && seconds != 0) {
                    var res = '';
                    if (seconds >= 60) {
                        var hour = parseInt(seconds / 3600);
                        if (hour != 0) {
                            switch (lang) {
                                case 'en':
                                    res += hour + '' + $filter('translate')('h') + ' ';
                                    break;
                                case 'ru':
                                    res += hour + '' + $filter('translate')('ч') + ' ';
                                    break;
                                case 'ua':
                                    res += hour + '' + $filter('translate')('г') + ' ';
                                    break;
                                case 'pl':
                                    res += hour + '' + $filter('translate')('g') + ' ';
                                    break;
                            }
                        }
                        var min = parseInt((seconds - hour * 3600) / 60);
                        switch (lang) {
                            case 'en':
                                res += min + '' + $filter('translate')('min');
                                break;
                            case 'ru':
                                res += min + '' + $filter('translate')('мин');
                                break;
                            case 'ua':
                                res += min + '' + $filter('translate')('хв');
                                break;
                            case 'pl':
                                res += min + '' + $filter('translate')('min');
                                break;
                        }
                        return res;
                    } else {
                        switch (lang) {
                            case 'en':
                                var sec = seconds + '' + $filter('translate')('sec');
                                break;
                            case 'ru':
                                var sec = seconds + '' + $filter('translate')('с');
                                break;
                            case 'ua':
                                var sec = seconds + '' + $filter('translate')('с');
                                break;
                            case 'pl':
                                var sec = seconds + '' + $filter('translate')('sek');
                                break;
                        }
                        return sec;
                    }
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('dayFormat', [
        '$filter',
        function ($filter) {
            return function (seconds) {
                if (seconds != undefined && seconds != null && !isNaN(seconds) && seconds != 0) {
                    var res = '';
                    var day = parseInt(seconds / 3600 / 24);
                    if (day != 0) {
                        res += day + ' ' + $filter('translate')('days_short') + '. ';
                    }
                    var hour = Math.floor((seconds / 3600) % 24);
                    if (hour != 0) {
                        res += hour + ' ' + $filter('translate')('hours_short') + '. ';
                    } else if (seconds < 3600) {
                        res += '1 ' + $filter('translate')('hours_short') + '. ';
                    }
                    return res;
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('dayFormat1', [
        '$filter',
        function ($filter) {
            return function (date) {
                if (date) {
                    const day = Math.round((new Date().getTime() - date) / (1000 * 60 * 60 * 24));
                    const translate = $filter('translate'),
                        cases = [2, 0, 1, 1, 1, 2];
                    let res = '';
                    if (day === 0) {
                        return translate('today');
                    }
                    res +=
                        day +
                        ' ' +
                        [translate('day'), translate('days'), translate('days_1')][
                            day % 100 > 4 && day % 100 < 20 ? 2 : cases[day % 10 < 5 ? day % 10 : 5]
                        ];
                    return res;
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('dayFormat2', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (date, done) {
                if (date) {
                    var seconds = (new Date().getTime() - date) / 1000;
                    var res = '';
                    var day = parseInt(seconds / 3600 / 24);
                    if (day != 0) {
                        res += day + ' ' + $filter('translate')('d') + '. ';
                    }
                    var hour = Math.round((seconds / 3600) % 24);
                    if (hour != 0) {
                        res += hour + ' ' + $filter('translate')('h') + '. ';
                    } else if (seconds < 3600) {
                        var min = parseInt(seconds / 60);
                        if (min >= 1) {
                            res += min + ' ' + $filter('translate')('min') + '. ';
                        }
                    }
                    if (res != '') {
                        res += ' ' + $filter('translate')('ago');
                    } else {
                        res = $filter('translate')('Just done');
                        if (done) {
                            if ($translate.use() != 'en') {
                                res += ' ' + $filter('translate')('done');
                            }
                        }
                    }
                    return res;
                } else {
                }
            };
        },
    ])
    .filter('translateLangs', [
        '$rootScope',
        function ($rootScope) {
            return function (translations) {
                if (translations) {
                    return $rootScope.currentLang === 'ru'
                        ? translations.russian[0].toUpperCase() + translations.russian.slice(1)
                        : $rootScope.currentLang === 'en' || $rootScope.currentLang === 'ro'
                        ? translations.english[0].toUpperCase() + translations.english.slice(1)
                        : $rootScope.currentLang === 'pl'
                        ? translations.polish[0].toUpperCase() + translations.polish.slice(1)
                        : translations.ukrainian[0].toUpperCase() + translations.ukrainian.slice(1);
                }
            };
        },
    ])
    .filter('bigFirstLetter', [
        '$rootScope',
        function ($rootScope) {
            return function (word) {
                return word && word[0].toUpperCase() + word.slice(1);
            };
        },
    ])
    .filter('bigFirstLetterCategory', [
        '$rootScope',
        function ($rootScope) {
            return function (word) {
                let category = word[0].toUpperCase() + word.slice(1).toLowerCase();
                const categories = {
                    Backend: 'Back End',
                    Big_data: 'Big Data',
                    C: 'C/C++',
                    Data_scientist: 'Data Scientist',
                    Cyber_security: 'Cyber security',
                    Dba: 'DBA',
                    Dev_ops: 'DevOps',
                    Erp_crm: 'ERP/CRM',
                    Frontend: 'Front End',
                    Full_stack: 'Full Stack',
                    Hr: 'HR in IT',
                    Sales: 'Sales in IT',
                    Gamedev: 'GameDev',
                    Ios_mac: 'iOS/macOS',
                    _net: '.Net',
                    Node: 'Node.js',
                    Product_manager: 'Product Manager',
                    Project_manager: 'Project Manager',
                    Qa: 'QA',
                    Seo: 'SEO',
                    System_administrator: 'System Administrator',
                    Technical_writer: 'Technical Writer',
                    Php: 'PHP',
                    Fpga_dsp: 'FPGA (DSP)',
                    Machine_learning: 'Machine Learning',
                };
                return categories[category] || category;
            };
        },
    ])
    .filter('dateFormatSimple', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date) {
                const dateObj = new Date(date);
                const dateToday = new Date().getTime();
                let thisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));

                const monthNames = getFullMonthNames($rootScope.currentLang);

                const res =
                    $rootScope.currentLang === 'en'
                        ? monthNames[dateObj.getMonth()] +
                          ' ' +
                          dateObj.getDate() +
                          (thisYear ? '' : ', ' + dateObj.getFullYear())
                        : dateObj.getDate() +
                          ' ' +
                          monthNames[dateObj.getMonth()] +
                          (thisYear ? '' : ' ' + dateObj.getFullYear());
                return res;
            };
        },
    ])
    .filter('dateFormatSimpleWithOutUTC', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date) {
                const dateObj = moment.utc(date);

                switch ($rootScope.currentLang) {
                    case 'en':
                        return dateObj.format('ll');
                    case 'ru':
                        return `${dateObj.date()} ${ruMonthNamesFull[dateObj.month()]} ${dateObj.year()}`;
                    case 'ro':
                        return `${dateObj.date()} ${roMonthNamesFull[dateObj.month()]} ${dateObj.year()}`;
                    case 'ua':
                        return `${dateObj.date()} ${uaMonthNamesFull[dateObj.month()]} ${dateObj.year()}`;
                    case 'pl':
                        return `${dateObj.date()} ${plMonthNamesFull[dateObj.month()]} ${dateObj.year()}`;
                }
            };
        },
    ])
    .filter('dateFormatShort', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, withToday) {
                if (!date) return;
                const lang = $rootScope.currentLang;
                const dateObj = new Date(date);
                const dateToday = new Date().getTime();
                let thisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));

                if (withToday && $filter('date')(dateToday, 'y MMM d') === $filter('date')(date, 'y MMM d')) {
                    return $filter('translate')('today');
                }

                const monthNames = getMonthNames(lang);

                const res =
                    lang === 'en'
                        ? monthNames[dateObj.getMonth()] +
                          ' ' +
                          dateObj.getDate() +
                          (thisYear ? '' : ', ' + dateObj.getFullYear())
                        : dateObj.getDate() +
                          ' ' +
                          monthNames[dateObj.getMonth()] +
                          (thisYear ? '' : ' ' + dateObj.getFullYear());
                return res;
            };
        },
    ])
    .filter('dateFormatSimple2', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, showHour) {
                const dateObj = new Date(date);
                if (!isNaN(dateObj.getDate())) {
                    let dateToday = new Date().getTime();
                    let lang = $rootScope.currentLang;
                    const thisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));

                    const monthNames = getFullMonthNames($rootScope.currentLang);

                    let hour = lang === 'en' ? 'h:mm a' : 'H:mm';
                    let hours = $filter('date')(date, hour);
                    let hour_tranclate = hours.split(' ');
                    if (hour_tranclate.length > 2) {
                        if (hour_tranclate[1] == 'после') {
                            hours = hour_tranclate[0] + ' PM';
                        } else {
                            hours = hour_tranclate[0] + ' AM';
                        }
                    }
                    if (!showHour && $filter('date')(dateToday, 'y MMM d') === $filter('date')(date, 'y MMM d')) {
                        const ret =
                            lang === 'ru'
                                ? 'сегодня'
                                : lang === 'en'
                                ? 'today'
                                : lang === 'ua'
                                ? 'сьогодні'
                                : lang === 'ro'
                                ? 'astăzi'
                                : 'dzisiaj';
                        return ret;
                    } else {
                        const res =
                            $rootScope.currentLang === 'en'
                                ? monthNames[dateObj.getMonth()] +
                                  ' ' +
                                  dateObj.getDate() +
                                  (thisYear ? '' : ', ' + dateObj.getFullYear()) +
                                  (showHour ? ' ' + hours : '')
                                : dateObj.getDate() +
                                  ' ' +
                                  monthNames[dateObj.getMonth()] +
                                  (thisYear ? '' : ' ' + dateObj.getFullYear()) +
                                  (showHour ? ' ' + hours : '');
                        return res;
                    }
                } else {
                    return null;
                }
            };
        },
    ])
    .filter('dateFormatSimpleExcelHistory', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, showHour) {
                if (date === 'no_value') {
                    return 'no_value';
                }

                const dateObj = new Date(date);
                if (!isNaN(dateObj.getDate())) {
                    let dateToday = new Date().getTime();
                    let lang = $rootScope.currentLang;
                    const thisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));

                    const monthNames = getMonthNames(lang);

                    let hour = lang === 'en' ? 'h:mm a' : 'H:mm';
                    let hours = $filter('date')(date, hour);
                    let hour_tranclate = hours.split(' ');

                    if (hour_tranclate.length > 2) {
                        if (hour_tranclate[1] == 'после') {
                            hours = hour_tranclate[0] + ' PM';
                        } else {
                            hours = hour_tranclate[0] + ' AM';
                        }
                    }

                    if ($rootScope.currentLang == 'en') {
                        if (hour_tranclate[1] == 'дп') {
                            hours = hour_tranclate[0] + ' AM';
                        } else {
                            hours = hour_tranclate[0] + ' PM';
                        }
                    }
                    const res =
                        $rootScope.currentLang === 'en'
                            ? monthNames[dateObj.getMonth()] +
                              ' ' +
                              dateObj.getDate() +
                              (thisYear ? '' : ' ' + dateObj.getFullYear()) +
                              (showHour ? ' ' + hours : '')
                            : dateObj.getDate() +
                              ' ' +
                              monthNames[dateObj.getMonth()] +
                              (thisYear ? '' : ' ' + dateObj.getFullYear()) +
                              (showHour ? ' ' + hours : '');
                    return res;
                } else {
                    return null;
                }
            };
        },
    ])
    .filter('dateFormatSimple3', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, showHour) {
                const dateObj = new Date(date);
                if (!isNaN(dateObj.getDate())) {
                    let dateToday = new Date().getTime();
                    let lang = $rootScope.currentLang;
                    const thisYear = angular.equals($filter('date')(dateToday, 'y'), $filter('date')(date, 'y'));

                    const monthNames = getMonthNames(lang);

                    // let hour = lang === 'en' ? "h:mm a" : "H:mm";
                    let hour;
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else if (lang === 'ua' || lang === 'pl' || lang === 'ro' || lang === 'ru') {
                        hour = 'H:mm';
                    }

                    let hours = $filter('date')(date, hour);
                    let hour_tranclate = hours.split(' ');
                    if (hour_tranclate.length > 2) {
                        if (hour_tranclate[1] == 'пп') {
                            hours = hour_tranclate[0] + ' PM';
                        } else if (hour_tranclate[1] == 'дп') {
                            hours = hour_tranclate[0] + ' AM';
                        }
                    }
                    if (!showHour && $filter('date')(dateToday, 'y MMM d') === $filter('date')(date, 'y MMM d')) {
                        const ret =
                            lang === 'ru'
                                ? 'сегодня'
                                : lang === 'en'
                                ? 'today'
                                : lang === 'ua'
                                ? 'сьогодні'
                                : lang === 'ro'
                                ? 'astăzi'
                                : 'dzisiaj';
                        return ret;
                    } else {
                        const res =
                            $rootScope.currentLang === 'en'
                                ? monthNames[dateObj.getMonth()] +
                                  ' ' +
                                  dateObj.getDate() +
                                  '<br/>' +
                                  dateObj.getFullYear() +
                                  '&nbsp;' +
                                  hours
                                : dateObj.getDate() +
                                  ' ' +
                                  monthNames[dateObj.getMonth()] +
                                  '<br/>' +
                                  (thisYear ? '' : ' ' + dateObj.getFullYear()) +
                                  ' ' +
                                  hours;
                        return res;
                    }
                } else {
                    return null;
                }
            };
        },
    ])
    .filter('dateFormatSimple3_Organizer', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, showHour) {
                const dateObj = new Date(date);
                if (!isNaN(dateObj.getDate())) {
                    let dateToday = new Date().getTime();
                    let lang = $rootScope.currentLang;
                    const thisYear = null;

                    const monthNames = getMonthNames(lang);

                    // let hour = lang === 'en' ? "h:mm a" : "H:mm";
                    let hour;
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else if (lang === 'ua' || lang === 'pl' || lang === 'ru' || lang === 'ro') {
                        hour = 'H:mm';
                    }
                    let hours = $filter('date')(date, hour);
                    let hour_tranclate = hours.split(' ');
                    if (hour_tranclate.length > 2) {
                        if (hour_tranclate[1] == 'пп') {
                            hours = hour_tranclate[0] + ' PM';
                        } else if (hour_tranclate[1] == 'дп') {
                            hours = hour_tranclate[0] + ' AM';
                        }
                    }
                    if (!showHour && $filter('date')(dateToday, 'MMM d') === $filter('date')(date, 'MMM d')) {
                        const ret =
                            lang === 'ru'
                                ? 'сегодня'
                                : lang === 'en'
                                ? 'today'
                                : lang === 'ua'
                                ? 'сьогодні'
                                : lang === 'ro'
                                ? 'astăzi'
                                : 'dzisiaj';
                        return ret;
                    } else {
                        if (hour_tranclate[1] == 'пп' || hour_tranclate[1] == 'после') {
                            hours = hour_tranclate[0] + ' PM';
                        } else if (hour_tranclate[1] == 'дп' || hour_tranclate[1] == 'до') {
                            hours = hour_tranclate[0] + ' AM';
                        }

                        const res =
                            $rootScope.currentLang === 'en'
                                ? monthNames[dateObj.getMonth()] + ' ' + dateObj.getDate() + '&nbsp;' + '<br>' + hours
                                : dateObj.getDate() + ' ' + monthNames[dateObj.getMonth()] + '<br>' + hours;
                        return res;
                    }
                } else {
                    return null;
                }
            };
        },
    ])
    .filter('dateFormatSimpleAction', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (date, showHour, showTodayAndHour) {
                const dateObj = new Date(date);
                if (!isNaN(dateObj.getDate())) {
                    let dateToday = new Date().getTime();
                    let lang = $rootScope.currentLang;
                    const thisYear = null;

                    const monthNames =
                        lang === 'ru'
                            ? ruMonthNames
                            : lang === 'en'
                            ? enMonthNames
                            : lang === 'pl'
                            ? plMonthNames
                            : uaMonthNames;
                    // let hour = lang === 'en' ? "h:mm a" : "H:mm";
                    let hour;
                    if (lang === 'en') {
                        hour = 'h:mm a';
                    } else if (lang === 'ru' || lang === 'ua' || lang === 'pl') {
                        hour = 'H:mm';
                    }

                    let hours = $filter('date')(date, hour);
                    let hour_tranclate = hours.split(' ');
                    if (hour_tranclate.length > 1) {
                        if (hour_tranclate[1] == 'пп') {
                            hours = hour_tranclate[0] + ' PM';
                        } else if (hour_tranclate[1] == 'дп') {
                            hours = hour_tranclate[0] + ' AM';
                        }
                    }
                    const today =
                        lang === 'ru'
                            ? 'сегодня'
                            : lang === 'en'
                            ? 'today'
                            : lang === 'ua'
                            ? 'сьогодні'
                            : lang === 'ro'
                            ? 'astăzi'
                            : 'dzisiaj';
                    if (showTodayAndHour && $filter('date')(dateToday, 'MMM d') === $filter('date')(date, 'MMM d')) {
                        return `${today} ${$filter('translate')('at')} ${hours}`;
                    } else if (!showHour && $filter('date')(dateToday, 'MMM d') === $filter('date')(date, 'MMM d')) {
                        return today;
                    } else {
                        if (hour_tranclate[1] == 'пп' || hour_tranclate[1] == 'после') {
                            hours = hour_tranclate[0] + ' PM';
                        } else if (hour_tranclate[1] == 'дп' || hour_tranclate[1] == 'до') {
                            hours = hour_tranclate[0] + ' AM';
                        }

                        const res =
                            $rootScope.currentLang === 'en'
                                ? monthNames[dateObj.getMonth()] + ' ' + dateObj.getDate() + ' ' + hours
                                : dateObj.getDate() + ' ' + monthNames[dateObj.getMonth()] + ' ' + hours;
                        return res;
                    }
                } else {
                    return null;
                }
            };
        },
    ])
    .filter('translateLocation', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (location) {
                let lang = $rootScope.currentLang;
                let res = '';
                if (location) {
                    switch (lang) {
                        case 'ru':
                            res =
                                (location.countryRu ? location.countryRu : '') +
                                (location.countryRu && location.cityRu ? ', ' : '') +
                                (location.cityRu ? location.cityRu : '');
                            break;
                        case 'ua':
                            res =
                                (location.countryUa ? location.countryUa : '') +
                                (location.countryUa && location.cityUa ? ', ' : '') +
                                (location.cityUa ? location.cityUa : '');
                            break;
                        case 'pl':
                            res =
                                (location.countryPl ? location.countryPl : '') +
                                (location.countryPl && location.cityPl ? ', ' : '') +
                                (location.cityPl ? location.cityPl : '');
                        case 'en':
                        case 'ru':
                        default:
                            res =
                                (location.countryEn ? location.countryEn : '') +
                                (location.countryEn && location.cityEn ? ', ' : '') +
                                (location.cityEn ? location.cityEn : '');
                            break;
                    }
                }
                return res;
            };
        },
    ])
    .filter('translateCountry', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (location) {
                let lang = $rootScope.currentLang;
                let res = '';
                switch (lang) {
                    case 'ru':
                        res = location.countryRu ? location.countryRu : '';
                        break;
                    case 'en':
                        res = location.countryEn ? location.countryEn : '';
                        break;
                    case 'ua':
                        res = location.countryUa ? location.countryUa : '';
                        break;
                    case 'pl':
                        res = location.countryPl ? location.countryPl : '';
                        break;
                }
                return res;
            };
        },
    ])
    .filter('translateCity', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (location) {
                let lang = $rootScope.currentLang;
                let res = '';
                switch (lang) {
                    case 'ru':
                        res = location.cityRu ? location.cityRu : '';
                        break;
                    case 'en':
                        res = location.cityEn ? location.cityEn : '';
                        break;
                    case 'ua':
                        res = location.cityUa ? location.cityUa : '';
                        break;
                    case 'pl':
                        res = location.cityPl ? location.cityPl : '';
                        break;
                }
                return res;
            };
        },
    ])
    .filter('translateCountry', [
        '$rootScope',
        '$filter',
        function ($rootScope, $filter) {
            return function (location) {
                let lang = $rootScope.currentLang;
                let res = '';
                switch (lang) {
                    case 'ru':
                        res = location.countryRu ? location.countryRu : '';
                        break;
                    case 'en':
                        res = location.countryEn ? location.countryEn : '';
                        break;
                    case 'ua':
                        res = location.countryUa ? location.countryUa : '';
                        break;
                    case 'pl':
                        res = location.countryPl ? location.countryPl : '';
                        break;
                }
                return res;
            };
        },
    ])
    .filter('translateStage', [
        '$filter',
        function ($filter) {
            return function (stage) {
                if (stage.customInterviewStateId) {
                    return stage.value;
                } else {
                    return $filter('translate')(stage.value);
                }
            };
        },
    ])
    .filter('translateStage2', [
        '$filter',
        '$rootScope',
        function ($filter, $rootScope) {
            return function (stage) {
                const customStage = $rootScope.customStages.find(
                    (stageObj) => stageObj.customInterviewStateId === stage,
                );
                return customStage ? customStage.name : $filter('translate')(stage);
            };
        },
    ])
    .filter('countFormat', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (count) {
                if ($translate.use() == 'en') {
                    return count + ' ' + $filter('translate')('Active vacancies');
                } else {
                    return count;
                }
            };
        },
    ])
    .filter('clientsCountFormat', [
        '$filter',
        function ($filter) {
            return function (count) {
                if (count) return count;
                return 0;
            };
        },
    ])
    .filter('YearsSinceDate', [
        function () {
            return function (date) {
                if (date) {
                    const today = new Date(),
                        birthDate = new Date(date),
                        m = today.getMonth() - birthDate.getMonth();
                    let age = today.getFullYear() - birthDate.getFullYear();
                    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                        age--;
                    }

                    return age;
                }
            };
        },
    ])
    .filter('YearsEndingForm', [
        function () {
            return function (date) {
                if (date) {
                    const today = new Date(),
                        birthDate = new Date(date),
                        m = today.getMonth() - birthDate.getMonth();
                    let age = today.getFullYear() - birthDate.getFullYear();
                    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                        age--;
                    }
                    const cases = [2, 0, 1, 1, 1, 2];

                    return ['years old1', 'years old2', 'age_1'][
                        age % 100 > 4 && age % 100 < 20 ? 2 : cases[age % 10 < 5 ? age % 10 : 5]
                    ];
                }
            };
        },
    ])
    .filter('ageDisplay', [
        '$filter',
        function ($filter) {
            return function (ageFrom, ageTo) {
                if (ageFrom != undefined && ageTo == undefined) {
                    return $filter('translate')('from') + ' ' + ageFrom;
                } else if (ageTo != undefined && ageFrom == undefined) {
                    return $filter('translate')('to') + ' ' + ageTo;
                } else if (ageTo != undefined && ageFrom != undefined) {
                    return ageFrom + '-' + ageTo;
                }
            };
        },
    ])
    .filter('adviceFilter', function () {
        return function (advices, criteria) {
            var filtered = [];
            var limit = criteria.limit;
            angular.forEach(advices, function (val, s, i) {
                if (limit != undefined && limit > s) {
                    if (val.scorePersent != undefined && val.scorePersent > 25) {
                        filtered.push(val);
                    }
                }
            });
            return filtered;
        };
    })
    .filter('candidateMPhone', function () {
        return function (value) {
            let phone =
                value.slice(0, -7) +
                ' ' +
                value.slice(0, -4).slice(-3) +
                ' ' +
                value.slice(0, -2).slice(-2) +
                ' ' +
                value.slice(-2);
            return phone;
        };
    })
    .filter('transliteration', [
        'transliteration',
        function (transliteration) {
            return function (value) {
                var transl = transliteration.getArray();
                var result = '';
                for (var i = 0; i < value.length; i++) {
                    if (transl[value[i]] != undefined) {
                        result += transl[value[i]];
                    } else {
                        if (value[i].match(/\w/)) {
                            result += value[i];
                        } else if (value[i] == '#') {
                            result += '-sharp';
                        } else if (value[i] == '/') {
                            result += '|';
                        } else {
                            result += '_';
                        }
                    }
                }
                return result;
            };
        },
    ])
    .filter('responsibleWithout', function () {
        return function (responsible, responsibleHas) {
            var filtered = [];
            angular.forEach(responsible, function (person) {
                if (person.userId != undefined) {
                    var has = false;
                    angular.forEach(responsibleHas, function (hasPerson) {
                        if (hasPerson.responsible && hasPerson.responsible.userId === person.userId) {
                            has = true;
                        }
                    });
                    if (!has) {
                        filtered.push(person);
                    }
                }
            });
            return filtered;
        };
    })
    .filter('scopeRegionFilter', function () {
        return function (regions, regionId) {
            var filtered = [];
            angular.forEach(regions, function (region) {
                if (angular.equals(region.regionId, regionId)) {
                    filtered.push(region);
                }
            });
            return filtered;
        };
    })
    .filter('cutName', function () {
        return function (value, wordwise, max, tail) {
            if (!value) return '';

            max = parseInt(max, 10);
            if (!max) return value;
            if (value.length <= max) return value;

            value = value.substr(0, max);
            if (wordwise) {
                var lastspace = value.lastIndexOf(' ');
                if (lastspace != -1) {
                    value = value.substr(0, lastspace);
                }
            }

            return value + (tail || '…');
        };
    })
    .filter('cut', function () {
        return function (value, wordwise, max, tail) {
            var endExtension = '';
            if (!value) return '';
            if (value.split('.').length === 2) {
                endExtension = value.split('.')[1];
                value = value.split('.')[0];
            }
            max = parseInt(max, 10);
            if (!max) return value;
            if (value.length + value.split(' ').length <= max) return value + endExtension;

            value = value.substr(0, max);
            if (wordwise) {
                var lastspace = value.lastIndexOf(' ');
                if (lastspace !== -1) {
                    value = value.substr(0, lastspace);
                }
            }
            return value + (tail || '...') + endExtension;
        };
    })
    .filter('modalchangestatusplaceholder', [
        '$filter',
        function ($filter) {
            return function (value) {
                if (value == 'declinedoffer') {
                    return $filter('translate')('Write a comment why candidate long offer (required)');
                } else {
                    return (
                        $filter('translate')('Write a comment about changing candidate status to') +
                        ' ' +
                        $filter('translate')('(optional)')
                    );
                }
            };
        },
    ])
    .filter('spellcheck', function () {
        return function (value) {
            if (!value) {
                return '';
            }
            if (value === 'admin') {
                return 'Admin';
            }
            if (value === 'client') {
                return 'Hiring manager';
            }
            if (value === 'hr:client') {
                return 'Hiring manager';
            }
            if (value === 'recruter') {
                return 'Recruiter';
            }
            if (value === 'researcher') {
                return 'Researcher';
            }
            if (value === 'hr:admin') {
                return 'Admin';
            }
            if (value === 'hr:recruter') {
                return 'Recruiter';
            }
            if (value === 'freelancer') {
                return 'Freelancer';
            }
            if (value === 'hr:freelancer') {
                return 'Freelancer';
            }
            return value;
        };
    })
    .filter('cutScope', function () {
        return function (value) {
            if (!value) return '';
            if (value.length < 15) {
                return value;
            } else {
                value = value.substr(0, 13);
                return value + '...';
            }
        };
    })
    .filter('unique', function () {
        return function (items, filterOn) {
            if (filterOn === false) {
                return items;
            }

            if ((filterOn || angular.isUndefined(filterOn)) && angular.isArray(items)) {
                var hashCheck = {},
                    newItems = [];

                var extractValueToCompare = function (item) {
                    if (angular.isObject(item) && angular.isString(filterOn)) {
                        return item[filterOn];
                    } else {
                        return item;
                    }
                };

                angular.forEach(items, function (item) {
                    var valueToCheck,
                        isDuplicate = false;

                    for (var i = 0; i < newItems.length; i++) {
                        if (angular.equals(extractValueToCompare(newItems[i]), extractValueToCompare(item))) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (!isDuplicate) {
                        newItems.push(item);
                    }
                });
                items = newItems;
            }
            return items;
        };
    })
    .filter('escape', function () {
        return window.escape;
    })
    .filter('interpolate', [
        'version',
        function (version) {
            return function (text) {
                return String(text).replace(/\%VERSION\%/gm, version);
            };
        },
    ])
    .filter('translatestatus', [
        '$filter',
        function ($filter) {
            return function (text) {
                switch (text) {
                    case 'shortlist':
                        return $filter('translate')('short_list');
                    case 'longlist':
                        return $filter('translate')('long_list');
                    case 'interview':
                        return $filter('translate')('interview');
                    case 'approved':
                        return $filter('translate')('approved');
                    case 'notafit':
                        return $filter('translate')('not_a_fit');
                    case 'declinedoffer':
                        return $filter('translate')('declined_offer');
                    case 'no_contacts':
                        return $filter('translate')('no_contacts');
                    case 'no_response':
                        return $filter('translate')('no_response');
                }
            };
        },
    ])
    .filter('translateDescr', [
        '$filter',
        function ($filter) {
            return function (data) {
                if (data) {
                    let locIgnore = data.slice(0, 8);
                    if (locIgnore === 'Location' || locIgnore === 'location') {
                        let arr = data.split('\n');
                        for (let i in arr) {
                            let bufer = arr[i].split(': ');
                            arr[i] = bufer.join(': ');
                        }
                        return arr.join('\n');
                    } else {
                        let arr = data.split('\n');
                        for (let i in arr) {
                            let bufer = arr[i].split(': ');
                            for (let k in bufer) {
                                bufer[k] = $filter('translate')(bufer[k]);
                            }
                            arr[i] = bufer.join(': ');
                        }
                        return arr.join('\n');
                    }
                }
            };
        },
    ])
    .filter('getorders', function () {
        return function (orders) {
            var filtered_list = [];
            if (orders != undefined) {
                for (var i = 0; i < orders.length; i++) {
                    var two_days_ago = new Date().getTime() - 24 * 60 * 60 * 1000;
                    var last_modified = new Date(orders[i].date).getTime();

                    filtered_list.push(orders[i]);
                }
            }

            return filtered_list;
        };
    })
    .filter('showRegionInVacancy', function () {
        return function (region) {
            if (region) {
                if (region.city) {
                    return region.city;
                } else if (region.country) return region.country;
            }
            return '';
        };
    })
    .filter('removeHtmlTagFromString', function () {
        return function (text) {
            if (text) {
                return text.replace(/<a [^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '$1');
            }
        };
    })
    .filter('linkify3', function () {
        return function (text) {
            if (text) {
                text = text.replace(/((https?\:\/\/)|(www\.))(\S+)/gi, function (url) {
                    const fullLinksList = ['https://meet.google.com', 'https://join.skype.com'];
                    const displayFullLink = fullLinksList.some((u) => url.includes(u));
                    // Remove <br> tag
                    var full_url = url.replace(/<br\b[^>]*>|<\/br>/g, '');

                    if (!full_url.match('^https?://')) {
                        full_url = 'http://' + full_url;
                    }

                    url = url.replace('http://', '').replace('https://', '').replace('www.', '');
                    var dot = '';

                    if (url.length > 24) {
                        dot = '...';
                    }

                    // In case of <br> was part of url - move it to the end to save line break
                    const addLineBreak = () => {
                        if (url.endsWith('<br>') || url.endsWith('<br />') || url.endsWith('<br/>')) {
                            return '<br>';
                        }
                        return '';
                    };

                    if (displayFullLink) {
                        return '<a target="_blank" href="' + full_url + '">' + url + '</a>' + addLineBreak();
                    } else {
                        return (
                            '<a target="_blank" href="' +
                            full_url +
                            '">' +
                            url.substr(0, 24) +
                            dot +
                            '</a>' +
                            addLineBreak()
                        );
                    }
                });
            }
            return text;
        };
    })
    .filter('textForNotice', function ($sce, $filter) {
        return function (text, withDot = false) {
            var respText;
            var dots = '';
            if ($filter('countOfTextInSticker')(text) > 210 && withDot) dots = '...';
            if (text && text.length > 0) {
                respText = $filter('linkify3')(text) + dots;
            } else {
                respText = '';
            }
            respText = respText.replace(new RegExp('\n', 'g'), '<br />');
            return $sce.trustAsHtml(respText);
        };
    })
    .filter('trust', [
        '$sce',
        function ($sce) {
            return function (htmlCode) {
                return $sce.trustAsHtml(htmlCode);
            };
        },
    ])
    .filter('countOfTextInSticker', function () {
        return function (text) {
            var limitRow = 30;
            var count = 0;
            if (text != undefined) {
                var arr = text.split(/\n/g);
                angular.forEach(arr, function (val) {
                    if (val.length >= limitRow) {
                        count = count + val.length;
                    } else {
                        count = count + limitRow;
                    }
                });
            }
            return count;
        };
    })
    .filter('stringToJson', function () {
        return function (text) {
            if (text != undefined) {
                var s = JSON.parse(text);
                if (angular.isArray(s)) {
                    return s;
                } else {
                    return s.split(',');
                }
            }
            return [];
        };
    })
    .filter('jsonFilt', function () {
        return function (item, name) {
            if (item) {
                item = JSON.parse(item);
                return item[name];
            }
        };
    })
    .filter('numberSpacing', function () {
        return function (number) {
            if (number != undefined) {
                var parts = number.toString().split('.');
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
                return parts.join('.');
            }
            return [];
        };
    })
    .filter('dateCounter', function ($filter, $sce) {
        return function (date, type) {
            if (!date) {
                return '';
            }
            var msDate = null;
            switch (type) {
                case 'String':
                    msDate = new Date(date);
                    break;
                case 'second':
                    msDate = new Date(date * 1000);
                    break;
                case 'milliseconds':
                    msDate = new Date(date);
                default:
                    msDate = date;
            }

            var today = new Date();
            today.setUTCHours(0, 0, 0, 0);
            msDate.setUTCHours(0, 0, 0, 0);
            var seconds = (today.getTime() - msDate.getTime()) / 1000;
            var future = false;
            if (seconds < 0) {
                seconds = Math.abs(seconds);
                future = true;
            }

            var res = '';
            var day = parseInt(seconds / 3600 / 24);
            var translate = $filter('translate');
            if (day === 0) {
                return $sce.trustAsHtml(translate('today'));
            } else if (day === 1) {
                if (future) {
                    return $sce.trustAsHtml(translate('tomorrow'));
                } else {
                    return $sce.trustAsHtml("<span style='color:#D95C5C'>" + translate('yesterday') + '</span>');
                }
            } else {
                var cases = [2, 0, 1, 1, 1, 2];
                res +=
                    day +
                    ' ' +
                    [translate('day'), translate('days'), translate('days_1')][
                        day % 100 > 4 && day % 100 < 20 ? 2 : cases[day % 10 < 5 ? day % 10 : 5]
                    ];
            }
            if (!future) {
                res += ' ' + translate('ago');
                res = "<span style='color:#D95C5C'>" + res + '</span>';
            } else {
                res = translate('in_1') + ' ' + res;
            }
            return $sce.trustAsHtml(res);
        };
    })
    .filter('makeLink', [
        function () {
            return function (linkName, itsClient) {
                if (itsClient && linkName) {
                    if (!linkName.match(/^(https?:\/\/|http:\/\/)/i)) {
                        return 'https://' + linkName;
                    } else {
                        return linkName;
                    }
                    return;
                }

                if (linkName != undefined) {
                    var linkNameArr = linkName.split(':');
                    if (linkNameArr[0] != 'http' && linkNameArr[0] != 'https') {
                        linkNameArr.unshift('http://');
                        linkNameArr = linkNameArr.join('');
                        return linkNameArr;
                    } else {
                        linkNameArr = linkNameArr.join(':');
                        return linkNameArr;
                    }
                } else {
                    return '';
                }
            };
        },
    ])
    .filter('filter_refToDomain', function () {
        return function (value) {
            if (!value) return;
            return value.split('/')[2];
        };
    })
    .filter('html', function () {
        return function (value) {
            if (!value) return;
            value = value.indexOf('&amp;') !== -1 ? value.replace('&amp;', '&') : value;
            return value;
        };
    })
    .filter('filter_parseObject', function () {
        return function (value) {
            var str,
                str2,
                i = 0,
                max,
                resault = '';

            if (!value) return;

            value = JSON.parse(value);
            max = Object.entries(value);

            for (; i < max.length; i++) {
                str = max[i][0];
                str2 = max[i][1];
                resault += str + ':' + str2 + '<br />';
            }

            return resault;
        };
    })
    .filter('filterDataForReport', filterFildsForCustomReports)
    .filter('parseCamelCase', function () {
        return function (string) {
            var result = [];
            Array.prototype.map.call(string, (letter) => {
                if (letter === letter.toLowerCase()) {
                    result.push(letter);
                } else {
                    result.push(' ');
                    result.push(letter);
                }
            });
            return result.join('');
        };
    })
    .filter('userTypes', [
        '$filter',
        function ($filter) {
            return function (access) {
                switch (access) {
                    case 'full-access':
                        return $filter('translate')('Paid_user');
                    case 'limited-access':
                        return $filter('translate')('Paid_user');
                    case 'free-access':
                        return $filter('translate')('Free_user');
                }
            };
        },
    ])
    .filter('mailingServiceMessageParser', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (sendMailingParams, mailsToSend) {
                let parsedWords = {
                    letter: $filter('getWordEndingForm')('letter', sendMailingParams.freeMailCount),
                    free: $filter('getWordEndingForm')('free_1', sendMailingParams.freeMailCount),
                };

                if (sendMailingParams.freeMailCount && !sendMailingParams.compaignPrice) {
                    return $filter('translate')('Available letters amount', {
                        amount: sendMailingParams.freeMailCount,
                        mailsToSend: mailsToSend,
                        parsedWords: parsedWords,
                    });
                }

                if (
                    !sendMailingParams.freeMailCount &&
                    sendMailingParams.compaignPrice <= sendMailingParams.accountBalance
                ) {
                    return $filter('translate')('The price of mailing is', {
                        price: sendMailingParams.compaignPrice.toFixed(2),
                    });
                }

                if (
                    sendMailingParams.freeMailCount &&
                    sendMailingParams.compaignPrice &&
                    sendMailingParams.compaignPrice <= sendMailingParams.accountBalance
                ) {
                    return (
                        $filter('translate')('Available letters amount', {
                            amount: sendMailingParams.freeMailCount,
                            mailsToSend: sendMailingParams.freeMailCount,
                            parsedWords: parsedWords,
                        }) +
                        ' ' +
                        $filter('translate')('The price of mailing is', {
                            price: sendMailingParams.compaignPrice.toFixed(2),
                        })
                    );
                }

                if (sendMailingParams.compaignPrice > sendMailingParams.accountBalance) {
                    return $filter('translate')('You do not have enough money on your balance to make a mailing.');
                }
            };
        },
    ])
    .filter('getWordEndingForm', [
        '$filter',
        '$translate',
        function ($filter, $translate) {
            return function (word, number) {
                let parsedNumber = number % 10,
                    lang = $translate.use();

                function getWordEndingForm() {
                    if (lang === 'en') {
                        if (number === 1) {
                            return $filter('translate')(word + '.single.0');
                        } else {
                            return $filter('translate')(word + '.plural.0');
                        }
                    }

                    if (lang === 'ru') {
                        if (number >= 10 && number <= 20) return $filter('translate')(word + '.plural.1');
                        if (parsedNumber === 1) return $filter('translate')(word + '.single.0');
                        if (parsedNumber === 0 || (parsedNumber >= 5 && parsedNumber <= 9))
                            return $filter('translate')(word + '.plural.1');
                        if (parsedNumber > 1 && parsedNumber <= 4) return $filter('translate')(word + '.plural.0');
                    }
                }

                return getWordEndingForm();
            };
        },
    ])
    .filter('paymentTariff', [
        '$filter',
        function () {
            return function (tarif) {
                switch (tarif) {
                    case 'free':
                        return '1 Recruiter';
                    case 'standard':
                    case 'standart':
                        return 'Standard';
                    case 'corporate':
                        return 'Corporate';
                    default:
                        return tarif;
                }
            };
        },
    ])
    .filter('customFieldsType', [
        '$filter',
        function ($filter) {
            return function (type, single) {
                type = angular.copy(type);

                if (single) {
                    switch (type) {
                        case 'vacancy':
                            return $filter('translate')('vacancy_l');
                        case 'candidate':
                            return $filter('translate')('candidate_l');
                        case 'client':
                            return $filter('translate')('client_l');
                        default:
                            return type;
                    }
                } else {
                    switch (type) {
                        case 'vacancy':
                            return $filter('translate')('vacancies_1');
                        case 'candidate':
                            return $filter('translate')('candidates_1');
                        case 'client':
                            return $filter('translate')('clients_1');
                        default:
                            return type;
                    }
                }
            };
        },
    ])
    .filter('customFieldType', [
        '$filter',
        function ($filter) {
            return function (type) {
                type = angular.copy(type);
                switch (type) {
                    case 'select':
                        return 'Drop-down list';
                    case 'string':
                        return 'text';
                    case 'date':
                        return 'date';
                    case 'datetime':
                        return 'Date and time';
                    default:
                        return type;
                }
            };
        },
    ])
    .filter('breakOnMiddleString', [
        '$sce',
        function ($sce) {
            return function (value) {
                let spaceBeforeIndex = null,
                    spaceAfterIndex = null;

                for (let i = Math.floor(value.length / 2); i >= 0; i--) {
                    if (value[i] === ' ') {
                        spaceBeforeIndex = i;
                        break;
                    }
                }

                for (let i = Math.floor(value.length / 2); i <= value.length; i++) {
                    if (value[i] === ' ') {
                        spaceAfterIndex = i;
                        break;
                    }
                }

                if (value.length - spaceAfterIndex < value.length - spaceBeforeIndex) {
                    spaceBeforeIndex = spaceAfterIndex;
                }

                value = value.split('');
                value[spaceBeforeIndex] = '<br/>';
                return $sce.trustAsHtml(value.join(''));
            };
        },
    ])
    .filter('displayDeletedCandidatesNames', [
        '$filter',
        function ($filter) {
            return function (deleteFromSystem, names) {
                const candidates = setCandidatesToDisplay();

                if (deleteFromSystem) {
                    switch (names.length) {
                        case 1:
                            return $filter('translate')('Are you sure want delete candidate from the system', {
                                name: candidates,
                            });
                        case 2:
                            return $filter('translate')('Are you sure want delete candidates from the system', {
                                candidates: candidates,
                            });
                        default:
                            return $filter('translate')('Are you sure want delete candidates from the system N', {
                                candidates: candidates,
                            });
                    }
                } else {
                    switch (names.length) {
                        case 1:
                            return $filter('translate')('Are you sure that you want to delete candidate', {
                                candidate: candidates,
                            });
                        case 2:
                            return $filter('translate')('Are you sure that you want to delete candidates', {
                                candidates: candidates,
                            });
                        default:
                            return $filter('translate')('Are you sure that you want to delete candidates N', {
                                candidates: candidates,
                            });
                    }
                }

                function setCandidatesToDisplay() {
                    switch (names.length) {
                        case 1:
                            return names[0];
                        case 2:
                            return `${names[0]} ${$filter('translate')('and')} ${names[1]}`;
                        default:
                            return names.length;
                    }
                }
            };
        },
    ])
    .filter('deletedCandidatesAmount', [
        '$filter',
        function ($filter) {
            return function (str = '', history) {
                const start = str.indexOf(':"') + 2;
                const end = str.indexOf('"}');
                const candidatesAmount = +str.substring(start, end);
                switch (candidatesAmount) {
                    case 1:
                        return $filter('translate')('User name removed candidate Removed Candidate from the database.');
                    default:
                        return $filter('translate')(
                            'User name removed N candidates Removed Candidate from the database.',
                            {
                                amount: candidatesAmount,
                            },
                        );
                }
            };
        },
    ])
    .filter('ageRange', [
        '$filter',
        function () {
            return function (years) {
                function ageRangeToYears(ms) {
                    return ms ? new Date().getFullYear() - new Date(ms).getFullYear() : ms;
                }

                return years ? ageRangeToYears(years + 1) : null;
            };
        },
    ])
    .filter('performanceReportStageValue', [
        '$sce',
        function ($sce) {
            return function (stage, isVacanciesSelected, showByMonths, month, week) {
                let firstMonth,
                    secondMonth,
                    showHint = false;
                if (stage == undefined || stage == null) {
                    if (month && week) {
                        firstMonth = +week.slice(3, 5);
                        secondMonth = +week.slice(-2);
                        if (firstMonth !== secondMonth && showByMonths && month !== 'Total_Period') {
                            showHint = true;
                        }
                    }
                } else {
                    if (month && week) {
                        firstMonth = +week.slice(3, 5);
                        secondMonth = +week.slice(-2);
                        let adjacentMonthIsFilled =
                            stage[getMonthFromString(month) + 1] || stage[getMonthFromString(month) - 1];
                        if (
                            firstMonth !== secondMonth &&
                            adjacentMonthIsFilled &&
                            (stage[firstMonth] || stage[secondMonth]) &&
                            showByMonths &&
                            month !== 'Total_Period'
                        ) {
                            showHint = true;
                        }
                    }
                    switch (stage) {
                        case stage || !isVacanciesSelected:
                            if (showHint) {
                                return $sce.trustAsHtml(
                                    Object.values(stage).reduce((prev, cur) => (prev += cur.length), 0) +
                                        '<span style="color:red;margin-left:5px">(' +
                                        returnLength(stage[firstMonth]) +
                                        '+' +
                                        returnLength(stage[secondMonth]) +
                                        ')*</span>',
                                );
                            } else {
                                return Object.values(stage).reduce((prev, cur) => (prev += cur.length), 0) || '-';
                            }

                        case !stage && isVacanciesSelected:
                            return ' ';
                    }
                }

                function getMonthFromString(mon) {
                    return new Date(Date.parse(mon + ' 1, 2021')).getMonth() + 1;
                }

                function returnLength(arr) {
                    if (arr) return arr.length;
                    else return 0;
                }
            };
        },
    ])
    .filter('performanceReportStageTitle', [
        '$filter',
        '$rootScope',
        function ($filter, $rootScope) {
            return function (stage, isVacanciesSelected, showByMonths, month, week) {
                let firstMonth,
                    secondMonth,
                    showHint = false;
                if (stage == undefined || stage == null) {
                    if (month && week) {
                        firstMonth = +week.slice(3, 5);
                        secondMonth = +week.slice(-2);
                        if (firstMonth !== secondMonth && showByMonths && month !== 'Total_Period') {
                            showHint = true;
                        }
                    }
                    if (showHint) {
                        if (stage) {
                            return `${returnLength(stage[firstMonth])} ${getLettersEndCandidates(
                                returnLength(stage[firstMonth]),
                                1,
                            )} ${returnMonthName(firstMonth)}, ${$filter('translate')('_and')} ${returnLength(
                                stage[secondMonth],
                            )} ${getLettersEndCandidates(returnLength(stage[secondMonth]), 2)} ${returnMonthName(
                                secondMonth,
                            )}`;
                        } else {
                            return '';
                        }
                    }
                    if (!stage && isVacanciesSelected) {
                        return $filter('translate')('No such stage in this vacancy');
                    }

                    return '';
                } else {
                    if (month && week) {
                        firstMonth = +week.slice(3, 5);
                        secondMonth = +week.slice(-2);
                        let adjacentMonthIsFilled =
                            stage[getMonthFromString(month) + 1] || stage[getMonthFromString(month) - 1];
                        if (
                            firstMonth !== secondMonth &&
                            adjacentMonthIsFilled &&
                            (stage[firstMonth] || stage[secondMonth]) &&
                            showByMonths &&
                            month !== 'Total_Period'
                        ) {
                            showHint = true;
                        }
                    }
                    if (showHint) {
                        if (stage) {
                            return `${returnLength(stage[firstMonth])} ${getLettersEndCandidates(
                                returnLength(stage[firstMonth]),
                                1,
                            )} ${returnMonthName(firstMonth)}, ${$filter('translate')('_and')} ${returnLength(
                                stage[secondMonth],
                            )} ${getLettersEndCandidates(returnLength(stage[secondMonth]), 2)} ${returnMonthName(
                                secondMonth,
                            )}`;
                        } else {
                            return '';
                        }
                    }
                    if (!stage && isVacanciesSelected) {
                        return $filter('translate')('No such stage in this vacancy');
                    }

                    return '';
                }
            };

            function getMonthFromString(mon) {
                return new Date(Date.parse(mon + ' 1, 2021')).getMonth() + 1;
            }

            function returnLength(arr) {
                if (arr) return arr.length;
                else return 0;
            }

            function getLettersEndCandidates(number, position) {
                let n = Math.abs(number) % 100,
                    n1 = n % 10,
                    form = 2,
                    message;

                if (n > 10 && n < 20) form = 2;
                if (n1 > 1 && n1 < 5) form = 1;
                if (n1 === 1) {
                    if (number > 1 && $rootScope.currentLang === 'en') {
                        form = 2;
                    } else {
                        form = 0;
                    }
                }
                if (n > 10 && n < 15 && n1 > 0 && n1 < 5) form = 2;
                if (position === 1) {
                    switch (form) {
                        case 0:
                            message = $filter('translate')('candidate was added in');
                            break;
                        case 1:
                            message = $filter('translate')('candidate_1 was added in');
                            break;
                        case 2:
                            message = $filter('translate')('candidates were added in');
                            break;
                    }
                } else {
                    switch (form) {
                        case 0:
                            message = $filter('translate')('candidate in');
                            break;
                        case 1:
                            message = $filter('translate')('candidate_1 in');
                            break;
                        case 2:
                            message = $filter('translate')('candidates in');
                            break;
                    }
                }
                return message;
            }

            function returnMonthName(month) {
                switch (month) {
                    case 1:
                        return $filter('translate')('in_January');
                    case 2:
                        return $filter('translate')('in_February');
                    case 3:
                        return $filter('translate')('in_March');
                    case 4:
                        return $filter('translate')('in_April');
                    case 5:
                        return $filter('translate')('in_May');
                    case 6:
                        return $filter('translate')('in_June');
                    case 7:
                        return $filter('translate')('in_July');
                    case 8:
                        return $filter('translate')('in_August');
                    case 9:
                        return $filter('translate')('in_September');
                    case 10:
                        return $filter('translate')('in_October');
                    case 11:
                        return $filter('translate')('in_November');
                    case 12:
                        return $filter('translate')('in_December');
                }
            }
        },
    ])
    .filter('DeclOfNumbers', [
        '$translate',
        'vocabulary',
        function ($translate, vocabulary) {
            return function (word, number) {
                const cases = [2, 0, 1, 1, 1, 2];
                number = Math.abs(number);

                if ($translate.use() === 'ru') {
                    return vocabulary['ru'][word][
                        number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]
                    ];
                } else if ($translate.use() === 'en') {
                    return number === 1 ? vocabulary['en'][word][0] : vocabulary['en'][word][1];
                } else if ($translate.use() === 'pl') {
                    return number === 1 ? vocabulary['pl'][word][0] : vocabulary['pl'][word][1];
                } else {
                    return vocabulary['ua'][word][
                        number % 100 > 4 && number % 100 < 20 ? 2 : cases[number % 10 < 5 ? number % 10 : 5]
                    ];
                }
            };
        },
    ])
    .filter('limitToEllipse', [
        '$filter',
        function ($filter) {
            return function (text, limit) {
                if (text) {
                    return text.length > limit ? $filter('limitTo')(text, limit) + '...' : text;
                }
                return text;
            };
        },
    ])
    .filter('htmlToText', function () {
        return function (text) {
            return text
                ? String(text)
                      .replace(/<[^>]+>/gm, '')
                      .replace(/&nbsp;/g, ' ')
                      .replace(/&rsquo;/, "'")
                      .replace(/(&ldquo;)|(&rdquo;)/g, '"')
                      .trim()
                : '';
        };
    })
    .filter('limitHtmlText', [
        '$filter',
        function ($filter) {
            return function (text, limit) {
                let modernText = text.replace(/<style((.|\n|\r)*?)<\/style>/g, '');
                const brIndex = modernText.indexOf('<br');
                const oneLine = brIndex > -1 ? modernText.slice(0, brIndex) : modernText;
                const clearText = modernText ? String(oneLine).replace(/<[^>]+>/gm, '') : '';
                return $filter('limitToEllipse')(clearText, limit);
            };
        },
    ])
    .filter('customSourceFilter', function () {
        return function (text) {
            const textArray = text.split('');
            let span = document.createElement('span');
            document.body.append(span);
            span.style.fontSize = '14px';
            span.style.fontFamily = 'Helvetica-Medium';
            span.textContent = '';

            for (let letter = 0; letter < textArray.length; letter++) {
                span.textContent += textArray[letter];
                if (window.screen.width < 420) {
                    if (span.offsetWidth > 135) {
                        span.remove();
                        return text.slice(0, letter + 1) + '...';
                    }
                } else {
                    if (span.offsetWidth > 220) {
                        span.remove();
                        return text.slice(0, letter + 1) + '...';
                    }
                }
            }
            span.remove();
            return text;
        };
    })
    .filter('clientCommentsFilter', function () {
        return function (text) {
            const textArray = text.split('');
            let span = document.createElement('span');
            document.body.append(span);
            span.style.fontSize = '14px';
            span.style.fontFamily = 'Helvetica-Medium';
            span.textContent = '';

            for (let letter = 0; letter < textArray.length; letter++) {
                span.textContent += textArray[letter];
                if (span.offsetWidth > 475) {
                    span.remove();
                    return text.slice(0, letter + 1) + '...';
                }
            }
            span.remove();
            return text;
        };
    })
    .filter('changeTabToSpace', function () {
        return function (text) {
            if (text) {
                return String(text).replace(/\t/g, ' ');
            }
            return text;
        };
    })
    .filter('setDefaultValue', function () {
        return function (value, defaultValue) {
            return value ? value : defaultValue;
        };
    })
    .filter('removeHTML', [
        '$filter',
        () => {
            return function (text) {
                if (text) {
                    return text
                        .replace(/&amp;/g, '&')
                        .replace(/&/g, '&amp;')
                        .replace(/<[^>]*>/g, '');
                }
            };
        },
    ])
    .filter('isTextLengthMoreThen', [
        '$filter',
        () => {
            return function (text, max = 30000) {
                if (text) {
                    let str = text.split('');
                    for (let i = 0; i < str.length; i++) {
                        if (
                            (str[i] === ' ' && str[i + 1] === ' ' && i !== 0 && i !== str.length - 1) ||
                            (str[i] === ' ' && i === str.length - 1)
                        ) {
                            str.splice(i, 1);
                            i--;
                        }
                    }
                    return str.length >= max;
                }
                return 0;
            };
        },
    ])
    .filter('sanitize', function ($sanitize) {
        return (text) => $sanitize(text);
    })
    .filter('changeLinkedSourceName', function () {
        return function (source) {
            const sources = {
                rabotaua: 'Robota.ua',
                workua: 'Work.ua',
                rabotauz: 'Rabota.uz',
                linkedin: 'LinkedIn',
                linkedinNew: 'LinkedIn',
                hh: 'HeadHunter',
                dou: 'Dou',
                grc: 'GRC',
                olx: 'OLX',
                superJob: 'SuperJob',
                jobkg: 'Job.kg',
                djinni: 'Djinni',
                pracuj: 'Pracuj.pl',
                estaffId: 'Estaff',
                jobsTutBy: 'jobs.tut.by',
            };

            return sources[source] || source;
        };
    })
    .filter('debounce', function () {
        return function (fn, ms) {
            let timeout;
            return function () {
                const fnCall = () => fn.apply(this, arguments);
                clearTimeout(timeout);
                timeout = setTimeout(fnCall, ms);
            };
        };
    })
    .filter('capitalize', function () {
        return function (value) {
            if (value) {
                return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
            }
            return value;
        };
    });

function linkify(inputText) {
    var replacedText, replacePattern1, replacePattern2, replacePattern3;

    //URLs starting with http://, https://, or ftp://
    replacePattern1 = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim;
    replacedText = inputText.replace(replacePattern1, '<a href="$1" target="_blank">$1</a>');

    //URLs starting with "www." (without // before it, or it'd re-link the ones done above).
    replacePattern2 = /(^|[^\/])(www\.[\S]+(\b|$))/gim;
    replacedText = replacedText.replace(replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>');

    return replacedText;
}

function filterFildsForCustomReports($filter) {
    return (field, scope) => {
        if (scope.ctrlReport) {
            scope = scope.ctrlReport;
        }

        if (field.type.value == 'responsibles') {
            if (!field.value) return '-';
            return field.value.map((item) => scope.getPersonFullName(item.personId)).join(', ');
        } else if (field.type.value == 'client') {
            return field['vacancy'].clientId.name;
        } else if (field.type.value == 'location') {
            return scope.regionIdToName(field['vacancy'].regionId);
        } else if (field.type.value == 'salary') {
            return field['vacancy'].salaryTo ? field['vacancy'].salaryTo + ' ' + field['vacancy'].currency : '-';
        } else if (field.type.value === 'closedBy' && field.value) {
            return field.value.fullName;
        } else if (
            field.type.value == 'dc' ||
            field.type.value == 'de' ||
            field.type.value == 'dateFinish' ||
            field.type.value == 'datePayment'
        ) {
            return formatDate(field.value);
        } else if (field.type.id && field.type.visiable) {
            return filterCustomFildsForCustomReports(field);
        }

        if (!field.value && field.type.visiable) {
            return '-';
        }

        return field.value;
    };
}

function formatDate(date) {
    if (!date) return '-';
    date = new Date(date);

    var dd = date.getDate();
    if (dd < 10) dd = '0' + dd;

    var mm = date.getMonth() + 1;
    if (mm < 10) mm = '0' + mm;

    var yy = date.getFullYear() % 100;

    if (yy < 10) yy = '0' + yy;

    return dd + '.' + mm + '.' + yy;
}

function filterCustomFildsForCustomReports(customField) {
    let vacancy = customField.vacancy;

    if (vacancy.customFieldsDto) {
        vacancy = vacancy.customFieldsDto.filter((item) => item.fieldId == customField.type.id);
        if (vacancy[0] && vacancy[0].type !== 'date' && vacancy[0].type !== 'datetime') {
            return vacancy[0].value;
        } else if (vacancy[0] && vacancy[0].type == 'date') {
            return vacancy[0].value.split(' ')[0];
        } else if (vacancy[0] && vacancy[0].type == 'datetime') {
            return vacancy[0].value.split(':')[0] + ':' + vacancy[0].value.split(':')[1];
        }
    }

    return '-';
}
