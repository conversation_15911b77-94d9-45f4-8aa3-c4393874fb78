component.component('regularPaymentsSettings', {
    bindings: {
        isBlur: '<',
        accountInfo: '<',
    },
    templateUrl: 'partials/regular-payments-settings.html',
    controller: class {
        constructor(Pay, Person, $rootScope, $interval, notificationService, $uibModal, $translate, $scope, $timeout) {
            this.Pay = Pay;
            this.Person = Person;
            this.$rootScope = $rootScope;
            this.$interval = $interval;
            this.notificationService = notificationService;
            this.$uibModal = $uibModal;
            this.$translate = $translate;
            this.$scope = $scope;
            this.$timeout = $timeout;
        }

        $onInit() {
            this.trialPeriod = new Date(this.$rootScope.me.orgParams.trialEndDate) - new Date() > 0;
            this.paidUsers = 0;
            this.Person.onGetAllPersons().then((users) => {
                Object.keys(users.object).forEach((key) => {
                    if (users.object[key].recrutRole !== 'client' && users.object[key].status === 'A') this.paidUsers++;
                });
            });
            this.getCards();
            const removeLastCard = this.$rootScope.$on('removeLastCard', (e, rmCardOrderId) => {
                this._removeCardRequest(rmCardOrderId);
                removeLastCard();
            });
            if (localStorage.getItem('isRedirectedFromPopup')) {
                this.$timeout(() => {
                    localStorage.removeItem('isRedirectedFromPopup');
                    document
                        .querySelector('.payments-settings')
                        .scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            }
        }
        getCards() {
            const self = this;
            this.Pay.getIntegratedCard(
                (resp) => {
                    if (resp.object) {
                        self.integratedCard = resp.object || [];
                        angular.forEach(self.integratedCard, function (val, index) {
                            if (val.status === 'A') self.activeCardIndex = index;
                        });
                    } else {
                        self.integratedCard = [];
                    }
                },
                () => {
                    self.notificationService.error(resp.message);
                },
            );
        }

        addCard() {
            (this.accountInfo.amount < 0 ||
                this.accountInfo.amount / (this.accountInfo.dailyRate * this.paidUsers) < 1) &&
            !this.trialPeriod
                ? (this.$rootScope.showNoMoney = true)
                : (this.$rootScope.showNoMoney = false);
            class AddCardConfirm {
                constructor($uibModalInstance, $rootScope, Pay, notificationService) {
                    this.$uibModalInstance = $uibModalInstance;
                    this.$rootScope = $rootScope;
                    this.Pay = Pay;
                    this.notificationService = notificationService;
                    this.originUrl = document.location.origin;
                }
                closeModal() {
                    this.$uibModalInstance.close();
                }
                addCardConfirm() {
                    this.$rootScope.loading = true;
                    this.Pay.addNewCard()
                        .catch((err) => this.notificationService.error(err.message))
                        .finally(() => (this.$rootScope.loading = false));
                }
            }
            this.$uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/add-card-confirm.html',
                controller: ['$uibModalInstance', '$rootScope', 'Pay', 'notificationService', AddCardConfirm],
                controllerAs: 'vm',
                windowClass: 'invoice-confirm-modal secondary-modal',
                resolve: {},
            });
        }

        setActiveCard(orderId, status) {
            if (this.integratedCard.length === 1 || status === 'A') {
                return;
            }
            const self = this;
            self.$rootScope.loading = true;
            self.Pay.onMakeCardDefault({ orderId })
                .then((resp) => {
                    if (resp.object) {
                        self.integratedCard = [...resp.object];
                        angular.forEach(self.integratedCard, (val, index) => {
                            if (val.status === 'A') self.activeCardIndex = index;
                        });
                    }
                })
                .catch((err) => {
                    self.notificationService.error(err.message);
                })
                .finally(() => {
                    self.$rootScope.loading = false;
                    self.$scope.$apply();
                });
        }
        onSuccessPayment() {
            class onSuccessPayment {
                constructor($uibModalInstance, $rootScope, Pay, notificationService) {
                    this.$uibModalInstance = $uibModalInstance;
                    this.$rootScope = $rootScope;
                    this.Pay = Pay;
                    this.notificationService = notificationService;
                }
                closeModal() {
                    this.$uibModalInstance.close();
                }
            }
            this.$uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/success-payment-message.html',
                controller: ['$uibModalInstance', '$rootScope', 'Pay', 'notificationService', onSuccessPayment],
                controllerAs: 'vm',
                windowClass: 'on-success-payment secondary-modal',
                resolve: {},
            });
        }
        toPayByCard() {
            const integratedCard = this.integratedCard[this.activeCardIndex];
            if (integratedCard && integratedCard.orderId) {
                this.$rootScope.loading = true;
                this.Pay.onMakePaymentByOrgId({
                    orderId: integratedCard.orderId,
                })
                    .then(() => {
                        this.onSuccessPayment();
                    })
                    .catch((err) => {
                        this.notificationService.error(err.message);
                    })
                    .finally(() => {
                        this.$rootScope.loading = false;
                        this.$scope.$apply();
                    });
            }
        }

        removeCard(rmCardOrderId, status) {
            this.rmCardOrderId = rmCardOrderId;
            this.rmCardStatus = status;
            if (this.integratedCard.length === 1 && this.integratedCard[0].orderId === rmCardOrderId) {
                class RemovingLastCardConfirm {
                    constructor($uibModalInstance, $rootScope, rmCardOrderId) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.$rootScope = $rootScope;
                        this.rmCardOrderId = rmCardOrderId;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                    }

                    removeCard() {
                        this.$rootScope.$emit('removeLastCard', this.rmCardOrderId);
                        this.closeModal();
                    }
                }
                this.$uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/removing-last-card-confirm.html',
                    controller: ['$uibModalInstance', '$rootScope', 'rmCardOrderId', RemovingLastCardConfirm],
                    controllerAs: 'vm',
                    windowClass: 'removing-last-card-confirm',
                    resolve: {
                        rmCardOrderId: () => rmCardOrderId,
                    },
                });
            } else {
                this._removeCardRequest(rmCardOrderId);
                this.removingCardTimer = 10;
                this.timerInterval = this.$interval(() => {
                    if (this.removingCardTimer > -1) {
                        this.removingCardTimer--;
                    } else {
                        this.getCards();
                        this.$interval.cancel(this.timerInterval);
                    }
                }, 1000);
            }
        }

        _removeCardRequest(rmCardOrderId) {
            const self = this;
            this.Pay.removeCard({ orderId: rmCardOrderId }, function (resp) {
                if (self.integratedCard.length === 1 && self.integratedCard[0].orderId === rmCardOrderId) {
                    self.getCards();
                }
            });
        }

        cancelRemovingCard() {
            const self = this;
            this.$interval.cancel(this.timerInterval);
            this.removingCardTimer = -1;
            this.Pay.restoreCard(
                { orderId: this.rmCardOrderId, status: this.rmCardStatus },
                (resp) => {
                    self.integratedCard = resp.object;
                },
                () => {
                    self.notificationService.error(resp.message);
                },
            );
        }

        getBankImage(bank) {
            switch (bank.toLowerCase()) {
                case 'monobank': {
                    return '../images/sprite/payments/banks/monoBank.svg';
                }
                case 'commercial bank privatbank':
                case 'privatbank vip':
                case 'privatbank': {
                    return '../images/sprite/payments/banks/privatBank.svg';
                }
                case 'oschadbank':
                case 'jsc oschadbank': {
                    return '../images/sprite/payments/banks/oschadBank.svg';
                }
                case 'wirecard card solutions, ltd': {
                    return '../images/sprite/payments/banks/wireCardBank.svg';
                }
                case 'wpublic jsc ukrsibbank':
                case 'pjsc ukrsibbank':
                case 'ukrsibbank':
                case 'укрсиббанк': {
                    return '../images/sprite/payments/banks/ukrsibBank.svg';
                }
                case 'cjsc alfa-bank':
                case 'public jsc alfa-bank':
                case 'commercial innovation bank alfa-bank':
                case 'alfabank': {
                    return '../images/sprite/payments/banks/AlfaBank.svg';
                }
                case 'first ukrainian international bank': {
                    return '../images/sprite/payments/banks/pumbBank.svg';
                }
                case 'public jsc raiffeisen bank aval': {
                    return '../images/sprite/payments/banks/raiffesenBank.svg';
                }
                case 'otp bank jsc': {
                    return '../images/sprite/payments/banks/otpBank.svg';
                }
                case 'silicon valley bank': {
                    return '../images/sprite/payments/banks/siliconValleyBank.svg';
                }
                case 'chase bank usa, n.a.': {
                    return '../images/sprite/payments/banks/chaseBank.svg';
                }
                case 'lloyds tsb bank plc': {
                    return '../images/sprite/payments/banks/lloydsBank.svg';
                }
                case 'kredo bank': {
                    return '../images/sprite/payments/banks/kredoBank.svg';
                }
                case 'barclays bank plc': {
                    return '../images/sprite/payments/banks/barclaysBank.svg';
                }
                case 'priorbank jscb': {
                    return '../images/sprite/payments/banks/priorBank.svg';
                }
                case 'public jsc commercial bank pravex bank': {
                    return '../images/sprite/payments/banks/praveksBank.svg';
                }
                case 'pjsc credit agricole bank': {
                    return '../images/sprite/payments/banks/creditAgricoleBank.svg';
                }
            }
        }
    },
    controllerAs: 'vm',
});
