class resetPasswordComponentCtrl {
    constructor(Person, notificationService, $scope, $rootScope, $translate) {
        this.Person = Person;
        this.notificationService = notificationService;
        this.$scope = $scope;
        this.$rootScope = $rootScope;
        this.$translate = $translate;
    }
    $onInit() {
        this.resetIsActive = false;
        this.emailIsError = false;
        this.btnIsDisabled = true;
        this.showForm = true;
        this.inputEmailValue = '';
        this.stateMessage = 'default';
        this.stateMessageColor = 'hide-text-message';
        this.reg =
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    }
    toggleResetPassword() {
        this.resetIsActive = !this.resetIsActive;
    }
    onChangeInput() {
        if (this.reg.test(String(this.inputEmailValue).toLowerCase())) {
            this.stateMessage = 'default';
            this.stateMessageColor = 'hide-text-message';
            this.emailIsError = false;
            this.btnIsDisabled = false;
        } else {
            this.emailIsError = true;
            this.stateMessage = this.$translate.instant('Please enter correct email');
            this.stateMessageColor = 'error-text-message';
            this.btnIsDisabled = true;
        }
    }
    closeResetPassword() {
        this.showResetPassword = false;
        this.emailIsError = false;
        this.showForm = true;
        this.btnIsDisabled = true;
        this.inputEmailValue = '';
        this.stateMessage = 'default';
        this.stateMessageColor = 'hide-text-message';
    }
    resetPassword() {
        if (this.reg.test(String(this.inputEmailValue).toLowerCase())) {
            this.$rootScope.loading = true;
            this.Person.forgetPasswordRequest(this.inputEmailValue)
                .then(() => {
                    this.showForm = false;
                })
                .catch((resp) => {
                    if (resp.code === 'unknownEmail') {
                        this.stateMessage = this.$translate.instant('There is no user with this email in CleverStaff');
                        this.stateMessageColor = 'error-text-message';
                    } else {
                        console.error(resp.message);
                    }
                })
                .finally(() => {
                    this.$rootScope.loading = false;
                    this.$scope.$apply();
                });
        } else {
            this.emailIsError = true;
            this.stateMessage = this.$translate.instant('Please enter correct email');
            this.stateMessageColor = 'error-text-message';
        }
    }
}
const resetPasswordComponentDefinition = {
    templateUrl: 'partials/reset-password.html',
    controller: resetPasswordComponentCtrl,
    controllerAs: 'vm',
    bindings: {
        showResetPassword: '=',
    },
};
component.component('resetPasswordComponent', resetPasswordComponentDefinition);
