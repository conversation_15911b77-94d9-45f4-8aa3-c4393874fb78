component.component('pageIntroComponent', {
    bindings: { pageTitle: '<', showBackButton: '<' },
    template: `
        <section class="section__intro" id="intro-section">
           <button-with-icon
                class="back-button"
                ng-if="vm.showBackButton !== undefined ? vm.showBackButton : true"
                ng-click="vm.goBack();"
                type="'secondary'"
                size="'small'"               
                text="'Go back_' | translate"
                icon-name="'arrow-left'"
           ></button-with-icon>       
        
            <h1 ng-if="vm.pageTitle === 'Account data visibility'" class="main-page-title" translate="{{vm.pageTitle}}"></h1>
        </section>
    `,
    controller: function ($window) {
        this.goBack = function () {
            if ($window.location.href.includes('candidates-source')) {
                $window.location.href = '#/reports';
            } else {
                $window.history.back();
            }
        };
        this.goReports = function () {
            $window.location.href = '#/reports';
        };
    },
    controllerAs: 'vm',
});
