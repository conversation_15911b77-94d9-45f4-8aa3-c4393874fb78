component.component('candidatesSearchComponent', {
    bindings: {
        onSearch: '&',
        tableParams: '<',
        candidatesCount: '<',
    },
    templateUrl: 'partials/candidates/search.html',
    controller: class {
        constructor(
            CandidateSearchService,
            Candidate,
            ScopeService,
            Person,
            $rootScope,
            $scope,
            $filter,
            CustomField,
            CustomFieldsSearchService,
            $translate,
            $timeout,
        ) {
            this.candidateSearchService = CandidateSearchService;
            this.candidateService = Candidate;
            this.scopeService = ScopeService;
            this.Person = Person;
            this.$rootScope = $rootScope;
            this.$scope = $scope;
            this.$filter = $filter;
            this.$translate = $translate;
            this.$timeout = $timeout;
            this.customFieldService = CustomField;
            this.customFieldsSearchService = CustomFieldsSearchService;
            this.candidatesPageView = this.$scope.$parent.candidatesPageView;
            this.filterSort = this.$scope.$parent.filterSort;
            this.changeSort = this.$scope.$parent.sortTable;

            this.$rootScope.scorecardInSearch = () => {
                return this.search.fields.scorecards.value;
            };
        }

        $onInit() {
            this.windowWidth = window.innerWidth;
            this._getCustomFields();

            // this.$rootScope.disableNoValue = false;
            this.$rootScope.testRefusalStatusToChangeStage = [];
            this.$rootScope.resetLanguagesFromActual = this.resetLanguagesFromActual.bind(this);
            this.$rootScope.resetEmploymentTypeFromActual = this.resetEmploymentTypeFromActual.bind(this);
            this.$rootScope.resetCityFromActual = this.resetCityFromActual.bind(this);
            this.$rootScope.resetCountryFromActual = this.resetCountryFromActual.bind(this);
            this.$rootScope.resetExperienceFromActual = this.resetExperienceFromActual.bind(this);
            this.$rootScope.resetSkillsFromActual = this.resetSkillsFromActual.bind(this);
            this.$rootScope.resetSalaryFromActual = this.resetSalaryFromActual.bind(this);
            this.$rootScope.resetRoleFromActual = this.resetRoleFromActual.bind(this);
            this.clearTags = this.clearTags.bind(this);

            this._initModel();
            this._initSearch();
            this._getSearch();
            this._initEmitEventAdvancedSearch();
            this.autocompletes = this.$rootScope.autocompletes;
            this.candidateService.search.resetPagination = false;

            this.$rootScope.resetPositionField = false;
            this.$rootScope.resetEmpTypeField = false;
            this.$rootScope.resetExperience = false;
            this.$rootScope.resetLocation = false;
            this.$rootScope.resetSalary = false;
            this.$rootScope.resetSkills = false;
            this.$rootScope.resetLanguages = false;
            this.$rootScope.resetRoleLevels = false;

            this.triggerSearch();
            this.$rootScope.triggerSearchCandidates = this.triggerSearch.bind(this);

            this.isAnyAppliedFIlter = () => {
                return (
                    this.$rootScope.currentSelectScope !== 'company' ||
                    (!this.search.isAnyFilledField() && this.$rootScope.currentSelectScope !== 'company') ||
                    (this.search.isAnyFilledField() && this.isTriggerSearchAll) ||
                    this._isAnyFilledCustomField() ||
                    this.search.fields.gdpr.selectedStatuses?.length > 0
                );
            };

            if (this.$rootScope.isTabletWidth() && this.$rootScope.me.personParams?.candidatesPageView === 'table') {
                this.toggleViewType();
            }
        }

        changeSearchType = (event) => {
            this.search.fields.matchType.value = event.target.checked;
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        removeSingleLanguage = (language) => {
            this.$rootScope.$emit('removeSingleLanguage', language);
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        /**
         * @param {number} amount
         * @return {string}
         */
        getRemoveCandidatesBtnText = (amount) => {
            if (amount === 1) return this.$translate.instant('Delete one candidate');
            if (amount >= 2 && amount <= 4) return this.$translate.instant('Delete from 2 to 4 candidates', { amount });
            if (amount >= 5) return this.$translate.instant('Delete candidates', { amount });
        };

        toggleViewType = () => {
            this.$rootScope.me.personParams['candidatesPageView'] =
                this.$rootScope.me.personParams['candidatesPageView'] === 'list' ? 'table' : 'list';

            this.candidatesPageView = this.$rootScope.me.personParams.candidatesPageView;
            this.$scope.$parent.candidatesPageView = this.$rootScope.me.personParams.candidatesPageView;

            this.Person.onChangePageView({
                name: 'candidatesPageView',
                value: this.$rootScope.me.personParams.candidatesPageView,
            });

            this.$rootScope.$$phase || this.$scope.$apply();
        };

        _getCustomFields() {
            this.customFieldService.getCustomFieldsByType({ objectType: 'candidate' }).then(
                (resp) => {
                    this.customFields = resp.objects;
                    this._initCustomFields();
                    this._getCandidateFilterTemplate();
                    this._setPreviousValuesForCustomFieldsSearch();
                },
                (error) => {
                    this.notificationService.error(error.message);
                    this.$rootScope.loading = false;
                },
            );
        }

        _initCustomFields() {
            if (this.customFields) {
                this.customFieldsSearch = new this.customFieldsSearchService();

                this.customFields.forEach((field) => {
                    switch (field.type) {
                        case 'select':
                            this.customFieldsSearch.addField({
                                id: field.fieldId,
                                name: field.fieldId,
                                type: field.type,
                                data: field.params,
                                placeholder: field.title,
                                path: {
                                    label: 'value.value',
                                    value: 'value.value',
                                },
                            });
                            break;
                        case 'date':
                        case 'datetime':
                            this.customFieldsSearch.addField({
                                id: field.fieldId,
                                name: field.fieldId,
                                type: field.type,
                                placeholder: field.title,
                            });
                            break;
                        default:
                            this.customFieldsSearch.addField({
                                id: field.fieldId,
                                name: field.fieldId,
                                type: field.type,
                                placeholder: field.title,
                                path: { label: 'value', value: 'value' },
                            });
                    }
                });
            }
        }

        _getCandidateFilterTemplate() {
            this.candidateService.getCandidateFilterTemplate().then(
                (resp) => {
                    if (resp.object.fields) {
                        this.selectedFields = resp.object.fields;

                        resp.object.fields.forEach((field) => {
                            if (this.search.fields[field]) {
                                this.search.fields[field].state.isSelected = true;
                            }
                            if (this.customFieldsSearch.fields[field]) {
                                this.customFieldsSearch.fields[field].state.isSelected = true;
                            }
                        });
                        this.$rootScope.loading = false;
                    }
                },
                (error) => {
                    this.$rootScope.loading = false;
                },
            );
        }

        _setPreviousValuesForCustomFieldsSearch() {
            if (this.candidateService.search.customFieldsValues && this.customFieldsSearch) {
                this.customFieldsSearch.fields =
                    angular.copy(this.candidateService.search.customFieldsValues) || this.customFieldsSearch.fields;
                this._setCustomFieldsValues();
            }
        }

        _setCustomFieldsValues() {
            this.$timeout(() => {
                Object.entries(this.customFieldsSearch.fields).forEach(([name, field]) => {
                    if (field.getValue()) {
                        if (field.type === 'date' || field.type === 'datetime') {
                            $('[field-id=' + field.id + ']').datetimepicker('setDate', new Date(field.value));
                        }
                        if (field.type === 'string') {
                            $('[field-id=' + field.id + ']').select2('data', { id: field.value, text: field.value });
                        }
                    }
                });
            });
        }

        resetLanguagesFromActual() {
            this.search.fields.languages.reset();
        }

        resetSkillsFromActual() {
            this.search.fields.skills.reset();
        }

        resetEmploymentTypeFromActual() {
            this.search.fields.employmentType.value = null;
            this.search.fields.employmentType.state.isSelected = false;
        }

        resetCityFromActual() {
            this.search.fields.city.value = null;
        }

        resetCountryFromActual() {
            this.search.fields.country.value = null;
        }

        resetExperienceFromActual() {
            this.search.fields.experience.state.isSelected = true;
            this.search.fields.experience.value = null;
        }

        resetSalaryFromActual() {
            this.search.fields.salaryTo.state.isSelected = false;
            this.search.fields.salaryTo.value = null;
        }

        resetRoleFromActual() {
            this.search.fields.roleLevels.state.isSelected = false;
            this.search.fields.roleLevels.value = null;
        }

        resetSingleResponsibleValue(responsibleId) {
            this.search.fields.responsible.selectedResponsibles =
                this.search.fields.responsible.selectedResponsibles.filter(
                    (item) => item.value.userId !== responsibleId,
                );
        }

        resetSingleGdprStatus(status) {
            this.search.fields.gdpr.selectedStatuses = this.search.fields.gdpr.selectedStatuses.filter(
                (gdpr) => gdpr.value !== status,
            );
        }

        resetSingleCandidateState(state) {
            this.search.fields.state.value = this.search.fields.state.value.filter((item) => item.value !== state);
        }

        /**
         * @param {string} tagId
         * @return {candidateGroupsOptions[]} selectedTags
         */
        resetSingleTag(tagId) {
            this.search.fields.candidateGroup.value = this.search.fields.candidateGroup.value.filter(
                ({ candidateGroupId }) => candidateGroupId !== tagId,
            );

            this.$rootScope.$$phase || this.$scope.$apply();
        }

        toggleTagsNoValue() {
            this.search.fields.candidateGroup.value = [];
            this.$rootScope.searchNullValues ??= [];

            if (!this.$rootScope.searchNullValues.includes('candidateGroup')) {
                this.$rootScope.searchNullValues.push('candidateGroup');
            } else {
                this.$rootScope.searchNullValues = this.$rootScope.searchNullValues.filter(
                    (name) => name !== 'candidateGroup',
                );
            }

            this.$rootScope.$$phase || this.$scope.$apply();
        }

        resetSingleRoleLevels(level) {
            this.search.fields.roleLevels.value = this.search.fields.roleLevels.value.filter((item) => item !== level);
        }

        resetSingleStage(index) {
            this.search.fields.stages.value.splice(index, 1);
        }

        _setActiveScopeParams() {
            this.activeScopeParam = this.scopeService.getActiveScopeObject();
        }

        _initModel() {
            this.model = {
                criteria: {
                    personal: true,
                    professional: true,
                    other: false,
                    customFields: true,
                },
                isDataReceived: false,
                previousValues: {},
            };
        }

        _initSearch() {
            this.search = new this.candidateSearchService();

            this.search.addField({
                name: 'boolean',
                placeholder: 'Search by candidates',
            });
            this.search.addField({
                name: 'name',
                type: 'personal',
                placeholder: 'first_name',
            });
            this.search.addField({
                name: 'surname',
                type: 'personal',
                placeholder: 'last_name',
            });
            this.search.addField({
                name: 'country',
                type: 'personal',
                data: this.countries,
                placeholder: 'country',
                path: { label: 'value.showName', value: 'value.value' },
            });
            this.search.addField({
                name: 'city',
                type: 'personal',
                data: this.filteredCities,
                placeholder: 'city',
                path: { label: 'value.showName', value: 'value.value' },
            });
            this.search.addField({
                name: 'sex',
                type: 'personal',
                placeholder: 'sex',
                path: { label: 'value.sex', value: 'value.value' },
            });
            this.search.addField({
                name: 'date',
                type: 'personal',
                placeholder: 'Age',
            });
            this.search.addField({
                name: 'withPersonalContacts',
                type: 'personal',
                placeholder: 'Contacts',
                path: { label: 'value.label', value: 'value.value' },
            });
            this.search.addField({
                name: 'state',
                type: 'personal',
                placeholder: 'status',
                path: { label: 'value.name', value: 'value.value' },
            });
            this.search.addField({
                name: 'position',
                type: 'professional',
                placeholder: 'position',
            });
            this.search.addField({
                name: 'roleLevels',
                type: 'professional',
                placeholder: 'Level',
            });
            this.search.addField({
                name: 'scorecards',
                type: 'professional',
                placeholder: 'Score cards',
            });
            this.search.addField({
                name: 'experience',
                type: 'professional',
                placeholder: 'experience',
                path: { label: 'value.value', value: 'value.value' },
            });
            this.search.addField({
                name: 'languages',
                type: 'professional',
                placeholder: 'languages',
            });
            this.search.addField({
                name: 'skills',
                type: 'professional',
                placeholder: 'skills',
            });
            this.search.addField({
                name: 'salaryTo',
                type: 'professional',
                placeholder: 'salary_before',
            });
            this.search.addField({
                name: 'industry',
                type: 'professional',
                placeholder: 'industry',
            });
            this.search.addField({
                name: 'employmentType',
                type: 'professional',
                placeholder: 'employment_type',
                path: { label: 'value.value', value: 'value.value' },
            });
            this.search.addField({
                name: 'currentWorkingPlace',
                type: 'professional',
                placeholder: 'currentWorkingPlace',
            });
            this.search.addField({
                name: 'gdpr',
                type: 'other',
                placeholder: 'Status Gdpr',
            });
            this.search.addField({
                name: 'origin',
                type: 'other',
                placeholder: 'Source',
            });
            this.search.addField({
                name: 'responsible',
                type: 'other',
                placeholder: 'responsible',
                // path: { label: 'value.fullName', value: 'value.value' },
            });
            this.search.addField({
                name: 'candidateGroup',
                type: 'other',
                placeholder: 'candidate tags',
            });
            this.search.addField({ name: 'matchType' });
            this.search.addField({
                name: 'comment',
                type: 'other',
                placeholder: 'comment',
            });
            this.search.addField({
                name: 'vacancyId',
                type: 'other',
                value: '',
                placeholder: 'participation_in_vacancies',
            });
            this.search.addField({
                name: 'activity',
                type: 'other',
                placeholder: 'activity',
            });
            this.search.addField({
                name: 'addedToDb',
                type: 'other',
                placeholder: 'addedToDb',
            });
            this.search.addField({
                name: 'stages',
                type: 'other',
                placeholder: 'search_by_stage',
            });
            this.search.addField({ name: 'dateDmTo' });
            this.search.addField({ name: 'dateDmFrom', placeholder: 'Last activity by candidate' });
            this.search.addField({ name: 'dateDcTo' });
            this.search.addField({ name: 'dateDcFrom', placeholder: 'Date added to the database of the candidate' });
        }

        searchOnEnter(e) {
            if (e.keyCode === 13) this.triggerSearch();
        }

        resetPositionField() {
            this.$rootScope.resetPositionField = true;
            this.search.fields.position.value = null;
        }

        resetEmpTypeField() {
            this.$rootScope.resetEmpTypeField = true;
            this.search.fields.employmentType.value = null;
        }

        redirectedFromVacancy() {
            if (!this.$rootScope.redirectToSearch) return;
            //set position
            if (this.$rootScope.vacancyPositionParam && !this.$rootScope.resetPositionField) {
                this.search.fields.position.state.isSelected = true;
                this.search.fields.position.value = this.$rootScope.vacancyPositionParam;
            } else {
                this.search.fields.position.value = null;
            }
            //set employment type
            if (this.$rootScope.employmentTypeParam && !this.$rootScope.resetEmpTypeField) {
                this.search.fields.employmentType.state.isSelected = true;
                this.search.fields.employmentType.value = this.$rootScope.employmentTypeParam;
            } else {
                this.search.fields.employmentType.value = null;
                this.search.fields.employmentType.state.isSelected = false;
            }
            // set experience
            if (this.$rootScope.experienceParam && !this.$rootScope.resetExperience) {
                this.search.fields.experience.state.isSelected = true;
                this.search.fields.experience.value = this.$rootScope.experienceParam;
            } else {
                this.search.fields.experience.value = null;
            }
            // set location
            if (this.$rootScope.regionParam && !this.$rootScope.resetLocation) {
                this.search.fields.city.value = this.$rootScope.regionParam.city;
                this.search.fields.country.value = [this.$rootScope.regionParam.country];
            } else {
                this.search.fields.city.value = null;
                this.search.fields.country.value = null;
            }
            // set salary
            if (this.$rootScope.salaryParam && !this.$rootScope.resetSalary) {
                this.search.fields.salaryTo.state.isSelected = true;
                this.search.fields.salaryTo.value = this.$rootScope.salaryParam;
            } else {
                this.search.fields.salaryTo.value = null;
            }
            // set skills
            if (this.$rootScope.skillsParam && !this.$rootScope.resetSkills) {
                this.search.fields.skills.state.isSelected = true;
                this.search.fields.skills.value = this.$rootScope.skillsParam;
                this.$rootScope.searchSkills = this.$rootScope.skillsParam;
                // this.search.fields.skills.level.type = this.$rootScope.skillsParam[0].type;
            } else {
                this.search.fields.skills.value = null;
                this.search.fields.skills.level = null;
            }
            //set languages
            if (this.$rootScope.languagesParam && !this.$rootScope.resetLanguages) {
                this.search.fields.languages.state.isSelected = true;
                this.search.fields.languages['value'] = this.$rootScope.languagesParam;
                this.search.fields.languages['appliedLanguages'] = this.search.fields.languages.getValue();
            } else {
                this.search.fields.languages.value = null;
                this.search.fields.languages.appliedLanguages = null;
                this.search.fields.languages.state.isSelected = false;
            }

            // set role
            if (this.$rootScope.roleLevelsParam && !this.$rootScope.resetRoleLevels) {
                this.search.fields.roleLevels.state.isSelected = true;
                if (this.$rootScope.roleLevelsParam && this.$rootScope.roleLevelsParam.label) {
                    this.search.fields.roleLevels.value = [
                        { value: this.$rootScope.roleLevelsParam.label, label: this.$rootScope.roleLevelsParam.label },
                    ];
                } else {
                    this.search.fields.roleLevels.value = [
                        { value: this.$rootScope.roleLevelsParam, label: this.$rootScope.roleLevelsParam },
                    ];
                }
            }
        }

        setParamsForActualCandidates() {
            if (!this.$rootScope.isSearchActualCandidates) return;

            //set position
            if (this.$rootScope.vacancyPositionParam) {
                this.search.fields.position.state.isSelected = true;
                this.search.fields.position.value = this.$rootScope.vacancyPositionParam;
            } else {
                this.search.fields.position.value = null;
            }
            //set employment type
            if (this.$rootScope.employmentTypeParam) {
                this.search.fields.employmentType.state.isSelected = true;
                this.search.fields.employmentType.value = this.$rootScope.employmentTypeParam;
            } else {
                this.search.fields.employmentType.value = null;
                this.search.fields.employmentType.state.isSelected = false;
            }
            // set experience
            if (this.$rootScope.experienceParam) {
                this.search.fields.experience.state.isSelected = true;
                this.search.fields.experience.value = this.$rootScope.experienceParam;
            } else {
                this.search.fields.experience.value = null;
            }
            // set location
            if (this.$rootScope.regionParam) {
                this.search.fields.city.value = this.$rootScope.regionParam.city;
                this.search.fields.country.value = [this.$rootScope.regionParam.country];
            } else {
                this.search.fields.city.value = null;
                this.search.fields.country.value = null;
            }
            // set salary
            if (this.$rootScope.salaryParam) {
                this.search.fields.salaryTo.state.isSelected = true;
                this.search.fields.salaryTo.value = this.$rootScope.salaryParam;
            } else {
                this.search.fields.salaryTo.value = null;
            }
            // set skills
            if (this.$rootScope.skillsParam) {
                this.search.fields.skills.state.isSelected = true;
                this.search.fields.skills.value = this.$rootScope.skillsParam;
                // this.$rootScope.searchSkills = this.$rootScope.skillsParam;
            } else {
                this.search.fields.skills.value = null;
                this.search.fields.skills.level = null;
            }
            //set languages
            if (this.$rootScope.languagesParam) {
                this.search.fields.languages.state.isSelected = true;
                this.search.fields.languages['value'] = this.$rootScope.languagesParam;
                this.search.fields.languages['appliedLanguages'] = this.search.fields.languages.getValue();
            } else {
                this.search.fields.languages.value = null;
                this.search.fields.languages.appliedLanguages = null;
                this.search.fields.languages.state.isSelected = false;
            }

            if (this.$rootScope.roleLevelsParam) {
                this.search.fields.roleLevels.state.isSelected = true;
                this.search.fields.roleLevels.value = [
                    { value: this.$rootScope.roleLevelsParam, label: this.$rootScope.roleLevelsParam },
                ];
            }
        }

        _setSearchParams() {
            this._setActiveScopeParams();
            let country = this.activeScopeParam.value
                ? this.activeScopeParam.value.value
                : this.search.fields.country.getValue();

            if (this.$rootScope.searchFromAdvice && !this.$rootScope.candidateAdvSearchDTO.region) {
                country = null;
            }

            this.redirectedFromVacancy();
            this.setParamsForActualCandidates();
            this.candidateService.setSearchParamsMethod(
                country,
                this.search,
                this.activeScopeParam,
                this.customFieldsSearch,
            );
            this.model.previousValues = null;
        }

        clearTags() {
            this.search.fields.candidateGroup.selectedTags = [];
            this.search.fields.candidateGroup.selectedTagsIds = [];
            // this.$rootScope.tagsAdvancedModel = [];
            // this.$rootScope.isAllSelected = false;
            // this.$rootScope.disableNoValue = true;
            this.$rootScope.searchNullValues = this.$rootScope.searchNullValues?.filter(
                (item) => item !== 'candidateGroupIds',
            );
        }

        removeSkillField = (index) => {
            this.search.fields.skills.value.splice(index, 1);
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        removeLanguagesField = (index) => {
            this.search.fields.languages.value.splice(index, 1);
            this.search.fields?.languages?.appliedLanguages?.splice(index, 1);
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        clearSalary() {
            this.search.toggleFieldActiveState({ name: 'salaryTo' });
            this.$rootScope.searchNullValues = this.$rootScope.searchNullValues.filter((item) => item !== 'salary');
        }

        clearCurrentWorkingPlace() {
            this.search.toggleFieldActiveState({ name: 'currentWorkingPlace' });
            this.$rootScope.searchNullValues = this.$rootScope.searchNullValues.filter(
                (item) => item !== 'currentWorkingPlace',
            );
        }

        _getSearch() {
            if (this.candidateService.search.searchValues) {
                this.isTriggerSearch = true;
                for (let searchParams in this.candidateService.search.searchValues) {
                    if (this.candidateService.search.searchValues.hasOwnProperty(searchParams)) {
                        this.search.fields[searchParams] = angular.copy(
                            this.candidateService.search.searchValues[searchParams],
                        );
                    }
                }
            }
            this.isTriggerSearchAll =
                (this.isTriggerSearch && this.search.isAnyFilledField()) ||
                this.$rootScope.scopeActiveObject.name === 'region' ||
                this.$rootScope.scopeActiveObject.name === 'onlyMy' ||
                this.search.searchFullTextType;
        }

        _resetCustomFields() {
            Object.entries(this.customFieldsSearch.fields).forEach(([name, field]) => {
                if (field.type === 'date' || field.type === 'datetime') {
                    field.state.isActive = true;
                }
                field.reset({ name });
            });
        }

        setEmploymentTypeAutocompleterPlaceholderValue() {
            if (this.$rootScope.searchNullValues) {
                this.$rootScope.searchNullValues = this.$rootScope.searchNullValues.filter(
                    (item) => item !== 'employmentType',
                );
            }
        }

        resetActualSearch() {
            this.search.fields.position.value = null;
            this.search.fields.skills.value = null;
            this.search.fields.skills.level = null;
            this.search.fields.salaryTo.value = null;
            this.search.fields.city.value = null;
            this.search.fields.country.value = null;
            this.search.fields.experience.value = null;
            this.search.fields.employmentType.value = null;
            this.search.fields.employmentType.state.isSelected = false;
        }

        resetFields(autocompletes) {
            this.resetActualSearch();
            this.$rootScope.isSearchActualCandidates = false;
            this.$rootScope.searchFromAdvice = false;
            autocompletes ? this.resetFieldsByType(autocompletes) : this.resetFieldsByType(this.autocompletes);
            // this.$rootScope.searchNullValues = [];
            // this.$rootScope.searchNullCustomFields = [];
            this.search.fields.salaryTo.state.isActive = true;
            this.search.fields.salaryTo.value = '';
            if (this.search.fields.boolean.value) this.search.fields.boolean.value = null;
            if (this.customFieldsSearch) this._resetCustomFields();
            this.isTriggerSearchAll = false;
            this.isTriggerSearch = false;
            this.search.fields.candidateGroup.selectedTags = [];
            this.search.fields.candidateGroup.selectedTagsIds = [];
            this.search.fields.gdpr.selectedStatuses = [];
            this.candidateService.search._params.candidateGroupIds = null;
            this.$rootScope.roleLevelsParam = null;
            this._resetLanguageField();
            this._resetDateField();
            this._resetOriginField();
            this._resetScoreCards();
            this.resetRoleFromActual();
            this._setSearchParams();
            this.tableParams.reload();
            this.candidateService.search.resetPagination = false;
            this.search.fields.currentWorkingPlace.state.isActive = true;

            this.search.resetAllSearchFields();

            if (this.candidateService.search.params.sort === 'dm') {
                this.$rootScope.candidatesSortType = {
                    name: 'Date of last activity',
                    values: 'dm',
                };
            }
        }

        removeSkill(index) {
            if (
                this.$rootScope.searchSkills &&
                this.$rootScope.searchSkills.length === 1 &&
                !this.$rootScope.isReducedNumberOfCandidates
            ) {
                this.$rootScope.searchSkills = [
                    {
                        skill: undefined,
                        experience: 'e00_no_experience',
                        new: false,
                        willPlus: false,
                    },
                ];
                this.search.fields.skills.value = null;
            } else if (
                this.$rootScope.searchSkills &&
                this.$rootScope.searchSkills.length > 1 &&
                !this.$rootScope.isReducedNumberOfCandidates
            ) {
                this.$rootScope.searchSkills = this.$rootScope.searchSkills.filter((item, ind) => ind !== index);
                this.search.fields.skills.value = this.search.fields.skills.value.filter((item, ind) => ind !== index);
            }

            if (this.$rootScope.isReducedNumberOfCandidates && this.$rootScope.skillsParam.length === 1) {
                this.$rootScope.skillsParam = [
                    {
                        skill: undefined,
                        experience: 'e00_no_experience',
                        new: false,
                        willPlus: false,
                    },
                ];
                this.search.fields.skills.value = null;
            } else if (this.$rootScope.isReducedNumberOfCandidates && this.$rootScope.skillsParam.length > 1) {
                this.$rootScope.skillsParam = this.$rootScope.skillsParam.filter((item, ind) => ind !== index);
                this.search.fields.skills.value = this.search.fields.skills.value.filter((item, ind) => ind !== index);
            }

            if (
                !this.$rootScope.redirectToSearch &&
                this.$rootScope.skillsParam &&
                this.$rootScope.skillsParam.length === 1
            ) {
                this.$rootScope.skillsParam = [
                    {
                        skill: undefined,
                        experience: 'e00_no_experience',
                        new: false,
                        willPlus: false,
                    },
                ];
                this.search.fields.skills.value = null;
            } else if (
                !this.$rootScope.redirectToSearch &&
                this.$rootScope.skillsParam &&
                this.$rootScope.skillsParam.length > 1
            ) {
                this.$rootScope.skillsParam = this.$rootScope.skillsParam.filter((item, ind) => ind !== index);
                this.search.fields.skills.value = this.search.fields.skills.value.filter((item, ind) => ind !== index);
            }

            this.tableParams.reload();
            this.$scope.$$phase || this.$rootScope.$$phase || this.$scope.$apply();
        }

        checkFieldVisibility(field) {
            if (field.name === 'state') {
                return !!field.value?.length;
            }
            if (field.name === 'skills') {
                return field.value[0]?.experience !== 'e00_no_experience';
            }
            return true;
        }

        removeAllSkills() {
            this.$rootScope.searchSkills = [
                {
                    skill: undefined,
                    experience: 'e00_no_experience',
                    new: false,
                    willPlus: false,
                },
            ];
            this.$rootScope.skillsParam = null;
            this.search.fields.skills.value = null;
            // this.tableParams.reload();
        }

        removeAllLanguages() {
            if (this.search.fields.languages && this.search.fields.languages.selectedLanguages) {
                this.search.fields.languages.selectedLanguages = null;
            } else {
                return;
            }
            // this.parentController.tableParams.reload();
        }

        removeCountry = (index) => {
            this.search.fields.country.value = this.search.fields.country.value.filter((_, i) => i !== index);
            if (!this.search.fields.country.value.length) this.search.fields.country.value = null;
            this.$rootScope.regionParam = null;
            this.$rootScope.$$phase || this.$scope.$apply();
        };

        resetFieldsByType(autocompletes = this.autocompletes) {
            if (!autocompletes) return;
            Object.entries(this.search.fields).forEach(([name, field]) => {
                if (field.value || name === 'languages' || name === 'candidateGroup') {
                    switch (name) {
                        case 'origin':
                            field.reset({
                                callback: autocompletes.setOriginAutocompleterPlaceholderValue,
                            });
                            break;
                        case 'industry':
                            field.reset({
                                callback: autocompletes.setIndustryAutocompleterPlaceholderValue,
                            });
                            break;
                        case 'position':
                            field.reset({
                                callback: autocompletes.setPositionAutocompleterPlaceholderValue,
                            });
                            break;
                        case 'skills':
                            field.reset({
                                callback: autocompletes.setSkillAutocompleterPlaceholderValue,
                            });
                            break;
                        case 'candidateGroup':
                            field.reset({ callback: autocompletes.clearTags });
                            break;
                        case 'languages':
                            if (this.search.fields.languages.selectedLanguages === null) {
                                field.reset({});
                                break;
                            } else {
                                let i = this.search.fields.languages.selectedLanguages.length - 1;
                                while (i >= 0) {
                                    this.search.fields.languages.removeField(i);
                                    i--;
                                }
                                field.reset({});
                                break;
                            }
                        default:
                            field.reset({});
                    }
                }
            });
        }

        resetCustomField(field) {
            field.state.isActive = true;
            if (this.$rootScope.searchNullCustomFields && this.$rootScope.searchNullValues) {
                this.$rootScope.searchNullCustomFields = this.$rootScope.searchNullCustomFields.filter(
                    (item) => item !== field.placeholder,
                );
                if (this.$rootScope.searchNullCustomFields.length === 0)
                    this.$rootScope.searchNullValues = this.$rootScope.searchNullValues.filter(
                        (item) => item !== 'customFields',
                    );
            }
        }

        _initEmitEventAdvancedSearch() {
            this.$scope.$on('openAdvancedSearch', () => {
                this.isToggleAdvancedSearch = true;
            });
        }

        _resetOriginField() {
            this.search.fields.origin.value = null;
        }

        _resetScoreCards() {
            this.$rootScope.dropdownScoreCardId = null;
            this.search.fields.scorecards.value = '';
            if (this.candidateService.search._params.sort === 'scoreCard') {
                this.candidateService.search._params.sort = 'dm';
                this.$rootScope.changeFilterToDefault('dm');
            }
        }

        _resetDateField() {
            this.search.fields.date.dateFrom.value = null;
            this.search.fields.date.dateTo.value = null;
        }

        _resetLanguageField(index) {
            if (
                this.search.fields &&
                this.search.fields.languages &&
                this.search.fields.languages.fields &&
                this.search.fields.languages.selectedLanguages
            ) {
                this.search.fields.languages.fields.splice(index, 1);
                this.search.fields.languages.selectedLanguages.splice(index, 1);
            }
        }

        _isAnyFilledCustomField() {
            return (
                this.customFieldsSearch &&
                this.customFieldsSearch.fields &&
                Object.entries(this.customFieldsSearch.fields).some(([name, field]) => {
                    return field.getLabelValue();
                })
            );
        }

        // resetVacancyId() {
        //     this.$rootScope.sendVacancyId = null;
        //     this.search.source = null;
        //     this.candidateService.search.source = null;
        //     this.search.fields.vacancyId.value = null;
        //     this.$rootScope.VacancyAddedInCandidate = null;
        //     this.$rootScope.vacancyValue = null;
        //     this.$rootScope.searchFromAdvice = false;
        //     this.$rootScope.actualVacancy = false;
        //     $('#candidatesAddToVacancy').select2('val', '');
        // }

        triggerSearch(index) {
            this._setSearchParams();
            if (this.tableParams && this.tableParams.reload()) {
                this.tableParams.reload();
            }
            this.candidateService.search.resetPagination = false;

            if (this.search.isAnyFilledField()) {
                this.search.source = 'advancedSource';
                this.candidateService.search.source = 'advancedSource';
            } else {
                this.search.source = null;
                this.candidateService.search.source = null;
            }

            this.isTriggerSearch = true;
            this.isTriggerSearchAll =
                (this.isTriggerSearch && this.search.isAnyFilledField()) ||
                this.$rootScope.scopeActiveObject.name === 'region' ||
                this.$rootScope.scopeActiveObject.name === 'onlyMy' ||
                this.search.searchFullTextType;
        }

        toggleAdvancedSearch() {
            this.isToggleAdvancedSearch = true;
        }
    },
    controllerAs: 'vm',
});
