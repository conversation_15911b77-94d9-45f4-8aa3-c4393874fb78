// integrated - integrated with external service
// isIntegrated - published vacancy

class rabotaUAPostingComponentCtrl {
    constructor(
        $uibModal,
        rabotaUaIntegrationService,
        $translate,
        $scope,
        notificationService,
        Vacancy,
        $location,
        $rootScope,
        $state,
        $localStorage,
        integrationPageService,
        $timeout,
        Candidate,
    ) {
        this.$uibModal = $uibModal;
        this.rabotaUaIntegrationService = rabotaUaIntegrationService;
        this.$translate = $translate;
        this.$scope = $scope;
        this.notificationService = notificationService;
        this.vacancyService = Vacancy;
        this.$location = $location;
        this.$rootScope = $rootScope;
        this.$state = $state;
        this.$localStorage = $localStorage;
        this.integrationPageService = integrationPageService;
        this.$timeout = $timeout;
        this.candidateService = Candidate;
        this.cityKey = 'nameEng';
        self = this;
    }

    connectVacancyHandler(self) {
        this.integrated ? this.connectVacancy(self) : this.integrateRabota(self);
    }

    integrateRabota(self) {
        class AuthController {
            constructor(
                parent,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                rabotaUaIntegrationService,
                integrationPageService,
                $location,
                $rootScope,
                $state,
                $filter,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.integrationPageService = integrationPageService;
                this.$location = $location;
                this.$rootScope = $rootScope;
                this.$state = $state;
                this.$filter = $filter;
                this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                self = this;
            }

            onChangeLogin = (value) => {
                this.rabota_login = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };
            onChangePass = (value) => {
                this.rabota_pass = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };
            authRabotaUA() {
                self.$rootScope.loading = true;
                this.rabotaUaIntegrationService.onIntegrateAccount(this.rabota_login, this.rabota_pass).then(
                    (resp) => {
                        this.closeModal();
                        self.$rootScope.loading = false;
                    },
                    (err) => {
                        this.notificationService.error(
                            this.$filter('translate')(
                                'Failed to enable integration: please check your email and password',
                            ),
                        );
                        this.closeModal();
                        self.$rootScope.loading = false;
                    },
                );
            }
            closeModal() {
                this.$uibModalInstance.close();
            }
        }

        const modalInstance = this.$uibModal.open({
            templateUrl: 'partials/modal/jobSitesIntegration/rabotaUA/authorization-rabota.html',
            windowClass: 'auth-popup-rabota-ua',
            controller: [
                'parent',
                '$uibModalInstance',
                '$translate',
                '$scope',
                '$uibModal',
                'notificationService',
                'rabotaUaIntegrationService',
                'integrationPageService',
                '$location',
                '$rootScope',
                '$state',
                '$filter',
                AuthController,
            ],
            controllerAs: 'vm',
            resolve: {
                parent: () => {
                    return self.parent;
                },
                $translate: () => {
                    return self.$translate;
                },
                $uibModal: () => {
                    return self.$uibModal;
                },
                notificationService: () => {
                    return self.notificationService;
                },
                rabotaUaIntegrationService: () => {
                    return self.rabotaUaIntegrationService;
                },
                integrationPageService: () => {
                    return self.integrationPageService;
                },
                $location: () => {
                    return self.$location;
                },
                $rootScope: () => {
                    return self.$rootScope;
                },
                $state: () => {
                    return self.$state;
                },
            },
        });
        modalInstance.closed.then(() => {
            this.integrationPageService
                .checkIntegration()
                .then((resp) => {
                    self.parent.integrationPayload = resp.object;
                    if (resp.object.rabotaua) this.$localStorage.set('rabotaIntegrated', 'rabotaUAEnabled');
                })
                .catch((err) => this.notificationService.error(err.message))
                .finally(() => this.$scope.$apply());
        });
    }

    integrateRabotaUA(parentScope, edit) {
        this.editPublication = edit;

        class publishRabotaUAController {
            constructor(
                parent,
                exportedData,
                VueData,
                editPublication,
                $uibModalInstance,
                $translate,
                $scope,
                $uibModal,
                notificationService,
                rabotaUaIntegrationService,
                Vacancy,
                $location,
                $rootScope,
                $state,
                Candidate,
            ) {
                this.parent = parent;
                this.exportedData = exportedData;
                this.VueData = VueData;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$scope = $scope;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.vacancyService = Vacancy;
                this.$location = $location;
                this.$rootScope = $rootScope;
                this.$state = $state;
                this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                this.editPublication = editPublication;
                this.candidateService = Candidate;
                self = this;
            }

            setActualDataFromRabotaUa(responce) {
                //set actual name from Rabota.ua vacancy
                this.exportedData.name = responce.vacancyName;

                //set actual city from Rabota.ua vacancy
                this.VueData.nameCityModel = { name: responce.cityRu, id: responce.cityId };
                this.exportedData.cityId = responce.cityId;

                //set actual jobtype from Rabota.ua vacancy
                this.exportedData.scheduleId = responce.scheduleId;

                this.rabotaUaIntegrationService.getJobTypesForRabotaUa().forEach((item) => {
                    if (item.id === +responce.scheduleId) {
                        this.VueData.jobtypeModel = { value: item.name, id: item.id };
                    }
                });

                //set actual Publish type
                this.VueData.publishTypeModel = { value: responce.publishType };
                this.exportedData.publishType = responce.publishType;

                //set actual category from Rabota.ua vacancy
                this.exportedData.rubricList = responce.rubricList.map((rubricObj) => rubricObj.parentRubricId);
                this.VueData.nameCategoryModel = responce.rubricList.map((rubricObj) => rubricObj.rubricName);

                //set actual email from Rabota.ua vacancy
                this.exportedData.contactEmail = responce.contactEmail;

                //set actual description from Rabota.ua vacancy
                this.exportedData.description = responce.description;

                //set actual phone from Rabota.ua vacancy
                this.exportedData.contactPhone = responce.contactPhone;

                //set actual contact Name from Rabota.ua vacancy
                this.exportedData.contactPerson = responce.contactPerson;

                //set actual address Name from Rabota.ua vacancy
                this.exportedData.address = responce.vacancyAddress;

                //set actual languages from Rabota.ua vacancy
                if (responce.languages) {
                    this.VueData.storageForLanguage = responce.languages.map((language) => {
                        return {
                            name: {
                                name:
                                    this.$rootScope.currentLang === 'en'
                                        ? language.nameEng
                                        : this.$rootScope.currentLang === 'ru'
                                        ? language.name
                                        : language.nameUkr,
                            },
                            level: { name: language.level },
                            languageId: language.languageId,
                            skillId: language.skillId,
                            valid: true,
                        };
                    });
                } else {
                    this.VueData.storageForLanguage = [];
                }

                this.VueData.storageForLanguage.push({
                    name: null,
                    level: null,
                    languageId: null,
                    skillId: null,
                    valid: true,
                });

                //set actual salary Name from Rabota.ua vacancy
                this.exportedData.salary = responce.salary;
                this.exportedData.salaryFrom = responce.salaryFrom;
                this.exportedData.salaryTo = responce.salaryTo;

                //set actual salary description Name from Rabota.ua vacancy
                this.exportedData.salaryDescr = responce.salaryDescr;

                //set id from rabota ua (only for edit request)
                this.exportedData.id = responce.vacancyId;
                this.$scope.$apply();
            }

            onChangePosition = (value) => {
                this.exportedData.name = value;
                if (value.length > 0) this.VueData.validName = true;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            onChangeEmail = (value) => {
                this.exportedData.contactEmail = value;
                if (value.length > 0) this.VueData.validContactEmail = true;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            setVacancyDataFromRabota() {
                this.rabotaUaIntegrationService.onGetVacancyForEdit(this.parent.vacancy.vacancyId).then((resp) => {
                    this.setActualDataFromRabotaUa(resp.object);
                });
            }

            calculateDescriptionLength() {
                if (typeof this.exportedData.description === 'undefined') {
                    return 150;
                } else {
                    let descriptionLength = 150 - this.exportedData.description.length;
                    return descriptionLength < 0 ? 0 : descriptionLength;
                }
            }

            // validate functions
            validateLevelOfLanguage() {
                this.VueData.storageForLanguage.forEach((item) => {
                    item.languageId && !item.skillId ? (item.valid = false) : (item.valid = true);
                });

                return this.VueData.storageForLanguage.every((item) => item.valid === true);
            }

            validateNameField() {
                this.VueData.validName = this.exportedData.name.length > 0 && this.exportedData.name.length <= 255;
            }

            validateJobtypeField() {
                this.VueData.validJobtype = !!this.VueData.jobtypeModel;
            }

            validatePublishType() {
                this.VueData.validPublishType = !!this.VueData.publishTypeModel;
            }

            validateCitiField() {
                this.VueData.validCity = !!this.VueData.nameCityModel;
            }

            validateCategory() {
                this.VueData.validCategory = this.VueData.nameCategoryModel.length > 0;
            }

            validateEmailField() {
                const pattern = /[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}/;
                this.VueData.validContactEmail = !!(
                    pattern.test(this.exportedData.contactEmail) && this.exportedData.contactEmail
                );
            }

            validateDescription() {
                this.VueData.validDescription = !!(
                    this.exportedData.description && this.exportedData.description.length >= 150
                );
            }

            validateContactPerson() {
                const pattern = /^[A-Za-zА-Яа-яёЁІіЇїЄєҐґ’'`\- ]{0,50}$/;
                this.VueData.validContactPerson = pattern.test(this.exportedData.contactPerson);
            }

            validateContactPhone() {
                this.VueData.validContactPhone =
                    !(
                        this.exportedData.contactPhone &&
                        this.exportedData.contactPhone.length > 0 &&
                        this.exportedData.contactPhone.length < 8
                    ) || this.exportedData.contactPhone.length > 20;
            }

            replaceSalaryChars = (value) => {
                this.exportedData.salary = value.replace(/[^+0-9]/g, '');
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            /**
             * @param {number} salaryFrom
             * @param {number} salaryTo
             * @returns {boolean}
             */
            validateSalary = (salaryFrom, salaryTo) => {
                if (!salaryFrom || !salaryTo) {
                    return (this.VueData.validSalary = false);
                } else if (parseInt(salaryFrom) > parseInt(salaryTo)) {
                    return (this.VueData.validSalary = false);
                }

                return (this.VueData.validSalary = true);
            };

            numberOnlyInputHandler = (value) => {
                this.validateSalary(parseInt(this.exportedData.salaryFrom), parseInt(this.exportedData.salaryTo));

                if (parseInt(value) <= 0) {
                    return (value = 0);
                }

                if (!value.match(/^[0-9]+$/)) {
                    return parseInt(value.replace(/[^0-9]/g, ''));
                }

                return parseInt(value);
            };

            onChangeCurrencyComment = (value) => {
                this.exportedData.salaryDescr = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            replacePhoneChars = (value) => {
                this.exportedData.contactPhone = value.replace(/[^+0-9]/g, '');
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            onChangeContactName = (value) => {
                this.exportedData.contactPerson = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            onChangeAddress = (value) => {
                this.exportedData.address = value;
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            switchAdditionalInfo() {
                this.VueData.showAdditional = !this.VueData.showAdditional;
            }

            //functions for selected items
            selectJobType(opt) {
                this.VueData.jobtypeModel = opt;
                this.exportedData.scheduleId = opt.id;
                this.VueData.validJobtype = true;
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            selectPublishType(opt) {
                this.VueData.publishTypeModel = opt;
                this.exportedData.publishType = opt.value;
                this.VueData.validPublishType = true;
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            selectCategory(opt, e) {
                if (e.target.checked && this.exportedData.rubricList.length < 3) {
                    if (
                        this.exportedData.rubricList.indexOf(
                            this.VueData.categorysId[this.VueData.nameCategoryOptions.indexOf(opt)],
                        ) === -1
                    ) {
                        this.exportedData.rubricList.push(
                            this.VueData.categorysId[this.VueData.nameCategoryOptions.indexOf(opt)],
                        );
                    }
                } else if (!e.target.checked) {
                    let del = this.VueData.categorysId[this.VueData.nameCategoryOptions.indexOf(opt)];
                    this.exportedData.rubricList.splice(this.exportedData.rubricList.indexOf(del), 1);
                }
            }

            selectLanguage = (opt, index) => {
                this.VueData.storageForLanguage[index].name = opt;
                this.VueData.storageForLanguage[index].languageId = opt.id;

                if (this.VueData.storageForLanguage[this.VueData.storageForLanguage.length - 1].name !== null) {
                    this.VueData.storageForLanguage.push({
                        name: null,
                        level: null,
                        languageId: null,
                        skillId: null,
                        valid: true,
                    });
                }
                this.$rootScope.$$phase || this.$scope.$apply();
            };

            selectLanguageLevel(opt, index) {
                this.VueData.storageForLanguage[index].level = opt;
                this.VueData.storageForLanguage[index].skillId = opt.id;
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            removeLanguageObject(index) {
                this.VueData.storageForLanguage.splice(index, 1);
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            selectCity(data) {
                this.VueData.nameCityModel = data;
                this.exportedData.cityId = data.id;
                this.VueData.validCity = true;
                this.$rootScope.$$phase || this.$scope.$apply();
            }

            compareVacancyFieldsWithPopUp() {
                if (
                    this.parent.vacancy.region &&
                    this.parent.vacancy.region.googlePlaceId &&
                    this.parent.vacancy.region.googlePlaceId.googlePlaceId
                ) {
                    this.rabotaUaIntegrationService
                        .onGetVacancyFields(
                            this.parent.vacancy.region.googlePlaceId.googlePlaceId,
                            this.parent.vacancy.languages,
                        )
                        .then((resp) => {
                            if (
                                this.exportedData.description !== this.parent.vacancy.descr ||
                                this.exportedData.name !== this.parent.vacancy.position ||
                                this.exportedData.scheduleId !=
                                    this.VueData.jobtypeId[
                                        this.VueData.jobtypeOptions.indexOf(
                                            this.$translate.instant(this.parent.vacancy.employmentType),
                                        )
                                    ] ||
                                this.exportedData.cityId !== resp.object.city.cityId
                            ) {
                                this.approveChanges();
                            }
                        });
                } else {
                    if (
                        this.exportedData.description !== this.parent.vacancy.descr ||
                        this.exportedData.name !== this.parent.vacancy.position ||
                        this.exportedData.scheduleId !=
                            this.VueData.jobtypeId[
                                this.VueData.jobtypeOptions.indexOf(
                                    this.$translate.instant(this.parent.vacancy.employmentType),
                                )
                            ]
                    ) {
                        this.approveChanges();
                    }
                }
            }

            checkForValidFields() {
                return (
                    this.VueData.validName &&
                    this.VueData.validJobtype &&
                    this.VueData.validCity &&
                    // this.VueData.validCategory &&
                    this.VueData.validContactEmail &&
                    this.VueData.validDescription &&
                    this.VueData.validContactPerson &&
                    this.VueData.validContactPhone &&
                    this.VueData.validSalary &&
                    this.VueData.validateLevelOfLanguage &&
                    this.validateLevelOfLanguage()
                );
            }

            publishVacancy(editVacancy) {
                this.validateNameField();
                this.validateJobtypeField();
                this.validatePublishType();
                this.validateCitiField();
                this.validateCategory();
                this.validateDescription();
                this.validateContactPerson();
                this.validateContactPhone();
                this.validateSalary(this.exportedData.salaryFrom, this.exportedData.salaryTo);

                if (!this.validateLevelOfLanguage()) {
                    this.VueData.showAdditional = true;
                }
                if (this.checkForValidFields()) {
                    if (this.VueData.storageForLanguage[this.VueData.storageForLanguage.length - 1].name === null) {
                        this.exportedData.languageSkills = [...this.VueData.storageForLanguage];
                        this.exportedData.languageSkills.splice(this.exportedData.languageSkills.length - 1, 1);
                    }
                    let objForSending = {
                        ...this.exportedData,
                        languageSkills: this.exportedData.languageSkills.map((item) => {
                            return {
                                languageId: item.languageId,
                                skillId: item.skillId,
                            };
                        }),
                    };

                    if (!editVacancy) {
                        self.$rootScope.loading = true;
                        this.rabotaUaIntegrationService
                            .onPublish(objForSending)
                            .then((resp) => {
                                this.notificationService.success(this.$translate.instant(resp.message));
                                this.closeModal();
                                self.$rootScope.loading = false;
                                this.compareVacancyFieldsWithPopUp();
                                this.$scope.$apply();
                            })
                            .catch((err) => {
                                this.notificationService.error(
                                    this.$translate.instant(
                                        err.message
                                            .replace('Not Published MESSAGE:', '')
                                            .replace('Не опубликовано MESSAGE:', ''),
                                    ),
                                );
                            })
                            .finally(() => {
                                self.$rootScope.loading = false;
                                this.$scope.$apply();
                            });
                    } else {
                        self.$rootScope.loading = true;
                        this.rabotaUaIntegrationService.onEditVacancy(objForSending).then(
                            (resp) => {
                                this.notificationService.success(this.$translate.instant(resp.message));
                                this.closeModal();
                                self.$rootScope.loading = false;
                                this.compareVacancyFieldsWithPopUp();
                                this.$scope.$apply();
                            },
                            (error) => {
                                this.notificationService.error(this.$translate.instant(error.message));
                                self.$rootScope.loading = false;
                                this.$scope.$apply();
                            },
                        );
                    }
                }
            }

            approveChanges() {
                class approveChangesController {
                    constructor(
                        $uibModalInstance,
                        parent,
                        $translate,
                        $uibModal,
                        notificationService,
                        rabotaUaIntegrationService,
                        $rootScope,
                        Vacancy,
                        exportedData,
                        $state,
                    ) {
                        this.$uibModalInstance = $uibModalInstance;
                        this.parent = parent;
                        this.$translate = $translate;
                        this.$uibModal = $uibModal;
                        this.notificationService = notificationService;
                        this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                        this.$rootScope = $rootScope;
                        this.vacancyService = Vacancy;
                        this.exportedData = exportedData;
                        this.$state = $state;

                        debugger;
                    }

                    closeModal() {
                        this.$uibModalInstance.close();
                    }

                    setCustomFields() {
                        if (self.parent.vacancy.customFields) {
                            self.parent.vacancy.fieldValues = [];
                            angular.forEach(self.parent.vacancy.customFields, (val) => {
                                if (val.fieldValue) {
                                    if (angular.equals(val.type, 'string')) {
                                        self.parent.vacancy.fieldValues.push({
                                            objType: 'vacancy',
                                            fieldValueId: val.fieldValue.fieldValueId,
                                            value: val.fieldValue.value,
                                            field: {
                                                fieldId: val.fieldId,
                                            },
                                        });
                                    }
                                    if (angular.equals(val.type, 'select')) {
                                        self.parent.vacancy.fieldValues.push({
                                            objType: 'vacancy',
                                            fieldValueId: val.fieldValue.fieldValueId,
                                            value: val.fieldValue.value,
                                            field: {
                                                fieldId: val.fieldId,
                                            },
                                        });
                                    }
                                    if (angular.equals(val.type, 'date')) {
                                        self.parent.vacancy.fieldValues.push({
                                            objType: 'vacancy',
                                            fieldValueId: val.fieldValue.fieldValueId,
                                            dateTimeValue: val.fieldValue.dateTimeValue,
                                            field: {
                                                fieldId: val.fieldId,
                                            },
                                        });
                                    }
                                    if (angular.equals(val.type, 'datetime')) {
                                        self.parent.vacancy.fieldValues.push({
                                            objType: 'vacancy',
                                            fieldValueId: val.fieldValue.fieldValueId,
                                            dateTimeValue: val.fieldValue.dateTimeValue,
                                            field: {
                                                fieldId: val.fieldId,
                                            },
                                        });
                                    }
                                }
                            });
                        }
                    }

                    save() {
                        self.$rootScope.loading = true;
                        this.setCustomFields();
                        this.rabotaUaIntegrationService.onGetRegionByCityId(self.exportedData.cityId).then((resp) => {
                            const objForEditVacancy = {
                                ...self.parent.vacancy,
                                descr: self.exportedData.description,
                                position: self.exportedData.name,
                                salaryFrom: self.exportedData.salaryFrom,
                                salaryTo: self.exportedData.salaryTo,
                                currency: 'UAH',
                                employmentType: this.rabotaUaIntegrationService
                                    .getJobTypesForRabotaUa()
                                    .filter((jobtype) => jobtype.id === +self.exportedData.scheduleId)
                                    .map((jobtypeObj) => jobtypeObj.name)[0],
                                region: resp.object,
                                regionId: resp.object && resp.object.regionId ? resp.object.regionId : null,
                            };

                            this.vacancyService.onEditVacancy(objForEditVacancy).then(
                                (resp) => {
                                    this.closeModal();
                                    self.$rootScope.loading = false;
                                    this.$rootScope.$emit('eventUpdateVacancyDescriptionData', {});
                                },
                                (error) => {
                                    this.closeModal();
                                    self.$rootScope.loading = false;
                                },
                            );
                        });
                    }
                }

                const modalInstance = self.$uibModal.open({
                    templateUrl: 'partials/modal/jobSitesIntegration/rabotaUA/approveChanges.html',
                    controller: [
                        '$uibModalInstance',
                        'parent',
                        '$translate',
                        '$uibModal',
                        'notificationService',
                        'rabotaUaIntegrationService',
                        '$rootScope',
                        'Vacancy',
                        approveChangesController,
                    ],
                    controllerAs: 'vm',
                    windowClass: 'approveChanges-rabota-popup secondary-modal',
                    resolve: {
                        parent: () => {
                            return self.parent;
                        },
                        $translate: () => {
                            return self.$translate;
                        },
                        $uibModal: () => {
                            return self.$uibModal;
                        },
                        notificationService: () => {
                            return self.notificationService;
                        },
                        rabotaUaIntegrationService: () => {
                            return self.rabotaUaIntegrationService;
                        },
                        $rootScope: () => {
                            return self.$rootScope;
                        },
                        $state: () => {
                            return self.$state;
                        },
                    },
                });
            }

            //function for get values for selector`s option and set prev values from CS vacancy
            setCities(edit) {
                if (!edit) {
                    if (
                        this.parent.vacancy.region &&
                        this.parent.vacancy.region.googlePlaceId &&
                        this.parent.vacancy.region.googlePlaceId.googlePlaceId
                    ) {
                        this.rabotaUaIntegrationService
                            .onGetVacancyFields(
                                this.parent.vacancy.region.googlePlaceId.googlePlaceId,
                                this.parent.vacancy.languages,
                            )
                            .then((resp) => {
                                if (resp.object && resp.object.city) {
                                    this.VueData.nameCityModel =
                                        this.$rootScope.currentLang === 'en'
                                            ? resp.object.city.cityEn
                                            : this.$rootScope.currentLang === 'ru'
                                            ? resp.object.city.cityRu
                                            : resp.object.city.cityUa;
                                    this.exportedData.cityId = resp.object.city.cityId;
                                }
                            });
                    }
                }
                this.rabotaUaIntegrationService.getCityListFromRabotaUa().then(
                    (resp) => {
                        this.VueData.nameCityOptions = resp.data;
                    },
                    (error) => {
                        this.notificationService.error(error.message);
                    },
                );
            }

            setJobTypes(edit) {
                const jobTypes = this.rabotaUaIntegrationService.getJobTypesForRabotaUa();
                this.VueData.jobtypeOptions = jobTypes.map((item) => ({ value: item.name, id: item.id }));
                this.VueData.jobtypeId = jobTypes.map((item) => item.id);

                if (!edit) {
                    jobTypes.forEach((jobType) => {
                        if (this.parent.vacancy.employmentType === jobType.name) {
                            this.exportedData.scheduleId = jobType.id;
                        }
                    });
                }
            }

            setVacancyPublishType(edit) {
                this.VueData.publishTypeOptions = this.rabotaUaIntegrationService.getAllPublishTypes();
            }

            setCategories() {
                this.rabotaUaIntegrationService.getRubricsFromRabotaUa().then((resp) => {
                    if (this.$rootScope.currentLang === 'en') {
                        this.VueData.nameCategoryOptions = resp.data.map((item) => item.rubricNameEng);
                    } else if (this.$rootScope.currentLang === 'ru') {
                        this.VueData.nameCategoryOptions = resp.data.map((item) => item.rubricName);
                    } else {
                        this.VueData.nameCategoryOptions = resp.data.map((item) => item.rubricNameUkr);
                    }
                    self.VueData.categorysId = resp.data.map((item) => item.id);
                });
            }

            setLanguages(edit) {
                this.rabotaUaIntegrationService.getLanguageListFromRabotaUa().then((resp) => {
                    this.VueData.languageOptions = resp.data;
                    this.VueData.languageOptions.forEach((item) => {
                        item.name =
                            this.$rootScope.currentLang === 'en'
                                ? item.nameEng
                                : this.$rootScope.currentLang === 'ru'
                                ? item.name
                                : item.nameUkr;
                    });
                });
                this.rabotaUaIntegrationService.getLanguageSkillListFromRabotaUa().then((resp) => {
                    this.VueData.languageLevelOptions = resp.data;
                    this.VueData.languageLevelOptions.forEach((item) => {
                        item.name =
                            this.$rootScope.currentLang === 'en'
                                ? item.nameEng
                                : this.$rootScope.currentLang === 'ru'
                                ? item.name
                                : item.nameUkr;
                    });
                });
                if (!edit) {
                    // set prev value for languages
                    this.rabotaUaIntegrationService
                        .onGetVacancyFields(null, this.parent.vacancy.languages)
                        .then((resp) => {
                            if (resp.object && resp.object.languages) {
                                resp.object.languages.forEach((language, index) => {
                                    this.VueData.storageForLanguage[index].name = {
                                        name:
                                            this.$rootScope.currentLang === 'en'
                                                ? language.nameEng
                                                : this.$rootScope.currentLang === 'ru'
                                                ? language.name
                                                : language.nameUkr,
                                    };
                                    this.VueData.storageForLanguage[index].level = { name: language.level };
                                    this.VueData.storageForLanguage[index].languageId = language.languageId;
                                    this.VueData.storageForLanguage[index].skillId = language.skillId;

                                    this.VueData.storageForLanguage.push({
                                        name: null,
                                        level: null,
                                        languageId: null,
                                        skillId: null,
                                        valid: true,
                                    });
                                });
                            }
                        });
                }
            }

            // create models
            _initExportedData() {
                let firstPhone;
                let secondPhone;
                this.$rootScope.me.contacts.forEach((item) => {
                    if (item.contactType == 'mphone') firstPhone = item.value;
                    if (item.contactType == 'phoneWork') secondPhone = item.value;
                });
                this.exportedData = {
                    vacancyId: this.parent.vacancy.vacancyId,
                    name: this.parent.vacancy.position,
                    description: this.parent.vacancy.descr,
                    scheduleId: null,
                    rubricList: [],
                    cityId: null,
                    contactEmail: '',
                    contactPhone: firstPhone ? firstPhone : secondPhone ? secondPhone : '',
                    contactPerson: this.$rootScope.me.fullName ? this.$rootScope.me.fullName : '',
                    address: null,
                    languageSkills: [],
                    salaryFrom: this.parent.vacancy.salaryFrom,
                    salaryTo: this.parent.vacancy.salaryTo,
                    salaryDescr: '',
                    publishType: '',
                    endingType: 1,
                    isEnableResponseWithNoResume: false,
                };
            }

            _initVueData() {
                this.VueData = {
                    storageForLanguage: [
                        {
                            name: null,
                            level: null,
                            languageId: null,
                            skillId: null,
                            valid: true,
                        },
                    ],
                    showAdditional: false,
                    jobtypeOptions: [],
                    jobtypeModel: '',
                    jobtypeId: null,
                    jobtypeDisabled: false,
                    categoryDisabled: false,
                    categorysId: null,
                    nameCategoryModel: [],
                    nameCategoryOptions: [],
                    nameCityModel: '',
                    nameCityOptions: [],
                    languageOptions: [],
                    languageLevelOptions: [],
                    validName: true,
                    validJobtype: true,
                    validPublishType: true,
                    validCity: true,
                    validCategory: true,
                    validContactEmail: true,
                    validDescription: true,
                    validContactPhone: true,
                    validContactPerson: true,
                    validateLevelOfLanguage: true,
                    validSalary: true,
                };
            }

            _getLocalizedCityKey(currentLang) {
                if (!currentLang) return 'nameEng';

                let cityKey = 'nameEng';
                switch (currentLang) {
                    case 'en':
                    case 'pl':
                        return (cityKey = 'nameEng');
                    case 'ua':
                        return (cityKey = 'nameUkr');
                    case 'ru':
                        return (cityKey = 'name');
                }
            }

            $onInit() {
                if (this.editPublication) {
                    this.$rootScope.loading = true;
                    Promise.all([
                        this._initExportedData(),
                        this._initVueData(),
                        this.setCategories(true),
                        this.setJobTypes(true),
                        this.setVacancyPublishType(true),
                        this.setLanguages(true),
                        this.setCities(true),
                        this.setVacancyDataFromRabota(),
                    ])
                        .catch((err) => this.notificationService.error(err))
                        .finally(() => (this.$rootScope.loading = false));
                } else {
                    this.$rootScope.loading = true;
                    Promise.all([
                        this._initExportedData(),
                        this._initVueData(),
                        this.setCategories(),
                        this.setJobTypes(),
                        this.setVacancyPublishType(),
                        this.setLanguages(),
                        this.setCities(),
                    ])
                        .catch((err) => this.notificationService.error(err))
                        .finally(() => (this.$rootScope.loading = false));
                }

                // Bindable to directive
                this.selectJobType = this.selectJobType.bind(this);
                this.selectPublishType = this.selectPublishType.bind(this);
                this.selectCategory = this.selectCategory.bind(this);
                this.selectLanguage = this.selectLanguage.bind(this);
                this.selectLanguageLevel = this.selectLanguageLevel.bind(this);
                this.removeLanguageObject = this.removeLanguageObject.bind(this);
                this.selectCity = this.selectCity.bind(this);
                this.cityKey = this._getLocalizedCityKey(this.$rootScope?.currentLang);
            }

            closeModal() {
                parentScope._reload();
                self.$uibModalInstance.close();
                self.VueData.storageForLanguage = [
                    {
                        name: null,
                        level: null,
                        languageId: null,
                        skillId: null,
                        valid: true,
                    },
                ];
            }
        }

        function openPublishModal(self) {
            const modalInstance = self.$uibModal.open({
                templateUrl: 'partials/modal/jobSitesIntegration/rabotaUA/publishVacancyRabotaUA.html',
                controller: [
                    'parent',
                    'exportedData',
                    'VueData',
                    'editPublication',
                    '$uibModalInstance',
                    '$translate',
                    '$scope',
                    '$uibModal',
                    'notificationService',
                    'rabotaUaIntegrationService',
                    'Vacancy',
                    '$location',
                    '$rootScope',
                    '$state',
                    'Candidate',
                    publishRabotaUAController,
                ],
                controllerAs: 'vm',
                windowClass: 'publish-rabota-popup',
                resolve: {
                    parent: () => self.parent,
                    exportedData: () => self.exportedData,
                    VueData: () => self.VueData,
                    editPublication: () => self.editPublication,
                    $translate: () => self.$translate,
                    $uibModal: () => self.$uibModal,
                    notificationService: () => self.notificationService,
                    rabotaUaIntegrationService: () => self.rabotaUaIntegrationService,
                    Vacancy: () => self.vacancyService,
                    $location: () => self.$location,
                    $rootScope: () => self.$rootScope,
                    $state: () => self.$state,
                    Candidate: () => self.candidateService,
                },
            });
        }

        if (parentScope.integrated && !parentScope.isIntegrated) {
            openPublishModal(this);
        } else if (parentScope.isIntegrated) {
            if (edit) {
                openPublishModal(this);
            }
        }
    }

    connectVacancy(self) {
        class connectVacancy {
            constructor(
                $uibModalInstance,
                parent,
                $translate,
                $uibModal,
                notificationService,
                rabotaUaIntegrationService,
                $rootScope,
                $scope,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                this.$rootScope = $rootScope;
                this.$scope = $scope;
                self = this;
            }
            connectVacancy() {
                if (this.validateUrl()) {
                    this.$rootScope.loading = true;

                    this.rabotaUaIntegrationService
                        .onLinkVacancyWithPublished({
                            vacancyId: this.parent.vacancy.vacancyId,
                            link: this.vacancyLink,
                        })
                        .then((resp) => {
                            if (resp.status === 'vacancyPosted') {
                                this.notificationService.error(
                                    this.$translate.instant(
                                        'A published vacancy only be linked to one vacancy in CleverStaff. With the vacancy you specified, there is already an integration with',
                                        { id: resp.object.localId, position: resp.object.position },
                                    ),
                                );
                            }
                            this.closeModal();
                        })
                        .catch((err) => {
                            if (err.status === 'vacancyIsNotPublished') {
                                this.notificationService.error(
                                    this.$translate.instant(
                                        'You can only link to the published one (currently active) a vacancy with rabota.ua',
                                    ),
                                );
                            }
                            err.message &&
                                this.notificationService.error(
                                    this.$translate.instant(
                                        err.message
                                            .replace('Not Published MESSAGE:', '')
                                            .replace('Не опубликовано MESSAGE:', ''),
                                    ),
                                );
                        })
                        .finally(() => {
                            this.$rootScope.loading = false;
                            this.$rootScope.$$phase || this.$scope.$apply();
                        });
                }
            }

            changeUrl = (value) => {
                this.vacancyLink = value;
                this.$scope.$apply();
            };

            validateUrl = () => {
                const regExp = /^(https?|chrome):\/\/[^\s$.?#].[^\s]*$/gm;

                this.showErrorLinkEmpty = !this.vacancyLink.trim();
                this.showErrorLinkInvalid = !this.showErrorLinkEmpty && !regExp.test(this.vacancyLink);
                return !this.showErrorLinkEmpty && !this.showErrorLinkInvalid;
            };

            closeModal() {
                this.$uibModalInstance.close();
            }

            $onInit() {
                this.vacancyLink = '';
            }
        }
        function openConnectVacancyModal(self) {
            const modalInstance = self.$uibModal.open({
                templateUrl: 'partials/modal/jobSitesIntegration/workUA/connect-vacancy.html',
                controller: [
                    '$uibModalInstance',
                    'parent',
                    '$translate',
                    '$uibModal',
                    'notificationService',
                    'rabotaUaIntegrationService',
                    '$rootScope',
                    '$scope',
                    connectVacancy,
                ],
                controllerAs: 'vm',
                resolve: {
                    parent: () => {
                        return self.parent;
                    },
                    $translate: () => {
                        return self.$translate;
                    },
                    $uibModal: () => {
                        return self.$uibModal;
                    },
                    notificationService: () => {
                        return self.notificationService;
                    },
                    rabotaUaIntegrationService: () => {
                        return self.rabotaUaIntegrationService;
                    },
                    $rootScope: () => {
                        return self.$rootScope;
                    },
                },
            });
            modalInstance.closed.then(() => {
                self._getVacancyStatistic();
            });
        }
        openConnectVacancyModal(self);
    }

    disconnectVacancy(self) {
        class disconnectVacancy {
            constructor(
                $uibModalInstance,
                parent,
                $translate,
                $uibModal,
                notificationService,
                rabotaUaIntegrationService,
                $rootScope,
                $scope,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                this.$rootScope = $rootScope;
                this.$scope = $scope;
                self = this;
            }
            disconnectVacancy() {
                this.$rootScope.loading = true;

                this.rabotaUaIntegrationService
                    .onUnlinkVacancy({
                        vacancyId: this.parent.vacancy.vacancyId,
                    })
                    .then((resp) => {
                        this.closeModal();
                    })
                    .catch((err) => {
                        err.message &&
                            this.notificationService.error(
                                this.$translate.instant(
                                    err.message
                                        .replace('Not Published MESSAGE:', '')
                                        .replace('Не опубликовано MESSAGE:', ''),
                                ),
                            );
                    })
                    .finally(() => {
                        this.$rootScope.loading = false;
                        this.$rootScope.$$phase || this.$scope.$apply();
                    });
            }

            closeModal() {
                this.$uibModalInstance.close();
            }
        }
        function openDisconnectVacancyModal(self) {
            const modalInstance = self.$uibModal.open({
                templateUrl: 'partials/modal/jobSitesIntegration/rabotaUA/disconnect-vacancy.html',
                controller: [
                    '$uibModalInstance',
                    'parent',
                    '$translate',
                    '$uibModal',
                    'notificationService',
                    'rabotaUaIntegrationService',
                    '$rootScope',
                    '$scope',
                    disconnectVacancy,
                ],
                controllerAs: 'vm',
                resolve: {
                    parent: () => {
                        return self.parent;
                    },
                    $translate: () => {
                        return self.$translate;
                    },
                    $uibModal: () => {
                        return self.$uibModal;
                    },
                    notificationService: () => {
                        return self.notificationService;
                    },
                    rabotaUaIntegrationService: () => {
                        return self.rabotaUaIntegrationService;
                    },
                    $rootScope: () => {
                        return self.$rootScope;
                    },
                },
            });
            modalInstance.closed.then(() => {
                self._getVacancyStatistic();
            });
        }
        openDisconnectVacancyModal(self);
    }

    removePublish(self) {
        class removePublish {
            constructor(
                $uibModalInstance,
                parent,
                $translate,
                $uibModal,
                notificationService,
                rabotaUaIntegrationService,
                $rootScope,
            ) {
                this.parent = parent;
                this.$uibModalInstance = $uibModalInstance;
                this.$translate = $translate;
                this.$uibModal = $uibModal;
                this.notificationService = notificationService;
                this.rabotaUaIntegrationService = rabotaUaIntegrationService;
                this.$rootScope = $rootScope;
                self = this;
            }

            removePublish() {
                self.$rootScope.loading = true;
                this.rabotaUaIntegrationService.onRemovePublish(this.parent.vacancy.vacancyId).then(
                    (resp) => {
                        if (resp.status === 'error') {
                            this.notificationService.error(this.$translate.instant(resp.message));
                        } else {
                            this.notificationService.success(this.$translate.instant('Publication deleted'));
                            this.closeModal();
                            self.$rootScope.loading = false;
                        }
                    },
                    (err) => {
                        this.notificationService.error(err.message);
                        this.closeModal();
                        self.$rootScope.loading = false;
                    },
                );
            }

            closeModal() {
                this.$uibModalInstance.close();
            }
        }

        function openRemoveModal(self) {
            const modalInstance = self.$uibModal.open({
                templateUrl: 'partials/modal/jobSitesIntegration/rabotaUA/remove-publish.html',
                controller: [
                    '$uibModalInstance',
                    'parent',
                    '$translate',
                    '$uibModal',
                    'notificationService',
                    'rabotaUaIntegrationService',
                    '$rootScope',
                    removePublish,
                ],
                controllerAs: 'vm',
                windowClass: 'remove-publish-popup secondary-modal',
                resolve: {
                    parent: () => {
                        return self.parent;
                    },
                    $translate: () => {
                        return self.$translate;
                    },
                    $uibModal: () => {
                        return self.$uibModal;
                    },
                    notificationService: () => {
                        return self.notificationService;
                    },
                    rabotaUaIntegrationService: () => {
                        return self.rabotaUaIntegrationService;
                    },
                    $rootScope: () => {
                        return self.$rootScope;
                    },
                },
            });
            modalInstance.closed.then(() => {
                self._getVacancyStatistic();
            });
        }

        openRemoveModal(self);
    }

    _onGetPublisherById(id) {
        return this.$rootScope.persons.find((person) => person.userId === id);
    }

    _onTransformVacancyStatistic(applyStatistic = {}, viewStatistic = {}) {
        if (Object.entries(applyStatistic).length !== 0 && applyStatistic.constructor === Object) {
            let sortedStatistic = Object.entries(applyStatistic.byDate).reduce((prev, cur) => {
                prev.push({
                    statisticDate: cur[0],
                    applyStatisticValue: cur[1],
                });
                return prev;
            }, []);

            sortedStatistic = Object.entries(viewStatistic).reduce((prev, cur) => {
                prev.push({
                    statisticDate: cur[0],
                    viewStatisticValue: cur[1],
                });
                return prev;
            }, sortedStatistic);

            let foo = _.groupBy(sortedStatistic, 'statisticDate');

            this.vacancyStatistic.transformedStatistic = Object.entries(foo).reduce((prev, cur) => {
                prev.push(Object.assign(...cur[1]));
                return prev;
            }, []);
        } else {
            let sortedStatistic = Object.entries(viewStatistic).reduce((prev, cur) => {
                prev.push({
                    statisticDate: cur[0],
                    viewStatisticValue: cur[1],
                });
                return prev;
            }, []);

            let foo = _.groupBy(sortedStatistic, 'statisticDate');

            this.vacancyStatistic.transformedStatistic = Object.entries(foo).reduce((prev, cur) => {
                prev.push(Object.assign(...cur[1]));
                return prev;
            }, []);
        }
    }

    _onCalculateTotalValueOfStatistic(obj) {
        if (obj !== undefined) {
            this.vacancyStatistic.totalViewStatistic = obj.reduce((prev, cur) => {
                prev += cur.viewStatisticValue || 0;
                return prev;
            }, 0);
            this.vacancyStatistic.totalApplyStatistic = obj.reduce((prev, cur) => {
                prev += cur.applyStatisticValue || 0;
                return prev;
            }, 0);
        }
    }

    _getVacancyStatistic() {
        this.rabotaUaIntegrationService
            .onGetVacancyInfo(
                this.parent.vacancy ? this.parent.vacancy.vacancyId : this.parent.parent.vacancy.vacancyId,
            )
            .then(
                (resp) => {
                    if (resp.object.creatorId) this.vacancyPublisher = this._onGetPublisherById(resp.object.creatorId);
                    this.$rootScope.loading = false;
                    this.vacancyStatistic = resp.object; /// resp.object || this.testData.object

                    if (this.vacancyStatistic.applyStatistic || this.vacancyStatistic.viewStatistic) {
                        this._onTransformVacancyStatistic(
                            this.vacancyStatistic.applyStatistic,
                            this.vacancyStatistic.viewStatistic,
                        );
                        this._onCalculateTotalValueOfStatistic(this.vacancyStatistic.transformedStatistic);
                    }
                    if (this.vacancyStatistic.status === false) {
                        this.isIntegrated = false;
                    } else {
                        this.rabotaStatistickSwitcher = false;
                        this.isIntegrated = true;
                    }

                    this.$scope.$apply();
                },
                (err) => {
                    this.$scope.$apply();
                },
            );
    }

    _reload() {
        this.isIntegrated = false;
        this._getVacancyStatistic();
    }

    $onInit() {
        this._reload();

        this.$scope.$on('rabotaChanged', (event, data) => {
            this.integrated = data;
            this._reload();
        });

        this.$rootScope.ckEditorRabota = {
            enterMode: CKEDITOR.ENTER_P,
            shiftEnterMode: CKEDITOR.ENTER_P,
            on: {
                instanceReady: function (ev) {
                    let text = CKEDITOR.instances['ckeditor'].getData();
                    text = text.replace(/(<br>)/g, '</p><p>');

                    setTimeout(() => {
                        CKEDITOR.instances['ckeditor'].setData(text);
                    }, 0);

                    setTimeout(() => {
                        ev.editor._.data = ev.editor._.data.replace(/<p>\s*<\/p>/g, '');
                        CKEDITOR.instances['ckeditor'].setData(ev.editor._.data);
                    }, 200);
                },
            },
        };
    }
}

const rabotaUaPostingDefinition = {
    bindings: {
        integrated: '=',
    },
    require: {
        parent: '^jobSitesIntegrationComponent',
    },
    templateUrl: 'partials/jobSitesIntegration/rabotaUA/rabotaUAIntegration.html',
    controller: rabotaUAPostingComponentCtrl,
    controllerAs: 'vm',
};

component.component('rabotaUaPostingComponent', rabotaUaPostingDefinition);
