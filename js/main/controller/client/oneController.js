function ClientOneController(
    serverAddress,
    $scope,
    $stateParams,
    $location,
    Client,
    Service,
    Contacts,
    Vacancy,
    vacancyStages,
    $rootScope,
    notificationService,
    $filter,
    ngTableParams,
    Person,
    Action,
    Task,
    CacheCandidates,
    File,
    FileInit,
    $translate,
    $uibModal,
    $route,
    Mail,
    $localStorage,
    $timeout,
    $anchorScroll,
    Email,
    $sce,
) {
    $scope.save = function () {
        Client.edit($scope.client, function (resp) {});
    };
    $rootScope.toAdd = function () {
        $location.url('/client/add/');
        $location.path('/client/add/');
    };
    $rootScope.closeModal = function () {
        $scope.modalInstance.close();
    };
    $scope.closeModal = function () {
        $scope.modalInstance.close();
    };
    $scope.showAddResponsibleUser = function (person) {
        $rootScope.clickedSaveResponsibleInVacancy = false;
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/client-adding-responsible.html',
            size: '',
            resolve: function () {},
        });
        $rootScope.changeResponsibleInClient.id = person.userId;
        $rootScope.changeResponsibleInClient.name = $rootScope.useAmericanNameStyle
            ? person.fullNameEn
            : person.fullName;
    };
    $scope.showRemoveResponsibleUser = function (user) {
        var firstName = user.firstName != undefined ? user.firstName : '';
        var lastName = user.lastName != undefined ? user.lastName : '';

        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/remove-responsible-from-client.html',
            size: '',
            resolve: function () {},
        });

        $rootScope.changeResponsibleInClient.id = user.userId;
        $rootScope.changeResponsibleInClient.name = $rootScope.useAmericanNameStyle
            ? firstName + ' ' + lastName
            : lastName + ' ' + firstName;
        $rootScope.changeResponsibleInClient.text =
            $filter('translate')('Do you want to remove the responsible') + ' ' + firstName + ' ' + lastName;
    };
    $rootScope.removeResponsibleUserInVacancy = function () {
        Client.removeResponsible(
            {
                clientId: $scope.client.clientId,
                personId: $rootScope.changeResponsibleInClient.id,
                comment: $rootScope.changeResponsibleInClient.comment,
            },
            function (resp) {
                if (resp.status === 'ok') {
                    notificationService.success($filter('translate')('vacancy remove responsible'));
                    Person.getAllPersons(
                        function (resp) {
                            $scope.associativePerson = resp.object;
                            $scope.persons = [];
                            angular.forEach($scope.associativePerson, function (val, key) {
                                if (angular.equals(resp.status, 'ok')) {
                                    $scope.persons.push($scope.associativePerson[key]);
                                }
                            });
                            $rootScope.persons = $scope.persons;
                        },
                        function (error) {
                            console.error(error);
                        },
                    );
                    $rootScope.updateClient();
                    $scope.tableParamsClientHistory.reload();
                } else {
                    notificationService.error(resp.message);
                }
                $rootScope.closeModal();
                $rootScope.changeResponsibleInClient.comment = '';
                $rootScope.changeResponsibleInClient.id = '';
            },
            function (err) {},
        );
    };
    $rootScope.saveResponsibleUserInVacancy = function () {
        Client.addResponsible(
            {
                clientId: $scope.client.clientId,
                personId: $rootScope.changeResponsibleInClient.id,
                comment: $rootScope.changeResponsibleInClient.comment,
            },
            function (resp) {
                if (resp.status === 'ok') {
                    notificationService.success($filter('translate')('set responsible'));
                    $rootScope.updateClient();
                    $scope.tableParamsClientHistory.reload();
                } else {
                    notificationService.error(resp.message);
                }
            },
            function (err) {},
        );

        $rootScope.closeModal();
        $rootScope.changeResponsibleInClient.comment = '';
        $rootScope.changeResponsibleInClient.id = '';
    };

    $rootScope.saveEditableTask = (task) => {
        Task.edit(
            {
                taskId: $rootScope.editableTask.taskId,
                status: $rootScope.editableTask.status.value.replace('tasksStatuses.', ''),
                title: $rootScope.editableTask.title,
                text: $rootScope.editableTask.text.trim(),
                targetDate: $rootScope.editableTask.targetDate,
                responsibleIds: $rootScope.responsiblePersons.map(({ userId }) => userId),
                type: $rootScope.editableTask.type.value,
            },
            (resp) => {
                if (resp.status !== 'ok') {
                    notificationService.error(resp.message);
                }
                $scope.updateTasks();
                $rootScope.closeTaskModal();
                notificationService.success($filter('translate')('Task saved'));
            },
        );
    };

    $scope.showModalEditCommentToClient = function (history) {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/edit-comment-client.html',
            size: '',
            backdrop: true,
            resolve: function () {},
        });

        $rootScope.closeClientCommentModal = function () {
            $rootScope.initTagsStyles(history, true);
            $scope.modalInstance.close();
        };

        $rootScope.initCkEditorClient(history);

        $rootScope.changeClientComment = function () {
            $rootScope.loading = true;
            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorOneClient.getData());
            $scope.actionComment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

            Action.editAction({ comment: $scope.actionComment, actionId: history.actionId }, function (resp) {
                if (resp.status && angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                } else {
                    history.editCommentFlag = false;
                    history.descr = resp.object.descr;
                    history.dateEdit = resp.object.dateEdit;
                    $rootScope.closeModal();
                    $scope.tableParamsClientHistory.reload();
                    $rootScope.loading = false;
                }
            });
        };
    };

    function deleteFromTags(id, type) {
        $scope.users.filter((user, index) => {
            if (user[type] == id) {
                $scope.users.splice(index, 1);
            }
        });
        $scope.repsonsibleUsers.forEach((user, index) => {
            if (user.userId == id) {
                $scope.repsonsibleUsers.splice(index, 1);
            }
        });
    }

    $rootScope.initCkEditorClient = function (history) {
        $scope.repsonsibleUsers = [];
        $scope.users = [];

        $rootScope.persons.forEach((i, index) => {
            $scope.client.responsiblesPerson.forEach((person) => {
                if (
                    i.userId === person.personId &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher'
                ) {
                    $scope.repsonsibleUsers.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: true,
                    });
                }
            });

            if (
                i.status === 'A' &&
                i.recrutRole === 'admin' &&
                i.recrutRole !== 'client' &&
                i.recrutRole !== 'freelancer' &&
                i.recrutRole !== 'researcher'
            ) {
                $scope.users.push({
                    id: index + 1,
                    fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                    userId: i.userId,
                    role: i.recrutRole,
                    responsible: false,
                });
            }
        });
        $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

        $rootScope.ckEditorOneClient = {
            height: 140,
            autoGrow_maxHeight: 340,
            toolbar: [],
            enterMode: CKEDITOR.ENTER_BR,
            shiftEnterMode: CKEDITOR.ENTER_BR,
            extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
            removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
            editorplaceholder: $translate.instant('mention placeholder'),
            mentions: [
                {
                    feed: dataCallback,
                    minChars: 0,
                    itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                    outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                    marker: '@',
                    pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                },
            ],
            on: {
                paste: function (ev) {
                    ev.data.dataValue = '';
                    CKEDITOR.instances['ckEditorOneClient'].insertText(ev.data.dataTransfer._.data.Text);
                    setTimeout(() => {
                        let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                        if (bodyHeight > ev.editor.config.height) {
                            if (bodyHeight < 605) {
                                ev.editor.resize(null, bodyHeight);
                            }
                        }
                    }, 0);
                },
                afterInsertHtml: function (ev) {
                    let user = ev.editor.data;
                    if (user) {
                        deleteFromTags(user.userId, 'userId');
                        $scope.deletedUsers.push(user);
                    }
                },
                instanceReady: function (ev) {
                    $scope.deletedUsers = [];
                    $scope.afterDelete = [];

                    ev.editor.dataProcessor.writer.setRules('p', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                    ev.editor.dataProcessor.writer.setRules('br', {
                        indent: false,
                        breakBeforeOpen: false,
                        breakAfterOpen: false,
                        breakBeforeClose: false,
                        breakAfterClose: false,
                    });
                    if (history !== undefined) {
                        $rootScope.convertTagsToSpan(history, 'ckEditorOneClient');
                    }
                },
                resize: function (event) {
                    $rootScope.adjustCKEditorHeight(event);
                },
            },
        };

        function dataCallback(opts, callback) {
            opts.query = opts.query.replace(/\u00A0/g, ' ');
            let text = CKEDITOR.instances['ckEditorOneClient'].getData();

            $scope.deletedUsers.forEach((user) => {
                if (!text.includes(`${user.fullname}`)) {
                    if (user.responsible) {
                        $scope.repsonsibleUsers.push(user);
                    } else {
                        $scope.users.push(user);
                    }
                    $scope.afterDelete.push(user.id);

                    $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                }
            });

            $scope.afterDelete.forEach((id) => {
                $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
            });
            $scope.afterDelete = [];

            let showAll =
                $scope.repsonsibleUsers.length === 1 && $scope.repsonsibleUsers[0].userId === $rootScope.me.userId;

            if (opts.query.length === 0 && $scope.repsonsibleUsers.length > 0 && !showAll) {
                setTimeout(function () {
                    callback(
                        $scope.repsonsibleUsers.filter(function (item) {
                            return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                        }),
                    );
                });
            } else {
                setTimeout(function () {
                    callback(
                        $scope.users.filter(function (item) {
                            return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                        }),
                    );
                });
            }
        }
    };

    $scope.showModalAddCommentToClient = function () {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/add-comment-client.html',
            size: '',
            resolve: function () {},
        });
        $scope.modalInstance.closed.then(function () {
            $rootScope.commentClient.comment = '';
        });

        $rootScope.initCkEditorClient();

        $(document)
            .unbind('keydown')
            .keydown(function (e) {
                if (e.ctrlKey == true && e.which == 13) {
                    $rootScope.addCommentInClient();
                }
            });
    };
    $rootScope.addCommentInClient = function () {
        let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorOneClient.getData());
        $rootScope.commentClient.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

        if ($rootScope.commentClient.comment != undefined && $rootScope.commentClient.comment.length > 0) {
            $rootScope.commentClient.loading = true;
            Client.setMessage(
                {
                    comment: $rootScope.commentClient.comment,
                    clientId: $scope.client.clientId,
                },
                function (resp) {
                    $rootScope.commentClient.loading = false;
                    $('.addMessageInClient').modal('hide');
                    $rootScope.commentClient.comment = null;
                    if (resp.status == 'ok') {
                        $scope.tableParamsClientHistory.reload();
                        $rootScope.closeModal();
                    }
                },
                function (error) {
                    $rootScope.commentClient.loading = false;
                },
            );
        }
    };
    $scope.showChangeStatusOfClient = function (status) {
        if (status === 'Canceled') {
            status = 'canceled';
        }

        if (status === 'Deleted') {
            status = 'deleted';
        }

        if ($scope.client.activeVacanciesNumber !== 0 && status === 'deleted') {
            notificationService.success($filter('translate')('This client has active vacancy'));
            $rootScope.clickedSaveClientStatus = false;
            $scope.selectedStatus = $scope.client.state;
            return;
        }
        $rootScope.changeStatusInClient.status = status;
        $rootScope.changeStatusInClient.status_old = $scope.client.state;
        $rootScope.changeStatusInClient.header = $filter('translate')('change_client_status');
        $rootScope.changeStatusInClient.placeholder = $filter('translate')(
            "Write a comment about changing client's status (optional)",
        );
        $rootScope.saveClientStatus();
    };
    $rootScope.saveClientStatus = function () {
        if (!$rootScope.clickedSaveClientStatus) {
            $rootScope.clickedSaveClientStatus = true;
            $scope.client.state = $rootScope.changeStatusInClient.status;

            Client.changeState(
                {
                    clientId: $scope.client.clientId,
                    comment: $rootScope.changeStatusInClient.comment,
                    clientState: $rootScope.changeStatusInClient.status,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.clickedSaveClientStatus = false;
                        notificationService.success($filter('translate')('client change state'));
                        $rootScope.changeStatusInClient.comment = '';
                        $rootScope.changeStatusInClient.status = null;
                        $scope.tableParamsClientHistory.reload();
                        $('.changeStatusInClient.modal').modal('hide');
                    } else {
                        $rootScope.clickedSaveClientStatus = false;
                        notificationService.error(resp.message);
                    }
                },
                function () {
                    $('.changeStatusInClient.modal').modal('hide');
                },
            );
        }
    };
    $scope.toAddContact = function (id) {
        $rootScope.loading = true;
        $rootScope.contactClientAddId = $scope.client.localId;
        $location.path('contact/add/' + $scope.client.localId);
    };

    $scope.toAllVacancy = function (id) {
        $rootScope.searchedClientId = id;
        Vacancy.setOptions('clientId', id);
        $rootScope.searchClientId = id;
        $location.path('/vacancies');
    };
    $scope.toAddVacancy = (obj) => ($rootScope.addVacancyClientId = obj.clientId);
    // $rootScope.openMenuWithCandidates = function (history, update) {
    //    if (update) {
    //        if (history.updateCandidate) {
    //            history.updateCandidate = false;
    //            history.editCommentFlag = false;
    //        } else {
    //            history.updateCandidate = true;
    //        }
    //    } else {
    //        if (history.showAllCandidates) {
    //            history.showAllCandidates = false;
    //            history.editCommentFlag = false;
    //        } else {
    //            history.showAllCandidates = true;
    //        }
    //    }
    // };
    $scope.changeCommentFlag = function (history) {
        history.editCommentFlag = !history.editCommentFlag;
        $scope.editComment = history.descr;
        history.showAllCandidates = false;
        $scope.showModalEditCommentToClient(history);
    };

    $scope.changeComment = function (action, comment) {
        Action.editAction({ comment: comment, actionId: action.actionId }, function (resp) {
            if (resp.status && angular.equals(resp.status, 'error')) {
                notificationService.error(resp.message);
            } else {
                action.editCommentFlag = false;
                action.descr = resp.object.descr;
                action.new_komment = '';
                action.dateEdit = resp.object.dateEdit;
                $scope.tableParamsClientHistory.reload();
            }
        });
    };
    $scope.showDeleteComment = function (resp) {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/delete-comment-candidate.html',
            scope: $scope,
            resolve: {
                items: function () {
                    return $scope.items;
                },
            },
        });
        $scope.commentRemove = resp;
        $rootScope.commentRemoveId = resp.actionId;
    };
    $rootScope.deleteComment = function () {
        Action.removeMessageAction(
            {
                actionId: $rootScope.commentRemoveId,
            },
            function (resp) {
                if (resp.status === 'ok') {
                    notificationService.success($filter('translate')('Comment removed'));
                    angular.forEach($scope.history, function (val) {
                        if ($rootScope.commentRemoveId == val.actionId) {
                            $scope.history.splice($scope.history.indexOf(val), 1);
                        }
                    });
                    $scope.tableParamsClientHistory.reload();
                } else {
                }
                $rootScope.closeModal();
            },
        );
    };
    $scope.showCommentsFirstTime = function () {
        Service.history(
            {
                vacancyId: null,
                clientId: $scope.client !== undefined ? $scope.client.clientId : null,
                page: { number: 0, count: $scope.historyLimit },
                onlyWithComment: ($scope.onlyWithComment = false),
            },
            function (res) {
                $scope.historyLimit = res.size;
                $scope.historyTotal = res.total;
                $scope.history = res.objects;
                $scope.onlyComments = false;
            },
        );
    };
    $scope.showComments = function () {
        $scope.historyLimit = 15;
        Service.history(
            {
                vacancyId: null,
                clientId: $scope.client !== undefined ? $scope.client.clientId : null,
                page: { number: 0, count: $scope.historyLimit },
                onlyWithComment: ($scope.onlyWithComment = true),
            },
            function (res) {
                $scope.historyLimit = res.size;
                $scope.historyTotal = res.total;
                $scope.history = res.objects;
                $scope.history.forEach((value) => {
                    value.descr = value.descr.replaceAll('&nbsp;', ' ');
                    value.descr = value.descr.replaceAll('rel="nofollow"', '');
                    if (
                        value.type === 'client_message' ||
                        value.type === 'vacancy_message' ||
                        value.type === 'interview_message' ||
                        value.type === 'interview_edit' ||
                        value.type === 'interview_add' ||
                        value.type === 'interview_edit_sent_email' ||
                        value.type === 'interview_add_sent_email' ||
                        value.type === 'interview_edit_date' ||
                        value.type === 'client_change_state' ||
                        value.type === 'client_set_responsible'
                    ) {
                        $rootScope.initTagsStyles(value);
                    }
                });
                $scope.objectSize = res['objects'] ? res['total'] : 0;
                $scope.onlyComments = true;
                $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);
            },
        );
    };

    $scope.updateCustomStages = function () {
        angular.forEach($scope.history, function (value) {
            if (value.stateNew || value.stateOld) {
                if ($rootScope.customStages) {
                    $rootScope.customStages.forEach((stage) => {
                        if (stage.customInterviewStateId === value.stateOld) {
                            value.customStateOld = value.stateOld;
                            value.stateOld = stage.type;
                        }
                        if (stage.customInterviewStateId === value.stateNew) {
                            value.customStateNew = value.stateNew;
                            value.stateNew = stage.type;
                        }
                    });
                }
            }
        });
    };

    $scope.showDetails = function () {
        $scope.historyLimit = 15;
        Service.history(
            {
                vacancyId: null,
                clientId: $scope.client !== undefined ? $scope.client.clientId : null,
                page: { number: 0, count: $scope.historyLimit },
                onlyWithComment: ($scope.onlyWithComment = false),
            },
            function (res) {
                $scope.historyLimit = res.size;
                $scope.historyTotal = res.total;
                $scope.history = res.objects;
                $scope.history.forEach((value) => {
                    value.descr = value.descr.replaceAll('&nbsp;', ' ');
                    value.descr = value.descr.replaceAll('rel="nofollow"', '');
                    if (
                        value.type === 'client_message' ||
                        value.type === 'vacancy_message' ||
                        value.type === 'interview_message' ||
                        value.type === 'interview_edit' ||
                        value.type === 'interview_add' ||
                        value.type === 'interview_edit_sent_email' ||
                        value.type === 'interview_add_sent_email' ||
                        value.type === 'interview_edit_date' ||
                        value.type === 'client_change_state' ||
                        value.type === 'client_set_responsible'
                    ) {
                        $rootScope.initTagsStyles(value);
                    }
                });
                $scope.objectSize = res['objects'] ? res['total'] : 0;
                $scope.onlyComments = false;
                $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);

                $scope.updateCustomStages();
            },
        );
    };
    $scope.showCommentsSwitch = function (flag) {
        if (flag) {
            $scope.showDetails();
        } else {
            $scope.showComments();
        }
    };
    $scope.updateTasks = function (needOpenModal) {
        Task.get(
            {
                clientId: $rootScope.client.clientId,
            },
            function (resp) {
                if (resp.status == 'ok') {
                    $scope.totalTasksNumber = 0;
                    $scope.totalTasksNumber = resp.total;
                    $scope.clientTasks = resp.objects;
                    $rootScope.clientTasks = $scope.clientTasks;
                    $rootScope.clientTasks.forEach((val, idx) => {
                        if (val.status === 'open') val.status = 'inwork';
                        val.status = 'tasksStatuses.' + $scope.clientTasks[idx].status;
                        val.clientTaskStatus = $scope.clientTasks[idx].status;
                        val.clientEditTaskStatus = $scope.clientTasks[idx].status;
                        val.editableTaskOptions = [
                            {
                                value: 'inwork',
                                name: 'inwork',
                                taskId: $scope.clientTasks[idx].taskId,
                                status: 'inwork',
                                sendStatus: 'open',
                            },
                            {
                                value: 'completed',
                                name: 'completed',
                                taskId: $scope.clientTasks[idx].taskId,
                                status: 'completed',
                                sendStatus: 'completed',
                            },
                            {
                                value: 'Cancelled',
                                name: 'Cancelled',
                                taskId: $scope.clientTasks[idx].taskId,
                                status: 'Cancelled',
                                sendStatus: 'Cancelled',
                            },
                        ];
                    });

                    if ($scope.urlTaskId) {
                        $rootScope.responsiblePersonsEdit = [];
                        angular.forEach($scope.clientTasks, function (resp) {
                            if (resp.taskId == $scope.urlTaskId && needOpenModal) {
                                $rootScope.editableTask = resp;
                                $scope.showModalEditTaskToCandidate($rootScope.editableTask);
                                $location.$$absUrl = $location.$$absUrl.split('?')[0];
                            }
                        });
                        if ($rootScope.editableTask && $location.$$absUrl.indexOf('&task=') == -1) {
                            $location.$$absUrl = $location.$$absUrl + '&task=' + $scope.urlTaskId;
                            angular.forEach($rootScope.editableTask.responsiblesPerson, function (resp) {
                                angular.forEach($rootScope.persons, function (res) {
                                    if (resp.responsible.userId == res.userId) {
                                        $rootScope.responsiblePersonsEdit.push(res);
                                        res.notShown = true;
                                    }
                                });
                            });
                            $('.editTaskInCandidate')
                                .modal('setting', {
                                    onHide: function () {
                                        $scope.urlTaskId = null;
                                        $location.$$absUrl = $location.$$absUrl.split('&')[0];
                                        $scope.$apply();
                                    },
                                })
                                .modal('show');
                        }
                    }
                } else {
                    notificationService.error(resp.message);
                }
            },
        );
    };

    $rootScope.testClientUpdateTasks = $scope.updateTasks;

    $scope.roundMinutes = function (date) {
        date.setHours(date.getHours());
        date.setMinutes(0);

        return date;
    };
    $rootScope.updateTaskInModal = function () {
        $scope.updateTasks();
        $scope.tableParamsClientHistory.reload();
    };
    $scope.showModalAddTaskToCandidate = function (size) {
        $rootScope.ckEditorTaskTextArea = {
            height: 228,
            autoGrow_minHeight: 228,
            extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
            removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
            editorplaceholder: $translate.instant('Task text'),
            on: {
                change: function (ev) {
                    $timeout(() => {
                        if ($rootScope.editableTask && $rootScope.editableTask.text) {
                            $rootScope.editedTaskText($rootScope.editableTask.text);
                        }
                    }, 0);
                },
            },
        };
        angular.forEach($rootScope.persons, function (res) {
            res.notShown = false;
        });
        $rootScope.newTask.targetDate = '';
        $rootScope.responsiblePersonsEdit = [];
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/adding-task.html',
            size: size,
            scope: $scope,
            resolve: {
                items: function () {
                    return $scope.items;
                },
            },
        });
    };
    $scope.isInactive = function (clientObj) {
        var inactive = false;
        $scope.inactiveVacancies = [];
        angular.forEach(clientObj, function (vac) {
            if (
                vac.status == 'completed' ||
                vac.status == 'canceled' ||
                vac.status == 'deleted' ||
                vac.status == 'recommendation'
            ) {
                inactive = true;
                $scope.inactiveVacancies.push(vac);
            }
        });
        return inactive;
    };
    $rootScope.changeTabOnTask = function (val) {
        if (val == 'Task') {
            $rootScope.editableTask.type = 'Task';
        } else if (val == 'Call') {
            $rootScope.editableTask.type = 'Call';
        } else if (val == 'Meeting') {
            $rootScope.editableTask.type = 'Meeting';
        }
        $rootScope.editNameTask(true);
    };
    $rootScope.changeTabOnTaskForNewTask = function (val) {
        $rootScope.newTask.type = val.value;
        $rootScope.typeOfTasksModel = val;
        $scope.$apply();
    };
    $rootScope.changeTabOnTaskForEditableTask = function (type) {
        $rootScope.editableTask.type = type;
        $scope.$apply();
    };
    $scope.onChangeSubject = function () {
        $rootScope.emailTemplateInModalSubjectError = false;
    };
    $scope.getFirstLetters = function (str) {
        return Service.firstLetters(str);
    };
    $scope.showCandidateSentEmail = function () {
        $scope.modalInstance = $uibModal.open({
            animation: true,
            templateUrl: '../partials/modal/client-send-email.html',
            size: '',
            scope: $scope,
            resolve: {},
        });
        $scope.modalInstance.closed.then(function () {
            $rootScope.emailTemplateInModal.title = '';
            if ($rootScope.currentLang === 'en') {
                $rootScope.emailTemplateInModal = {
                    text: "Hi!<br/><br/>--<br/>Best, <br/>[[recruiter's name]]",
                };
            }
            if ($rootScope.currentLang === 'ru') {
                $rootScope.emailTemplateInModal = {
                    text: "Здравствуйте!<br/><br/>--<br/>С уважением, <br/>[[recruiter's name]]",
                };
            }
            if ($rootScope.currentLang === 'ua') {
                $rootScope.emailTemplateInModal = {
                    text: "Вітаю!<br/><br/>--<br/>З повагою, <br/>[[recruiter's name]]",
                };
            }
        });
        $scope.modalInstance.opened.then(function () {
            if ($rootScope.currentLang === 'en') {
                $rootScope.emailTemplateInModal = {
                    text: "Hi!<br/><br/>--<br/>Best, <br/>[[recruiter's name]]",
                };
            }
            if ($rootScope.currentLang === 'ru') {
                $rootScope.emailTemplateInModal = {
                    text: "Здравствуйте!<br/><br/>--<br/>С уважением, <br/>[[recruiter's name]]",
                };
            }
            if ($rootScope.currentLang === 'ua') {
                $rootScope.emailTemplateInModal = {
                    text: "Вітаю!<br/><br/>--<br/>З повагою, <br/>[[recruiter's name]]",
                };
            }
            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                .replace(/\[\[recruiter&#39;s name\]\]/g, $rootScope.me.fullName)
                .replace(
                    /\[\[recruiter's name\]\]/g,
                    $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                );
            if (Email.getDefaultMailbox()) {
                $rootScope.emailTemplateInModal.email = Email.getDefaultMailbox()[0].email;
            }
            Email.setDefaultSignature(Email.getDefaultMailbox());
        });
    };
    $scope.callbackFileForTemplate = function (resp, names) {
        $scope.fileForSave.push({
            fileId: resp,
            fileName: names,
            fileResolution: Service.getFileResolutionFromName(names),
        });
        $rootScope.fileForSave.push({
            fileId: resp,
            fileName: names,
            fileResolution: Service.getFileResolutionFromName(names),
        });
    };
    // $scope.openMenuWithCandidates = function (history) {
    //     history.showAllCandidates = !history.showAllCandidates;
    //     history.editCommentFlag = false;
    // };
    $rootScope.addEmailFromWhatSend = function (email) {
        if ($rootScope.emailThatAlreadyUsed) {
            $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                $rootScope.emailThatAlreadyUsed.email,
                email.email,
            );
        }
        $rootScope.emailTemplateInModal.email = [];
        $rootScope.emailThatAlreadyUsed = email;
        localStorage.emailThatAlreadyUsed = email.email;
        $rootScope.emailTemplateInModal.email = $rootScope.emailTemplateInModal.email + email.email;
        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
            /\[\[recruiterEmail\]\]/g,
            $rootScope.emailTemplateInModal.email,
        );
        $rootScope.emailTemplateInModal.text = Email.insertSignatures(
            $rootScope.emailTemplateInModal.text,
            email.signature,
        );
    };
    $rootScope.sentEmail = function () {
        if (isFieldsFilled()) {
            let filesIdName = {};
            if ($rootScope.fileForSave && $rootScope.fileForSave.length > 0) {
                const jsonMap = $rootScope.fileForSave.reduce((map, obj) => {
                    map[obj.fileId] = obj.fileName;
                    return map;
                }, {});
                filesIdName = JSON.stringify(jsonMap);
            } else {
                filesIdName = null;
            }
            $rootScope.loading = true;
            Mail.sendMailByTemplate(
                {
                    toEmails: $rootScope.emailToSend,
                    fullName: $scope.client.fullName,
                    email: $rootScope.emailTemplateInModal.email,
                    date: null,
                    lang: $translate.use(),
                    clientId: $scope.client.clientId,
                    template: {
                        type: $rootScope.emailTemplateInModal.type,
                        title: $rootScope.emailTemplateInModal.title,
                        text: $rootScope.emailTemplateInModal.text,
                        filesIdName,
                    },
                },
                function (resp) {
                    $rootScope.loading = false;
                    if (resp.status != 'ok') {
                        notificationService.error(
                            $filter('translate')('Error connecting integrate with email. Connect it again'),
                        );
                    } else {
                        notificationService.success($filter('translate')('Letter sent'));
                        $rootScope.closeModal();
                        $rootScope.emailToSend = null;
                        $rootScope.fileForSave = [];
                        $rootScope.emailTemplateInModal = {
                            text: "Hi!<br/><br/>--<br/>Best, <br/>[[recruiter's name]]",
                        };
                    }
                },
            );
        }
    };

    function validateMultipleEmails(email) {
        let emails = email.split(',');
        const re =
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        emails = emails.map((item) => re.test(String(item).toLowerCase()));
        return emails.every((item) => item);
    }

    $rootScope.updateClient = function () {
        $scope.showAddedLinks = false;
        $scope.showAddedFiles = false;
        $rootScope.loading = true;
        Client.one(
            { localId: $stateParams.id },
            function (resp) {
                if (angular.equals(resp.status, 'ok')) {
                    $scope.client = resp.object;
                    setTimeout(() => {
                        if ($scope.client.responsiblesPerson.length && $scope.persons.length) {
                            $scope.persons = $scope.persons.filter((person) => {
                                let includes = false;
                                $scope.client.responsiblesPerson.forEach((responsible) => {
                                    if (person.personId === responsible.personId) {
                                        includes = true;
                                    }
                                });
                                return !includes;
                            });
                        }
                        $rootScope.$$phase || $scope.$apply();
                    }, 0);

                    var iUser = null;
                    for (var i = 0; i <= $scope.persons.length - 1; i++) {
                        if ($rootScope.me.userId == $scope.persons[i].userId) {
                            iUser = $scope.persons[i];
                            $scope.persons.splice(i, 1);
                            break;
                        }
                    }
                    if (iUser) {
                        $scope.persons.unshift(iUser);
                    }

                    onSanitizeClientFields();
                    $scope.client.contactClientListActive = $scope.client.contactClientList.filter(
                        (item) => item.active,
                    );
                    $scope.client.contactClientListNotActive = $scope.client.contactClientList.filter(
                        (item) => !item.active,
                    );
                    $rootScope.client = $scope.client;
                    $scope.selectedStatus = $scope.client.state;
                    $scope.locationBeforeCustomFields = $location.$$path.replace(
                        '/clients/' + $scope.client.localId,
                        'clients',
                    );
                    $localStorage.set('previousHistoryCustomFields', $scope.locationBeforeCustomFields);
                    $rootScope.newTask.clientId = $rootScope.client.clientId;
                    $scope.updateTasks(true);
                    $rootScope.title = resp.object.name + ' | CleverStaff';
                    if ($scope.client && $scope.client.customFields) {
                        $scope.customFieldsIsExists = false;
                        $scope.client.customFields.forEach((item) => {
                            if (item.fieldValue && (item.fieldValue.value || item.fieldValue.dateTimeValue))
                                $scope.customFieldsIsExists = true;
                        });
                    }
                    if ($scope.client.files) {
                        if ($scope.client.files.length != undefined && $scope.client.files.length != 0) {
                            angular.forEach($scope.client.files, function (val) {
                                val.fileResolution = Service.getFileResolutionFromName(val.fileName);
                                if (val.url) {
                                    $scope.showAddedLinks = true;
                                }
                                if (!val.url) {
                                    $scope.showAddedFiles = true;
                                }
                                Service.initDocuments(val);
                            });
                        }
                        $rootScope.$$phase || $scope.$apply();
                    } else {
                        $scope.showAddedLinks = false;
                        $scope.showAddedFiles = false;
                    }
                    $scope.statisticObj = {
                        requestObj: { clientId: resp.object.clientId },
                        objId: resp.userId,
                        objType: 'user',
                    };
                    $location.hash('');
                    var name = '';
                    name =
                        resp.object.name != undefined && !$scope.urlTaskId
                            ? name + resp.object.name.replace(/\W+/g, '_')
                            : '';
                    $location.search($filter('transliteration')(name)).replace();
                    let pageNumber = 0;
                    let pageCount = 15;
                    $scope.tableParamsClientHistory = new ngTableParams(
                        {
                            page: 1,
                            count: 15,
                        },
                        {
                            total: 0,
                            getData: function ($defer, params) {
                                function getHistory(page, count) {
                                    if (page || count) {
                                        pageNumber = page;
                                        pageCount = count;
                                        $scope.candidateSearchOptions.page.number = page;
                                        $scope.candidateSearchOptions.page.count = count;
                                        $scope.candidateSearchOptions = {
                                            vacancyId: null,
                                            candidateId: null,
                                            clientId: $scope.client !== undefined ? $scope.client.clientId : null,
                                            page: {
                                                number: pageNumber,
                                                count: pageCount,
                                            },
                                            onlyWithComment: $scope.onlyWithComment,
                                        };
                                    } else {
                                        pageNumber = params.$params.page - 1;
                                        pageCount = params.$params.count;
                                        $scope.candidateSearchOptions = {
                                            vacancyId: null,
                                            candidateId: null,
                                            clientId: $scope.client !== undefined ? $scope.client.clientId : null,
                                            page: {
                                                number: pageNumber,
                                                count: pageCount,
                                            },
                                            onlyWithComment: $scope.onlyWithComment,
                                        };
                                        $scope.isShowMore = false;
                                        if (document.getElementById('scrollup'))
                                            document.getElementById('scrollup').style.display = 'none';
                                        if (pageNumber > 1) {
                                            $timeout(function () {
                                                $anchorScroll('mainTable');
                                            });
                                        }
                                    }

                                    $rootScope.getAllActionForCliens = () => {
                                        Service.history($scope.candidateSearchOptions, function (response) {
                                            if (page) {
                                                $scope.history = $scope.history.concat(response['objects']);
                                            } else {
                                                $scope.history = response['objects'];
                                            }
                                            response.objects.forEach((action) => {
                                                if (action.type === 'task_change_status') {
                                                    if (action.stateNew === 'open') action.stateNew = 'inwork';
                                                    if (action.stateOld === 'open') action.stateOld = 'inwork';
                                                }

                                                action.descr = action.descr.replaceAll('&nbsp;', ' ');
                                                action.descr = action.descr.replaceAll('rel="nofollow"', '');
                                                if (
                                                    action.type === 'client_message' ||
                                                    action.type === 'vacancy_message' ||
                                                    action.type === 'interview_message' ||
                                                    action.type === 'interview_edit' ||
                                                    action.type === 'interview_add' ||
                                                    action.type === 'interview_edit_date' ||
                                                    action.type === 'interview_edit_sent_email' ||
                                                    action.type === 'interview_add_sent_email' ||
                                                    action.type === 'client_change_state' ||
                                                    action.type === 'client_set_responsible'
                                                ) {
                                                    $rootScope.initTagsStyles(action);
                                                }
                                            });

                                            $scope.updateCustomStages();

                                            $scope.objectSize = response['objects'] ? response['total'] : 0;
                                            $scope.showHistory = response.objects != undefined;
                                            params.total(response['total']);
                                            $scope.paginationParams = {
                                                currentPage: $scope.candidateSearchOptions.page.number,
                                                totalCount: $scope.objectSize,
                                                allPageCount: response.allPageCount,
                                            };
                                            $defer.resolve($scope.history);
                                            $rootScope.loading = false;
                                            $scope.displayShowMore =
                                                $scope.candidateSearchOptions.page.number <
                                                params.total() / $scope.candidateSearchOptions.page.count - 1;
                                        });
                                    };
                                    $rootScope.getAllActionForCliens();
                                }

                                getHistory();
                                $scope.showMore = function () {
                                    $scope.isShowMore = true;
                                    $scope.displayShowMore = Service.dynamicTableLoading(
                                        params.total(),
                                        $scope.candidateSearchOptions.page.number,
                                        $scope.candidateSearchOptions.page.count,
                                        getHistory,
                                    );
                                };

                                $scope.previousSearchNumber = $scope.tableParamsClientHistory.page();

                                $scope.changePage = (pageNumber) => {
                                    if ($scope.isShowMore) {
                                        $scope.tableParamsClientHistory.page($scope.previousSearchNumber);
                                    } else $scope.tableParamsClientHistory.page(pageNumber);

                                    $scope.tableParamsClientHistory.reload();
                                };

                                $scope.changeAmountOfElements = (amount) => {
                                    if ($scope.tableParamsClientHistory.count() === amount) return;
                                    $scope.tableParamsClientHistory.count(amount);
                                    $scope.tableParamsClientHistory.reload();
                                };
                            },
                        },
                    );
                    $('#descr').html($scope.client.descr);
                } else {
                    notificationService.error($filter('translate')('client not found'));
                    $location.path('clients');
                }
                $rootScope.contactEmails = [];
                angular.forEach($scope.client.contactClientList, function (val) {
                    angular.forEach(val.contacts, function (valC) {
                        if (valC.type == 'email') {
                            var text = '';
                            if (val.firstName) text = text + val.firstName;
                            if (val.lastName) text = text + ' ' + val.lastName;
                            if (!isIncludesId(valC.value)) {
                                $rootScope.contactEmails.push({
                                    id: valC.value,
                                    text: text,
                                });
                            }
                        }
                    });
                });
                if ($rootScope.contactEmails.length === 1) {
                    $rootScope.emailToSend = $rootScope.contactEmails[0].id;
                }
                if ($scope.client.files) {
                    if ($scope.client.files.length != undefined && $scope.client.files.length != 0) {
                        angular.forEach($scope.client.files, function (val) {
                            if (val.url) {
                                $scope.showAddedLinks = true;
                            }
                            if (!val.url) {
                                $scope.showAddedFiles = true;
                            }
                            val.fileResolution = Service.getFileResolutionFromName(val.fileName);
                            Service.initDocuments(val);
                        });
                    }
                } else {
                    $scope.showAddedLinks = false;
                    $scope.showAddedFiles = false;
                }
                $scope.showEditFileNameFunc = function (file) {
                    file.showEditFileName = !file.showEditFileName;
                    file.showMenuEdDelFile = !file.showMenuEdDelFile;
                    $scope.showMenuEdDelFile = false;
                };
                $scope.editFileName = function (data) {
                    File.changeFileName(
                        {
                            fileId: data.fileId,
                            name: data.fileName,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                data.showEditFileName = false;
                            } else {
                                notificationService.error(resp.message);
                            }
                        },
                    );
                };
                $scope.showModalResume = function (file) {
                    Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
                };
                $scope.onDownloadFile = function (file) {
                    $rootScope.downloadFile(file.fileId, file.fileName);
                    $scope.showMenuEdDelFile = true;
                };
                $scope.MenuEdDelFile = function (file) {
                    file.showMenuEdDelFile = !$scope.showMenuEdDelFile;

                    if (!file.showMenuEdDelFile) {
                        $scope.showMenuEdDelFile = false;
                    }

                    $('body').mouseup(function (e) {
                        var element = $('.editFileMenu');
                        if ($('.editFileMenu').has(e.target).length == 0) {
                            file.showMenuEdDelFile = false;
                            $(document).off('mouseup');
                            if (!$scope.$$phase) $scope.$apply();
                        }
                    });
                };
                $scope.objectId = resp.object.clientId;
                FileInit.initFileOption($scope, 'client', undefined, $filter);
                $scope.fileForSave = [];
                $scope.linksForSave = [];
                $rootScope.fileForSave = []; /*For modal window*/

                $scope.callbackFileTemplateInClient = function (resp, names) {
                    $scope.fileForSave.push({
                        fileId: resp,
                        fileName: names,
                        fileResolution: Service.getFileResolutionFromName(names),
                    });
                    $rootScope.fileForSave.push({
                        fileId: resp,
                        fileName: names,
                        fileResolution: Service.getFileResolutionFromName(names),
                    });
                };
                $scope.removeFile = function (file) {
                    Client.removeFile(
                        {
                            clientId: $scope.client.clientId,
                            fileId: file.fileId,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                file.showMenuEdDelFile = false;
                                $('#file').val('');
                                $scope.tableParamsClientHistory.reload();
                            }
                            $rootScope.updateClient();
                        },
                    );
                    angular.forEach($scope.fileForSave, function (val, ind) {
                        if (val.attId === id) {
                            $scope.fileForSave.splice(ind, 1);
                        }
                    });
                    if ($scope.client.files.length === 0) {
                        delete $scope.client.files;
                        Client.progressUpdate($scope, false);
                    }
                };
                $scope.removeFileAttach = function (id) {
                    angular.forEach($rootScope.fileForSave, function (val, ind) {
                        if (val.fileId === id) {
                            $rootScope.fileForSave.splice(ind, 1);
                        }
                    });
                };
                $scope.callbackFile = function (resp, name) {
                    if (!$scope.client.files) {
                        $scope.client.files = [];
                    }
                    $scope.client.files.push({
                        ...resp,
                        fileResolution: Service.getFileResolutionFromName(name),
                    });
                    $scope.tableParamsClientHistory.reload();
                };
                $rootScope.removeFile = function (id) {
                    angular.forEach($rootScope.fileForSave, function (val, ind) {
                        if (val.attId === id) {
                            $rootScope.fileForSave.splice(ind, 1);
                        }
                    });
                };
                $scope.showAddLinkFunc = function () {
                    $scope.showAddLink = true;
                };
                $scope.closeAddLinkFunc = function () {
                    $scope.showAddLink = false;
                    $scope.addLinkToClient.name = '';
                    $scope.addLinkToClient.url = '';
                    $scope.addLinkErrorShow = false;
                };

                $scope.getParsedCustomStringField = function (value) {
                    if (!value) return;
                    return value.replace(/(<([^>]+)>)/gi, '');
                };

                $scope.addLinkInClient = function () {
                    if ($scope.addLinkToClient.name && $scope.addLinkToClient.url) {
                        if ($rootScope.me.recrutRole != 'client') {
                            $scope.addLinkToClient.url = $rootScope.addLinkPrefix($scope.addLinkToClient.url);
                            Client.addLink(
                                {
                                    clientId: $scope.client.clientId,
                                    name: $scope.addLinkToClient.name,
                                    url: $scope.addLinkToClient.url,
                                },
                                function (resp) {
                                    if (resp.status === 'ok') {
                                        $scope.addLinkToClient.name = '';
                                        $scope.addLinkToClient.url = '';
                                        $scope.addLinkErrorShow = false;
                                        $scope.showAddLink = false;
                                        $rootScope.updateClient();
                                        notificationService.success($filter('translate')('history_info.added_link'));
                                        $scope.tableParamsClientHistory.reload();
                                    } else {
                                        notificationService.error(resp.message);
                                    }
                                },
                            );
                        } else {
                            notificationService.error(
                                $filter('translate')('This feature is available only to administrators and recruiters'),
                            );
                        }
                    } else {
                        $scope.addLinkErrorShow = true;
                        if (!$scope.addLinkToClient.name && !$scope.addLinkToClient.url) {
                            notificationService.error($filter('translate')('Please enter a title and URL'));
                        }

                        if ($scope.addLinkToClient.name && !$scope.addLinkToClient.url) {
                            notificationService.error($filter('translate')('Please enter a URL'));
                        }

                        if (!$scope.addLinkToClient.name && $scope.addLinkToClient.url) {
                            notificationService.error($filter('translate')('Please enter a title'));
                        }
                    }
                };
                $rootScope.loading = false;
            },
            function () {
                $rootScope.loading = false;
            },
        );
    };

    function validateEmail(email) {
        const re =
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    function isFieldsFilled() {
        if (!$rootScope.emailToSend || $rootScope.emailToSend.trim().length === 0) {
            notificationService.error($filter('translate')('Enter Email of recipient'));
            $rootScope.emailToSendError = true;
            return false;
        } else $rootScope.emailToSendError = false;

        if ($rootScope.emailToSend.split(',').length === 1) {
            if (!validateEmail($rootScope.emailToSend)) {
                notificationService.error($filter('translate')('Wrong Email of recipient'));
                $rootScope.emailToSendError = true;
                return false;
            } else $rootScope.emailToSendError = false;
        } else {
            if (!validateMultipleEmails($rootScope.emailToSend)) {
                notificationService.error($filter('translate')('Wrong Email of recipient'));
                $rootScope.emailToSendError = true;
                return false;
            } else $rootScope.emailToSendError = false;
        }

        if (!$rootScope.emailTemplateInModal.title || $rootScope.emailTemplateInModal.title.trim().length === 0) {
            notificationService.error($filter('translate')('Enter the subject line'));
            $rootScope.emailTemplateInModalSubjectError = true;
            return false;
        } else $rootScope.emailTemplateInModalSubjectError = false;

        return true;
    }

    function initController() {
        setInitialState();
        $(window).scrollTop(0);
        $('.withoutTimeTask')
            .datetimepicker({
                format: 'dd/mm/yyyy hh:00',
                startView: 2,
                minView: 1,
                autoclose: true,
                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                language: $translate.use(),
                initialDate: new Date(),
                startDate: new Date(),
            })
            .on('changeDate', function (data) {
                $rootScope.editableTask.targetDate = $('.withoutTimeTask').datetimepicker('getDate');
                $scope.roundMinutes($rootScope.editableTask.targetDate);
                if ($rootScope.editableTask.targetDate) {
                    Task.changeTargetDate(
                        {
                            taskId: $rootScope.editableTask.taskId,
                            date: $rootScope.editableTask.targetDate,
                        },
                        function (resp) {
                            $scope.updateTasks();
                            $scope.tableParamsClientHistory.reload();
                        },
                    );
                }
            })
            .on('hide', function () {
                if ($('.withoutTimeTask').val() == '') {
                    $rootScope.editableTask.date = '';
                }
                $('.withoutTimeTask').blur();
            });
        $('.ui.dropdown').dropdown();
        Task.task($scope, $rootScope, $location, $translate, $uibModal);
        Person.getAllPersons(
            function (resp) {
                $scope.associativePerson = resp.object;
                angular.forEach($scope.associativePerson, function (val, key) {
                    if (angular.equals(resp.status, 'ok')) {
                        if (val.status === 'A' && !val.hideClients) {
                            $scope.persons.push($scope.associativePerson[key]);
                        }
                    }
                });
                $rootScope.persons = $scope.persons;

                $rootScope.persons = Person.getPersonListWithMe($rootScope.persons);
            },
            function (error) {
                console.error(error);
            },
        );
        $rootScope.updateClient();
        if ($rootScope.me.recrutRole == 'client') {
            Person.getAllPersons(
                function (resp) {
                    $scope.tableParams = new ngTableParams(
                        {
                            page: 1,
                            count: 15,
                        },
                        {
                            total: 10,
                            getData: function ($defer, params) {
                                $scope.usersFound = false;
                                $scope.personAll = [];
                                var persons = angular.copy(resp.object);
                                angular.forEach(persons, function (val, key) {
                                    if (
                                        ($scope.regionId === undefined || $scope.regionId === null) &&
                                        persons[key].status === 'A'
                                    ) {
                                        $scope.personAll.push(persons[key]);
                                        $scope.usersFound = true;
                                    } else if (persons[key].region !== undefined) {
                                        if (
                                            $scope.regionIdType == 'country' &&
                                            persons[key].region.country == $scope.regionId &&
                                            persons[key].status === 'A'
                                        ) {
                                            $scope.personAll.push(persons[key]);
                                            $scope.usersFound = true;
                                        } else if (
                                            $scope.regionIdType == 'city' &&
                                            $scope.regionId == persons[key].region.regionId &&
                                            persons[key].status === 'A'
                                        ) {
                                            $scope.personAll.push(persons[key]);
                                            $scope.usersFound = true;
                                        }
                                    }
                                });
                                var personSS = [];
                                angular.forEach($scope.personAll, function (resp) {
                                    if (resp.recrutRole == 'client') {
                                        personSS.push(resp);
                                    }
                                });
                                $defer.resolve($filter('orderBy')(personSS, params.orderBy()));
                            },
                        },
                    );
                },
                function (error) {
                    console.error(error);
                },
            );
        }
        FileInit.initVacancyTemplateFileOption($scope, '', '', false, $filter);
        (function getPersonEmails() {
            Email.getMailboxes().then(
                (resp) => {
                    let isPermittedEmail = resp.filter((email) => email.personalMailing);
                    $rootScope.me.emails = isPermittedEmail;
                    Email.setDefaultMailbox(isPermittedEmail);
                    if (!isPermittedEmail && resp.length) $scope.noAllowedMails = true;
                },
                (error) => notificationService.error(error),
            );
        })();
    }

    function setInitialState() {
        $rootScope.isNeedToResetSearch = $rootScope.previousLocation !== '/clients';
        delete $rootScope.candidate;
        $scope.status = Client.getStateNew();
        $scope.statusNames = $scope.status && $scope.status.map((status) => status.value);
        $scope.contactLimit = 3;
        $scope.vacancyCounter = 0;
        $scope.historyLimit = 15;
        $scope.serverAddress = serverAddress;
        $scope.todayDate = new Date().getTime();
        $rootScope.newDate = new Date();
        $scope.showAddedLinks = false;
        $scope.showAddedFiles = false;
        $scope.linked = false;
        $scope.showEditFileName = false;
        $scope.showMenuEdDelFile = false;
        $scope.showAddLink = false;
        $scope.onlyWithComment = false;
        $rootScope.changeResponsibleInClient = {
            id: '',
            comment: '',
            text: null,
            name: null,
        };
        $scope.vacancy = null;
        $scope.onlyComments = true;
        $rootScope.clickedSaveClientStatus = false;
        $scope.showAllCandidates = false;
        $scope.customStringValueClassToggle = false;
        $scope.showClientMessageShort = true;
        $scope.client = null;

        $scope.urlTaskId = $location.$$absUrl.split('?task=')[1];

        $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);
        $rootScope.newTask = {
            text: '',
            title: '',
            clientId: '',
            targetDate: '',
            responsibleIds: [],
            type: 'Task',
        };
        $scope.addLinkToClient = {
            name: '',
            url: '',
        };
        $scope.showNotActive = false;
        $scope.persons = [];
        $scope.personId = $rootScope.me.personId;
        $rootScope.commentClient = {
            comment: '',
            loading: false,
        };

        $rootScope.openSkype = (val) => {
            const a = document.createElement('a');
            a.href = `skype:${val}?chat`;
            a.click();
        };

        $rootScope.changeStatusInClient = {
            status: '',
            comment: '',
            header: '',
            placeholder: '',
            status_old: '',
            type: 'edit',
        };
        $rootScope.emailTemplateInModal = {
            text: "Hi!<br/><br/>--<br/>Best, <br/>[[recruiter's name]]",
        };

        if (!$rootScope.customStages) {
            vacancyStages.get(function (resp) {
                $rootScope.customStages = resp.object.interviewStates;
            });
        }
    }

    $scope.showFilePreview = function (history) {
        const file = {
            fileName: history.descr,
            fileId: JSON.parse(history.data).fileId,
        };
        Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
    };

    function onSanitizeClientFields(client) {
        if (!client) return;

        if (client.descr) client.descr = Service.sanitizeStringFromXSS(client.descr);
        if (client.customFields)
            client.customFields.forEach((customField) => {
                if (customField.type === 'string' && customField.fieldValue) {
                    customField.fieldValue.value = Service.sanitizeStringFromXSS(customField.fieldValue.value);
                }
            });
    }

    function isIncludesId(id) {
        let includes = false;
        $rootScope.contactEmails.forEach((item) => {
            if (item.id === id) includes = true;
        });
        return includes;
    }

    initController();
}

controller.controller('ClientOneController', [
    'serverAddress',
    '$scope',
    '$stateParams',
    '$location',
    'Client',
    'Service',
    'Contacts',
    'Vacancy',
    'vacancyStages',
    '$rootScope',
    'notificationService',
    '$filter',
    'ngTableParams',
    'Person',
    'Action',
    'Task',
    'CacheCandidates',
    'File',
    'FileInit',
    '$translate',
    '$uibModal',
    '$route',
    'Mail',
    '$localStorage',
    '$timeout',
    '$anchorScroll',
    'Email',
    '$sce',
    ClientOneController,
]);
