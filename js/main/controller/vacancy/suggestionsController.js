component.component('vacancySuggestions', {
    templateUrl: 'partials/vacancy/suggestions.html',
    require: {
        parent: '^vacancyComponent',
    },
    controller: function (
        $rootScope,
        $scope,
        Vacancy,
        $location,
        notificationService,
        $filter,
        vacancySuggestions,
        $uibModal,
        Service,
        $stateParams,
        RegionInputService,
        serverAddress,
        Candidate,
        allRolesList,
        CandidatesSlider,
    ) {
        const vm = this;

        const setCandidatesSlider = function () {
            CandidatesSlider.setParamsForSlider('suggestions', vm.candidateData, vm.candidateData.length, 1, {
                page: { number: 0, count: vm.candidateData.length },
            });

            $rootScope.showSlider = true;
        };

        vm.sliderId = CandidatesSlider.getSliderId();

        vm.closeModal = function () {
            vm.modalInstance.close();
        };

        function getCountries() {
            RegionInputService.regionRequests().getAllCountries(
                { lang: $rootScope.currentLang },
                (resp) => {
                    if (resp.status === 'ok') {
                        vm.countriesOptions = resp.objects.map((item) => ({ label: item, value: item }));
                    }
                },
                (err) => {},
            );
        }

        function getCities(country) {
            if (!country) return;
            RegionInputService.regionRequests().autocompleteCity({ country }, function (resp) {
                if (resp.status === 'ok') {
                    vm.citiesOptions = RegionInputService.formatCitiesOptions(resp.object[country]);
                }
            });
        }

        vm.setSuggestionTab = function (tab) {
            vm.suggestionTab = tab;
            vm.hideTab = true;
            setTableParams();
        };

        vm.redirectToTable = function (tab = '') {
            setTableParams();
            vm.suggestionTab = tab;
            vm.hideTab = true;
        };

        vm.replaceEverythingExceptNumbers = function (field, value) {
            if (vm.vacancy[field]) {
                vm.vacancy[field] = value.replace(/[^+\d]/g, '');
            }
        };

        vm.onChangeEmploymentType = (value) => {
            vm.setEmploymentType = value;
            vm.employmentError = false;
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeCurrency = (newValue) => {
            vm.vacancy.currency = newValue;
            $scope.$$phase || $scope.$apply();
        };

        vm.changeSalaryVisibility = (event) => {
            vm.hideSalary = event.target.checked;
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeCountry = (newValue) => {
            vm.vacancy.country = newValue;
            getCities(vm.vacancy.country?.value);
            vm.regionError = false;
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeCity = (newValue) => {
            vm.vacancy.city = newValue;
            $scope.$$phase || $scope.$apply();
        };

        vm.saveVacancyFromSuggestionLocation = function () {
            if (vm.vacancy.country) {
                vm.sendRegion = {
                    area: vm.vacancy.city && vm.vacancy.city.label,
                    city: vm.vacancy.city && vm.vacancy.city.label,
                    country: vm.vacancy.country.label,
                    fromGoogle: vm.vacancy.city && false,
                    fullName: vm.vacancy.city && vm.vacancy.country.label + ', ' + vm.vacancy.city.label,
                    googlePlaceId: vm.vacancy.city && { googlePlaceId: vm.vacancy.city.value.googlePlaceId },
                };
            }

            vm.vacancy.fieldValues = [];
            if (vm.vacancy.customFields) {
                angular.forEach(vm.vacancy.customFields, (val) => {
                    if (val.fieldValue) {
                        if (angular.equals(val.type, 'string')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                value: val.fieldValue.value,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'select')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                value: val.fieldValue.value,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'date')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                dateTimeValue: val.fieldValue.dateTimeValue,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'datetime')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                dateTimeValue: val.fieldValue.dateTimeValue,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                    }
                });
            }

            vacancySuggestions
                .saveVacancyForRecommendation(vm.vacancy.vacancyId, vm.sendRegion, null, null, null, null, null)
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            vm.vacancy.region = vm.sendRegion;
                            getSuggestions();
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.regionError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.saveVacancyFromSuggestionSalary = function () {
            if (vm.vacancy.salaryTo < vm.vacancy.salaryFrom) {
                notificationService.error($filter('translate')('Please enter the correct salary values'));
                return;
            }

            vm.vacancy.fieldValues = [];
            if (vm.vacancy.customFields) {
                angular.forEach(vm.vacancy.customFields, (val) => {
                    if (val.fieldValue) {
                        if (angular.equals(val.type, 'string')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                value: val.fieldValue.value,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'select')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                value: val.fieldValue.value,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'date')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                dateTimeValue: val.fieldValue.dateTimeValue,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                        if (angular.equals(val.type, 'datetime')) {
                            vm.vacancy.fieldValues.push({
                                objType: 'vacancy',
                                fieldValueId: val.fieldValue.fieldValueId,
                                dateTimeValue: val.fieldValue.dateTimeValue,
                                field: {
                                    fieldId: val.fieldId,
                                },
                            });
                        }
                    }
                });
            }

            vacancySuggestions
                .saveVacancyForRecommendation(
                    vm.vacancy.vacancyId,
                    null,
                    vm.vacancy.currency?.label,
                    vm.vacancy.salaryFrom,
                    vm.vacancy.salaryTo,
                    null,
                    null,
                    null,
                    null,
                    null,
                    vm.hideSalary,
                )
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            getSuggestions();
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.salaryError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.saveEmploymentTypeFromSuggestion = function () {
            vacancySuggestions
                .saveVacancyForRecommendation(vm.vacancy.vacancyId, null, null, null, null, vm.setEmploymentType, null)
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            vm.vacancy.employmentType = vm.setEmploymentType.value;
                            getSuggestions();
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.employmentError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.addNewLanguageField = () => {
            vm.setLanguages.push({
                name: null,
                level: null,
            });
            $scope.$$phase || $scope.$apply();
        };

        vm.removeLanguageField = (index) => {
            vm.setLanguages.splice(index, 1);
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeLanguage = (newValue, index) => {
            vm.setLanguages[index]['name'] = newValue;
            vm.languagesError = false;
            vm.languagesOptions = vm.languagesOptions.filter(({ value }) => value.english !== newValue.value.english);
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeLanguageLevel = (newValue, index) => {
            vm.setLanguages[index]['level'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        vm.saveLanguagesFromSuggestion = function () {
            vm.sendLanguages = vm.setLanguages.map((item) => {
                return {
                    level: item?.level?.value,
                    name: item?.name?.label,
                };
            });
            if (vm.setLanguages.length && vm.sendLanguages[0].name) {
                vm.languagesSelect = true;
            } else {
                vm.languagesSelect = false;
            }
            vacancySuggestions
                .saveVacancyForRecommendation(
                    vm.vacancy.vacancyId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    vm.sendLanguages.length && vm.sendLanguages[0].name ? vm.sendLanguages : null,
                )
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            getSuggestions();
                            vm.vacancy.languages = vm.setLanguages;
                            vm.setLanguages = [];
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.languagesError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.saveExpFromSuggestion = function () {
            if (vm.setExpirience) {
                vm.experienceSelect = true;
            } else {
                vm.experienceSelect = false;
            }
            vacancySuggestions
                .saveVacancyForRecommendation(
                    vm.vacancy.vacancyId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    vm.setExpirience,
                )
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            getSuggestions();
                            vm.vacancy.experience = vm.setExpirience.value;
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.expError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.changeRole = (value) => {
            vm.setRole = value;
            vm.roleError = false;
            $scope.$apply();
        };

        vm.changeExp = (value) => {
            vm.setExpirience = value;
            vm.expError = false;
            $scope.$apply();
        };

        vm.saveRoleFromSuggestion = (value) => {
            if (vm.setRole) {
                vm.roleSelect = true;
            } else {
                vm.roleSelect = false;
            }
            vacancySuggestions
                .saveVacancyForRecommendation(
                    vm.vacancy.vacancyId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    vm.setRole ? vm.setRole.label : null,
                )
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            getSuggestions();
                            vm.vacancy.role = vm.setRole;
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            vm.roleError = true;
                            $scope.$apply();
                            notificationService.error(resp.message);
                        } else if (resp.status === 'error') {
                            vm.roleError = true;
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.closeModalSkip = function () {
            vm.closeModal();
        };

        vm.updateCandidateCount = function () {
            Vacancy.onGetCounts({
                vacancyId: vm.vacancy.vacancyId,
                ...($rootScope.currentSelectScope === 'onlyMy' && {
                    userId: $rootScope.me.userId,
                }),
            }).then((resp) => {
                $rootScope.countCandidates =
                    resp.objects.length > 0
                        ? resp.objects.map((val) => val.count).reduce((prev, curr) => prev + curr, 0)
                        : 0;
            });
        };

        vm.saveCandidateFromSuggestions = function (candidate) {
            $rootScope.loading = true;
            vacancySuggestions
                .addCandidateToVacancy({
                    vacancyId: vm.vacancy.vacancyId,
                    position: vm.vacancy.position,
                    candidateId: candidate.candidateId,
                    comment: '',
                    interviewState: 'longlist',
                    isFromAdvice: true,
                    date: null,
                })
                .then(
                    (resp) => {
                        setTimeout(() => {
                            $scope.$apply();
                            vm.candidatesAddList.splice(vm.candidatesAddList.indexOf(candidate), 1);
                            if (vm.candidatesAddList.length === 0) {
                                if (vm.addListLength) {
                                    notificationService.success($filter('translate')('Candidates added in vacancy'));
                                } else {
                                    notificationService.success($filter('translate')('Candidate added in vacancy'));
                                }
                                vm.updateCandidateCount();
                                setTableParams();
                                vm.checkAllCandidates = false;
                            }
                        }, 1000);
                    },
                    (error) => {
                        notificationService.error(error.message);
                    },
                );
        };

        vm.addCandidateToVacancy = function (empty, candidate) {
            candidate.added = !candidate.added;
            if (candidate.added) {
                vm.candidatesAddList.push(candidate);
            } else {
                vm.candidatesAddList.splice(vm.candidatesAddList.indexOf(candidate), 1);
            }
            vm.checkAllCandidates = vm.candidateData.length === vm.candidatesAddList.length;
            $scope.$apply();
        };

        vm.goToAdvancedSearchByCandidates = function () {
            $rootScope.candidateAdvSearchDTO = {
                languages: vm.languagesSelect && vm.vacancy.languages.length ? vm.vacancy.languages : null,
                position: vm.positionSelect ? vm.vacancy.position : null,
                salary: vm.salarySelect ? vm.vacancy.salaryTo : null,
                employmentType: vm.employmentTypeSelect ? vm.vacancy.employmentType : null,
                experience: vm.experienceSelect && vm.vacancy.experience ? vm.vacancy.experience : null,
                skills: vm.skillsSelect && vm.vacancy.skills.length ? vm.vacancy.skills : null,
                region: vm.locationSelect ? vm.vacancy.region : null,
            };

            $rootScope.redirectToSearch = true;
            $rootScope.searchFromAdvice = true;
            $location.path('/candidates');
        };

        vm.selectLocation = () => {
            if (!vm.vacancy.region || vm.vacancy.region == undefined) {
                getCountries();
                !vm.locationSelect && checkLocationModal();
            } else {
                vm.locationSelect = !vm.locationSelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.selectSalary = () => {
            if (!vm.vacancy.salaryTo && !vm.vacancy.salaryFrom) {
                vm.vacancyEmptyRequiredFields = checkRequiredFieldsCompletion(vm.vacancy).emptyFields;
                vm.isSalaryNotFilled = vm.vacancyEmptyRequiredFields.some(
                    (item) => item === 'salaryFrom' || item === 'salaryTo',
                );
                openSuggestionModal();
            } else {
                vm.salarySelect = !vm.salarySelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.selectEmploymentType = () => {
            if (!vm.vacancy.employmentType) {
                checkEmploymentTypeModal();
            } else {
                vm.employmentTypeSelect = !vm.employmentTypeSelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.selectLanguages = () => {
            if (!vm.vacancy.languages.length) {
                Service.onGetLanguagesOptionsCached();
                checkLanguagesModal();
            } else {
                vm.languagesSelect = !vm.languagesSelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.addNewSkillField = () => {
            vm.allSkills.push({
                skill: undefined,
                experience: {
                    label: 'e00_no_experience',
                    value: 'e00_no_experience',
                },
                mustHave: true,
            });
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeSkill = (newValue, index) => {
            if ($scope.errorFields?.skills && newValue.value) $scope.errorFields.skills = false;
            if (newValue.isNew) newValue.value = { skill: newValue.value };
            newValue['new'] = newValue?.isNew || false;
            vm.allSkills[index]['skill'] = newValue;
            vm.skillsError = false;
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeSkillExperience = (newValue, index) => {
            vm.allSkills[index]['experience'] = newValue;
            $scope.$$phase || $scope.$apply();
        };

        vm.onChangeSkillMustHave = (event, index) => {
            vm.allSkills[index]['mustHave'] = !event.target.checked;
            $scope.$$phase || $scope.$apply();
        };

        vm.removeSkillField = (index) => {
            vm.allSkills.splice(index, 1);
            $scope.$$phase || $scope.$apply();
        };

        vm.selectSkills = () => {
            Vacancy.onValidateVacancy({ position: vm.vacancy.position }).then((resp) => {
                if (resp.status === 'ok') {
                    if (resp.object.industry) {
                        vm.industry = resp.object.industry;
                        vm.category = resp.object.category;
                    }
                } else {
                    console.error(resp.message);
                }
            });

            Vacancy.onGetSkillsByCategory(vm.category).then((resp) => {
                vm.skillsForCategory = resp.objects.filter((skill) => skill.type === 'general');
                vm.customSkills = resp.objects.filter((skill) => skill.type === 'custom');
                $rootScope.skills = resp.objects;
                $rootScope.$emit('putSkills', {
                    skills: vm.skillsForCategory,
                    customSkills: vm.customSkills,
                });
                vm.skillsForCategory.forEach((item) => {
                    vm.skillsObject[item.skill.toLowerCase()] = item.skillId;
                });

                vm.skillsOptions = [
                    {
                        label: 'systemSkillsGroup',
                        options: vm.skillsForCategory?.map((skill) => ({ label: skill.skill, value: skill })),
                    },
                    {
                        label: 'customSkillsGroup',
                        options: vm.customSkills?.map((skill) => ({ label: skill.skill, value: skill })),
                    },
                ];
            });

            if (!vm.vacancy.skills || vm.vacancy.skills.length === 0) {
                checkSkillsModal();
            } else {
                vm.skillsSelect = !vm.skillsSelect;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        vm.saveSkillsSuggestion = function () {
            if (vm.allSkills.length > 0 && vm.allSkills[0].skill) {
                vm.sendSkills = vm.allSkills.map((item) => {
                    return {
                        skillId: item.skill.value.skillId || undefined,
                        experience: item.experience.value,
                        mustHave: item.mustHave,
                        type: item.skill.value.type || 'custom',
                    };
                });
            }

            if (vm.allSkills.length && vm.allSkills[0].skill) {
                vm.skillsSelect = true;
            } else {
                vm.skillsSelect = false;
            }

            vacancySuggestions
                .saveVacancyForRecommendation(
                    vm.vacancy.vacancyId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    vm.sendSkills,
                )
                .then(
                    (resp) => {
                        if (resp.status === 'ok') {
                            notificationService.success(
                                $filter('translate')('vacancy_save_1') +
                                    vm.vacancy.position +
                                    $filter('translate')('vacancy_save_2'),
                            );
                            vm.closeModal();
                            getSuggestions();
                            vm.vacancy.skills = vm.allSkills;
                            vm.allSkills = [];
                        }
                        if (resp.message === 'Пустые обязательные поля!') {
                            notificationService.error(resp.message);
                            vm.skillsError = true;
                            $scope.$apply();
                        } else if (resp.status === 'error') {
                            notificationService.error(resp.message);
                        }
                    },
                    (error) => notificationService.error(error),
                );
        };

        vm.selectExperience = function () {
            if (!vm.vacancy.experience) {
                checkExpirienceModal();
            } else {
                vm.experienceSelect = !vm.experienceSelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.selectRole = function () {
            if (vm.vacancy.industry == 'IT' && !vm.vacancy.role) {
                checkRoleModal();
            } else {
                vm.roleSelect = !vm.roleSelect;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        vm.saveDefaultParamsForAutoSelection = async function () {
            //validation
            vm.params = [
                { position: vm.positionSelect },
                { location: vm.locationSelect },
                { salary: vm.salarySelect },
                { employmentType: vm.employmentTypeSelect },
                { languages: vm.languagesSelect },
                { skills: vm.skillsSelect },
                { experience: vm.experienceSelect },
                { role: vm.roleSelect },
            ];
            const validationParams = [];
            vm.params.map((param) => Object.entries(param).forEach(([key, value]) => validationParams.push(value)));
            let validate = validationParams.some((param) => param === true);
            if (validate) {
                const payload = {
                    position: vm.positionSelect,
                    location: vm.locationSelect,
                    salary: vm.salarySelect,
                    employmentType: vm.employmentTypeSelect,
                    languages: vm.languagesSelect,
                    skills: vm.skillsSelect,
                    experience: vm.experienceSelect,
                    role: vm.roleSelect,
                    vacancyId: vm.vacancy.vacancyId,
                };
                try {
                    const response = fetch(serverAddress + '/advice/setDefault', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });
                    const data = await response;
                    if (data.status === 200) {
                        vm.setSuggestionTab('');
                        notificationService.success($filter('translate')('Default-settings-save'));
                        vm.hideTab = false;
                    }
                } catch (error) {
                    console.error('error', error);
                }
            } else {
                notificationService.error($filter('translate')('save with at least one specified parameter'));
            }
        };

        vm.saveParamsForAutoSelection = async function () {
            //validation
            vm.params = [
                { position: vm.positionSelect },
                { location: vm.locationSelect },
                { salary: vm.salarySelect },
                { employmentType: vm.employmentTypeSelect },
                { languages: vm.languagesSelect },
                { skills: vm.skillsSelect },
                { experience: vm.experienceSelect },
                { role: vm.roleSelect },
            ];
            const validationParams = [];
            vm.params.map((param) => Object.entries(param).forEach(([key, value]) => validationParams.push(value)));
            let validate = validationParams.some((param) => param === true);

            if (validate) {
                const payload = {
                    position: vm.positionSelect,
                    location: vm.locationSelect,
                    salary: vm.salarySelect,
                    employmentType: vm.employmentTypeSelect,
                    languages: vm.languagesSelect,
                    skills: vm.skillsSelect,
                    experience: vm.experienceSelect,
                    role: vm.roleSelect,
                    vacancyId: vm.vacancy.vacancyId,
                };
                try {
                    const response = fetch(serverAddress + '/advice/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });
                    const data = await response;
                    if (data.ok) {
                        notificationService.success($filter('translate')('Changes are saved'));
                        vm.setSuggestionTab('');
                        vm.hideTab = false;
                    }
                } catch (error) {
                    console.error('error', error);
                }
            } else {
                notificationService.error($filter('translate')('save with at least one specified parameter'));
            }
        };

        vm.saveCheckedCandidates = function () {
            vm.addListLength = vm.candidatesAddList.length > 1;
            angular.forEach(vm.candidatesAddList, function (candidate) {
                vm.saveCandidateFromSuggestions(candidate);
            });
        };

        vm.sortCandidatesBy = (head) => {
            if (head !== vm.SuggestionsSortCriteria) {
                vm.SuggestionsSortCriteria = head;
                vm.reverseSort = true;
            } else {
                vm.reverseSort = !vm.reverseSort;
            }
        };

        vm.closeModal = function () {
            vm.modalInstance.close();
        };

        function translateLanguages() {
            Service.onGetLanguagesCached().then((languages) => {
                angular.forEach(vm.candidates, function (candidate) {
                    if (candidate.languages) {
                        angular.forEach(candidate.languages, function (lang) {
                            if (lang.name != undefined) {
                                angular.forEach(languages, function (item) {
                                    if (item.key == lang.name) {
                                        lang.name = $filter('translateLangs')(item.translation);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        }

        const tableWrapper = document.querySelector('table.table');
        const wandifyBorder = document.querySelector('.wandify-border');
        const cannonBorder = document.querySelector('.job-cannon-border');

        const setWandifyBorder = (wandifyNumber) => {
            if (!wandifyNumber) return;

            const top = tableWrapper.querySelector('thead').clientHeight;

            const wandifyRows = tableWrapper.querySelectorAll('tbody tr.wandify-row');
            const wandifyHeight = Array.from(wandifyRows).reduce((acc, current) => acc + current.offsetHeight, 0);

            wandifyBorder.style.display = 'block';
            wandifyBorder.style.top = top + 'px';
            wandifyBorder.style.width = tableWrapper.clientWidth + 'px';
            wandifyBorder.style.height = wandifyHeight - 1 + 'px';
        };

        const setJobCannonBorder = (cannonNumber) => {
            if (!cannonNumber) return;

            const firstJobCannonRow = tableWrapper.querySelector('tbody tr.job-cannon-row');
            const top = firstJobCannonRow.offsetTop;

            const jobCannonRows = tableWrapper.querySelectorAll('tbody tr.job-cannon-row');
            const jobCannonHeight = Array.from(jobCannonRows).reduce((acc, current) => acc + current.offsetHeight, 0);

            cannonBorder.style.display = 'block';
            cannonBorder.style.top = top + 'px';
            cannonBorder.style.height = jobCannonHeight + 'px';
            cannonBorder.style.width = tableWrapper.clientWidth + 'px';
        };

        vm.refreshAfterEditAdvice = (value) => {
            getData(value);
        };

        function getData(adiveRefresh) {
            $rootScope.loading = true;
            fetch(serverAddress + `/advice?vacancyId=${vm.vacancy.vacancyId}`)
                .then((response) => {
                    return response.json();
                })
                .then((data) => {
                    vm.positionSelect = data.object.position;
                    vm.locationSelect = data.object.location;
                    vm.salarySelect = data.object.salary;
                    vm.employmentTypeSelect = data.object.employmentType;
                    vm.languagesSelect = data.object.languages;
                    vm.skillsSelect = data.object.skills;
                    vm.experienceSelect = data.object.experience;
                    vm.roleSelect = data.object.role;
                });
            vacancySuggestions
                .getSuggestions({
                    vacancyId: vm.vacancy.vacancyId,
                })
                .then(
                    (resp) => {
                        const wandifyCandidates = resp.object?.wandifyAdvices || [];
                        const jobCannonCandidates = resp.object?.jobCannonAdvices || [];
                        vm.candidates = [...wandifyCandidates, ...jobCannonCandidates, ...resp.object?.advices];
                        vm.WandifyCount = wandifyCandidates?.length;
                        vm.JobCannonCount = jobCannonCandidates?.length;

                        if (jobCannonCandidates?.length) {
                            jobCannonCandidates.forEach((candidate) => {
                                candidate['jobCannon'] = true;
                                candidate['jobCannonOpened'] = candidate.isOpen;
                            });
                        }

                        if (wandifyCandidates?.length) {
                            wandifyCandidates.forEach((candidate) => {
                                candidate['wandify'] = true;
                            });
                        }

                        translateLanguages();
                        angular.forEach(vm.candidates, function (cand) {
                            cand.candidateIdObj = {
                                localId: cand.localId,
                            };
                        });
                        vm.checkAllCandidates = false;
                        vm.parent.setSuggestionTabCounter(resp);
                        vm.suggestionsLength.exact = filterCandidatesByMatching(vm.candidates, true).length;
                        vm.suggestionsLength.suitable = filterCandidatesByMatching(vm.candidates, false).length;
                        if (vm.candidatesAddList.length) {
                            angular.forEach(vm.candidatesAddList, function (candidate) {
                                angular.forEach(vm.candidates, function (candidate_2) {
                                    if (candidate.localId == candidate_2.localId) {
                                        candidate_2.added = true;
                                    }
                                });
                            });
                        }
                        if (vm.suggestionTab === 'settings') {
                            // vm.suggestionTitle = setSuggestionTitle();
                        } else {
                            $scope.objectSize = vm.candidates !== undefined ? vm.candidates.length : 0;
                        }

                        setTimeout(() => {
                            vm.isGetLanguages = false;
                            $rootScope.loading = false;
                        }, 1000);

                        vm.candidateData = vm.candidates;
                        vm.showTitle = true;

                        if (adiveRefresh) {
                            const index = vm.candidateData.findIndex((item) => item.candidateId === adiveRefresh);
                            const findCand = vm.candidateData.find((item) => item.candidateId === adiveRefresh);
                            const tbody = document.querySelector('.tbodyCandidates').children[index];
                            const scrollContent = document.querySelector('.suggested-candidates__body');
                            scrollContent.scrollTo(0, tbody?.offsetTop);
                        }

                        setCandidatesSlider();

                        $rootScope.$$phase || $scope.$apply();
                    },
                    (error) => {
                        $rootScope.loading = false;
                        vm.isGetLanguages = false;
                        notificationService.error(error.message);
                        $rootScope.$$phase || $scope.$apply();
                    },
                )
                .then(() => {
                    setTimeout(() => {
                        if (vm.WandifyCount) {
                            // setWandifyBorder(vm.WandifyCount);
                        }
                        if (vm.JobCannonCount) {
                            setJobCannonBorder(vm.JobCannonCount);
                        }

                        // if (vm.WandifyCount || vm.JobCannonCount) {
                        if (vm.JobCannonCount) {
                            watchTableResize();
                        }
                    });
                })
                .finally(() => {
                    $rootScope.loading = false;
                    vm.isGetLanguages = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        }

        let tableObserver;
        function watchTableResize() {
            if (!tableWrapper) return;

            tableObserver = new ResizeObserver((entries) => {
                for (let entry of entries) {
                    requestAnimationFrame(() => {
                        // setWandifyBorder(vm.WandifyCount);
                        setJobCannonBorder(vm.JobCannonCount);
                    });
                }
            });

            tableObserver.observe(tableWrapper);
        }

        function setTableParams() {
            vm.isGetLanguages = true;
            getData();
        }

        function getSuggestions() {
            $rootScope.loading = true;
            vacancySuggestions.getSuggestions({ vacancyId: vm.vacancy.vacancyId }).then(
                (resp) => {
                    $rootScope.loading = false;
                    $scope.$apply();
                },
                (error) => {
                    $rootScope.loading = false;
                    $scope.$apply();
                    notificationService.error(error.message);
                },
            );
        }

        vm.addAllCandidates = function () {
            vm.checkAllCandidates = !vm.checkAllCandidates;
            angular.forEach(vm.candidateData, function (candidate) {
                if (vm.checkAllCandidates) {
                    if (candidate.jobCannon && !candidate.jobCannonOpened) return;

                    if (!candidate.added && candidate.adviceType !== 'has') {
                        vm.candidatesAddList.push(candidate);
                        candidate.added = true;
                    }
                } else {
                    vm.candidatesAddList.splice(vm.candidatesAddList.indexOf(candidate), 1);
                    candidate.added = false;
                }
            });
            $scope.$apply();
        };

        function filterCandidatesByMatching(candidates, matchingType) {
            return candidates.filter((candidate) => {
                return candidate.exactlyAppropriate === matchingType;
            });
        }

        function checkRequiredFieldsCompletion(
            object,
            requiredFields = vacancySuggestions.getVacancyRequiredFields(vm.vacancy),
        ) {
            let emptyFields = [];
            let emptyFieldsNoRequired;

            Object.keys(object).forEach((key) => {
                requiredFields.forEach((field) => {
                    if (key === field && !object[key]) {
                        if (emptyFields.indexOf(key) === -1) {
                            emptyFields.push(key);
                        }
                    }
                    if (!object[field]) {
                        if (emptyFields.indexOf(field) === -1) {
                            emptyFields.push(field);
                        }
                    }
                    if (key === 'region' && object[key] && !object[key].city) {
                        if (emptyFields.indexOf(key) === -1) {
                            emptyFields.push(key);
                        }
                    }
                });
            });

            emptyFieldsNoRequired = emptyFields.filter((item) => item !== 'salaryFrom' && item !== 'salaryTo');

            return {
                emptyFields: emptyFields,
                invalid: Boolean(emptyFields.length),
                invalidWithNoRequired: Boolean(emptyFieldsNoRequired.length),
            };
        }

        function openSuggestionModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-suggestion-check-fields.html',
                size: '',
                scope: $scope,
            });

            vm.modalInstance.result.then(() => {
                if (vm.vacancy.salaryFrom || vm.vacancy.salaryTo) {
                    vm.salarySelect = true;
                } else {
                    vm.salarySelect = false;
                }
            });
        }

        function checkLocationModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-location.html',
                size: '',
                scope: $scope,
            });

            vm.modalInstance.result.then(() => {
                if (vm.vacancy.region) {
                    vm.locationSelect = true;
                } else {
                    vm.locationSelect = false;
                }
            });
        }

        function checkSkillsModal() {
            vm.modalInstance = $uibModal.open({
                windowClass: 'secondary-modal',
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-skills.html',
                size: '750px',
                scope: $scope,
            });
            vm.modalInstance.result.then(() => {
                if (vm.allSkills && vm.allSkills[0].skill) {
                    vm.skillsSelect = true;
                } else {
                    vm.skillsSelect = false;
                }
                $rootScope.$$phase || $scope.$apply();
            });
        }

        function checkLanguagesModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-languages.html',
                size: '',
                scope: $scope,
            });
            vm.modalInstance.result.then(() => {
                if (vm.vacancy.languages.length && vm.vacancy.languages[0].name) {
                    vm.languagesSelect = true;
                } else {
                    vm.languagesSelect = false;
                }
            });
        }

        function checkExpirienceModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-exp.html',
                size: '',
                scope: $scope,
            });
            vm.modalInstance.result.then(() => {
                if (vm.vacancy.experience) {
                    vm.experienceSelect = true;
                } else {
                    vm.experienceSelect = false;
                }
            });
        }

        function checkRoleModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-role.html',
                size: '',
                scope: $scope,
            });
            vm.modalInstance.result.then(() => {
                if (vm.vacancy.role) {
                    vm.roleSelect = true;
                } else {
                    vm.roleSelect = false;
                }
            });
        }

        function checkEmploymentTypeModal() {
            vm.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-auto-selection/check-employment.html',
                size: '',
                scope: $scope,
            });

            vm.modalInstance.result.then(() => {
                if (vm.vacancy.employmentType) {
                    vm.employmentTypeSelect = true;
                } else {
                    vm.employmentTypeSelect = false;
                }
            });
        }

        function initInitialState() {
            vm.employmentTypeForModal = Service.employmentType();
            vm.candidates = [];
            vm.currency = Service.currency();
            vm.vacancyEmptyRequiredFields = [];
            vm.candidatesEmptyRequiredFields = [];
            vm.suggestionsLength = { exact: null, suitable: null };
            vm.candidatesAddList = [];
            vm.checkAllCandidates = false;
            vm.positionSelect = true;
            vm.locationSelect = false;
            vm.salarySelect = false;
            vm.employmentTypeSelect = false;
            vm.languagesSelect = false;
            vm.skillsSelect = false;
            vm.experienceSelect = false;
            vm.roleSelect = false;
            vm.hideSalary = true;
            $rootScope.redirectToSearch = false;
            vm.setEmploymentType = {};
            vm.setExpirience;
            vm.setRole;
            vm.allRoles = allRolesList.map((role) => ({ label: role }));
            vm.experience = Service.experience();
            vm.skillsExp = Service.skillExperience();
            vm.languagesLevel = Service.languagesLevel();
            vm.setLanguages = [];
            vm.params = [];
            vm.candidateData;
            vm.candidatesLanguages = [];
            vm.languagesOptions = [];

            vm.allSkills = [];
            vm.industry;
            vm.category;
            vm.customSkills;
            vm.skillsObject = {};
            vm.skillsForCategory;

            vm.suggestionTab = '';
            vm.hideTab = false;
            vm.isGetLanguages = false;
            vm.suggestionTitle = '';

            vm.SuggestionsSortCriteria = 'scorePersent';
            vm.reverseSort = true;
            vm.initialLoad = true;
        }

        vm.$onInit = function () {
            initInitialState();

            $rootScope.loading = true;
            const localId = $stateParams.id.includes('&')
                ? $stateParams.id.substr(0, $stateParams.id.indexOf('&'))
                : $stateParams.id;
            Vacancy.onGetByLocalId({
                localId: localId,
                fieldToGet: ['customFields', 'region', 'skills', 'languages'],
            })
                .then(
                    (resp) => {
                        $rootScope.loading = false;
                        vm.vacancy = resp.object;
                        vm.vacancy.currency = { label: 'USD', value: 'USD' };
                        if (checkRequiredFieldsCompletion(vm.vacancy).invalid) {
                            vm.isSalaryNotFilled = vm.vacancyEmptyRequiredFields.some(
                                (item) => item === 'salaryFrom' || item === 'salaryTo',
                            );
                        }
                    },
                    (error) => {
                        $rootScope.loading = false;
                        notificationService.error(error.message);
                    },
                )
                .then(() => {
                    setTableParams();
                });
        };

        vm.$onDestroy = function (e) {
            tableObserver?.disconnect();
        };
    },
    controllerAs: 'vm',
});
