class dashboardComponentCtrl {
    constructor(
        $translate,
        notificationService,
        $rootScope,
        $scope,
        Service,
        Statistic,
        Person,
        serverAddress,
        $timeout,
        $state,
    ) {
        this.$translate = $translate;
        this.notificationService = notificationService;
        this.$rootScope = $rootScope;
        this.$scope = $scope;
        this.Service = Service;
        this.Person = Person;
        this.Statistic = Statistic;
        this.serverAddress = serverAddress;
        this.$timeout = $timeout;
        this.$state = $state;

        this.dashboardFilterType = this.$rootScope.me.personParams.dashboardFilterType || 'time'; //time || vacancies
        this.vacanciesModel = [];
        this.debounceTimer = null;

        this.abortController = new AbortController();
        $scope.$on('$destroy', () => {
            this.abortController.abort();
        });
    }

    redirectTo(href) {
        window.location.hash = `#/${href}`;
    }

    initCurrentMonth() {
        let currentDate = new Date(),
            currentDateStart = new Date(),
            currentDateFinish = new Date();

        this.startVacancyDate = currentDateStart.setDate(1);
        this.endDate = currentDateFinish.setDate(currentDate.getDate());
        this.modelDateFrom = new Date(this.startVacancyDate);
        this.modelDateTo = new Date(this.endDate);
        this.sendStatistic.from = this.startVacancyDate;
        this.sendStatistic.to = this.endDate;
        this.timeRange.startDate = this.startVacancyDate;
        this.timeRange.endDate = this.endDate;
    }

    getStatistic() {
        if (
            this.dashboardFilterType === 'vacancies' &&
            Array.isArray(this.sendStatistic.vacancyIds) &&
            !this.sendStatistic.vacancyIds.length
        ) {
            return;
        }

        this.$rootScope.loading = true;

        this.Statistic.onGetDashboardStatistic(this.sendStatistic)
            .then((resp) => {
                const data = resp.object;
                this.offerAcceptedRate = data.offerAcceptedRate;
                this.interviewToHireRatio = data.interviewToHireRatio;
                this.averageVacancyCloseTime = data.averageVacancyCloseTime;
                this.daysDec = this.daysDeclination(data.averageVacancyCloseTime, 'hours');
                this.timeToHire = this.milliToDate(data.timeToHire);
                this.timeToHire.daysTitle = 'days_short';
                this.timeToHire.hoursTitle = 'hours_short';
                this.timeToHire.minutesTitle = 'minutes_short';

                this.timeToFill = this.milliToDate(data.timeToFill);
                this.timeToFill.daysTitle = 'days_short';
                this.timeToFill.hoursTitle = 'hours_short';
                this.timeToFill.minutesTitle = 'minutes_short';

                this.$rootScope.loading = false;
                this.$rootScope.$$phase || this.$scope.$apply();
            })
            .catch(() => (this.$rootScope.loading = false))
            .finally(() => (this.$rootScope.loading = false));
    }

    async getVacancies() {
        try {
            const signal = this.abortController.signal;

            const response = await fetch(`${this.serverAddress}/vacancy/all`, {
                signal,
            });
            const jsonData = await response.json();
            this.vacancies = jsonData.objects.sort((a, b) => b.dc - a.dc);
            this.vacanciesModel = jsonData.objects;
            this.allVacancyIds = this.vacanciesModel.map((vacancy) => vacancy.vacancyId);
            this.sendStatistic.vacancyIds = this.allVacancyIds;

            this.$rootScope.$$phase || this.$scope.$apply();
        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('Request aborted');
            }
        }
    }

    onChangeFiltrationType() {
        this.dashboardFilterType = this.dashboardFilterType === 'time' ? 'vacancies' : 'time';
        this.$rootScope.me.personParams.dashboardFilterType = this.dashboardFilterType;

        const updateStatistic = () => {
            if (this.dashboardFilterType === 'vacancies') {
                this.sendStatistic.from = null;
                this.sendStatistic.to = null;
                this.sendStatistic.vacancyIds = this.allVacancyIds;
            } else {
                this.sendStatistic.from = Date.parse(this.modelDateFrom);
                this.sendStatistic.to = Date.parse(this.modelDateTo);
                this.sendStatistic.vacancyIds = null;
            }
            this.getStatistic();
        };

        if (this.dashboardFilterType === 'vacancies' && !this.vacanciesModel.length) {
            this.getVacancies().then(updateStatistic);
        } else {
            updateStatistic();
        }

        this.Person.changeUserParam(
            {
                name: 'dashboardFilterType',
                value: this.dashboardFilterType,
            },
            function (resp) {
                if (resp.status !== 'ok') {
                    this.notificationService.error(resp.message);
                }
            },
        );
    }

    onDateChange() {
        this.sendStatistic.from = Date.parse(this.modelDateFrom);
        this.sendStatistic.to = Date.parse(this.modelDateTo);
        this.timeRange.selectRange = { label: 'customRange', value: 'customRange' };
        this.timeRange.disabledTimeRange = false;

        if (this.modelDateFrom && this.modelDateTo) {
            this.getStatistic();
        }
    }

    activateTimeRange() {
        this.timeRange.disabledTimeRange = false;
        this.timeRange.selectRange = { label: 'customRange', value: 'customRange' };
    }

    selectDateRange = (dateRange) => {
        this.timeRange.selectRange = dateRange;

        let currentDate = new Date(),
            currentDateStart = new Date(),
            currentDateFinish = new Date(),
            timeRange = this.timeRange;

        this.timeRange.disabledTimeRange = true;

        switch (dateRange.value) {
            case 'currentWeek':
                if (currentDate.getDay() === 0) {
                    this.modelDateFrom = currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() + 6));
                } else {
                    this.modelDateFrom = currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() - 1));
                }
                this.modelDateTo = currentDateFinish.setDate(currentDate.getDate());
                break;
            case 'previousWeek':
                if (currentDate.getDay() === 0) {
                    this.modelDateFrom = currentDateStart.setDate(currentDate.getDate() - (currentDate.getDay() + 13));
                    this.modelDateTo = currentDateFinish.setDate(currentDate.getDate() - (currentDate.getDay() + 7));
                } else {
                    this.modelDateFrom = currentDateStart.setDate(
                        currentDate.getDate() - (currentDate.getDay() - 1) - 7,
                    );
                    this.modelDateTo = currentDateFinish.setDate(
                        currentDate.getDate() - (currentDate.getDay() - 1) - 1,
                    );
                }
                break;
            case 'currentMonth':
                this.modelDateFrom = currentDateStart.setDate(1);
                this.modelDateTo = currentDateFinish.setDate(currentDate.getDate());
                break;
            case 'previousMonth':
                this.modelDateFrom = currentDateStart.setMonth(currentDate.getMonth() - 1, 1);
                currentDateFinish.setMonth(currentDate.getMonth(), 0);
                break;
            case 'currentYear':
                currentDateStart.setFullYear(currentDate.getFullYear(), 0, 1);
                currentDateFinish.setDate(currentDate.getDate());
                break;
            case 'previousYear':
                this.modelDateFrom = currentDateStart.setFullYear(currentDate.getFullYear() - 1, 0, 1);
                this.modelDateTo = currentDateFinish.setFullYear(currentDate.getFullYear(), 0, 0);
                break;
            case 'customRange':
                timeRange.disabledTimeRange = false;
                currentDateStart = this.modelDateFrom;
                currentDateFinish = this.modelDateTo;
                break;
        }

        this.modelDateFrom = new Date(currentDateStart);
        this.modelDateTo = new Date(currentDateFinish);

        this.sendStatistic.from = Date.parse(this.modelDateFrom);
        this.sendStatistic.to = Date.parse(this.modelDateTo);

        if (this.modelDateFrom && this.modelDateTo) {
            this.getStatistic();
        }

        this.$rootScope.$$phase || this.$scope.$apply();
    };

    datePickerClick = (event) => {
        const elementScope = angular.element($(event.target))?.scope();
        elementScope?.$parent?.showPicker && elementScope.$parent.showPicker(event);
    };

    onVacanciesChange = (newValue) => {
        this.vacanciesModel = newValue.sort((a, b) => b.dc - a.dc);
        this.$rootScope.$$phase || this.$scope.$apply();

        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.sendStatistic.vacancyIds = this.vacanciesModel.map((vacancy) => vacancy.vacancyId);
            this.getStatistic();
        }, 1000);
    };

    daysDeclination(value, type) {
        if (value === 0 && type === 'interviews') return 'Interviews-0';
        if (value === 1 && type === 'interviews') return 'Interviews-3';
        const cases = [2, 0, 1, 1, 1, 2];
        const titles = ['Days-2', 'Days-1', 'Days'];
        const interviews = ['Interviews-2', 'Interviews-1', 'Interviews'];
        const hourTitle = titles[value % 100 > 4 && value % 100 < 20 ? 2 : cases[value % 10 < 5 ? value % 10 : 5]];
        const interviewTitle =
            interviews[value % 100 > 4 && value % 100 < 20 ? 2 : cases[value % 10 < 5 ? value % 10 : 5]];

        if (type === 'hours') {
            return hourTitle;
        } else {
            return interviewTitle;
        }
    }

    milliToDate(date) {
        const cases = [2, 0, 1, 1, 1, 2];
        const titles = ['Days-2', 'Days-1', 'Days'];
        const titlesHour = ['Hours-2', 'Hours-1', 'Hours'];
        const titlesSeconds = ['Minutes-2', 'Minutes-1', 'Minutes'];
        let minutes = Math.floor(date / 60000);
        let hours = Math.round(minutes / 60);
        let days = Math.floor(hours / 24);
        hours = hours % 24;
        const dayTitle = titles[days % 100 > 4 && days % 100 < 20 ? 2 : cases[days % 10 < 5 ? days % 10 : 5]];
        const hourTitle = titlesHour[hours % 100 > 4 && hours % 100 < 20 ? 2 : cases[hours % 10 < 5 ? hours % 10 : 5]];
        const minutesTitle =
            titlesSeconds[minutes % 100 > 4 && minutes % 100 < 20 ? 2 : cases[minutes % 10 < 5 ? minutes % 10 : 5]];

        return {
            hours: hours,
            hoursTitle: hourTitle,
            days: days,
            daysTitle: dayTitle,
            minutes: minutes,
            minutesTitle: minutesTitle,
        };
    }

    onClickGoToVacancies(status) {
        this.$state.go('vacancies', { status: status });
    }

    $onInit() {
        this.$rootScope.loading = true;
        this.isBrowserSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        // this.$rootScope.loading = true;
        this.vacancies = [];
        this.vacanciesModel = [];
        this.newDatePerfomance = new Date();
        this.modelDateTo = null;
        this.modelDateFrom = null;
        this.sendStatistic = {
            to: null,
            from: null,
            vacancyIds: null,
        };

        this.selectRangeOptions = [
            { label: 'currentWeek', value: 'currentWeek' },
            { label: 'previousWeek', value: 'previousWeek' },
            { label: 'currentMonth', value: 'currentMonth' },
            { label: 'previousMonth', value: 'previousMonth' },
            { label: 'currentYear', value: 'currentYear' },
            { label: 'previousYear', value: 'previousYear' },
            { label: 'customRange', value: 'customRange' },
        ];

        this.timeRange = {
            selectRange: { label: 'currentMonth', value: 'currentMonth' },
            startDate: null,
            endDate: null,
            disabledTimeRange: true,
        };
        this.initCurrentMonth();

        this.Statistic.onGetDashboardCountInfo()
            .then((resp) => {
                const data = resp.object;
                this.openVacancyCount = data.openVacancyCount;
                this.inWorkVacancyCount = data.inWorkVacancyCount;
                this.interviewCount = data.interviewCount;
                this.positionsCount = data.positionsCount;
                this.interviewDec = this.daysDeclination(data.interviewCount, 'interviews');
                this.$rootScope.$$phase || this.$scope.$apply();
            })
            .catch((resp) => {
                if (resp.code === 'accessDenied') {
                    this.$state.go('organizer', {}, { reload: true });
                    this.$rootScope.loading = false;
                }
            });

        const updateStatistic = () => {
            if (this.dashboardFilterType === 'vacancies') {
                this.sendStatistic.from = null;
                this.sendStatistic.to = null;
                this.sendStatistic.vacancyIds = this.allVacancyIds;
            } else if (this.dashboardFilterType === 'time') {
                this.sendStatistic.vacancyIds = null;
            }
            this.getStatistic();
        };

        if (this.dashboardFilterType === 'vacancies') {
            this.getVacancies().then(updateStatistic);
        } else {
            this.$rootScope.loading = true;
            updateStatistic();
        }
        this.$rootScope.$$phase || this.$scope.$apply();
    }
}

const dashboardComponentCtrlDefinition = {
    bindings: {},
    templateUrl: 'partials/dashboard.html',
    controller: dashboardComponentCtrl,
    controllerAs: 'vm',
};

component.component('dashboardComponent', dashboardComponentCtrlDefinition);
