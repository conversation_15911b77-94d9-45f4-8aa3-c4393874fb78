controller.controller('ActivityGlobalHistoryController', [
    '$scope',
    '$rootScope',
    'Service',
    'Person',
    'Company',
    'notificationService',
    '$filter',
    '$translate',
    '$location',
    '$sce',
    '$uibModal',
    'vacancyStages',
    'Action',
    'CacheCandidates',
    'Mailing',
    'ngTableParams',
    '$timeout',
    '$anchorScroll',
    'Test',
    'Vacancy',
    'Client',
    '$state',
    'CandidatesSlider',
    '$window',
    function (
        $scope,
        $rootScope,
        Service,
        Person,
        Company,
        notificationService,
        $filter,
        $translate,
        $location,
        $sce,
        $uibModal,
        vacancyStages,
        Action,
        CacheCandidates,
        Mailing,
        ngTableParams,
        $timeout,
        $anchorScroll,
        Test,
        Vacancy,
        Client,
        $state,
        CandidatesSlider,
        $window,
    ) {
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $scope.closeModal = function () {
            $scope.modalInstance.close();
        };
        $scope.getMoreHistory = function () {
            var country = null;
            var city = null;
            if ($scope.regionId) {
                if ($scope.regionIdType == 'country') {
                    country = $scope.regionId;
                } else if ($scope.regionIdType == 'city') {
                    city = $scope.regionId;
                }
            }
            Service.history(
                {
                    country: country,
                    city: city,
                    personId: $rootScope.onlyMe ? $rootScope.userId : null,
                    page: { number: 0, count: ($scope.historyLimit *= 2) },
                    withRemovedUsers: true,
                },
                function (res) {
                    $scope.history = res.objects;
                    $scope.loading = false;
                },
                function (error) {},
            );
        };
        $scope.callbackAddLogo = function (photo) {
            $rootScope.companyLogo = photo;
            $rootScope.logoLink = $rootScope.companyLogo
                ? $scope.serverAddress + '/getapp?id=' + $rootScope.companyLogo + '&d=true'
                : null;
        };
        $scope.callbackErr = function (err) {
            notificationService.error(err);
        };
        $scope.resetComment = function (history) {
            history.editCommentFlag = false;
            history.editedComment = history.descr;
        };
        function deleteFromTags(id, type) {
            $scope.users.filter((user, index) => {
                if (user[type] == id) {
                    $scope.users.splice(index, 1);
                }
            });
            $scope.repsonsibleUsers.forEach((user, index) => {
                if (user.userId == id) {
                    $scope.repsonsibleUsers.splice(index, 1);
                }
            });
        }
        $scope.changeCommentFlag = function (history) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/edit-comment-full-history.html',
                size: '',
                backdrop: true,
                scope: $scope,
                resolve: {},
            });
            $rootScope.closeModal = function () {
                $rootScope.initTagsStyles(history, true);
                $scope.modalInstance.close();
            };

            $scope.repsonsibleUsers = [];
            $scope.users = [];

            $rootScope.persons.forEach((i, index) => {
                if (history.candidate) {
                    if (
                        i.userId === history.candidate.responsibleId &&
                        i.recrutRole !== 'client' &&
                        i.recrutRole !== 'freelancer' &&
                        i.recrutRole !== 'researcher'
                    ) {
                        $scope.repsonsibleUsers.push({
                            id: index + 1,
                            fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                            userId: i.userId,
                            responsible: true,
                        });
                    }
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher' &&
                    $rootScope.checkUserForTagsList(history.type, i.hideClients)
                ) {
                    $scope.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));

            $rootScope.ckEditorUserEditFull = {
                height: 140,
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href="#" style="text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;">@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorUserEditFull'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            $scope.deletedUsers.push(user);
                        }
                    },
                    instanceReady: function (ev) {
                        $scope.deletedUsers = [];
                        $scope.afterDelete = [];
                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        $rootScope.convertTagsToSpan(history, 'ckEditorUserEditFull');
                    },
                    resize: function (event) {
                        $rootScope.adjustCKEditorHeight(event);
                    },
                },
            };

            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                let text = CKEDITOR.instances['ckEditorUserEditFull'].getData();

                $scope.deletedUsers.forEach((user, index) => {
                    if (!text.includes(`${user.fullname}`)) {
                        if (user.responsible) {
                            $scope.repsonsibleUsers.push(user);
                        } else {
                            $scope.users.push(user);
                        }
                        $scope.afterDelete.push(user.id);

                        $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                    }
                });

                $scope.afterDelete.forEach((id) => {
                    $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
                });
                $scope.afterDelete = [];

                if (opts.query.length === 0 && $scope.repsonsibleUsers.length > 0) {
                    setTimeout(function () {
                        callback(
                            $scope.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            $scope.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }

            $scope.editComment = history.descr;
            history.showAllCandidates = false;

            $scope.changeComment = function () {
                $rootScope.loading = true;
                let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorUserEditFull.getData());
                $scope.historyComment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');
                Action.editAction(
                    {
                        comment: $scope.historyComment,
                        actionId: history.actionId,
                        type: history.type,
                    },
                    function (resp) {
                        if (resp.status && angular.equals(resp.status, 'error')) {
                            notificationService.error(resp.message);
                        } else {
                            $scope.tableParamsFullHistory.reload();
                            history.descr = resp.object.descr;
                            history.new_komment = '';
                            history.dateEdit = resp.object.dateEdit;
                            $rootScope.loading = false;
                            $rootScope.closeModal();
                        }
                    },
                );
            };
        };
        $scope.openMenuWithCandidatesNotSend = function (history) {
            history.showAllCandidatesNotSend = !history.showAllCandidatesNotSend;
            history.showAllCandidates = false;
            history.editCommentFlag = false;
        };
        $scope.openMenuWithCandidates = function (history) {
            history.showAllCandidatesNotSend = false;
            history.showAllCandidates = !history.showAllCandidates;
            history.editCommentFlag = false;
        };
        $scope.showDeleteComment = function (resp) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-comment-candidate.html',
                scope: $scope,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });
            $scope.commentRemove = resp;
            $rootScope.commentRemoveId = resp.actionId;
        };
        $scope.toggleCandidates = function (history) {
            $scope.history[$scope.history.indexOf(history)].candidatesToShow =
                !$scope.history[$scope.history.indexOf(history)].candidatesToShow;
        };
        $rootScope.deleteComment = function () {
            Action.removeMessageAction(
                {
                    actionId: $rootScope.commentRemoveId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        notificationService.success($filter('translate')('Comment removed'));
                        $rootScope.closeModal();
                        if (!$scope.onlyComments) {
                            updateHistory();
                        }
                    } else {
                        $rootScope.closeModal();
                    }
                },
            );
        };
        $scope.toSentPreview = function (mailing) {
            Mailing.showSentCompaignById(mailing);
        };

        $scope.toScorecard = function (value) {
            let stateParams = {
                scoreCardId: value.scoreCardId,
                candidateObj: value.candidate,
                id: value.candidate.localId,
                isFromVacancyToCandidate: value.vacancyId,
                isFromVacancyToEvaluate: true,
                sliderDataId: $scope.sliderId,
                vacancyName: value.vacancy ? value.vacancy.position : null,
                vacancyId: value.vacancyId,
            };

            let link = $state.href('candidate-slide', stateParams, {
                reload: true,
            });
            window.open(link, '_blank');
        };

        $scope.getVacancyById = function (id) {
            return $scope.vacancies.filter((vacancy) => {
                return vacancy.vacancyId === id;
            })[0];
        };
        $scope.getTestById = function (id) {
            return $scope.tests.filter((test) => {
                return test.test.id === id;
            })[0];
        };
        $scope.getVacanciesByIds = function (idsString) {
            const ids = idsString.replace('[', '').replace(']', '').split(', ');

            const vacancies = [];

            $scope.vacancies.forEach((vacancy) => {
                ids.forEach((id) => {
                    if (vacancy.vacancyId === id) {
                        vacancies.push(vacancy);
                    }
                });
            });

            return vacancies;
        };
        $scope.search = function () {
            var country = null;
            var city = null;
            if ($scope.regionId) {
                if ($scope.regionIdType == 'country') {
                    country = $scope.regionId;
                } else if ($scope.regionIdType == 'city') {
                    city = $scope.regionId;
                }
            }
            vacancyStages.get(function (resp) {
                $scope.customStages = resp.object ? resp.object.interviewStates : [];
            });
            let pageNumber = 0;
            let pageCount = 15;
            $scope.candidateSearchOptions = {};
            $scope.tableParamsFullHistory = new ngTableParams(
                {
                    page: 1,
                    count: 15,
                },
                {
                    total: 0,
                    getData: function ($defer, params) {
                        function getHistory(page, count) {
                            if (page || count) {
                                pageNumber = page;
                                pageCount = count;
                                $scope.candidateSearchOptions = {
                                    page: {
                                        number: pageNumber,
                                        count: pageCount,
                                    },
                                    ignoreType: [
                                        'candidate_profile_updated_wandify',
                                        'candidate_contacts_updated_wandify',
                                        'candidate_profile_not_updated_wandify',
                                        'candidate_contacts_not_updated_wandify',
                                        'candidate_add_from_jobCannon',
                                        'candidate_update_from_jobCannon',
                                        'add_candidate_to_bamboo_hr',
                                        'test_result_auto_sent',
                                        'removed_test_and_disabled_auto_sending_on_vacancy',
                                        'interview_add_from_advice_v1',
                                        'candidate_email_add',
                                        'candidate_remove_candidate_link',
                                        'candidate_autoaction_test_send',
                                        'candidate_autoaction_mail_send',
                                        'candidate_autoaction_mail_send_failed',
                                        'candidate_autoaction_test_send_failed',
                                    ],
                                    userId: $rootScope.onlyMe ? $rootScope.userId : null,
                                };
                            } else {
                                pageNumber = params.$params.page;
                                pageCount = params.$params.count;
                                $scope.candidateSearchOptions = {
                                    page: {
                                        number: pageNumber - 1,
                                        count: pageCount,
                                    },
                                    ignoreType: [
                                        'candidate_profile_updated_wandify',
                                        'candidate_contacts_updated_wandify',
                                        'candidate_profile_not_updated_wandify',
                                        'candidate_contacts_not_updated_wandify',
                                        'candidate_add_from_jobCannon',
                                        'candidate_update_from_jobCannon',
                                        'add_candidate_to_bamboo_hr',
                                        'test_result_auto_sent',
                                        'removed_test_and_disabled_auto_sending_on_vacancy',
                                        'interview_add_from_advice_v1',
                                        'candidate_email_add',
                                        'candidate_remove_candidate_link',
                                        'candidate_autoaction_test_send',
                                        'candidate_autoaction_mail_send',
                                        'candidate_autoaction_mail_send_failed',
                                        'candidate_autoaction_test_send_failed',
                                    ],
                                    userId: $rootScope.onlyMe ? $rootScope.userId : null,
                                };
                                $scope.isShowMore = false;
                                if (document.getElementById('scrollup'))
                                    document.getElementById('scrollup').style.display = 'none';
                                $timeout(function () {
                                    $anchorScroll('mainTable');
                                });
                            }
                            $rootScope.loading = true;
                            Service.history($scope.candidateSearchOptions, function (response) {
                                if (page) {
                                    $scope.history = $scope.history.concat(response['objects']);
                                } else {
                                    $scope.history = response['objects'];
                                }
                                response.objects.forEach((item) => {
                                    if (item.type === 'task_change_status') {
                                        if (item.stateNew === 'open') item.stateNew = 'inwork';
                                        if (item.stateOld === 'open') item.stateOld = 'inwork';
                                    }

                                    if (item.type === 'set_interview_status') {
                                        item.stagesArray = item.stateNew.split(',').map((state) => ({ value: state }));
                                    }

                                    if (
                                        item.type === 'candidate_merge_group' ||
                                        item.type === 'edit_group' ||
                                        item.type === 'merge_group' ||
                                        item.type === 'candidate_edit_group'
                                    )
                                        item.descr = JSON.parse(item.descr);
                                });
                                response.objects.forEach((action) => {
                                    if (
                                        action.type !== 'candidate_merge_group' &&
                                        action.type !== 'edit_group' &&
                                        action.type !== 'merge_group' &&
                                        action.type !== 'candidate_edit_group'
                                    ) {
                                        action.descr = action.descr.replaceAll('&nbsp;', ' ');
                                        action.descr = action.descr.replaceAll('rel="nofollow"', '');
                                        $rootScope.initTagsStyles(action);
                                    }
                                    if (
                                        action.type === 'scorecard_assign' ||
                                        action.type === 'delete_scorecard_assign'
                                    ) {
                                        if (action.scoreCardNames) {
                                            action.cards = JSON.parse(action.scoreCardNames);
                                        } else {
                                            action.cards = [action.descr];
                                        }
                                    }
                                });

                                angular.forEach($scope.history, (value) => {
                                    if (value.stateNew || value.stateOld) {
                                        if ($scope.customStages) {
                                            $scope.customStages.forEach((stage) => {
                                                if (value.type === 'set_interview_status') {
                                                    value.stagesArray.forEach((originalStage) => {
                                                        if (originalStage.value === stage.customInterviewStateId) {
                                                            originalStage['customInterviewStateId'] =
                                                                stage.customInterviewStateId;
                                                            originalStage.value = stage.value;
                                                        }
                                                    });
                                                } else {
                                                    if (stage.customInterviewStateId === value.stateOld) {
                                                        value.customStateOld = value.stateOld;
                                                        value.stateOld = stage.type;
                                                    }
                                                    if (stage.customInterviewStateId === value.stateNew) {
                                                        value.customStateNew = value.stateNew;
                                                        value.stateNew = stage.type;
                                                    }
                                                }
                                            });
                                        }
                                    }
                                    if (value.customInterviewStates) {
                                        $scope.customStages.forEach((stage) => {
                                            value.customInterviewStates.forEach((custom) => {
                                                if (stage.customInterviewStateId === custom.customInterviewStateId) {
                                                    custom.stateNew = stage.type;
                                                }
                                            });
                                        });
                                    }
                                });

                                $scope.objectSize = response['objects'] ? response['total'] : 0;
                                $scope.showHistory = response.objects != undefined;
                                params.total(response['total']);
                                $scope.paginationParams = {
                                    currentPage: $scope.candidateSearchOptions.page.number,
                                    totalCount: $scope.objectSize,
                                    allPageCount: response.allPageCount,
                                };
                                if (response['allPageCount'] === $scope.candidateSearchOptions.page.number + 1) {
                                    $('#show_more').hide();
                                } else {
                                    $('#show_more').show();
                                }
                                $defer.resolve($scope.history);
                                $rootScope.loading = false;
                                $scope.displayShowMore = true;
                            });
                        }

                        if ($rootScope.personal === undefined) {
                            setTimeout(() => {
                                getHistory();
                            }, 200);
                        } else {
                            getHistory();
                        }

                        $scope.showMore = function () {
                            $scope.isShowMore = true;
                            $scope.displayShowMore = Service.dynamicTableLoading(
                                params.total(),
                                $scope.candidateSearchOptions.page.number,
                                $scope.candidateSearchOptions.page.count,
                                getHistory,
                            );
                        };

                        $scope.previousSearchNumber = $scope.tableParamsFullHistory.page();
                    },
                },
            );

            $scope.changePage = (pageNumber) => {
                if ($scope.isShowMore) {
                    $scope.tableParamsFullHistory.page($scope.previousSearchNumber);
                } else $scope.tableParamsFullHistory.page(pageNumber);

                $scope.tableParamsFullHistory.reload();
            };

            $scope.changeAmountOfElements = (amount) => {
                if ($scope.tableParamsFullHistory.count() === amount) return;
                $scope.tableParamsFullHistory.count(amount);
                $scope.tableParamsFullHistory.reload();
            };
        };

        $scope.routeOnCandidates = function (ids) {
            const data = JSON.parse(ids);
            let link = $state.href('candidates', { ids: data }, { reload: true });
            $window.open(link, '_blank');
        };

        $scope.showFilePreview = function (history) {
            const file = {
                fileName: history.descr,
                fileId: JSON.parse(history.data).fileId,
            };
            Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
        };

        function updateHistory() {
            if ($scope.onlyComments) {
                showCommentsFirstTime();
            } else {
                showDetails();
            }
        }

        function showCommentsFirstTime() {
            Service.history(
                {
                    vacancyId: null,
                    candidateId: null,
                    clientId: null,
                    page: { number: 0, count: vm.historyLimit },
                    type: 'all_actions',
                },
                function (res) {
                    vm.history = res.objects;
                    vm.historyLimit = res.objects ? res.size : null;
                    vm.historyTotal = res.objects ? res.total : null;
                    vm.sliderId = CandidatesSlider.getSliderId();
                },
            );
        }

        function showDetails() {
            Service.history(
                {
                    page: {
                        number: $scope.candidateSearchOptions.page.number,
                        count: $scope.candidateSearchOptions.page.count,
                    },
                    candidateId: null,
                    onlyWithComment: ($scope.onlyWithComment = false),
                    ignoreType: [
                        'candidate_profile_updated_wandify',
                        'candidate_contacts_updated_wandify',
                        'candidate_profile_not_updated_wandify',
                        'candidate_contacts_not_updated_wandify',
                        'candidate_add_from_jobCannon',
                        'candidate_update_from_jobCannon',
                        'add_candidate_to_bamboo_hr',
                        'test_result_auto_sent',
                        'removed_test_and_disabled_auto_sending_on_vacancy',
                        'interview_add_from_advice_v1',
                        'candidate_email_add',
                        'candidate_remove_candidate_link',
                        'candidate_autoaction_test_send',
                        'candidate_autoaction_mail_send',
                        'candidate_autoaction_mail_send_failed',
                        'candidate_autoaction_test_send_failed',
                    ],
                },
                function (res) {
                    let keepGoing = true;
                    angular.forEach($scope.history, function (val) {
                        if (keepGoing) {
                            if (
                                val.type === 'vacancy_message' ||
                                val.type === 'candidate_message' ||
                                val.type === 'interview_message' ||
                                val.type === 'client_message'
                            ) {
                                $scope.showHistoryForPrint = true;
                                keepGoing = false;
                            }
                        }
                    });
                    $scope.history = res.objects;
                    $scope.objectSize = res['objects'] ? res['total'] : 0;
                },
            );
        }

        function initController() {
            if ($rootScope.personal === undefined) {
                $rootScope.persons = [];
                Person.getAllPersons((resp) => {
                    angular.forEach(resp.object, function (val) {
                        $rootScope.persons.push({
                            fullName: val.fullName,
                            userId: val.userId,
                            status: val.status,
                            recrutRole: val.recrutRole,
                            fullNameEn: val.fullNameEn,
                            hideClients: val.hideClients,
                        });
                    });
                });
            }
            $scope.sliderId = CandidatesSlider.getSliderId();
            $scope.showHistory = true;
            $scope.paginationParams = {
                currentPage: 1,
                totalCount: 0,
            };
            localStorage.setItem('isAddCandidates', JSON.stringify(false));
            $scope.vacancies = [];
            $scope.search();
            Service.listenerForScopeLight($scope, $rootScope);
        }

        initController();
    },
]);
