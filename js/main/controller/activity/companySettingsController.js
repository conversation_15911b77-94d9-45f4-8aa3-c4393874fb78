controller.controller('ActivityCompanySettingsController', [
    '$scope',
    '$rootScope',
    '$stateParams',
    'Service',
    'Person',
    'Company',
    'notificationService',
    '$filter',
    '$translate',
    'Vacancy',
    '$location',
    '$uibModal',
    '$timeout',
    'Account',
    'serverAddress',
    function (
        $scope,
        $rootScope,
        $stateParams,
        Service,
        Person,
        Company,
        notificationService,
        $filter,
        $translate,
        Vacancy,
        $location,
        $uibModal,
        $timeout,
        Account,
        serverAddress,
    ) {
        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };

        $scope.icsChecked = false;
        $scope.interviewChecked = $rootScope.me.orgParams.icsFileInEmailsInterview !== 'N';
        $scope.taskChecked = $rootScope.me.orgParams.icsFileInEmailsTask !== 'N';
        $scope.hideCommentFromHm = $rootScope.me.orgParams.hideCommentFromHm !== 'N';
        $rootScope.isSAMLEnabled = false;
        $scope.isSAMLActivated = false;
        $scope.isSAMLButtonAvtive = false;
        $scope.IdpParams = {};
        $scope.directSAMLLoginLink = `${window.location.origin}/signin.html?saml=true&orgId=${$rootScope.me.orgId}`;

        $scope.SAMLSettingsForm = {
            ssoUrl: '',
            entityId: '',
            idpCert: '',
        };
        $scope.customFields = [
            { value: 'text', translate: 'recallTypes.text' },
            { value: 'date', translate: 'recallTypes.date' },
            { value: 'datetime', translate: 'recallTypes.datetime' },
            { value: 'select', translate: 'recallTypes.select' },
            { value: 'textFile', translate: 'recallTypes.textFile' },
            { value: 'file', translate: 'recallTypes.file' },
            { value: 'selectFile', translate: 'recallTypes.selectFile' },
        ];
        $scope.filesCount = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];

        $scope.editFieldAndSavePrevValue = function (type) {
            $scope.showEditMode[type] = true;
            $scope.prevValues[type] = $scope.companyInfo[type];
        };
        $scope.setPrevValue = function (type) {
            $scope.showEditMode[type] = false;
            $scope.companyInfo[type] = $scope.prevValues[type];
        };
        $scope.closeEditMode = function (type) {
            $scope.showEditMode[type] = false;
        };
        $scope.openEditContactsMode = function () {
            $scope.contactsPrevValues = Object.keys($scope.contactsPrevValues).reduce((prev, cur) => {
                prev[cur] = $scope.companyInfo[cur];
                return prev;
            }, {});

            $scope.contactsShowEditMode = true;
        };
        $scope.setPrevContactsValue = function () {
            Object.keys($scope.contactsPrevValues).forEach((item) => {
                $scope.companyInfo[item] = $scope.contactsPrevValues[item];
            });
            $scope.closeEditContactsMode();
        };
        $scope.closeEditContactsMode = function () {
            $scope.resetContactsErrors();
            $scope.contactsShowEditMode = false;
        };
        $scope.resetContactsErrors = function () {
            Object.keys($scope.contactsErrors).forEach((contactError) => {
                $scope.contactsErrors[contactError] = false;
            });
        };
        $scope.checkContactErrors = function () {
            return Object.keys($scope.contactsErrors).some((contactError) => $scope.contactsErrors[contactError]);
        };
        $scope.updateCompanyContacts = function () {
            if ($scope.checkContactErrors()) {
                notificationService.error($filter('translate')('Please fill the fields'));
                return;
            }

            let promiseArray = [];

            // checking need to send request for update contact
            Object.keys($scope.contactsPrevValues).forEach((contactField) => {
                if ($scope.contactsPrevValues[contactField] !== $scope.companyInfo[contactField]) {
                    promiseArray.push(
                        Company.onChangeAccountInformationParam.bind(null, {
                            param: contactField,
                            value: $scope.companyInfo[contactField],
                        }),
                    );
                }
            });

            Promise.all(promiseArray.map((func) => func()))
                .then((resp) => {
                    if (resp.every((item) => item.status === 'ok')) {
                        notificationService.success($filter('translate')('contacts_saved'));
                    } else {
                        $scope.setPrevContactsValue();
                    }
                })
                .catch((error) => {
                    console.error(error.message);
                    $scope.setPrevContactsValue();
                })
                .finally(() => {
                    $scope.closeEditContactsMode();
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };
        $scope.validationOfEmailField = function () {
            let email = $scope.companyInfo.companyEmail;
            if (email.length > 0) {
                const re =
                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                $scope.contactsErrors.companyEmail = !re.test(String(email).toLowerCase());
                if (email.length > 50) {
                    $scope.contactsErrors.companyEmail = true;
                    email = email.slice(0, 50);
                }
            } else {
                $scope.contactsErrors.companyEmail = false;
            }
        };
        $scope.validationOfContactField = function (fieldName) {
            if (fieldName === 'companyPhone') {
                if ($scope.companyInfo[fieldName] && $scope.companyInfo[fieldName].length > 20) {
                    $scope.contactsErrors[fieldName] = true;
                    return $scope.companyInfo[fieldName].slice(0, 20);
                } else {
                    $scope.contactsErrors[fieldName] = false;
                    return $scope.companyInfo[fieldName];
                }
            }
            if (fieldName === 'companyFacebookPage' || fieldName === 'companyWebSite') {
                if ($scope.companyInfo[fieldName] && $scope.companyInfo[fieldName].length > 100) {
                    $scope.contactsErrors[fieldName] = true;
                    return $scope.companyInfo[fieldName].slice(0, 100);
                } else {
                    $scope.contactsErrors[fieldName] = false;
                    return $scope.companyInfo[fieldName];
                }
            }
            if (fieldName === 'companyEmail') {
                if ($scope.companyInfo[fieldName] && $scope.companyInfo[fieldName].length > 50) {
                    $scope.contactsErrors[fieldName] = true;
                    return $scope.companyInfo[fieldName].slice(0, 50);
                } else {
                    return $scope.companyInfo[fieldName];
                }
            }
            if ($scope.companyInfo[fieldName] && $scope.companyInfo[fieldName].length > 50) {
                $scope.contactsErrors[fieldName] = true;
                return $scope.companyInfo[fieldName].slice(0, 50);
            } else {
                $scope.contactsErrors[fieldName] = false;
                return $scope.companyInfo[fieldName];
            }
        };
        $scope.callbackErr = function (err) {
            notificationService.error(err);
        };
        $scope.changeRestrictFileView = function (model) {
            $rootScope.loading = true;
            Account.onGetRestrictFileView({ value: model })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $rootScope.me.orgParams.restrictFileView = model;
                        notificationService.success($filter('translate')('Settings successfully changed'));
                    } else {
                        $scope.restrictFileView = $rootScope.me.orgParams.restrictFileView;
                        notificationService.error($filter('translate')(resp.message));
                    }
                })
                .catch((resp) => {
                    $scope.restrictFileView = $rootScope.me.orgParams.restrictFileView;
                    notificationService.error($filter('translate')(resp.message));
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };
        $scope.allowAttachFile = function (model) {
            $rootScope.loading = true;
            Account.onRequiredAttachment({ value: model })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $rootScope.me.orgParams.requiredAttachment = model;
                        notificationService.success($filter('translate')('Settings successfully changed'));
                    } else {
                        $scope.requiredAttachment = $rootScope.me.orgParams.requiredAttachment;
                        notificationService.error($filter('translate')(resp.message));
                    }
                })
                .catch((resp) => {
                    $scope.requiredAttachment = $rootScope.me.orgParams.requiredAttachment;
                    notificationService.error($filter('translate')(resp.message));
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.changeNameStyle = function (flag) {
            $rootScope.loading = true;

            fetch(`${window.location.origin}/hr/company/useAmericanNameStyle/${flag}`)
                .then((res) => {
                    return res.json();
                })
                .then((res) => {
                    $scope.useAmericanNameStyle = res.object;
                    $rootScope.useAmericanNameStyle = res.object;

                    $rootScope.loading = false;
                    notificationService.success($filter('translate')('Settings successfully changed'));
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        (function initSAML() {
            if ($rootScope.me.recrutRole !== 'admin') return;

            Service.onGetSAMLSettings().then((data) => {
                if (data.status === 'ok' && data.object && data.object.entityId && data.object.ssoUrl) {
                    $rootScope.isSAMLEnabled = true;
                    $scope.isSAMLActivated = true;
                    const { ssoUrl, entityId, idpCert } = data.object;
                    $scope.SAMLSettingsForm = {
                        ssoUrl,
                        entityId,
                        idpCert,
                    };

                    $scope.validateSAMLForm();
                }
            });

            Service.onGetIdpParams().then((data) => {
                if (data.status !== 'ok') return;
                $scope.IdpParams = data.object;
            });
        })();

        $scope.toggleHMComments = function (e) {
            $scope.hideCommentFromHm = !e;

            Account.onToggleHMComments({ value: !e, userId: $rootScope.me.userId })
                .then((resp) => {
                    if (!resp.status === 'ok') return;
                    $rootScope.me.orgParams.hideCommentFromHm = resp.message === 'true' ? 'Y' : 'N';
                    $scope.hideCommentFromHm = $rootScope.me.orgParams.hideCommentFromHm === 'Y' ? true : false;
                    notificationService.success($filter('translate')('Settings successfully changed'));
                })
                .catch((resp) => {
                    $scope.hideCommentFromHm = !!$rootScope.me.orgParams.hideCommentFromHm === 'Y' ? true : false;
                    notificationService.error($filter('translate')(resp?.message));
                })
                .finally(() => {
                    $rootScope.$$phase || $scope.$apply();
                });

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.toggleSAMLSettings = function (e) {
            if (!e && $scope.isSAMLActivated) {
                Service.onDeleteSAMLSettings().then((data) => {
                    notificationService.success($filter('translate')('Settings successfully changed'));
                });
                $rootScope.isSAMLEnabled = e;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.openSAMLConfirmationModal = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/saml-confirmation.html',
                scope: $scope,
                resolve: {},
            });

            $scope.closeSAMLModal = () => {
                $scope.modalInstance.close();
            };
        };

        $scope.openSAMLDeactivationModal = function () {
            return new Promise((resolve, reject) => {
                if (!$rootScope.isSAMLEnabled || !$scope.isSAMLActivated) {
                    resolve();
                    $rootScope.$$phase || $scope.$apply();
                    return;
                }

                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/saml-deactivation.html',
                    scope: $scope,
                    resolve: {},
                });

                $scope.closeSAMLModal = () => {
                    $scope.modalInstance.close();
                    reject('Deactivation canceled');
                };
                $scope.acceptSAMLDeactivation = () => {
                    $scope.modalInstance.close();
                    $scope.toggleSAMLSettings(false);
                    $scope.SAMLSettingsForm = {
                        ssoUrl: '',
                        entityId: '',
                        idpCert: '',
                    };
                    $scope.isSAMLActivated = false;
                    reject('Deactivation canceled');
                };
            });
        };

        $scope.cancelDeactivation = function () {
            $scope.modalInstance.close();
            $rootScope.isSAMLEnabled = true;
        };

        $scope.activateSAML = function () {
            Service.onSaveSAMLSettings($scope.SAMLSettingsForm).then((data) => {
                if (data.object) {
                    $scope.isSAMLActivated = true;
                    notificationService.success($filter('translate')('Settings successfully changed'));
                    $rootScope.$$phase || $scope.$apply();
                }
            });
        };

        $scope.validateSAMLForm = function () {
            const { ssoUrl, entityId, idpCert } = $scope.SAMLSettingsForm;

            if (ssoUrl.length && entityId.length && idpCert.length) {
                $scope.isSAMLButtonAvtive = true;
                return true;
            } else {
                $scope.isSAMLButtonAvtive = false;
                return false;
            }
        };

        $scope.changeICSChecked = function (type) {
            let checkedValue;
            if (type === 'interview') {
                $scope.interviewChecked = !$scope.interviewChecked;
                checkedValue = $scope.interviewChecked ? 'Y' : 'N';
            } else if (type === 'task') {
                $scope.taskChecked = !$scope.taskChecked;
                checkedValue = $scope.taskChecked ? 'Y' : 'N';
            } else {
                return;
            }

            fetch(
                `${window.location.origin}/hr/account/icsFileInEmails?name=icsFileInEmails${
                    type.charAt(0).toUpperCase() + type.slice(1)
                }&value=${checkedValue}`,
            )
                .then((res) => res.json())
                .then((res) => {
                    $rootScope.loading = false;
                    notificationService.success($filter('translate')('Settings successfully changed'));
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        $scope.openInstructionModal = function () {
            $scope.instructionModalInstance = $uibModal.open({
                size: 'lg',
                animation: true,
                templateUrl: '../partials/modal/saml-instruction.html',
                scope: $scope,
                resolve: {},
            });

            $scope.closeInstructionModal = () => $scope.instructionModalInstance.close();
        };

        function enlargeImageModalController(uibModalInstance, src) {
            const vm = this;
            vm.closeModal = () => uibModalInstance.close();
            vm.src = src;
        }

        $scope.enlargeImage = (src) => {
            $scope.modalInstance = $uibModal.open({
                templateUrl: '../partials/modal/enlargeImageModal.html',
                controllerAs: 'vm',
                windowClass: 'enlarge-image-modal-wrapper',
                controller: ['$uibModalInstance', 'src', enlargeImageModalController],
                resolve: {
                    src: () => src,
                },
            });
        };

        $scope.changeICS = async function () {
            let interviewValue = !$scope.icsChecked ? 'N' : 'Y';
            let taskValue = !$scope.icsChecked ? 'N' : 'Y';
            $scope.icsChecked = !$scope.icsChecked;
            $rootScope.loading = true;

            try {
                const interviewResponse = await fetch(
                    `${window.location.origin}/hr/account/icsFileInEmails?name=icsFileInEmailsInterview&value=${interviewValue}`,
                );
                const interviewResult = await interviewResponse.json();

                const taskResponse = await fetch(
                    `${window.location.origin}/hr/account/icsFileInEmails?name=icsFileInEmailsTask&value=${taskValue}`,
                );
                const taskResult = await taskResponse.json();

                $scope.interviewChecked = interviewValue === 'Y';
                $scope.taskChecked = taskValue === 'Y';
                $rootScope.loading = false;
                notificationService.success($filter('translate')('Settings successfully changed'));
                $rootScope.$$phase || $scope.$apply();
            } catch (error) {
                console.error('Error:', error);
                $rootScope.loading = false;
            }
        };

        $scope.changeOrgName = function () {
            if ($scope.prevValues.companyName === $scope.companyInfo.companyName) return;
            if ($scope.companyInfo.companyName === '') {
                $scope.companyInfo.companyName = $scope.prevValues.companyName;
                return;
            }

            $rootScope.loading = true;
            Person.onChangeOrgName({
                orgName: $scope.companyInfo.companyName,
                lang: $rootScope.currentLang,
            })
                .then((resp) => {
                    if (resp.status && angular.equals(resp.status, 'error')) {
                        notificationService.error(resp.message);
                    } else {
                        notificationService.success(
                            $filter('translate')('The account name changed to') + ' ' + $scope.companyInfo.companyName,
                        );
                        $rootScope.me.orgName = $scope.companyInfo.companyName;
                        $scope.showChangeOrgName = false;
                        angular.forEach($rootScope.me.orgs, function (org) {
                            if (org.orgId == $rootScope.me.orgId) {
                                org.orgName = $scope.companyInfo.companyName;
                            }
                        });
                        angular.forEach($rootScope.orgs, function (org) {
                            if (org.orgId == $rootScope.me.orgId) {
                                org.orgName = $scope.companyInfo.companyName;
                            }
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    $scope.closeEditMode('companyName');
                    $rootScope.loading = false;
                    $scope.$$phase || $scope.$apply();
                });
        };
        $scope.updateFBpages = function () {
            Company.orgPages(function (resp) {
                $rootScope.fbPages = resp.objects;
                for (var i = $rootScope.fbPages.length - 1; i >= 0; i--) {
                    if ($rootScope.fbPages[i].status === 'D') {
                        $rootScope.fbPages.splice(i, 1);
                    }
                }
            });
        };
        $scope.replaceCharacters = function (name) {
            Company.replaceSpecialCharacters(name, $scope);
        };
        $scope.addFacebookTabInCs = function () {
            if (FB) {
                FB.ui(
                    {
                        method: 'pagetab',
                        redirect_uri: 'https://www.facebook.com/dialog/pagetab?app_id=579184295576444',
                    },
                    function (response) {
                        if (response === undefined) {
                        } else {
                            $scope.FBpagesId = Object.keys(response.tabs_added);
                            checkFbAuth();
                        }
                    },
                );
            }

            function checkFbAuth() {
                if (FB) {
                    FB.getLoginStatus(function (response) {
                        if (response.status === 'connected') {
                            var uid = response.authResponse.userID;
                            var accessToken = response.authResponse.accessToken;
                        } else if (response.status === 'not_authorized') {
                            FB.login(function (response) {
                                checkFbAuth();
                                // Original FB.login code
                            });
                            // the user is logged in to Facebook,
                            // but has not authenticated your app
                        } else {
                            // the user isn't logged in to Facebook.
                            FB.login(function (response) {
                                checkFbAuth();
                                // Original FB.login code
                            });
                        }
                        if (accessToken) {
                            angular.forEach($scope.FBpagesId, function (data, key) {
                                FB.api(data, { accessToken: accessToken }, function (response1) {
                                    if (response1 && !response1.error) {
                                        Company.addFacebookPage(
                                            {
                                                facebookPageId: data,
                                                name: response1.name,
                                            },
                                            function (resp) {
                                                if (resp.status == 'ok') {
                                                    function facebooklogout() {
                                                        FB.logout(function (response) {});
                                                    }

                                                    $scope.updateFBpages();
                                                }
                                            },
                                        );
                                    }
                                });
                            });
                        }
                    });
                }
            }
        };
        $rootScope.showDeleteFbPagesOnCompany = function (tab) {
            $scope.deletedTabCompany = tab;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/company-settings-remove-fb-tab.html',
                resolve: {},
            });
        };
        $rootScope.deleteTabOnFacebook = function () {
            Company.deleteFacebookPage(
                {
                    facebookPageId: $scope.deletedTabCompany.facebookPageId,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.updateFBpages();
                        $rootScope.closeModal();
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        $scope.setCompanyLocationPrevValue = function (param) {
            $scope.companyLocationPrevValues[param] = $scope.companyInfo.companyLocation[param];
        };
        $scope.setLocationParam = function (param) {
            if ($scope.companyLocationPrevValues[param] === $scope.companyInfo.companyLocation[param]) return;

            Company.onChangeAccountInformationParam({
                param: 'companyLocation',
                value: JSON.stringify({
                    ...$scope.companyInfo.companyLocation,
                    [param]: $scope.companyInfo.companyLocation[param],
                }),
            })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $scope.companyInfo.companyLocation = JSON.parse(resp.object.companyLocation);
                        notificationService.success($filter('translate')('Changes saved'));
                    } else {
                        notificationService.error(resp.message);
                    }
                })
                .catch((error) => {
                    console.error(error.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $rootScope.$apply();
                });
        };
        $scope.changeCompanyDescription = function () {
            Company.onChangeAccountInformationParam({
                param: 'companyDescription',
                value: $scope.companyInfo.companyDescription,
            })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        $scope.companyInfo.companyDescription = resp.object.companyDescription;
                        notificationService.success($filter('translate')('Changes saved'));
                    } else {
                        notificationService.error(resp.message);
                    }
                })
                .catch((error) => {
                    console.error(error.message);
                })
                .finally(() => {
                    $scope.closeEditMode('companyDescription');
                    $rootScope.loading = false;
                    $rootScope.$$phase || $rootScope.$apply();
                });
        };
        $scope.openDeleteAccountModal = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-account.html',
                scope: $scope,
                resolve: {},
            });
        };
        $scope.deleteAccount = function (comment) {
            if (comment && comment.trim().length > 0) {
                $rootScope.loading = true;
                Account.accountDeletion({ comment })
                    .then(() => {
                        $rootScope.closeModal();
                        notificationService.success($filter('translate')('Your account will be deleted in 7 days'));
                        return Company.params();
                    })
                    .then((resp) => {
                        $rootScope.accountDeletion = {
                            isActive: resp.object.dateToDelete && resp.object.whoDeletes,
                            days: differenceBetweenTwoDates(new Date(resp.object.dateToDelete), new Date()),
                            whoDeletes: resp.object.whoDeletes,
                        };
                        $rootScope.loading = false;
                        $scope.$apply();
                    })
                    .catch((error) => {
                        $rootScope.loading = false;
                        $scope.$apply();
                        notificationService.error(error.message);
                        $rootScope.closeModal();
                    });
            } else {
                notificationService.error($filter('translate')('Specify the reason for deleting the account'));
            }
        };
        $scope.selectIndustry = function (opt) {
            Company.onChangeAccountInformationParam({
                param: 'companyIndustry',
                value: (opt && opt.id) || '',
            })
                .then((resp) => {
                    $scope.modelForTranslate = resp.object.companyIndustry;
                    $scope.traslater = resp.object.companyIndustry;
                    if (resp.status === 'ok') {
                        $scope.companyInfo.companyIndustry = $scope.listOfIndustries.filter(
                            (industryItem) => industryItem.id === resp.object.companyIndustry,
                        )[0].text;
                        notificationService.success($filter('translate')('Changes saved'));
                    } else {
                        notificationService.error(resp.message);
                    }
                })
                .catch((error) => {
                    console.error(error.message);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $rootScope.$apply();
                });
        };
        $scope.getIndustries = function () {
            $scope.listOfIndustries = Service.getIndustries();
            if ($scope.listOfIndustries.length) transformIndustries();
        };

        function onSanitizeAccountFields(companyInfo) {
            if (!companyInfo) return;

            if (companyInfo.companyDescription)
                companyInfo.companyDescription = Service.sanitizeStringFromXSS(companyInfo.companyDescription);
        }

        function initCompanyInfoModel() {
            return {
                companyName: $rootScope.me.orgName,
                companyIndustry: '',
                modelForTranslate: '',
                companyWallpaper: '',
                companyBackground: '',
                companyDescription: '',
                companyPhone: '',
                companyEmail: '',
                companyFacebookPage: '',
                companyWebSite: '',
                companyLinkedIn: '',
                companyLocation: {},
            };
        }

        function validateEmail(email) {
            const re =
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }

        function transformIndustries() {
            $scope.listOfIndustries = $scope.listOfIndustries.map(function (oneIndustry) {
                return {
                    id: oneIndustry.value,
                    text: $translate.instant('industries_assoc.' + oneIndustry.value),
                };
            });
        }

        function differenceBetweenTwoDates(firstDate, secondDate) {
            var oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds

            return Math.ceil((new Date(firstDate) - secondDate) / oneDay);
        }

        $scope.showHistory = true;
        $scope.facebookAppId = facebookAppId;
        $scope.loading = true;
        $scope.serverAddress = serverAddress;
        $scope.showChangeOrgName = false;

        $scope.companyInfo = initCompanyInfoModel();

        $scope.showEditMode = {
            companyDescription: false,
        };
        $scope.prevValues = {
            companyName: null,
            companyDescription: null,
        };
        $scope.contactsShowEditMode = false;
        $scope.contactsPrevValues = {
            companyFacebookPage: null,
            companyWebSite: null,
            companyPhone: null,
            companyEmail: null,
        };
        $scope.contactsErrors = {
            companyFacebookPage: false,
            companyWebSite: false,
            companyPhone: false,
            companyEmail: false,
        };
        $scope.companyLocationPrevValues = {
            country: null,
            city: null,
            street: null,
        };
        $scope.ckEditorOptions = {
            toolbarGroups: [
                { name: 'clipboard', groups: ['clipboard', 'undo'] },
                { name: 'document', groups: ['mode', 'document', 'doctools'] },
                {
                    name: 'editing',
                    groups: ['find', 'selection', 'spellchecker', 'editing'],
                },
                { name: 'forms', groups: ['forms'] },
                { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                { name: 'colors', groups: ['colors'] },
                {
                    name: 'paragraph',
                    groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                },
                { name: 'links', groups: ['links'] },
                { name: 'insert', groups: ['insert'] },
                { name: 'styles', groups: ['styles'] },
                { name: 'tools', groups: ['tools'] },
                { name: 'others', groups: ['others'] },
                { name: 'about', groups: ['about'] },
            ],
            removeButtons:
                'Source,Save,Templates,Cut,Find,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,CopyFormatting,RemoveFormat,NumberedList,BulletedList,Outdent,Indent,Blockquote,CreateDiv,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,BidiLtr,BidiRtl,Language,Link,Unlink,Anchor,Image,Flash,Table,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Styles,Format,Font,FontSize,Maximize,ShowBlocks,About,NewPage,Preview,Print,Copy,Paste,PasteText,PasteFromWord,Replace,BGColor',
            height: 350,
        };
        Service.listenerForScopeLight($scope, $rootScope);

        Service.listenerForScopeLight($scope, $rootScope);
        $rootScope.loading = true;

        $scope.$watch(
            () => $rootScope.currentLang,
            () => {
                setTimeout(() => {
                    $rootScope.modelForTranslate = $filter('translate')($scope.traslater);
                    $rootScope.$$phase || $scope.$apply();
                }, 500);
            },
        );

        Company.onGetAccountInformation()
            .then((resp) => {
                $scope.useAmericanNameStyle = resp.object.useAmericanNameStyle;

                $rootScope.persons = [];
                Person.getAllPersons((resp) => {
                    angular.forEach(resp.object, function (val) {
                        $rootScope.persons.push({
                            fullName: val.fullName,
                            userId: val.userId,
                            status: val.status,
                            recrutRole: val.recrutRole,
                        });
                    });
                });
                $scope.traslater = resp.object.companyIndustry;
                $rootScope.modelForTranslate = resp.object.companyIndustry;
                $scope.companyInfo =
                    resp.object && resp.object.companyLocation
                        ? {
                              ...$scope.companyInfo,
                              ...resp.object,
                              companyLocation: JSON.parse(resp.object.companyLocation),
                          }
                        : { ...$scope.companyInfo, ...resp.object };
                if ($scope.companyInfo.companyIndustry) {
                    $scope.companyInfo.companyIndustry = $scope.listOfIndustries.filter(
                        (industryItem) => industryItem.id === $scope.companyInfo.companyIndustry,
                    )[0].text;
                }
                onSanitizeAccountFields($scope.companyInfo);
            })
            .catch((error) => {
                console.error(error.message);
                notificationService.error(error.message);
                $rootScope.loading = false;
            })
            .finally(() => {
                $rootScope.loading = false;
                $rootScope.$$phase || $scope.$apply();
            });

        if ($rootScope.me.orgParams.restrictFileView === true || $rootScope.me.orgParams.restrictFileView === 'true') {
            $scope.restrictFileView = true;
        } else $scope.restrictFileView = false;
        if (
            $rootScope.me.orgParams.requiredAttachment === false ||
            $rootScope.me.orgParams.requiredAttachment === 'false'
        ) {
            $scope.requiredAttachment = false;
        } else {
            $scope.requiredAttachment = true;
        }

        Vacancy.getVacancyExampleForLogoDemo(function (resp) {
            if (angular.equals(resp.status, 'ok') && resp.object.localId != undefined) {
                if ($rootScope.frontMode == 'war') {
                    $scope.publicLink =
                        $location.$$protocol + '://' + $location.$$host + '/i/pv/' + resp.object.localId;
                } else {
                    $scope.publicLink =
                        $location.$$protocol + '://' + $location.$$host + '/di#/pv/' + resp.object.localId;
                }
            }
        });
        $scope.copyToPhone = function (str) {
            const el = document.createElement('textarea');
            el.value = str;
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
            notificationService.success($filter('translate')('Copied!'));
        };
        $scope.updateFBpages();
        $.getScript('//connect.facebook.net/en_UK/all.js', function () {
            if (FB) {
                FB.init({
                    appId: apiKey.facebook.appId,
                    version: 'v2.9',
                });
                if (Service.getUrlVars($location.$$absUrl).q1) {
                    $('.showNotHaveIntegration.modal').modal('hide');
                    $scope.addFacebookTabInCs();
                }
            }
        });
        $scope.getIndustries();

        $rootScope.$on('$translateChangeSuccess', () => {
            $scope.getIndustries();
        });

        $scope.moveCustomField = (type, index, field) => {
            let elements = $scope.currentTemp.fields.custom;
            const newPosition = type === 'down' ? index + 1 : index - 1;

            if (newPosition >= 0 && newPosition < elements.length) {
                const removedItem = elements.splice(index, 1);
                elements.splice(newPosition, 0, removedItem[0]);
            }

            getOldTemplates();
        };

        $scope.saveCurrentTemplate = () => {
            $scope.currentTemp.additionalInfo = JSON.stringify($scope.currentTemp.fields);

            Company.onEditTemplate($scope.currentTemp)
                .then((resp) => {
                    $scope.currentTemp.copyTemplate = angular.copy($scope.currentTemp);
                    notificationService.success(
                        ($scope.currentTemp.default ? '' : $filter('translate')('Template')) +
                            ' ' +
                            ($scope.currentTemp.default
                                ? $filter('translate')('Default-recall-template')
                                : $scope.currentTemp.templateName) +
                            ' ' +
                            $filter('translate')('save-recall-template'),
                    );
                    getOldTemplates();
                })
                .catch((error) => {
                    console.error(error.message);
                    notificationService.error(error.message);
                    $rootScope.loading = false;
                });
        };

        $scope.openEditTemplate = (type, temp) => {
            $scope.nameError = false;
            $scope.templateName = temp ? temp.templateName : '';
            $scope.typeOfEdit = type;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/edit-recall-template.html',
                scope: $scope,
                resolve: {},
            });

            $scope.closeFromOtherModal = () => $scope.modalInstance.close();

            $scope.checkInput = (name) => {
                $scope.nameError = !name;
            };

            $scope.saveTemplate = (name) => {
                if (!name) {
                    $scope.nameError = true;
                    return;
                }

                const request = type === 'create' ? 'onCreateTemplate' : 'onEditName';
                let params = Company.getParamsForTemplates;
                params.templateName = name;

                Company[request](
                    type === 'create'
                        ? params
                        : {
                              templateName: name,
                              recallTemplateId: temp.recallTemplateId,
                          },
                )
                    .then((resp) => {
                        if (type === 'create') {
                            let newTemplate = Company.getParamsForTemplates;
                            newTemplate.templateName = name;

                            notificationService.success(
                                $filter('translate')('Template') +
                                    ' ' +
                                    name +
                                    ' ' +
                                    $filter('translate')('create-recall-template'),
                            );
                            let newRecall = resp.object;
                            newRecall.fields = JSON.parse(resp.object.additionalInfo);
                            newRecall.saveBtn = false;
                            newRecall.copyTemplate = angular.copy(resp.object);
                            $scope.templates.push(newRecall);

                            $scope.currentTemp = newRecall;
                            $scope.copyCurrentTemp = angular.copy(newRecall);

                            getOldTemplates();
                            adjustTabs();
                        } else {
                            $scope.currentTemp.templateName = name;
                            notificationService.success(
                                $filter('translate')('Template') +
                                    ' ' +
                                    name +
                                    ' ' +
                                    $filter('translate')('edit-recall-template'),
                            );
                            adjustTabs();
                        }
                        $scope.modalInstance.close();
                    })
                    .catch((error) => {
                        console.error(error.message);
                        notificationService.error(error.message);
                        $rootScope.loading = false;
                    });
            };

            $scope.removeTemplate = () => {
                $scope.modalInstanceDelete = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/custom-fields/template-remove.html',
                    scope: $scope,
                    resolve: {},
                });

                $scope.modalRemoveTemplate = () => {
                    $rootScope.loading = true;
                    Company.onDeleteRecall({ id: temp.recallTemplateId })
                        .then(() => {
                            $scope.templates = $scope.templates.filter(
                                (template) => template.recallTemplateId !== temp.recallTemplateId,
                            );
                            $scope.currentTemp = $scope.templates[0];

                            $scope.modalInstanceDelete.close();
                            $scope.modalInstance.close();
                            $rootScope.loading = false;
                            adjustTabs();
                            notificationService.success(
                                $filter('translate')('Template') +
                                    ' ' +
                                    $scope.templateName +
                                    ' ' +
                                    $filter('translate')('remove-recall-template'),
                            );
                        })
                        .catch((error) => {
                            notificationService.error(error.error);
                            $rootScope.loading = false;
                        });
                };

                $rootScope.closeNextModal = () => $scope.modalInstanceDelete.close();
            };
        };

        $scope.openNewFieldModal = (field) => {
            $scope.nameError = false;
            $scope.emptyDropDown = false;
            $scope.disabledField = !!field?.name;

            $scope.selectedTags = field?.params ? field.params : [];

            $scope.editRecallField = {
                name: field ? field.name : '',
                type: field
                    ? { value: field.type, translate: 'recallTypes.' + field.type }
                    : { value: 'text', translate: 'recallTypes.text' },
                count: field ? field.fileLimit : '1',
            };

            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/add-field-for-recall.html',
                windowClass: 'new-recall-field',
                scope: $scope,
                resolve: {},
            });

            $timeout(() => {
                $('#customFullField').select2('val', field?.params);

                $scope.selectedTags = field?.params
                    ? field?.params.map((tag) => {
                          return { id: tag, text: tag };
                      })
                    : [];
            }, 100);

            $scope.checkInput = (name) => {
                $scope.nameError = !name;
            };

            $scope.addCustomField = () => {
                if (!$scope.editRecallField.name) {
                    $scope.nameError = true;
                    return;
                }

                if (
                    ($scope.editRecallField.type.value === 'select' ||
                        $scope.editRecallField.type.value === 'selectFile') &&
                    $scope.selectedTags.length === 0
                ) {
                    $scope.emptyDropDown = true;
                    notificationService.error(
                        $filter('translate')('Please enter at least one value for the drop-down list'),
                    );
                    return;
                } else {
                    $scope.emptyDropDown = false;
                }

                if (!$scope.currentTemp.fields.custom) $scope.currentTemp.fields.custom = [];

                if (field) {
                    field.name = $scope.editRecallField.name;
                    field.type = $scope.editRecallField.type.value;
                    field.fileLimit = $scope.editRecallField.count;
                    field.params = $scope.selectedTags.map((value) => value.text);
                } else {
                    $scope.currentTemp.fields.custom.push({
                        fileLimit: $scope.editRecallField.count,
                        mandatory: false,
                        name: $scope.editRecallField.name,
                        type: $scope.editRecallField.type.value,
                        disabled: false,
                        params: $scope.selectedTags.map((value) => value.text),
                    });
                }

                getOldTemplates();
                $scope.modalInstance.close();
            };

            $scope.clearSelectedTag = function (tagToRemove) {
                $scope.selectedTags = $scope.selectedTags.filter((tag) => tag.id !== tagToRemove.id);
            };
        };

        $scope.selectTemplate = (field) => {
            if (field.recallTemplateId === $scope.currentTemp.recallTemplateId) {
                $scope.copyCurrentTemp = angular.copy(field);
            }
            $scope.currentTemp = field;
            getOldTemplates();
        };

        $scope.checkDisabled = (field) => {
            field.disabled = !field.disabled;
            let fields = [...$scope.currentTemp.fields.system];
            if ($scope.currentTemp.fields.custom)
                fields = [...$scope.currentTemp.fields.system, ...$scope.currentTemp.fields.custom];
            const countMandatory = fields.reduce((acc, item) => {
                if (!item.disabled) acc += 1;
                return acc;
            }, 0);

            if (countMandatory === 0) {
                field.disabled = false;
                // notificationService.error($filter('translate')('Need-one-enabled'));
                notificationService.error($filter('translate')('Need-one-field'));
            }
            getOldTemplates();
        };

        $scope.checkChanges = () => {
            getOldTemplates();
        };

        $scope.removeField = (fieldName, disabled) => {
            const fields = [...$scope.currentTemp.fields.system, ...$scope.currentTemp.fields.custom];
            const countMandatory = fields.reduce((acc, item) => {
                if (!item.disabled) acc += 1;
                return acc;
            }, 0);

            if (countMandatory === 1 && !disabled) {
                notificationService.error($filter('translate')('Need-one-field'));
                return;
            }
            $scope.currentTemp.fields.custom = $scope.currentTemp.fields.custom.filter(
                (field) => field.name !== fieldName,
            );
            getOldTemplates();
        };

        function initBuilderFields() {
            $scope.saveBtn = false;
            $scope.showHidden = false;
            Company.getRecallTemplate()
                .then((resp) => {
                    $scope.templates = resp.objects;
                    $scope.templates.forEach((value) => {
                        value.saveBtn = false;
                        value.copyTemplate = angular.copy(value);
                    });

                    $scope.currentTemp = $scope.templates[0];
                    $scope.copyCurrentTemp = angular.copy($scope.templates[0]);

                    getOldTemplates();
                    adjustTabs();
                })
                .catch((error) => {
                    console.error(error.message);
                    notificationService.error(error.message);
                    $rootScope.loading = false;
                });
        }

        function getOldTemplates() {
            $timeout(() => {
                $scope.currentTemp.saveBtn = !angular.equals(
                    $scope.currentTemp.fields,
                    $scope.currentTemp.copyTemplate.fields,
                );
            }, 0);
        }

        $scope.resetCurrentTemplate = () => {
            $scope.currentTemp = angular.copy($scope.currentTemp.copyTemplate);
            $scope.currentTemp.copyTemplate = angular.copy($scope.currentTemp);
            const index = $scope.templates.findIndex(
                (val) => val.recallTemplateId === $scope.currentTemp.recallTemplateId,
            );
            $scope.templates[index] = $scope.currentTemp;
            getOldTemplates();
        };

        function adjustTabs() {
            this.loading = true;
            $timeout(() => {
                if (window.outerWidth < 575) return;
                const container = document.querySelector('.feedback-builder__header__tabs__wrapper');
                const tabs = document.querySelectorAll('.feedback-builder__header__tabs__wrapper__items');
                const containerWidth = container.offsetWidth - 250;

                let totalTabsWidth = 0;
                let hiddenArrays = [];
                $scope.hiddenTabs = 0;

                tabs.forEach((tab) => {
                    totalTabsWidth += tab.offsetWidth;
                    if (totalTabsWidth > containerWidth) {
                        tab.classList.add('hidden-tab');
                        $scope.hiddenTabs++;
                    }
                });

                document.querySelectorAll('.hidden-tab').forEach((item) => {
                    hiddenArrays.push(item.dataset.ids);
                });

                $scope.hiddenElements = $scope.templates.filter((item) => hiddenArrays.includes(item.recallTemplateId));
            }, 300);
        }

        function dontCloseHandler(event) {
            if (event.target.className.includes('dont-close')) return;
            $scope.showHidden = false;
            $rootScope.$$phase || $scope.$apply();
        }

        document.addEventListener('click', dontCloseHandler);
        window.addEventListener('resize', adjustTabs);

        initBuilderFields();

        $scope.$on('$destroy', function () {
            document.removeEventListener('click', dontCloseHandler);
            window.removeEventListener('resize', adjustTabs);
        });
    },
]);
