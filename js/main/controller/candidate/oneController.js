controller.controller('CandidateOneController', [
    'CacheCandidates',
    '$localStorage',
    '$scope',
    'frontMode',
    '$translate',
    'googleService',
    '$location',
    '$stateParams',
    'Candidate',
    'Service',
    '$rootScope',
    'Person',
    'PersonalMailing',
    'serverAddress',
    'FileInit',
    'notificationService',
    '$filter',
    'Vacancy',
    'Action',
    'vacancyStages',
    'Task',
    'File',
    '$sce',
    '$window',
    'Mail',
    '$uibModal',
    '$timeout',
    '$route',
    'Test',
    'CandidateGroup',
    'CandidatesSlider',
    'PDConsent',
    'Mailing',
    '$state',
    'ngTableParams',
    '$anchorScroll',
    'Client',
    'Email',
    '$document',
    'gamificationNotify',
    '$http',
    'scoreCardsMainService',
    'BambooHrIntegrationService',
    '$httpParamSerializer',
    'meetTimeValues',
    'ScopeService',
    function (
        CacheCandidates,
        $localStorage,
        $scope,
        frontMode,
        $translate,
        googleService,
        $location,
        $stateParams,
        Candidate,
        Service,
        $rootScope,
        Person,
        PersonalMailing,
        serverAddress,
        FileInit,
        notificationService,
        $filter,
        Vacancy,
        Action,
        vacancyStages,
        Task,
        File,
        $sce,
        $window,
        Mail,
        $uibModal,
        $timeout,
        $route,
        Test,
        CandidateGroup,
        CandidatesSlider,
        PDConsent,
        Mailing,
        $state,
        ngTableParams,
        $anchorScroll,
        Client,
        Email,
        $document,
        gamificationNotify,
        $http,
        scoreCardsMainService,
        BambooHrIntegrationService,
        $httpParamSerializer,
        meetTimeValues,
        ScopeService,
    ) {
        delete $rootScope.client;
        delete $rootScope.allSkills;
        $rootScope.loading = true;
        $scope.serverAddress = serverAddress;
        $rootScope.objectSize = null;
        $rootScope.tagForEdit = {};
        $rootScope.meetTimeValues = meetTimeValues;

        const inputSearchThreshold = 2;
        $localStorage.remove('candidateForTest');
        $scope.requiredInputs = ['candidateEmail', 'calendarTitle'];
        $scope.emptyRequiredInputs = [];
        $scope.curentSavedPDconsentTemplate = { subject: '', text: '' };
        $scope.tagCandidateModel = {};
        $scope.tagCandidates = [];
        $scope.showPopup = false;
        $scope.PDCSTmpltChanged = false;
        $scope.PDCSTmplLang = $rootScope.currentLang;
        $scope.allTests = [];
        $rootScope.fromSendModel = {};
        $rootScope.transferAndSent = false;
        $scope.candidateProperties = {};
        $scope.candidate = {};
        $scope.isExternal = true;
        $scope.notSelectedResponsabilities = $filter('translate')('Not selected');
        $scope.contactAvailability = {};
        $scope.oldResponsibleUserInCandidate = null;
        $scope.allListDisplayed = false;

        try {
            $scope.sliderId = CandidatesSlider.getSliderId();

            if ($state.$current.name === 'candidate-slide') {
                $scope.sliderData = CandidatesSlider.initSlider();
                $scope.slideForward = CandidatesSlider.slideForward;
                $scope.slideBack = CandidatesSlider.slideBack;

                if (
                    ['search-candidates', 'advanced-search-candidates', 'suggestions'].includes(
                        $scope.sliderData?.source,
                    )
                ) {
                    $rootScope.showSlider = true;
                }
            }
        } catch (e) {
            console.error('Error in Slider init: ', e);
        }

        $rootScope.me.orgParams.logoId
            ? ($scope.logoLink = '/hr/getlogo?id=' + $rootScope.me.orgParams.logoId)
            : ($scope.logoLink = null);
        $rootScope.emailTemplateInModal = $rootScope.emailTemplateInModal || {
            title: '',
            text: '',
        };

        $scope.urlTaskId = $location.$$absUrl.split('?task=')[1];

        $scope.isShowContacts = () => {
            if ($scope.candidate.status === 'external_recommendation') {
                return $scope.candidateProperties?.candidateExternalIntegration?.status !== 'A';
            }

            return true;
        };

        $scope.historyLimit = 15;
        $scope.lang = $translate.use();

        $rootScope.isNeedToResetSearch =
            $rootScope.previousLocation !== '/candidates?ids' &&
            $rootScope.previousLocation.indexOf('/candidates/slide/{sliderDataId}/{id}') === -1;

        if ($state.$current.name === 'candidate-slide') {
            $state.go('candidate-slide.mainInfo');
        } else if ($state.$current.name === 'candidate') {
            $state.go('candidate.mainInfo');
        }

        const reloadPage = () => {
            $timeout(function () {
                $state.reload();
            }, 0);
        };

        $scope.handleWandifySuccess = () => {
            reloadPage();
        };

        Person.getGoogleCalendar(function (resp) {
            if (resp.object) {
                $scope.googleCalendar = true;
            } else {
                $scope.googleCalendar = false;
            }
        });

        Person.getOutlookCalendar(function (resp) {
            $scope.outlookLabelText = 'Create teams-skype-meet';
            $scope.outlookTooltipText = '';
            if (resp.object) {
                $scope.outlookCalendar = true;
                $scope.availableOutlookMeetingType = resp.object.onlineMeetingProviderType;
                switch ($scope.availableOutlookMeetingType) {
                    case 'TEAMS_FOR_BUSINESS':
                        $scope.outlookLabelText = 'Create teams-meet';
                        break;
                    case 'SKYPE_FOR_BUSINES':
                    case 'SKYPE_FOR_CONSUMER':
                        $scope.outlookLabelText = 'Create skype-meet';
                        break;
                    case 'UNKNOWN':
                        $scope.outlookTooltipText = 'Blocked Skype/Teams Meetings';
                        $scope.outlookLabelText = 'Create teams-skype-meet';
                }
            } else {
                $scope.outlookCalendar = false;
            }
        });

        Person.isIntegratedEmail().then(
            (resp) => {
                $scope.isIntegratedMail = true;
                if (resp.object.emails.some((email) => email.personalMailing)) {
                    $scope.isIntegratedMailAndSwitched = true;
                }
            },
            (resp) => {
                if (resp) {
                    $scope.isIntegratedMail = false;
                    $scope.isIntegratedMailAndSwitched = false;
                }
            },
        );
        $scope.variableForTooltip = $sce.trustAsHtml(
            $filter('translate')('Email log displays all your email correspondence with a candidate via email import') +
                $filter('translate')(
                    'This feature works only after you integrate your email with your CleverStaff account. If you did not integrate your email, do it here: https://cleverstaff.net/!#/email-integration',
                ) +
                $filter('translate')(
                    'CleverStaff updates the email history 1 time per 1 hour. The time and date of the last integration + the number of imported emails are listed below',
                ),
        );
        $rootScope.staticEmailTemplate = {
            candidateName: 'John Dou',
            date: *************,
            recruiterName: $rootScope.me.fullName,
            recruiterEmail: $rootScope.me.emails.length > 0 ? $rootScope.me.emails[0].email : $rootScope.me.login,
        };
        angular.forEach($rootScope.me.contacts, function (val) {
            if (val.contactType == 'phoneWork') {
                $rootScope.staticEmailTemplate.phoneWork = val.value;
            }
            if (val.contactType == 'skype') {
                $rootScope.staticEmailTemplate.skype = val.value;
            }
            if (val.contactType == 'linkedin') {
                $rootScope.staticEmailTemplate.linkedin = val.value;
            }
            if (val.contactType == 'facebook') {
                $rootScope.staticEmailTemplate.facebook = val.value;
            }
        });
        if ($stateParams.isFromVacancyToCandidate) {
            sessionStorage.setItem('isFromVacancyToCandidateId', $stateParams.isFromVacancyToCandidate);
        }

        $scope.advicesLimit = 3;
        $scope.showAddLink = false;
        $scope.editCommentFlag = false;
        $scope.vacancy = null;
        $rootScope.inactiveVacancies = false;
        $scope.addLinkErrorShow = false;
        $scope.showAddedLinks = false;
        $scope.showAddedFiles = false;
        $scope.linked = false;
        $scope.currentTab = 'profile';
        $scope.showHistoryForPrint = false;
        $scope.showEditFileName = false;
        $rootScope.googleMeet = false;
        $rootScope.outlookMeet = false;
        $rootScope.meetDuration = { name: '1 hour', value: 60 };
        $scope.showGoogleComment = false;
        $rootScope.currentCandidateTabs = '';
        $scope.meetUsers = [];
        $rootScope.showMeetusers = [];
        $scope.googleCalendar = false;
        $rootScope.responsiblePersonsEdit = [];
        $rootScope.showGoogleComment = false;
        $scope.showMenuEdDelFile = false;
        $rootScope.showEmployedFields = false;
        $rootScope.probationaryPeriods = ['week_2', 'month_1', 'month_2', 'month_3'];
        $rootScope.saveFromAdviceClicked = false;
        $scope.todayDate = new Date().getTime();
        $rootScope.showEditNameTask = false;
        $rootScope.editableTaskOuter = false;
        $rootScope.showEditTextTask = false;
        $scope.todayDate = new Date().getTime();
        $rootScope.clickedSaveStatusOfCandidate = false;
        $rootScope.clickedAddVacancyInCandidate = false;
        $scope.deleteFromSystem = false;
        $scope.onlyWithComment = false;
        $('html, body').animate({ scrollTop: 0 }, 'slow');
        $scope.stageUrl = stageUrlHandrel(localStorage.getItem('stageUrl'));
        $scope.paginationParams = {
            currentPage: 1,
            totalCount: 0,
        };
        $scope.vcv = { vacancy: null, vacancies: [], candidate: null };
        $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);

        $scope.probationaryPeriods = [
            { value: 'week_2' },
            { value: 'month_1' },
            { value: 'month_2' },
            { value: 'month_3' },
        ];

        const ourStages = Vacancy.interviewStatusNew();
        const checkIsRefuseStage = (stage) => {
            if (ourStages[0].status.some((item) => item.value === stage && item?.type === 'refuse')) return true;
            if (
                $rootScope.customStages.some(
                    (item) => item.name === stage && item.type === 'refuse', // here used name, not customID
                )
            )
                return true;
        };
        const changeTemplatesOptions = () => {
            $rootScope.templatesCustomLetter.forEach((item) => {
                item.label = item.type ? item.type : item.name;
            });
            $rootScope.templatesGeneralLetter.forEach((item) => {
                item.label = item.type ? item.type : item.name;
            });
            $scope.templatesGrouped = [
                {
                    label: 'My templates',
                    options: $rootScope.templatesCustomLetter,
                },
                {
                    label: 'General templates',
                    options: $rootScope.templatesGeneralLetter,
                },
            ];
        };
        const getAllEmailTemplates = (status) => {
            Mail.getAllTemplates(
                { forVacancy: true, type: checkIsRefuseStage(status) ? 'refusal' : 'interview' },
                function (resp) {
                    $rootScope.loading = false;
                    if (resp.status === 'ok') {
                        $rootScope.testTemplates = resp.object;
                        $rootScope.templatesCustomLetter = resp.object.custom;
                        $rootScope.templatesGeneralLetter = resp.object.general;
                        changeTemplatesOptions();
                    }
                },
            );
        };

        function stageUrlHandrel(stageUrl) {
            const parsedStageUrl = JSON.parse(stageUrl);
            resetLocalStorageParam('stageUrl');

            if (!sessionStorage.getItem('localStageUrl') && sessionStorage.getItem('localStageUrl') != 'null') {
                parsedStageUrl && sessionStorage.setItem('localStageUrl', JSON.stringify(parsedStageUrl));
            }
            return sessionStorage.getItem('isFromVacancyToCandidateId')
                ? JSON.parse(sessionStorage.getItem('localStageUrl'))
                : null;
        }

        function resetLocalStorageParam(param) {
            localStorage.removeItem(param);
        }

        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
        };

        $rootScope.openSkype = (val) => {
            const a = document.createElement('a');
            a.href = `skype:${val}?chat`;
            a.click();
        };

        $rootScope.openViber = (val) => {
            const a = document.createElement('a');

            if (val.slice(0, 2) === '80') {
                a.href = `viber://chat?number=+3${val}`;
            } else {
                a.href = `viber://chat?number=${val}`;
            }

            a.click();
        };

        $rootScope.openWhatsapp = (val) => {
            val = val.trim();
            const a = document.createElement('a');

            if (val.slice(0, 2) === '80') {
                a.href = `https://wa.me/+3${val}`;
            } else if (val.slice(0, 1) === '0') {
                a.href = `https://wa.me/+38${val}`;
            } else {
                a.href = `https://wa.me/${val}`;
            }

            a.target = '_blank';
            a.click();
        };

        $rootScope.openTelegram = (val) => {
            val = val.trim();
            const a = document.createElement('a');

            if (val.slice(0, 2) === '80') {
                a.href = `https://t.me/+3${val}`;
            } else if (val.slice(0, 3) === '380') {
                a.href = `https://t.me/+${val}`;
            } else if (val.slice(0, 1) === '0') {
                a.href = `https://t.me/+38${val}`;
            } else if (val.slice(0, 5) === 'https') {
                a.href = val;
            } else if (val.slice(0, 1) === '@') {
                const str = val.substring(1);
                a.href = `https://t.me/${str}`;
            } else {
                a.href = `https://t.me/${val}`;
            }

            a.target = '_blank';
            a.click();
        };

        $scope.closeModal = function (status) {
            if (status) {
                $scope.changeStatus = status;
            }
            $scope.modalInstance.close();
        };

        $rootScope.newTask = {
            title: '',
            text: '',
            targetDate: '',
            candidateId: '',
            responsibleIds: [],
            type: 'Task',
        };
        $scope.addLinkToCandidate = {
            name: '',
            url: '',
        };
        $rootScope.changeStatusOfInterviewEmployed = {
            candidate: '',
            comment: '',
            status: '',
            date: null,
            exportgoogle: false,
        };
        $scope.tests = [];
        $rootScope.deleteFromSystem = null;
        $rootScope.changeStateInCandidate = {
            status: '',
            comment: '',
            placeholder: null,
            status_old: null,
        };
        $scope.status = Candidate.getStatus();

        if ($rootScope.me.recrutRole === 'client') {
            $scope.status = $scope.status.filter((status) => status.value !== 'archived');
        }

        $scope.statusAssoc = Candidate.getStatusAssociative();
        $scope.showMap =
            $localStorage.get('vacancyShowMap') != undefined ? JSON.parse($localStorage.get('vacancyShowMap')) : true;
        $scope.showHideMap = function () {
            $scope.showMap = !$scope.showMap;
            $localStorage.set('vacancyShowMap', $scope.showMap);
        };

        $scope.candProgress = function () {
            Candidate.progressUpdate($scope, false);
        };
        $scope.addLinkToCandidate = {
            name: '',
            url: '',
        };
        $scope.map = {
            center: {
                latitude: 48.379433,
                longitude: 31.165579999999977,
            },
            zoom: 5,
            options: {
                panControl: true,
                zoomControl: true,
                scaleControl: true,
                mapTypeControl: true,
                mapTypeId: 'roadmap',
            },
        };
        $scope.marker = {
            id: 1,
            title: '',
            coords: {
                latitude: null,
                longitude: null,
            },
        };

        $rootScope.statusInter = Vacancy.getInterviewStatus();

        $rootScope.addCandidateInVacancySelect2Obj = {
            status: null,
        };

        $rootScope.addCandidateInVacancy = {
            id: '',
            comment: '',
            status: 'longlist',
            date: null,
            showSelect: '',
            showText: false,
            text: '',
        };
        $rootScope.errorMessageForAddCandidateInVacancy = {
            show: false,
            text: '',
        };

        $scope.changeTab = function (tabs) {
            $scope.currentTab = tabs;
        };

        // $scope.addToMeetArray = function (event) {
        //     let user = event;
        //     let removeUser = $scope.meetUsers.findIndex(
        //         (person) => person.userId === user.userId || person.userId === null,
        //     );
        //
        //     if (user.candidateId && user.candidateEmail === null) {
        //         notificationService.error($filter('translate')('withoutEmail'));
        //         return;
        //     }
        //     $rootScope.showMeetusers.push(user);
        //     $scope.meetUsers.splice(removeUser, 1);
        // };

        $scope.addToMeetArray = function (event) {
            $rootScope.showMeetusers = event;
            $scope.$$phase || $scope.$apply();
        };

        $scope.showNotifyEmailArrowAddToVacancy = function () {
            return (
                $rootScope.addCandidateInVacancy?.status?.value != 'accept_offer' &&
                !$scope.emailAutoActionForStage &&
                !$scope.testAutoActionForStage &&
                !$scope.isAutoActionForStage &&
                !$rootScope.outlookMeet &&
                ($rootScope.addCandidateInVacancy.status.withDate ||
                    $rootScope.addCandidateInVacancy?.status?.value == 'interview' ||
                    $rootScope.addCandidateInVacancy?.status?.value == 'notafit' ||
                    $rootScope.addCandidateInVacancy?.status?.value == 'declinedoffer' ||
                    $rootScope.addCandidateInVacancy?.status?.value == 'no_response' ||
                    $rootScope.addCandidateInVacancy?.status?.value == 'no_contacts' ||
                    $rootScope.addCandidateInVacancy?.status?.type == 'interview' ||
                    $rootScope.addCandidateInVacancy?.status?.type == 'refuse') &&
                !$scope.isPresent
            );
        };

        $scope.openNotifyEmail = (editorId) => {
            $rootScope.candnotify.show = !$rootScope.candnotify.show;
            $rootScope.candnotifyClick();
            $rootScope.detectCkEditor(editorId);
        };

        $scope.deletUserFromMeet = function (event) {
            const person = event.target;
            $rootScope.showMeetusers.find((user, index) => {
                if (user !== undefined) {
                    if ((user.userId || user.candidateId) === (person.dataset.userid || person.dataset.candidateid)) {
                        $rootScope.showMeetusers.splice(index, 1);
                        if (person.dataset.userid !== 'null') {
                            $scope.meetUsers.unshift(user);
                        }
                    }
                }
            });

            person.parentElement.remove();
        };

        $scope.initMeetingUsers = function (responsibles, candidate, responsibleVacancy) {
            function createObject(person) {
                return {
                    id: person.userId ? person.userId : person.candidateId,
                    fullName: person.fullName,
                    email: person.login ? person.login : 'no email',
                    isUser: !!person.userId,
                    key: `${person.login ? person.login : ''}-${window.crypto.randomUUID()}`,
                };
            }

            $scope.meetUsers = $rootScope.persons
                .filter((user) => user.status === 'A')
                .map((user) => createObject(user));

            if (!candidate.contacts) candidate.contacts = [];
            let candidateMail = candidate.contacts.filter((mail) => mail.type === 'email' && mail.default === true);
            candidate.login = candidateMail.length > 0 ? candidateMail[0].value : null;

            if (candidate.login) {
                const candidateObj = createObject(candidate);
                $scope.meetUsers.push(candidateObj);
                $rootScope.showMeetusers.push(candidateObj);
            }

            responsibles.forEach((responsible) => {
                const responsibleUser = $scope.meetUsers.find((user) => user.id === responsible.userId);
                responsibleUser && $rootScope.showMeetusers.push(responsibleUser);
            });

            if (responsibleVacancy) {
                responsibleVacancy.forEach((responsible) => {
                    const responsibleUser = $scope.meetUsers.find((user) => user.id === responsible.personId);
                    responsibleUser && $rootScope.showMeetusers.push(responsibleUser);
                });
            }

            $rootScope.showMeetusers = Service.addMeToMeetUsers($rootScope.showMeetusers, createObject);

            $rootScope.showMeetusers = $rootScope.showMeetusers.filter(
                (tag, index, array) => array.findIndex((t) => t['id'] == tag['id']) == index,
            );
            $rootScope.changeStatusOfInterviewInVacancy.meetComment = '';
        };

        $scope.createMapForMeeting = function () {
            $scope.meetParticipants = new Map();
            $rootScope.showMeetusers.forEach((user) => {
                $scope.meetParticipants.set(user.email, user.id && !user.isNew ? user.id : 'null');
            });

            $scope.meetParticipants = Object.fromEntries($scope.meetParticipants.entries());
        };

        $scope.changeStageTemplate = function (vacancyId) {
            let changeObj = $rootScope.changeStatusOfInterviewInVacancy;
            let stage = changeObj.status;
            $scope.isPresent = false;
            $scope.isAutoActionForStage = true;
            $scope.emailAutoActionForStage = false;
            $scope.testAutoActionForStage = false;

            const isPresentReq = {
                method: 'GET',
                url: `/hr/autoaction/isPresent?vacancyId=${vacancyId}&stage=${
                    stage.customInterviewStateId ? stage.customInterviewStateId : stage.value
                }`,
            };

            $http(isPresentReq).then(function (resp) {
                if (resp.data.object === true) $scope.isPresent = true;
            });

            const req = {
                method: 'POST',
                url: `/hr/autoaction/getByStage`,
                data: {
                    vacancyId: vacancyId,
                    customStateId: stage.customInterviewStateId ? stage.customInterviewStateId : stage.value,
                },
            };
            $http(req).then(function (resp) {
                $scope.isAutoActionForStage = false;
                if (resp.data.status === 'ok') {
                    resp.data.objects.forEach((item) => {
                        if (item.type === 'sendMail') {
                            $scope.emailAutoActionForStage = item;
                        }
                        if (item.type === 'sendTest') {
                            $scope.testAutoActionForStage = item;
                        }
                    });
                }
            });

            const smsRequest = {
                method: 'POST',
                url: '/hr/interviewReminder/get',
                data: {
                    vacancyId: vacancyId,
                    state: stage?.customInterviewStateId ? stage?.customInterviewStateId : stage?.value,
                },
            };
            $http(smsRequest).then(function (resp) {
                $scope.smsAutoActionForStage = resp.data.object || null;
            });
        };

        $scope.getStageTemplate = function (resetTime) {
            if (resetTime) $scope.addCandidateInvacancyPicker = null;
            let stage = $rootScope.status2;
            let vacancyId = $rootScope.VacancyAddedInCandidate.vacancyId;
            $scope.isPresent = false;
            $scope.isAutoActionForStage = true;
            $scope.emailAutoActionForStage = false;
            $scope.testAutoActionForStage = false;

            const isPresentReq = {
                method: 'GET',
                url: `/hr/autoaction/isPresent?vacancyId=${vacancyId}&stage=${
                    stage?.customInterviewStateId ? stage?.customInterviewStateId : stage?.value
                }`,
            };

            $http(isPresentReq).then(function (resp) {
                if (resp.data.object === true) $scope.isPresent = true;
            });

            const req = {
                method: 'POST',
                url: `/hr/autoaction/getByStage`,
                data: {
                    vacancyId: vacancyId,
                    customStateId: stage?.customInterviewStateId ? stage?.customInterviewStateId : stage?.value,
                },
            };
            $http(req).then(function (resp) {
                $scope.isAutoActionForStage = false;
                if (resp.data.status === 'ok') {
                    resp.data.objects.forEach((item) => {
                        if (item.type === 'sendMail') {
                            $scope.emailAutoActionForStage = item;
                        }
                        if (item.type === 'sendTest') {
                            $scope.testAutoActionForStage = item;
                        }
                    });
                }
            });
            if ($scope.addCandidateInvacancyPicker) {
                $timeout(
                    () =>
                        $rootScope.checkDateTimeCandidate(
                            $scope.addCandidateInvacancyPicker,
                            'addCandidateInvacancyPicker',
                        ),
                    800,
                );
            }

            const smsRequest = {
                method: 'POST',
                url: '/hr/interviewReminder/get',
                data: {
                    vacancyId: vacancyId,
                    state: stage?.customInterviewStateId ? stage?.customInterviewStateId : stage?.value,
                },
            };
            $http(smsRequest).then(function (resp) {
                $scope.smsAutoActionForStage = resp.data.object || null;
            });
        };

        $scope.exportAllScorecards = function () {
            scoreCardsMainService
                .exportToExcel({
                    candidateId: $scope.candidate.candidateId,
                    status: 'A',
                })
                .then((resp) => {
                    if (resp.object) {
                        $('#downloadScorecardExcel')[0].href = '/hr/' + 'getapp?id=' + resp.object;
                        $('#downloadScorecardExcel')[0].click();
                    }
                });
        };

        const fetchVacancies = (inputText, callback) => {
            if (inputText?.length && inputText?.length < inputSearchThreshold) return callback();

            const fetchOnlyMyVacancies =
                ScopeService.getActiveScopeObject().name === 'onlyMy' && ScopeService.getActiveScopeObject().check;

            Vacancy.onGetAllVacancies({
                position: inputText.trim(),
                candidateId: $scope.candidate.candidateId,
                showCandidate: false,
                ...(fetchOnlyMyVacancies && { responsibleId: $rootScope.userId }),
            }).then((resp) => {
                if (resp.status !== 'ok') return;
                callback($rootScope.addClientAndLocationToVacancyLabels(resp.objects));
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.toAddVacancyForm = function (state, showSelect, showText) {
            $rootScope.tempEmailTemplate = null;
            $scope.isPresent = false;
            $scope.isAutoActionForStage = true;
            $scope.emailAutoActionForStage = false;
            $scope.testAutoActionForStage = false;
            if ($scope.candidate.status != 'archived') {
                $rootScope.addCandidateInVacancy.showText = showText;
                $rootScope.addCandidateInVacancy.showSelect = showSelect;
                $rootScope.addCandidateInVacancy.inVacancy = false;
                $rootScope.addCandidateInVacancy.statusObject = null;
                $rootScope.VacancyStatusFiltered = null;
                $rootScope.candidateAddedInVacancy = null;
                $rootScope.addCandidateInVacancy.comment = '';
                $('#candidateAddToVacancy').select2('val', null);
                $rootScope.addCandidateInVacancy.status =
                    state !== null
                        ? state
                        : {
                              value: 'longlist',
                              withDate: false,
                              defaultS: true,
                              single: false,
                              added: true,
                              active_color: 'longlist_color',
                              useAnimation: false,
                              count: 0,
                              forAdd: true,
                          };
                if ($rootScope.me.recrutRole != 'client') {
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        templateUrl: '../partials/modal/candidate-add-in-vacancy.html',
                        windowClass: 'change-state-modal-wrapper',
                        scope: $scope,
                        resolve: {
                            items: function () {
                                return $scope.items;
                            },
                        },
                        controller: [
                            '$scope',
                            function ($scope) {
                                $scope.onFetchVacancies = $rootScope.makeDebounce(fetchVacancies, 300);
                                $scope.onChangeVacancy = (value) => {
                                    $scope.vacancyToAddCandidate = value;
                                    $rootScope.VacancyAddedInCandidate = value;
                                    $rootScope.changeCalendarTitle(value);
                                    const sortedStages = [];
                                    const array = value.interviewStatus.split(',');
                                    let VacancyStatus = Vacancy.interviewStatusNew();
                                    let i = 0;

                                    angular.forEach(array, (resp) => {
                                        angular.forEach(VacancyStatus, (vStatus) => {
                                            if (vStatus.used) {
                                                if (i === 0) {
                                                    angular.forEach($rootScope.customStages, (res) => {
                                                        res.value = res.name;
                                                        res.movable = true;
                                                        res.added = false;
                                                        res.count = 0;
                                                        vStatus.status.push(res);
                                                        i = i + 1;
                                                    });
                                                }
                                                angular.forEach(vStatus.status, (vStatusIn) => {
                                                    if (
                                                        resp === vStatusIn.value &&
                                                        vStatusIn.value !== 'applied_people'
                                                    ) {
                                                        vStatusIn.added = true;
                                                        sortedStages.push(vStatusIn);
                                                    } else if (resp === vStatusIn.customInterviewStateId) {
                                                        vStatusIn.added = true;
                                                        sortedStages.push(vStatusIn);
                                                    }
                                                });
                                            }
                                        });
                                    });
                                    $rootScope.VacancyStatusFiltered = [...sortedStages];
                                    $rootScope.VacancyStatusFiltered.forEach((stage) => {
                                        if (stage.value === 'approved') stage.color = '#77B472';
                                        if (stage.type === 'refuse') stage.color = '#d67354';
                                    });
                                    $rootScope.status2 = $rootScope.VacancyStatusFiltered.find(
                                        (status) => status.value === 'longlist',
                                    );

                                    $rootScope.$$phase || $rootScope.$apply();
                                };
                                $scope.onChangeVacancyStatus = (value) => {
                                    const resetTime = $rootScope.status2?.withDate && !value?.withDate;
                                    $rootScope.status2 = value;
                                    // $rootScope.addCandidateInVacancy.status.value = value.value;
                                    $rootScope.changeTemplateInAddCandidate($rootScope.status2);
                                    getAllEmailTemplates($rootScope.status2.value);
                                    $scope.getStageTemplate(resetTime);
                                    $rootScope.$$phase || $rootScope.$apply();
                                };
                            },
                        ],
                    });
                    $scope.modalInstance.opened.then(() => {
                        initUsersTags();
                        $scope.changeVacancyStage = false;
                        $scope.isCalendarTitleError = false;
                        $scope.addCandidateInvacancyPicker = null;
                        $rootScope.ckEditorCandidateAddVacancy = {
                            height: 140,
                            fontSize_defaultLabel: '10px',
                            fontSize_sizes: '10px',
                            width: '100%',
                            toolbar: [],
                            enterMode: CKEDITOR.ENTER_BR,
                            shiftEnterMode: CKEDITOR.ENTER_BR,
                            autoGrow_minHeight: 140,
                            autoGrow_maxHeight: 340,
                            extraPlugins:
                                'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                            removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                            editorplaceholder: $translate.instant('mention you'),
                            mentions: [
                                {
                                    feed: dataCallback,
                                    minChars: 0,
                                    itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                                    outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                                    marker: '@',
                                    pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                                },
                            ],
                            on: {
                                paste: function (ev) {
                                    ev.data.dataValue = '';
                                    CKEDITOR.instances['ckEditorCandidateAddVacancy'].insertText(
                                        ev.data.dataTransfer._.data.Text,
                                    );
                                    setTimeout(() => {
                                        let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                                        if (bodyHeight > ev.editor.config.height) {
                                            if (bodyHeight < 605) {
                                                ev.editor.resize(null, bodyHeight);
                                            }
                                        }
                                    }, 0);
                                },
                                afterInsertHtml: function (ev) {
                                    let user = ev.editor.data;
                                    if (user) {
                                        deleteFromTags(user.userId, 'userId');
                                        $scope.deletedUsers.push(user);
                                    }
                                },
                                contentDom: function (evt) {
                                    var editable = evt.editor.editable();
                                    editable.attachListener(editable, 'keyup', function (evt) {
                                        if (evt.data.getKeystroke() === 1114125) {
                                            $rootScope.addVacancyInCandidate(null, null, null);
                                        }
                                    });
                                },
                                instanceReady: function (ev) {
                                    $scope.deletedUsers = [];
                                    $scope.afterDelete = [];

                                    ev.editor.dataProcessor.writer.setRules('p', {
                                        indent: false,
                                        breakBeforeOpen: false,
                                        breakAfterOpen: false,
                                        breakBeforeClose: false,
                                        breakAfterClose: false,
                                    });
                                    ev.editor.dataProcessor.writer.setRules('br', {
                                        indent: false,
                                        breakBeforeOpen: false,
                                        breakAfterOpen: false,
                                        breakBeforeClose: false,
                                        breakAfterClose: false,
                                    });
                                },
                            },
                        };

                        function dataCallback(opts, callback) {
                            opts.query = opts.query.replace(/\u00A0/g, ' ');
                            editUsersInTags('ckEditorCandidateAddVacancy');

                            if (
                                opts.query.length === 0 &&
                                $scope.repsonsibleUsers.length > 0 &&
                                $scope.repsonsibleUsers[0].userId !== $rootScope.me.userId
                            ) {
                                setTimeout(function () {
                                    callback(
                                        $scope.repsonsibleUsers.filter(function (item) {
                                            return item.fullname;
                                        }),
                                    );
                                });
                            } else {
                                setTimeout(function () {
                                    callback(
                                        $scope.users.filter(function (item) {
                                            return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                                        }),
                                    );
                                });
                            }
                        }

                        $rootScope.candnotify = {};
                        $rootScope.candnotify.emails =
                            $scope.candidate.contacts &&
                            $scope.candidate.contacts.filter((contact) => contact.type === 'email');
                        $rootScope.candnotify.sendMail = $rootScope.candnotify.emails
                            .filter((email) => email.default)
                            .map((email) => email.value)[0];
                        $rootScope.candnotify.show = false;
                        $rootScope.candnotify.name = $scope.candidate;
                        $rootScope.candnotify.fullName = $scope.candidate.fullName;
                        $rootScope.googleMeet = false;
                        $rootScope.outlookMeet = false;
                        $rootScope.meetDuration = { name: '1 hour', value: 60 };
                        $rootScope.addCandidateInVacancy.date = null;
                        $scope.showGoogleComment = false;
                        $scope.meetUsers = [];
                        $rootScope.showMeetusers = [];
                        $scope.meetParticipants = {};
                    });
                    $scope.modalInstance.closed.then(function () {
                        $scope.emptyRequiredInputs = [];
                        $rootScope.candidateTimeChangeStatus = null;
                        $rootScope.addCandidateInVacancy.date = null;
                        $scope.addCandidateInvacancyPicker = null;
                        delete $rootScope.vacancyForAddCandidate;
                    });
                } else {
                    notificationService.error(
                        $filter('translate')(
                            'Only recruiters, admins and freelancers can adding candidates in vacancy',
                        ),
                    );
                }
            } else {
                notificationService.error($filter('translate')('Remote candidates can not be added to the job'));
            }
            Service.createEmailTemplateFunc($scope, $rootScope, 'addCandidateInVacancyMCE', Mail, $location);
        };
        $rootScope.checkDateTimeCandidate = function (val, name) {
            let checkInterviewDate = $rootScope.emailTemplateInModal.text;
            if (!val) {
                return;
            }
            if ($rootScope.VacancyAddedInCandidate) {
                Vacancy.onGetVacancyResponsibles({ vacancyId: $rootScope.VacancyAddedInCandidate.vacancyId }).then(
                    (resp) => {
                        if ($scope.candidateProperties.responsible.fullName !== '') {
                            $scope.initMeetingUsers(
                                [$scope.candidateProperties.responsible],
                                $scope.candidate,
                                resp.object.responsibles,
                            );
                        } else {
                            $scope.initMeetingUsers([], $scope.candidate, resp.object.responsibles);
                        }
                    },
                );
            }

            let text = $rootScope.emailTemplateInModal.text;
            $scope.addCandidateInvacancyPicker = Date.parse(val) ? Date.parse(val) : val;
            $rootScope.addCandidateInVacancy.date = $scope.addCandidateInvacancyPicker;
            if (
                (name === 'addCandidateInvacancyPicker' || name === 'changeStatusOfInterviewInVacancyPick') &&
                checkInterviewDate.includes('[[interview date and time]]')
            ) {
                let indexOfOperator =
                    text.indexOf('[[interview date and time]]') > -1
                        ? text.indexOf('[[interview date and time]]')
                        : text.indexOf('[[attendance date and time]]') > -1
                        ? text.indexOf('[[attendance date and time]]')
                        : -1;

                let indexOfClassTime = text.indexOf('</a> at');

                if (indexOfOperator > -1) {
                    text = text.replace(
                        /\[\[interview date and time\]\]|\[\[attendance date and time\]\]/g,
                        $filter('dateFormatInterview')($scope.addCandidateInvacancyPicker, true),
                    );
                } else {
                    let start = text.slice(0, indexOfClassTime + '</a> at'.length + 1),
                        startWithClass = text.slice(indexOfClassTime + '</a> at'.length + 1),
                        end = startWithClass.slice(startWithClass.indexOf('</span>'));
                    text = start + $filter('dateFormatInterview')($scope.addCandidateInvacancyPicker, true) + end;
                }
                $rootScope.emailTemplateInModal.text = text;
                if ($scope.changeVacancyStage) {
                    $rootScope.saveDefaultTemplate = text;
                }
            } else if (name === 'addCandidateInvacancyPicker' || name === 'changeStatusOfInterviewInVacancyPick') {
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\b\d{2}\.\d{2} \d{2}:\d{2}\b/g,
                    $filter('dateFormatInterview')($scope.addCandidateInvacancyPicker, true),
                );
            }
            $rootScope.candidateTimeChangeStatus = $filter('dateFormatInterview')(
                $scope.addCandidateInvacancyPicker,
                true,
            );
            if (
                $rootScope.candidateTimeChangeStatus &&
                $rootScope.emailTemplateInModal &&
                $rootScope.emailTemplateInModal.text
            ) {
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\[\[interview date and time\]\]/g,
                    $rootScope.candidateTimeChangeStatus,
                );
            }
        };

        $rootScope.changeCalendarTitle = function (vacancy = null) {
            $rootScope.calendarTitle = $filter('translate')('Default title for the calendar', {
                vacancy: vacancy ? vacancy.position : $rootScope.VacancyAddedInCandidate.vacancy.position,
                candidate: $rootScope.useAmericanNameStyle ? $scope.candidate.fullNameEn : $scope.candidate.fullName,
            });
        };

        $scope.toggleMeetType = function (meetType) {
            $rootScope[meetType] = !$rootScope[meetType];
            $rootScope[meetType === 'googleMeet' ? 'outlookMeet' : 'googleMeet'] = false;
        };

        $scope.changeTemplateForGoogleMeet = function (time, candidate, vacancy) {
            const link = window.location.origin;
            const templateRu = `
            <div style='font-size: 14px'>
            <p>Здравствуйте, ${$rootScope.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName}!</p>
            <p>В продолжение нашего разговора, подтверждаю онлайн-собеседование на вакансию 
                <a href='${link}/i/vacancy-${vacancy.localId}'>${vacancy.position}</a>. 
                Ждем вас по ссылке "[[google meet link]]" 
                ${$filter('dateFormatInterview')(time, true)}</p>
            <p>Если будут дополнительные вопросы, свяжитесь с нами по телефону или по электронной почте.
                <p>Хорошего дня и до встречи!</p> -- 
                <p>С наилучшими пожеланиями!
                <br>
                ${$rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName}
                </p>
            </div>
            `;
            const templateUa = `
            <div style='font-size: 14px'>
            <p>Доброго дня, ${$rootScope.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName}!</p>
            <p>Продовжуючи нашу розмову, підтверджую онлайн-співбесіду на вакансію 
                <a href='${link}/i/vacancy-${vacancy.localId}'>${vacancy.position}</a>. 
                Чекаємо на вас за посиланням "[[google meet link]]" 
                ${$filter('dateFormatInterview')(time, true)}</p>
            <p>Якщо будуть додаткові питання, зв'яжіться з нами по телефону або електронною поштою.
                <p>Гарного дня та до зустрічі!</p> -- 
                <p>З найкращими побажаннями!
                <br>
                ${$rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName}
                </p>
            </div>`;
            const templateEn = `
            <div style='font-size: 14px'>
            <p>Hi, ${$rootScope.useAmericanNameStyle ? candidate.fullNameEn : candidate.fullName}!</p>
            <p>Following our previous  conversation, I confirm our online-interview for the 
                <a href='${link}/i/vacancy-${vacancy.localId}'>${vacancy.position}</a> position. 
                We are waiting for you by this link "[[google meet link]]" on 
                ${$filter('dateFormatInterview')(time, true)}</p>
            <p>If you have any additional questions, feel free to contact us by e-mail or phone.
                <p>Have a nice day and see you soon!</p> -- 
                <p>Best regards!
                <br>
                ${$rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName}
                </p>
            </div>`;
            const templatePl = `
            <div style='font-size: 14px'>
            <p>Dzień dobry, ${$rootScope.useAmericanNameStyle ? candidate?.fullNameEn : candidate?.fullName}!</p>
            <p>Kontynuując naszą rozmowę, potwierdzam rozmowę rekrutacyjną w formie online na stanowisko 
                <a href='${link}/i/vacancy-${vacancy?.localId}'>${vacancy?.position}</a>. 
                Czekamy na Państwa pod linkiem "[[google meet link]]" ${$filter('dateFormatInterview')(time, true)}</p>
            <p>W razie dodatkowych pytań, proszę o kontakt telefoniczny lub e-mailowy.
                <p>Życzę miłego dnia i do zobaczenia!</p> -- 
                <p>Wszystkiego najlepszego!!
                <br>
                ${$rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName}
                </p>
            </div>`;

            if ($rootScope.googleMeet) {
                if ($rootScope.currentLang === 'en') {
                    $rootScope.emailTemplateInModal.text = templateEn;
                    $rootScope.emailTemplateInModal.title = 'Invitation to an online interview';
                } else if ($rootScope.currentLang === 'ru') {
                    $rootScope.emailTemplateInModal.text = templateRu;
                    $rootScope.emailTemplateInModal.title = 'Приглашение на интервью онлайн';
                } else if ($rootScope.currentLang === 'ua') {
                    $rootScope.emailTemplateInModal.text = templateUa;
                    $rootScope.emailTemplateInModal.title = "Запрошення на інтерв'ю онлайн";
                } else {
                    $rootScope.emailTemplateInModal.text = templatePl;
                    $rootScope.emailTemplateInModal.title = 'Zaproszenie do wywiadu online';
                }
            } else {
                if (!$scope.changeVacancyStage) {
                    $rootScope.checkDateTimeCandidate(
                        $scope.addCandidateInvacancyPicker,
                        'addCandidateInvacancyPicker',
                    );
                } else {
                    $rootScope.checkDateTimeCandidate(
                        $scope.changeStatusOfInterviewInVacancyPick,
                        'changeStatusOfInterviewInVacancyPick',
                    );
                }
                if ($rootScope.tempEmailTemplate) {
                    $rootScope.emailTemplateInModal.title = $rootScope.tempEmailTemplate.title;
                    $rootScope.emailTemplateInModal.text = $rootScope.tempEmailTemplate.text;
                    $rootScope.replaceOperators($scope);
                } else {
                    $rootScope.emailTemplateInModal.text = $rootScope.saveDefaultTemplate;
                }
            }
        };

        // const mandatoryDateTimeOperators = ['[[interview date and time]]', '[[attendance date and time]]'];
        const mandatoryDateTimeOperators = ['[[interview date and time]]'];

        function exisitingDateTimeOperators() {
            return mandatoryDateTimeOperators.some(
                (operator) =>
                    $rootScope.emailTemplateInModal?.title.includes(operator) ||
                    $rootScope.emailTemplateInModal?.text.includes(operator),
            );
        }

        $rootScope.addVacancyInCandidate = function (sendTemplate, flag, event) {
            if (checkRequiredInputs(sendTemplate)) return;
            initUsersTags();

            const dateAndTimeText = $rootScope.status2.value.includes('accept_offer')
                ? 'Please enter a date and time'
                : 'Please enter a date and time of interview';

            if (event && event.type === 'keypress' && !keyBoardActionWithCtrlEnterButtons(event)) return;

            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorCandidateAddVacancy.getData());
            $rootScope.addCandidateInVacancy.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

            if ($rootScope.googleMeet || $rootScope.outlookMeet) {
                $scope.createMapForMeeting();
                $scope.googleMeetFlag = $rootScope.googleMeet;
            }

            if (
                $rootScope.candnotify.show &&
                !$rootScope.status2.value.includes('refuse') &&
                exisitingDateTimeOperators()
            ) {
                $rootScope.scrollToDomElement('#modalDateElement', 'smooth');
                notificationService.error($filter('translate')(dateAndTimeText));
                return;
            }

            if (flag && !$rootScope.addCandidateInVacancy.date) {
                $rootScope.scrollToDomElement('#modalDateElement', 'smooth');
                notificationService.error($filter('translate')(dateAndTimeText));
                return;
            }

            if (flag && !$rootScope.addCandidateInVacancy.date) {
                if ($rootScope.addCandidateInVacancy.status.type !== 'refuse' && exisitingDateTimeOperators()) {
                    $rootScope.scrollToDomElement('#modalDateElement', 'smooth');
                    return notificationService.error($filter('translate')(dateAndTimeText));
                }
            }

            if (!$rootScope.clickedAddVacancyInCandidate) {
                $rootScope.clickedAddVacancyInCandidate = true;

                if ($('#candidateAddToVacancy').select2('data') == null) {
                    $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                    $rootScope.errorMessageForAddCandidateInVacancy.text = $filter('translate')(
                        'You must specify the position',
                    );
                    $rootScope.clickedAddVacancyInCandidate = false;
                    return;
                }

                var vacancyId = $rootScope.VacancyAddedInCandidate.vacancyId;
                $rootScope.errorMessageForAddCandidateInVacancy.show = false;
                $rootScope.addCandidateInVacancy.date =
                    $scope.addCandidateInvacancyPicker != null &&
                    ($rootScope.addCandidateInVacancy.status.withDate ||
                        $rootScope.addCandidateInVacancy.status.type == 'interview')
                        ? $scope.addCandidateInvacancyPicker
                        : null;

                if (sendTemplate == true) {
                    Service.vacancyAddInterviewAndNotify(
                        Vacancy,
                        vacancyId,
                        null,
                        $scope.candidate.candidateId,
                        $rootScope.addCandidateInVacancy.comment,
                        $rootScope.addCandidateInVacancy.status.customInterviewStateId
                            ? $rootScope.addCandidateInVacancy.status.customInterviewStateId
                            : $rootScope.addCandidateInVacancy.status.value,
                        $rootScope.addCandidateInVacancy.date,
                        function (resp) {
                            resp.object.vacancyId.interviewStatusNotTouchable = resp.object.vacancyId.interviewStatus;
                            $scope.setParticipationInVacancies();
                            $rootScope.$emit('onUpdateCandidateHistory');
                            updateCandidateProperties();
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.addCandidateInVacancy.comment = '';
                            $rootScope.addCandidateInVacancy.id = null;
                            $scope.candidate.interviews = [];
                            $scope.candidate.interviews.push(resp.object);
                            angular.forEach($scope.candidate.interviews, function (interview) {
                                if (interview.vacancyId.interviewStatus == undefined) {
                                    interview.vacancyId.interviewStatus =
                                        'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                    interview.vacancyId.interviewStatusNotTouchable =
                                        interview.vacancyId.interviewStatus;
                                }
                            });
                            angular.forEach($scope.candidate.interviews, function (value) {
                                var array = value.vacancyId.interviewStatus;
                                angular.forEach($scope.customStages, function (resp) {
                                    if (value.state == resp.customInterviewStateId) {
                                        value.state = resp.name;
                                        value.customInterviewStateId = resp.customInterviewStateId;
                                    }
                                    angular.forEach(array, function (res) {
                                        if (resp.customInterviewStateId == res) {
                                            array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                        }
                                    });
                                });

                                if ($rootScope.candnotify.show && sendTemplate) {
                                    if ($scope.googleMeetFlag && !resp.object.meetLink) {
                                        notificationService.error($filter('translate')('google_meet_error'));
                                        return;
                                    } else {
                                        $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                                            .replace('// See you in (skype / google hangouts etc).', '')
                                            .replace(
                                                "// We'll wait for you at our office: (write company address, floor, office number)",
                                                '',
                                            )
                                            .replace(
                                                '// Please see how to find our office on the map in the attached file.',
                                                '',
                                            );
                                        var candnotify = $rootScope.candnotify;
                                        var changeObj = $rootScope.addCandidateInVacancy;
                                        let templateForRequest = {
                                            type: $rootScope.emailTemplateInModal.type,
                                            title: $rootScope.emailTemplateInModal.title,
                                            text: $rootScope.emailTemplateInModal.text,
                                        };
                                        if ($rootScope.addCandidateInVacancy.status.value === 'accept_offer') {
                                            templateForRequest.type = 'offerAccepted';
                                            templateForRequest.filesIdName = Service.filesIdNameToObjectInString(
                                                $rootScope.fileForSave,
                                            );
                                        } else {
                                            templateForRequest.filesIdName = Service.filesIdNameToObjectInString(
                                                $rootScope.fileForSave,
                                            );
                                        }

                                        if ($scope.googleMeetFlag) {
                                            templateForRequest.text = templateForRequest.text.replace(
                                                '[[google meet link]]',
                                                `
                                    <a href='${resp.object.meetLink}' target='_blank'>${resp.object.meetLink}</a>
                                    `,
                                            );
                                        }

                                        Mail.sendMailByTemplateVerified(
                                            {
                                                toEmails: candnotify.sendMail,
                                                vacancyId: $rootScope.VacancyAddedInCandidate.vacancyId,
                                                candidateId: $scope.candidate.candidateId,
                                                fullName: $scope.candidate.fullName,
                                                email:
                                                    $rootScope.emailTemplateInModal.email ||
                                                    $rootScope.me.emails[0].email,
                                                date: $rootScope.addCandidateInVacancy.date,
                                                lang: $translate.use(),
                                                template: templateForRequest,
                                            },
                                            function (resp) {
                                                if (resp.status == 'ok') {
                                                    notificationService.success($filter('translate')('Letter sent'));
                                                    $rootScope.closeModal();
                                                } else {
                                                    notificationService.error(resp.message);
                                                }
                                            },
                                        );
                                    }
                                }

                                value.vacancyId.interviewStatus = array;
                                if (
                                    value.vacancyId.status != 'completed' &&
                                    value.vacancyId.status != 'deleted' &&
                                    value.vacancyId.status != 'canceled' &&
                                    value.vacancyId.status != 'recommendation'
                                ) {
                                    $scope.participationInVacancy = true;
                                }
                            });
                            $rootScope.addCandidateInVacancy.status = null;
                            $rootScope.addCandidateInVacancy.date = null;
                            $rootScope.VacancyStatusFiltered = '';
                            $rootScope.candidateAddedInVacancy = null;
                            $rootScope.closeModal();
                            notificationService.success(
                                $filter('translate')('Candidate added in vacancy and sent mail'),
                            );
                            if ($rootScope.currentCandidateTabs === 'evaluate') {
                                $timeout(() => {
                                    $state.go('candidate-slide.evaluate', {}, { reload: 'candidate-slide.evaluate' });
                                }, 0);
                            }

                            if (resp.object.points != 0) {
                                let changeStagesText = 'You have added a candidate to the stage';
                                let reward = 'Reward';
                                setTimeout(() => {
                                    gamificationNotify.showNotify(
                                        `${$filter('translate')(changeStagesText)}`,
                                        `${
                                            resp.object?.customInterviewStateId
                                                ? resp.object.state
                                                : $filter('translate')(resp.object.state)
                                        }`,
                                        `${$filter('translate')(reward)} +${resp.object.points} ${$filter('translate')(
                                            'of experience',
                                        )}`,
                                    );
                                }, 1500);
                            }
                            $rootScope.transferAndSent = true;
                        },
                        function (resp) {
                            if (resp.code === 'googleMeetError') {
                                $rootScope.clickedAddVacancyInCandidate = false;
                                notificationService.error($filter('translate')('google_meet_error'));
                                $rootScope.loading = false;
                                return;
                            }
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                            $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                        },
                        frontMode,
                        notificationService,
                        googleService,
                        $scope.selectedCalendar != undefined ? $scope.selectedCalendar.id : null,
                        $filter,
                        $translate.use(),
                        $rootScope,
                        $rootScope.googleMeet,
                        $rootScope.changeStatusOfInterviewInVacancy.meetComment,
                        $scope.meetParticipants,
                        $rootScope.outlookMeet,
                        $rootScope.meetDuration.value,
                        $rootScope.calendarTitle,
                    );
                } else if ($rootScope.addCandidateInVacancy.status && sendTemplate == undefined) {
                    Service.vacancyAddInterview(
                        Vacancy,
                        vacancyId,
                        null,
                        $scope.candidate.candidateId,
                        $rootScope.addCandidateInVacancy.comment,
                        $rootScope.addCandidateInVacancy.status.customInterviewStateId
                            ? $rootScope.addCandidateInVacancy.status.customInterviewStateId
                            : $rootScope.addCandidateInVacancy.status.value,
                        $rootScope.addCandidateInVacancy.date,
                        function (resp) {
                            if (resp.status === 'ok') {
                                notificationService.success($filter('translate')('Candidate added in vacancy'));
                                if ($rootScope.currentCandidateTabs === 'evaluate') {
                                    $timeout(() => {
                                        $state.go(
                                            'candidate-slide.evaluate',
                                            {},
                                            { reload: 'candidate-slide.evaluate' },
                                        );
                                    }, 0);
                                }
                            }

                            var changeObj = {
                                candidate: {
                                    candidateId: {
                                        fullName: resp.object.candidateId.fullName,
                                    },
                                },
                                status: { value: resp.object.state },
                            };
                            $rootScope.changedStatusVacancy = resp.object.vacancyId;
                            resp.object.vacancyId.interviewStatusNotTouchable = resp.object.vacancyId.interviewStatus;
                            $scope.setParticipationInVacancies();
                            $rootScope.$emit('onUpdateCandidateHistory');
                            updateCandidateProperties();
                            if (!$scope.candidate.interviews) {
                                $scope.candidate.interviews = [];
                            }
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.addCandidateInVacancy.comment = '';
                            $rootScope.addCandidateInVacancy.id = null;
                            $scope.candidate.interviews.push(resp.object);
                            let changeStagesText = 'You have added a candidate to the stage';
                            let reward = 'Reward';
                            if (resp.object.points != 0) {
                                let changeObjForVacancy = $rootScope.addCandidateInVacancy.status;
                                setTimeout(() => {
                                    gamificationNotify.showNotify(
                                        `${$filter('translate')(changeStagesText)}`,
                                        `${$filter('translateStage')(changeObjForVacancy)}`,
                                        `${$filter('translate')(reward)} +${resp.object.points} ${$filter('translate')(
                                            'of experience',
                                        )}`,
                                    );
                                }, 1500);
                            }
                            angular.forEach($scope.candidate.interviews, function (interview) {
                                if (interview.vacancyId.interviewStatus == undefined) {
                                    interview.vacancyId.interviewStatus =
                                        'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                    interview.vacancyId.interviewStatusNotTouchable =
                                        interview.vacancyId.interviewStatus;
                                }
                            });
                            angular.forEach($scope.candidate.interviews, function (value) {
                                var array = value.vacancyId.interviewStatus;
                                angular.forEach($scope.customStages, function (resp) {
                                    if (value.state == resp.customInterviewStateId) {
                                        value.state = resp.name;
                                    }
                                    angular.forEach(array, function (res) {
                                        if (resp.customInterviewStateId == res) {
                                            array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                        }
                                    });
                                });
                                value.vacancyId.interviewStatus = array;
                                if (
                                    value.vacancyId.status != 'completed' &&
                                    value.vacancyId.status != 'deleted' &&
                                    value.vacancyId.status != 'canceled' &&
                                    value.vacancyId.status != 'recommendation'
                                ) {
                                    $scope.participationInVacancy = true;
                                }
                            });
                            $rootScope.addCandidateInVacancy.status = null;
                            $rootScope.addCandidateInVacancy.date = null;
                            $rootScope.VacancyStatusFiltered = '';
                            $rootScope.candidateAddedInVacancy = null;
                            $rootScope.closeModal();
                            if (resp.object.showCloseVacancyPopup) initModalRemainder(changeObj);
                        },
                        function (resp) {
                            if (resp.code === 'googleMeetError') {
                                notificationService.error($filter('translate')('google_meet_error'));
                                $rootScope.clickedAddVacancyInCandidate = false;
                                $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                                $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                                return;
                            }
                            notificationService.error(
                                $filter('translate')('Candidate has been added to this position'),
                            );
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                            $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                        },
                        frontMode,
                        notificationService,
                        googleService,
                        $scope.selectedCalendar != undefined ? $scope.selectedCalendar.id : null,
                        $filter,
                        $translate.use(),
                        $rootScope,
                        $rootScope.googleMeet,
                        $rootScope.changeStatusOfInterviewInVacancy.meetComment,
                        $scope.meetParticipants,
                        $rootScope.outlookMeet,
                        $rootScope.meetDuration.value,
                        $rootScope.calendarTitle,
                    );
                } else {
                    Service.vacancyAddInterview(
                        Vacancy,
                        vacancyId,
                        null,
                        $scope.candidate.candidateId,
                        $rootScope.addCandidateInVacancy.comment,
                        $rootScope.addCandidateInVacancy.status.value,
                        $rootScope.addCandidateInVacancy.date,
                        function (resp) {
                            var changeObj = {
                                candidate: {
                                    candidateId: {
                                        fullName: resp.object.candidateId.fullName,
                                    },
                                },
                                status: { value: resp.object.state },
                            };
                            $rootScope.changedStatusVacancy.status = resp.object.status;
                            resp.object.vacancyId.interviewStatusNotTouchable = resp.object.vacancyId.interviewStatus;
                            $scope.setParticipationInVacancies();
                            $rootScope.$emit('onUpdateCandidateHistory');
                            updateCandidateProperties();
                            if (!$scope.candidate.interviews) {
                                $scope.candidate.interviews = [];
                            }
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.addCandidateInVacancy.comment = '';
                            $rootScope.addCandidateInVacancy.id = null;
                            $scope.candidate.interviews.push(resp.object);
                            angular.forEach($scope.candidate.interviews, function (interview) {
                                if (interview.vacancyId.interviewStatus == undefined) {
                                    interview.vacancyId.interviewStatus =
                                        'longlist,shortlist,interview,approved,notafit,declinedoffer';
                                    interview.vacancyId.interviewStatusNotTouchable =
                                        interview.vacancyId.interviewStatus;
                                }
                            });
                            angular.forEach($scope.candidate.interviews, function (value) {
                                var array = value.vacancyId.interviewStatus;
                                angular.forEach($scope.customStages, function (resp) {
                                    if (value.state == resp.customInterviewStateId) {
                                        value.state = resp.name;
                                    }
                                    angular.forEach(array, function (res) {
                                        if (resp.customInterviewStateId == res) {
                                            array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                        }
                                    });
                                });
                                value.vacancyId.interviewStatus = array;
                                if (
                                    value.vacancyId.status != 'completed' &&
                                    value.vacancyId.status != 'deleted' &&
                                    value.vacancyId.status != 'canceled' &&
                                    value.vacancyId.status != 'recommendation'
                                ) {
                                    $scope.participationInVacancy = true;
                                }
                            });
                            $rootScope.addCandidateInVacancy.status = null;
                            $rootScope.addCandidateInVacancy.date = null;
                            $rootScope.VacancyStatusFiltered = '';
                            $rootScope.candidateAddedInVacancy = null;
                            $rootScope.closeModal();
                            if ($rootScope.currentCandidateTabs === 'evaluate') {
                                $timeout(() => {
                                    $state.go('candidate-slide.evaluate', {}, { reload: 'candidate-slide.evaluate' });
                                }, 0);
                            }
                            if (resp.object.showCloseVacancyPopup) initModalRemainder(changeObj);
                        },
                        function (resp) {
                            $rootScope.clickedAddVacancyInCandidate = false;
                            $rootScope.errorMessageForAddCandidateInVacancy.show = true;
                            $rootScope.errorMessageForAddCandidateInVacancy.text = resp.message;
                        },
                        frontMode,
                        notificationService,
                        googleService,
                        $scope.selectedCalendar != undefined ? $scope.selectedCalendar.id : null,
                        $filter,
                        $translate.use(),
                        $rootScope,
                        $rootScope.googleMeet,
                        $rootScope.changeStatusOfInterviewInVacancy.meetComment,
                        $scope.meetParticipants,
                        $rootScope.outlookMeet,
                        $rootScope.meetDuration.value,
                        $rootScope.calendarTitle,
                    );
                }
            }
        };
        $('.addFromAdvicePicker')
            .datetimepicker({
                format: 'dd/mm/yyyy hh:ii',
                startView: 2,
                minView: 0,
                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                autoclose: true,
                language: $translate.use(),
            })
            .on('changeDate', function (data) {
                $rootScope.addFromAdvice.date = data.date;
            })
            .on('hide', function () {
                if ($('.addFromAdvicePicker').val() == '') {
                    $rootScope.addFromAdvice.date = null;
                }
                $('.addFromAdvicePicker').blur();
            });
        $rootScope.addFromAdvice = {
            id: '',
            comment: '',
            status: 'longlist',
            date: null,
            showSelect: '',
            showText: false,
            text: '',
            state: '',
        };
        $rootScope.errorAddFromAdvice = {
            show: false,
            text: '',
        };
        $scope.showFromAdvice = function (vacancyId, vacancyPosition) {
            var vacancyStatus = Vacancy.interviewStatusNew();
            $rootScope.addFromAdvice.statusObject = $filter('filter')(vacancyStatus[0].status, { forAdd: true });
            if (vacancyId.interviewStatus) {
                var array = vacancyId.interviewStatus.split(',');
                var sortedStages = [];
                angular.forEach(vacancyStatus, function (vStatus) {
                    if (vStatus.used) {
                        var statusNotDef = $filter('filter')(vStatus.status, {
                            defaultS: false,
                        });
                        angular.forEach(statusNotDef, function (statusND) {
                            angular.forEach(array, function (statusA) {
                                if (statusND.value == statusA) {
                                    statusND.added = true;
                                } else if (
                                    statusND.value != statusA &&
                                    (statusND.value == 'shortlist' || statusND.value == 'interview')
                                ) {
                                    statusND.added = false;
                                }
                            });
                        });
                    }
                });
                var i = 0;
                angular.forEach(array, function (resp) {
                    angular.forEach(vacancyStatus, function (vStatus) {
                        if (vStatus.used) {
                            if (i == 0) {
                                angular.forEach($scope.customStages, function (res) {
                                    res.value = res.name;
                                    res.movable = true;
                                    res.added = false;
                                    res.count = 0;
                                    vStatus.status.push(res);
                                    i = i + 1;
                                });
                            }
                            angular.forEach(vStatus.status, function (vStatusIn) {
                                if (resp == vStatusIn.value) {
                                    vStatusIn.added = true;
                                    sortedStages.push(vStatusIn);
                                } else if (resp == vStatusIn.customInterviewStateId) {
                                    vStatusIn.added = true;
                                    sortedStages.push(vStatusIn);
                                }
                            });
                        }
                    });
                });
                $scope.VacancyStatusFiltered = sortedStages;
                $rootScope.addFromAdvice.statusObject = $scope.VacancyStatusFiltered;
            }
            $('.addFromAdvicePicker').val('');
            $rootScope.addFromAdvice.status = {
                value: 'longlist',
                withDate: false,
                defaultS: true,
                single: false,
                added: true,
                active_color: 'longlist_color',
                useAnimation: false,
                count: 0,
                forAdd: true,
            };
            $rootScope.addFromAdvice.vacancyId = vacancyId.vacancyId;
            $rootScope.addFromAdvice.title = vacancyPosition;
            $rootScope.addFromAdvice.state = 'candidate';
            $('.addFromAdvice').modal('show');
            $rootScope.candnotify = {};
            Candidate.getContacts({ candidateId: $scope.candidate.candidateId }, function (resp) {
                var email = '';
                angular.forEach(resp.objects, function (c) {
                    if (c.type == 'email') {
                        email = c.value;
                    }
                });
                $rootScope.candnotify.emails = email.replace(/ /gi, '').split(',');
                $rootScope.candnotify.sendMail = $rootScope.candnotify.emails[0];
            });
            $rootScope.candnotify.show = false;
            $rootScope.candnotify.name = $scope.candidate;
            $rootScope.candnotify.fullName = $scope.candidate.fullName;
        };

        function initCkEditor() {
            $scope.ckEditorOptions = {
                toolbarGroups: [
                    {
                        name: 'document',
                        groups: ['mode', 'document', 'doctools'],
                    },
                    { name: 'clipboard', groups: ['clipboard', 'undo'] },
                    {
                        name: 'editing',
                        groups: ['find', 'selection', 'spellchecker', 'editing'],
                    },
                    { name: 'forms', groups: ['forms'] },
                    { name: 'basicstyles', groups: ['basicstyles', 'cleanup'] },
                    { name: 'colors', groups: ['colors'] },
                    {
                        name: 'paragraph',
                        groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph'],
                    },
                    { name: 'links', groups: ['links'] },
                    { name: 'insert', groups: ['insert'] },
                    { name: 'styles', groups: ['styles'] },
                    { name: 'tools', groups: ['tools'] },
                    { name: 'others', groups: ['others'] },
                    { name: 'about', groups: ['about'] },
                ],
                removeButtons:
                    'Source,Save,NewPage,Preview,Print,Templates,Cut,Copy,Paste,PasteText,PasteFromWord,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Subscript,Superscript,RemoveFormat,CopyFormatting,Outdent,Indent,Blockquote,CreateDiv,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,BidiLtr,BidiRtl,Language,Anchor,Unlink,Link,Image,Flash,Table,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe,Styles,Format,BGColor,ShowBlocks,Maximize,About',
                height: 250,
                on: {
                    pluginsLoaded: function () {
                        var editor = this,
                            config = editor.config;

                        editor.addCommand('insertCandidateName', {
                            exec: function (editor) {
                                editor.insertHtml('[candidate_name]');
                            },
                        });
                        editor.ui.addButton('insertCandidateName', {
                            label: `${$filter('translate')('Insert name')}`,
                            icon: null,
                            command: 'insertCandidateName',
                            toolbar: 'about,0',
                        });
                    },
                },
            };
        }

        $scope.printPage = () => {
            $scope.candidate.status === 'external_recommendation' || window.print();
        };

        initCkEditor();
        $scope.updateCandidate = function () {
            $scope.showAddedLinks = false;
            $scope.showAddedFiles = false;
            $rootScope.loading = true;
            getCandidatesProperties();
            Candidate.one(
                { localId: $stateParams.id },
                function (resp) {
                    $rootScope.currentCandidateId = resp.object.candidateId;
                    $timeout(initHistoryTable);

                    if (angular.equals(resp.status, 'error')) {
                        notificationService.error($filter('translate')('candidate not found'));
                        $location.path('/candidates');
                        return;
                    }
                    $rootScope.loading = false;
                    $timeout(() => {
                        if ($stateParams.isFromVacancyToEvaluate) {
                            $scope.currentCandidateContentTab = 'evaluate';
                            $state.go('candidate-slide.evaluate', {
                                candidateObj: resp.object,
                                infoList: {
                                    vacancyName: $stateParams.vacancyName,
                                    isEvulate: true,
                                    scoreCardId: $stateParams.scoreCardId,
                                    vacancyId: $stateParams.vacancyId,
                                },
                            });
                            $rootScope.currentCandidateTabs = 'evaluate';
                        }
                        if ($stateParams.isFromNotificationsLetters) {
                            $scope.currentCandidateContentTab = 'personalMailing';
                            $state.go('candidate-slide.personalMailing');
                        }
                    }, 0);

                    $rootScope.mailingHistoryCalculate = [];
                    PersonalMailing.onGetHistory(resp.object.candidateId).then((resp) => {
                        $rootScope.mailingHistoryCalculate = resp.unreadCount;
                        $scope.$$phase || $scope.$apply();
                    });

                    if (
                        $rootScope.me.recrutRole === 'client' ||
                        $rootScope.me.recrutRole === 'freelancer' ||
                        $rootScope.me.recrutRole === 'researcher'
                    ) {
                        if (resp.object.access === false) {
                            $scope.limitedAccessCandidate();
                        } else {
                            document.querySelector('.candidate-page-wrapper').style.visibility = 'visible';
                        }
                    } else {
                        document.querySelector('.candidate-page-wrapper').style.visibility = 'visible';
                    }

                    $scope.candidate = resp.object;

                    $scope.isExternal = $scope.candidate.status === 'external_recommendation';

                    if ($scope.candidate && $scope.candidate.photo) {
                        var regex = /(http|https):\/\/\S+/gi;
                        $rootScope.isPhotoLink = regex.test($scope.candidate.photo);
                    }
                    $rootScope.candidateIdTag = resp.object.candidateId;
                    onSanitizeCandidateFields($scope.candidate);
                    $scope.fullDataCandidate = angular.copy($scope.candidate);

                    if (
                        resp.object.region != undefined &&
                        resp.object.region.lat != undefined &&
                        resp.object.region.lng != undefined
                    ) {
                        $scope.map.center.latitude = resp.object.region.lat;
                        $scope.map.center.longitude = resp.object.region.lng;

                        $scope.marker.coords.latitude = resp.object.region.lat;
                        $scope.marker.coords.longitude = resp.object.region.lng;
                    }
                    var name = '';
                    name = resp.object.fullName != undefined ? name + resp.object.fullName.replace(/\W+/g, '_') : '';
                    if (resp.object.position.length > 1) {
                        name =
                            resp.object.position != undefined
                                ? name + '_' + resp.object.position.replace(/\W+/g, '_')
                                : '';
                    }
                    if (name.length > 0) {
                        $location.search($filter('transliteration')(name)).replace();
                    }
                    if (!resp.object.fullName && resp.object.source == 'add_from_hh') {
                        $rootScope.title = $filter('translate')('Hidden by hh') + ' | CleverStaff';
                    } else if (!resp.object.fullName) {
                        $rootScope.title = $filter('translate')('Hidden') + ' | CleverStaff';
                    } else {
                        $rootScope.title = resp.object.fullName + ' | CleverStaff';
                    }
                    $scope.contacts = {
                        skype: [],
                        mphone: [],
                        email: [],
                        telegram: [],
                        linkedin: [],
                        facebook: [],
                        homepage: [],
                        github: [],
                        behance: [],
                        viber: [],
                        whatsApp: [],
                        other: [],
                    };
                    setContacts();
                    $scope.setParticipationInVacancies();
                    isContacts();
                    if ($scope.candidate && $scope.candidate.candidateSkills) {
                        $scope.importedSkills = $scope.candidate.candidateSkills.filter(
                            (item) => item.type === 'imported',
                        );
                        $scope.allSkills = $scope.candidate.candidateSkills.filter((item) => item.type !== 'imported');
                        $scope.lastUpdated = $scope.candidate.candidateSkills.sort((a, b) =>
                            a.dm > b.dm ? 1 : b.dm > a.dm ? -1 : 0,
                        )[0];
                    }
                    if ($scope.candidate && $scope.candidate.customFields) {
                        $scope.customFieldsIsExists = false;
                        $scope.candidate.customFields.forEach((item) => {
                            if (item.fieldValue && (item.fieldValue.value || item.fieldValue.dateTimeValue))
                                $scope.customFieldsIsExists = true;
                        });
                    }

                    $scope.clickInRegionMap = () => {
                        $scope.showRegion2Map = !$scope.showRegion2Map;
                        if ($scope.candidate.region) {
                            $rootScope.map = {
                                center: {
                                    latitude: $scope.candidate.region.lat,
                                    longitude: $scope.candidate.region.lng,
                                },
                                zoom: 14,
                            };
                            $scope.options = { scrollwheel: false };
                        }
                    };

                    $scope.statusModel = $scope.status.filter((item) => item.value === $scope.candidate.status)[0];
                    $scope.candidate.files = $scope.candidateProperties.files;

                    function translateLanguages() {
                        Service.onGetLanguagesCached().then((languages) => {
                            angular.forEach($scope.candidate.languages, function (val) {
                                if (val.name != undefined) {
                                    angular.forEach(languages, function (item) {
                                        if (item.key == val.name) {
                                            val.name = $filter('translateLangs')(item.translation);
                                        }
                                    });
                                }
                            });
                        });
                    }

                    translateLanguages();
                    $rootScope.$on('$translateChangeSuccess', () =>
                        $timeout(() => {
                            translateLanguages();
                        }, 0),
                    );

                    Candidate.onChangeOpenStatus($scope.candidate.candidateId, $scope.candidate.openStatus);
                    $rootScope.candidate = resp.object;
                    $rootScope.localIdOfMerged = $scope.candidate.localId;
                    $state.current.data.title = $scope.candidate.fullName + ' | CleverStaff';
                    $scope.locationBeforeCustomFields = $location.$$path.replace(
                        '/candidates/' + $scope.candidate.localId,
                        'candidates',
                    );
                    $localStorage.set('previousHistoryCustomFields', $scope.locationBeforeCustomFields);
                    $scope.changeStatus = $scope.candidate.status;

                    $rootScope.newTask.candidateId = $scope.candidate.candidateId;
                    var array = [];
                    angular.forEach($scope.candidate.interviews, function (value) {
                        value.vacancyId.interviewStatusNotTouchable = value.vacancyId.interviewStatus;
                        array = value.vacancyId.interviewStatus.split(',');
                        angular.forEach($scope.customStages, function (resp) {
                            if (value.state == resp.customInterviewStateId) {
                                value.state = resp.name;
                                value.customInterviewStateId = resp.customInterviewStateId;
                            }
                            angular.forEach(array, function (res) {
                                if (resp.customInterviewStateId == res) {
                                    array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                                }
                            });
                        });
                        value.vacancyId.interviewStatus = array;
                        if (
                            value.vacancyId.status != 'completed' &&
                            value.vacancyId.status != 'deleted' &&
                            value.vacancyId.status != 'canceled' &&
                            value.vacancyId.status != 'recommendation'
                        ) {
                            $scope.participationInVacancy = true;
                        } else {
                            $rootScope.inactiveVacancies = true;
                        }
                    });
                    cascadeStages();
                    cascadeStagesUnactive();
                    $rootScope.candidateForUpdateResume = resp.object;
                    if ($scope.candidate.files) {
                        if ($scope.candidate.files.length) {
                            angular.forEach($scope.candidate.files, function (val) {
                                val.url ? ($scope.showAddedLinks = true) : ($scope.showAddedFiles = true);
                                Service.initDocuments(val);
                            });
                        }
                    } else {
                        $scope.showAddedLinks = false;
                        $scope.showAddedFiles = false;
                    }

                    $('#candidateEducation').html($scope.candidate.education);
                    $('.candidateCoreSkills').html($scope.candidate.coreSkills);

                    $scope.objectId = resp.object.candidateId;
                    $rootScope.candidateIdForVacancyId = resp.object.candidateId;

                    $scope.candidate.descr = $scope.candidate.descr
                        .replace(/hide/gi, '')
                        .replace(/alert-warning/gi, '')
                        .replace(new RegExp('<link[^>]*>', 'g'), '');
                    $scope.candidate.descr = $scope.candidate.descr
                        .replaceAll(/hide/gi, '')
                        .replaceAll(/alert-warning/gi, '')
                        .replaceAll(new RegExp('<link[^>]*>', 'g'), '');
                    // document.querySelector('.content-description').innerHTML = $scope.candidate.descr;

                    $('#descr').html($scope.candidate.descr);
                    $scope.candProgress();
                    FileInit.initFileOption($scope, 'candidate', undefined, $filter);
                    FileInit.initFileOptionForEditFromResume($scope, 'candidate');
                    $scope.fileForSave = [];
                    $scope.linksForSave = [];
                    $rootScope.fileForSave = [];

                    $scope.initGdprFileAttache();
                    /*For modal window*/

                    FileInit.initVacancyTemplateInCandidateFileOption($scope, $rootScope, '', '', false, $filter);
                    $scope.callbackFileTemplateInCandidate = function (resp, names) {
                        $scope.fileForSave.push({
                            fileId: resp,
                            fileName: names,
                            fileResolution: Service.getFileResolutionFromName(names),
                        });
                        $rootScope.fileForSave.push({
                            fileId: resp,
                            fileName: names,
                            fileResolution: Service.getFileResolutionFromName(names),
                        });
                    };
                    $scope.removeFileAttach = function (id) {
                        const files = $scope.fileForSave.length ? $scope.fileForSave : $rootScope.fileForSave;
                        angular.forEach(files, function (val, ind) {
                            if (val.attId === id || val.fileId === id) {
                                files.splice(ind, 1);
                            }
                        });
                    };
                    $scope.removeFile = function (id) {
                        Candidate.removeFile(
                            {
                                candidateId: $scope.candidate.candidateId,
                                fileId: id,
                            },
                            function (resp) {
                                if (resp.status == 'ok') {
                                    $rootScope.$emit('onUpdateCandidateHistory');
                                }
                            },
                        );
                        updateCandidateProperties();
                        if ($scope.candidateProperties.files.length === 0) {
                            delete $scope.candidateProperties.files;
                            Candidate.progressUpdate($scope, false);
                        }
                        angular.forEach($scope.fileForSave, function (val, ind) {
                            if (val.fileId === id) {
                                $scope.fileForSave.splice(ind, 1);
                                $rootScope.fileForSave.splice(ind, 1);
                            }
                        });
                    };
                    $rootScope.removeFile = function (id) {
                        angular.forEach($rootScope.fileForSave, function (val, ind) {
                            if (val.attId === id) {
                                $rootScope.fileForSave.splice(ind, 1);
                            }
                        });
                    };
                    $rootScope.$on('onEventForShearingCandidateTasks', function (event, data) {
                        $scope.candidateTasks = data;
                        $rootScope.candidateTasks = data;
                        $scope.updateTasks();
                    });

                    $rootScope.updateTasksData = function () {
                        $scope.updateTasks();
                    };

                    $scope.updateTasks = function () {
                        Task.get(
                            {
                                candidateId: $scope.candidate.candidateId,
                            },
                            function (resp) {
                                $scope.candidateTasks = resp.objects;
                                $rootScope.candidateTasks = resp.objects;
                                $rootScope.candidateTasks.forEach((val, idx) => {
                                    if (val.status === 'open') val.status = 'inwork';
                                    val.status = {
                                        status: 'tasksStatuses.' + $scope.candidateTasks[idx].status,
                                        color: val.status === 'inwork' ? '#77b472' : '#eaa24d',
                                        value: 'tasksStatuses.' + $scope.candidateTasks[idx].status,
                                    };
                                    val.editableTaskOptions = [
                                        {
                                            value: 'inwork',
                                            name: 'tasksStatuses.inwork',
                                            taskId: $scope.candidateTasks[idx].taskId,
                                            status: 'inwork',
                                            sendStatus: 'open',
                                            color: '#77B472',
                                        },
                                        {
                                            value: 'completed',
                                            name: 'tasksStatuses.completed',
                                            taskId: $scope.candidateTasks[idx].taskId,
                                            status: 'tasksStatuses.completed',
                                            sendStatus: 'completed',
                                            color: '#eaa24d',
                                        },
                                        {
                                            value: 'Cancelled',
                                            name: 'tasksStatuses.Cancelled',
                                            taskId: $scope.candidateTasks[idx].taskId,
                                            sendStatus: 'Cancelled',
                                            status: 'tasksStatuses.Cancelled',
                                            color: '#eaa24d',
                                        },
                                    ];
                                });

                                const personIds = $rootScope.candidateTasks.reduce(
                                    (arr, curr) => {
                                        return curr.responsibleIds.length > arr.responsibleIds.length ? curr : arr;
                                    },
                                    { responsibleIds: [] },
                                );

                                $rootScope.personsLength = personIds.responsibleIds.length.toString();

                                if ($scope.urlTaskId) {
                                    $rootScope.responsiblePersonsEdit = [];
                                    angular.forEach($scope.candidateTasks, function (resp) {
                                        if (resp.taskId == $scope.urlTaskId) {
                                            $rootScope.editableTask = resp;
                                        }
                                    });
                                    if ($rootScope.editableTask && $location.$$absUrl.indexOf('&task=') == -1) {
                                        $location.$$absUrl = $location.$$absUrl + '&task=' + $scope.urlTaskId;
                                        angular.forEach($rootScope.editableTask.responsiblesPerson, function (resp) {
                                            angular.forEach($rootScope.persons, function (res) {
                                                if (resp.responsible.userId == res.userId) {
                                                    $rootScope.responsiblePersonsEdit.push(res);
                                                    res.notShown = true;
                                                }
                                            });
                                        });
                                        $('.editTaskInCandidate')
                                            .modal('setting', {
                                                onHide: function () {
                                                    $scope.urlTaskId = null;
                                                    $location.$$absUrl = $location.$$absUrl.split('&')[0];
                                                    $scope.$apply();
                                                },
                                            })
                                            .modal('show');
                                    }
                                }
                            },
                        );
                    };

                    $rootScope.testUpdate = $scope.updateTasks;

                    $rootScope.$emit('onUpdateCandidateHistory');
                },
                function (err) {
                    console.error(err);
                },
            );
        };
        $scope.updateCandidate();

        // Candidate content tab

        $scope.currentCandidateContentTab = 'generalInformation';

        $scope.changeCandidateContentTab = function (tabName) {
            $scope.currentCandidateContentTab = tabName;
        };

        // Candidate content tab end

        $scope.removeTag = function (currentTarget) {
            $rootScope.tagForEdit.name = $(currentTarget).parent().children().first().html();
            const selectedTag = findSelectedTag($rootScope.tagForEdit.name);

            if (selectedTag) {
                $rootScope.tagForEdit.id = selectedTag.candidateGroupId;
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/tag-remove.html',
                    size: '',
                    scope: $scope,
                    resolve: {},
                });
            }
        };
        $scope.editTagName = function (currentTarget) {
            $rootScope.tagForEdit.name = $(currentTarget).parent().children().first().html();
            $scope.oldTagName = $rootScope.tagForEdit.name;
            const selectedTag = findSelectedTag($rootScope.tagForEdit.name);

            if (selectedTag) {
                $rootScope.tagForEdit.id = selectedTag.candidateGroupId;
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/tag-name-edit.html',
                    size: '',
                    resolve: {},
                });
            }
        };
        $scope.getGroups = function () {
            Service.requestGetGroups()
                .then((resp) => {
                    $scope.groups = resp.objects;
                })
                .catch(() => {});
        };

        $scope.changeCandidateTags = (arr, additionalArg, actionMeta) => {
            const singleTag = actionMeta.option || actionMeta.removedValue;

            if (actionMeta.action === 'select-option') {
                $rootScope.loading = true;

                CandidateGroup.onAdd({
                    candidateIds: [$scope.candidate.candidateId],
                    candidateGroupId: singleTag.candidateGroupId,
                })
                    .then((resp) => {
                        if (resp.status === 'ok') {
                            $scope.candidate.groups = arr;

                            $rootScope.$emit('onUpdateCandidateHistory');
                            notificationService.success($filter('translate')('Tag added'));
                        }
                    })
                    .catch((err) => console.error(err))
                    .finally(() => {
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                    });
            } else if (actionMeta.action === 'remove-value' || actionMeta.action === 'deselect-option') {
                $rootScope.loading = true;

                CandidateGroup.onRemove({
                    candidateIds: [$scope.candidate.candidateId],
                    candidateGroupId: singleTag.candidateGroupId,
                })
                    .then((resp) => {
                        if (resp.status === 'ok') {
                            $scope.candidate.groups = arr;

                            $rootScope.$emit('onUpdateCandidateHistory');
                            notificationService.success($filter('translate')('Tag deleted'));
                        }
                    })
                    .catch((err) => console.error(err))
                    .finally(() => {
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                    });
            }

            $rootScope.$$phase || $scope.$apply();
        };

        function findSelectedTag(name) {
            return $scope.candidate.groups.filter((group) => {
                return group.name === name;
            })[0];
        }

        $scope.removeTagFromAccount = function () {
            $rootScope.loading = true;
            CandidateGroup.removeGroupFromAccount({
                id: $rootScope.tagForEdit.id,
            }).then(
                (resp) => {
                    $rootScope.loading = false;
                    let beforeEdit = $scope.getSelect2Group().split(',');
                    angular.forEach(beforeEdit, function (tagName, index) {
                        if (tagName === $rootScope.tagForEdit.name) {
                            beforeEdit.splice(index, 1);
                            $scope.candidate.groups.splice(index, 1);
                            $scope.setSelect2Group(beforeEdit);
                        }
                        $('a.select2-search-choice-edit').attr(
                            'title',
                            $filter('translate')('Edit tag for all candidates'),
                        );
                        $('a.select2-search-choice-edit')
                            .off()
                            .on('click', function (e) {
                                $scope.editTagName(e.currentTarget);
                            });
                        $('a.select2-search-choice-remove').attr(
                            'title',
                            $filter('translate')('Remove tag from account'),
                        );
                        $('a.select2-search-choice-remove')
                            .off()
                            .on('click', function (e) {
                                $scope.removeTag(e.currentTarget);
                            });
                    });
                    $scope.closeModal();
                    notificationService.success($filter('translate')('Tag completely removed from system'));
                },
                (error) => {
                    $scope.closeModal();
                    $rootScope.loading = false;
                    notificationService.error(error.message);
                },
            );
        };

        $rootScope.saveEditTagName = function () {
            var newGroupList = $scope.getSelect2Group().split(',');
            if (newGroupList.indexOf($rootScope.tagForEdit.name) === -1) {
                CandidateGroup.editGroup(
                    {
                        candidateGroupId: $rootScope.tagForEdit.id,
                        name: $rootScope.tagForEdit.name,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            notificationService.success($filter('translate')('Tag_name_saved'));
                            var beforeEdit = $scope.getSelect2Group().split(',');
                            angular.forEach(beforeEdit, function (tagName, index) {
                                if (tagName == $scope.oldTagName) {
                                    beforeEdit[index] = $rootScope.tagForEdit.name;
                                    $scope.setSelect2Group(beforeEdit);
                                    angular.forEach($scope.candidate.groups, function (group) {
                                        if (group.name == $scope.oldTagName) {
                                            group.name = $rootScope.tagForEdit.name;
                                            group.candidateGroupId = resp.object.candidateGroupId;
                                        }
                                    });
                                    $('a.select2-search-choice-edit').attr(
                                        'title',
                                        $filter('translate')('Edit tag for all candidates'),
                                    );
                                    $('a.select2-search-choice-edit')
                                        .off()
                                        .on('click', function (e) {
                                            $scope.editTagName(e.currentTarget);
                                        });
                                    $('a.select2-search-choice-remove').attr(
                                        'title',
                                        $filter('translate')('Remove tag from account'),
                                    );
                                    $('a.select2-search-choice-remove')
                                        .off()
                                        .on('click', function (e) {
                                            $scope.removeTag(e.currentTarget);
                                        });
                                }
                            });
                        } else {
                            notificationService.error(resp.message);
                        }
                    },
                );
            } else {
                notificationService.error($filter('translate')('This tag has already assigned'));
            }

            $rootScope.closeModal();
        };

        $scope.getUsers = () => {
            Person.getUsers(function (resp) {
                $scope.persons = [];
                $rootScope.persons = [];
                $rootScope.personsNotChanged = [];
                $scope.associativePerson = resp.object;
                angular.forEach($scope.associativePerson, function (val, key) {
                    if (val.recrutRole === 'researcher' && val.userId !== $scope.candidate.creatorId) {
                    } else {
                        $scope.persons.push($scope.associativePerson[key]);
                        if (val.status == 'A') {
                            $rootScope.persons.push($scope.associativePerson[key]);
                        }
                        $rootScope.personsNotChanged.push($scope.associativePerson[key]);
                    }
                });

                $rootScope.persons = Person.getPersonListWithMe($rootScope.persons);

                const activePersonFilter = (person) => person?.status === 'A';
                const personOptionMap = (person) => ({ ...person, label: person.fullName, value: person });

                $scope.responsibleOptionsGrouped = [
                    {
                        label: 'Active',
                        options: $scope.persons.filter(activePersonFilter).map(personOptionMap),
                    },
                ];

                setIUser();

                if ($scope.candidateProperties.responsible && $scope.persons.length) {
                    $scope.persons = $scope.persons.filter(
                        (person) => person.personId !== $scope.candidateProperties.responsible.personId,
                    );
                }

                $rootScope.$$phase || $scope.$apply();
            });
        };

        $timeout(() => {
            $scope.getUsers();
        }, 100);

        $timeout(() => {
            $scope.getUsers();
        }, 1500);

        $scope.imgWidthFunc = function (id) {
            var img = new Image();
            img.onload = function () {
                var width = this.width;
                var height = this.height;
                var minus = width - height;
                if (width >= height && minus > 40 && minus <= 100) {
                    $('#page-avatar').css({ width: '100%', height: 'auto' });
                } else if ((width >= 300 && width <= 349) || width == height) {
                    $('#page-avatar').css({
                        width: '100%',
                        'object-fit': 'fill',
                    });
                } else if (width >= 350) {
                    $('#page-avatar').css({ width: '100%', height: 'auto' });
                } else if (width >= 201) {
                    $('#page-avatar').css({ width: '100%', height: 'auto' });
                } else {
                    $('#page-avatar').css({
                        width: 'inherit',
                        height: 'inherit',
                        display: 'block',
                        margin: '0 auto',
                    });
                }
            };
            img.src = $scope.candidate.photo
                ? $location.$$protocol +
                  '://' +
                  $location.$$host +
                  $scope.serverAddress +
                  '/getapp?id=' +
                  $scope.candidate.photo +
                  '&d=' +
                  $rootScope.me.personId
                : null;
        };
        $scope.pathName = 'candidate';
        $scope.callbackFile = function (resp, name) {
            if (!$scope.candidateProperties.files) {
                $scope.candidateProperties.files = [];
            }
            $scope.candidateProperties.files.push({
                ...resp,
                fileResolution: Service.getFileResolutionFromName(name),
            });
            if ($scope.candidateProperties.files.length > 0) {
                Candidate.progressUpdate($scope, false);
            }
            $rootScope.$emit('onUpdateCandidateHistory');
        };

        $scope.removeFile = function (id) {
            Candidate.removeFile({ candidateId: $scope.candidate.candidateId, fileId: id }, function (resp) {
                if (resp.status == 'ok') {
                    $rootScope.$emit('onUpdateCandidateHistory');
                }
            });
            updateCandidateProperties();
            if ($scope.candidateProperties.files.length === 0) {
                delete $scope.candidateProperties.files;
                Candidate.progressUpdate($scope, false);
            }
        };

        $rootScope.changeResponsibleUserInCandidate = {
            id: '',
            comment: '',
        };

        function editUsersInTags(ckeditor) {
            let text = CKEDITOR.instances[ckeditor].getData();

            $scope.deletedUsers.forEach((user, index) => {
                if (!text.includes(`${user.fullname}`)) {
                    if (user.responsible) {
                        $scope.repsonsibleUsers.push(user);
                    } else {
                        $scope.users.push(user);
                    }
                    $scope.afterDelete.push(user.id);
                    $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
                }
            });

            $scope.afterDelete.forEach((id) => {
                $scope.deletedUsers = $scope.deletedUsers.filter((user) => user.id !== id);
            });
            $scope.afterDelete = [];
        }

        function initTagsInModal() {
            $scope.users = [];
            $scope.repsonsibleUsers = [];

            $rootScope.persons.forEach((i, index) => {
                if (i.userId === $scope.candidateProperties.responsible.userId && i.recrutRole !== 'researcher') {
                    $scope.repsonsibleUsers.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: true,
                    });
                }
                if (
                    i.status === 'A' &&
                    i.recrutRole !== 'client' &&
                    i.recrutRole !== 'freelancer' &&
                    i.recrutRole !== 'researcher'
                ) {
                    $scope.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });

            $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
        }

        $scope.showModalAddCommentToCandidate = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/add-comment-candidate.html',
                size: '',
                scope: $scope,
                resolve: {},
            });
            $scope.modalInstance.closed.then(function () {
                $rootScope.commentCandidate.comment = '';
                $scope.repsonsibleUsers = [];
                $scope.users = [];
            });

            initTagsInModal();

            $rootScope.ckEditorOneCandidate = {
                height: 140,
                autoGrow_maxHeight: 340,
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                shiftEnterMode: CKEDITOR.ENTER_BR,
                extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                editorplaceholder: $translate.instant('mention placeholder'),
                mentions: [
                    {
                        feed: dataCallback,
                        minChars: 0,
                        itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                        outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                        marker: '@',
                        pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                    },
                ],
                on: {
                    paste: function (ev) {
                        ev.data.dataValue = '';
                        CKEDITOR.instances['ckEditorOneCandidate'].insertText(ev.data.dataTransfer._.data.Text);
                        setTimeout(() => {
                            let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                            if (bodyHeight > ev.editor.config.height) {
                                if (bodyHeight < 605) {
                                    ev.editor.resize(null, bodyHeight);
                                }
                            }
                        }, 0);
                    },
                    afterInsertHtml: function (ev) {
                        let user = ev.editor.data;
                        if (user) {
                            deleteFromTags(user.userId, 'userId');
                            $scope.deletedUsers.push(user);
                        }
                    },
                    contentDom: function (evt) {
                        var editable = evt.editor.editable();
                        editable.attachListener(editable, 'keyup', function (evt) {
                            if (evt.data.getKeystroke() === 1114125) {
                                $rootScope.addCommentInCandidate();
                            }
                        });
                    },
                    instanceReady: function (ev) {
                        $scope.deletedUsers = [];
                        $scope.afterDelete = [];

                        ev.editor.dataProcessor.writer.setRules('p', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                        ev.editor.dataProcessor.writer.setRules('br', {
                            indent: false,
                            breakBeforeOpen: false,
                            breakAfterOpen: false,
                            breakBeforeClose: false,
                            breakAfterClose: false,
                        });
                    },
                },
            };

            function dataCallback(opts, callback) {
                opts.query = opts.query.replace(/\u00A0/g, ' ');
                editUsersInTags('ckEditorOneCandidate');

                if (
                    opts.query.length === 0 &&
                    $scope.repsonsibleUsers.length > 0 &&
                    $scope.repsonsibleUsers[0].userId !== $rootScope.me.userId
                ) {
                    setTimeout(function () {
                        callback(
                            $scope.repsonsibleUsers.filter(function (item) {
                                return item.fullname;
                            }),
                        );
                    });
                } else {
                    setTimeout(function () {
                        callback(
                            $scope.users.filter(function (item) {
                                return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                            }),
                        );
                    });
                }
            }

            $rootScope.$$phase || $scope.$apply();
            $(document)
                .unbind('keydown')
                .keydown(function (e) {
                    if (e.ctrlKey == true && e.which == 13) {
                        $rootScope.addCommentInCandidate();
                        $(document).unbind();
                    }
                });
        };

        $rootScope.commentCandidate = {
            comment: '',
            loading: false,
        };

        $scope.openSendSmsModal = (answerData) => {
            if ($rootScope.me.orgParams.alphaSms !== 'Y') {
                $location.path('/integration-page');
                return;
            }
            $rootScope.loading = true;
            $uibModal
                .open({
                    animation: true,
                    templateUrl: '../partials/modal/create-sms-modal.html',
                    windowClass: 'create-sms-modal-wrapper',
                    size: '',
                    controller: 'createSms',
                    controllerAs: 'vm',
                    resolve: {
                        candidate: () => $scope.candidate,
                        candidates: () => null,
                        answerData: () => answerData,
                        vacancy: () => null,
                        client: () => null,
                        stages: () => null,
                        currentStage: () => null,
                        fetchVacancies: () => $rootScope.getAllVacancies,
                        responsibleOptionsGrouped: () => $scope.responsibleOptionsGrouped,
                    },
                })
                .closed.then(() => {
                    $rootScope.$emit('onUpdateCandidateHistory');
                });
        };

        $scope.showModalAddTaskToCandidate = function (size) {
            $rootScope.responsiblePersons = [];
            $rootScope.newTask.targetDate = '';
            angular.forEach($rootScope.persons, function (res) {
                res.notShown = false;
            });
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/adding-task.html',
                size: size,
                scope: $scope,
            });
        };

        $scope.showAddResponsibleUser = function (id) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-adding-responsible.html',
                controller: [
                    '$uibModalInstance',
                    '$uibModal',
                    function ($uibModalInstance, $uibModal) {
                        this.closeModal = function () {
                            $scope.candidateProperties.responsible = $scope.oldResponsibleUserInCandidate;
                            $uibModalInstance.close();
                        };
                    },
                ],
                controllerAs: 'vm',
                scope: $scope,
                size: '',
                resolve: function () {},
            });
            $scope.modalInstance.closed.then(function () {
                $rootScope.changeResponsibleUserInCandidate.comment = '';
            });
            $rootScope.changeResponsibleUserInCandidate.id = id.personId || id.userId;
        };

        $rootScope.saveResponsibleUserInCandidate = function () {
            if (!$rootScope.clickedSaveResponsibleUserInCandidate) {
                $rootScope.clickedSaveResponsibleUserInCandidate = true;
                Candidate.setResponsible(
                    {
                        candidateId: $scope.candidate.candidateId,
                        personId: $rootScope.changeResponsibleUserInCandidate.id,
                        comment: $rootScope.changeResponsibleUserInCandidate.comment,
                        lang: $translate.use(),
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            $scope.candidateProperties.responsible = $scope.persons.filter((person) => {
                                return person.personId === $rootScope.changeResponsibleUserInCandidate.id;
                            })[0];
                            $scope.oldResponsibleUserInCandidate = $scope.candidateProperties.responsible;

                            $scope.responsibleName = $scope.candidateProperties.responsible.fullName;
                            notificationService.success($filter('translate')('set responsible'));
                            $rootScope.$emit('onUpdateCandidateHistory');
                        }
                        $scope.getUsers();
                        $rootScope.closeModal();
                        $rootScope.changeResponsibleUserInCandidate.id = '';
                        $rootScope.changeResponsibleUserInCandidate.commnet = '';
                        $rootScope.clickedSaveResponsibleUserInCandidate = false;
                    },
                    function (err) {
                        console.error(err);
                    },
                );
            }
        };
        $scope.showChangeStatusOfCandidate = function (status) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-change-status-in-candidate.html',
                controller: [
                    '$uibModalInstance',
                    '$uibModal',
                    function ($uibModalInstance, $uibModal) {
                        this.closeModal = function () {
                            setStatusPrevValue();
                            $uibModalInstance.close();
                        };
                    },
                ],
                controllerAs: 'vm',
                scope: $scope,
                size: '',
                resolve: function () {},
            });
            $scope.modalInstance.closed.then(function () {
                $rootScope.changeStateInCandidate.comment = '';
                $scope.changeStatusOfInterviewInVacancyPick = null;
                $scope.addCandidateInvacancyPicker = null;
            });
            $rootScope.changeStateInCandidate.status = status.value;
            $rootScope.changeStateInCandidate.status_old = $scope.candidate.status;
            $rootScope.changeStateInCandidate.placeholder = $filter('translate')(
                'write_a_comment_why_do_you_change_candidate_status',
            );
        };

        $rootScope.saveStatusOfCandidate = function () {
            if ($rootScope.changeStateInCandidate.status != '' && !$rootScope.clickedSaveStatusOfCandidate) {
                $rootScope.clickedSaveStatusOfCandidate = true;
                $rootScope.changeStateInCandidate.status === 'our employee'
                    ? ($rootScope.changeStateInCandidate.status = 'work')
                    : null;

                if ($rootScope.deleteFromSystem) {
                    $rootScope.loading = true;
                    Candidate.deleteCandidate(
                        {
                            candidateId: $scope.candidate.candidateId,
                            comment: $rootScope.changeStateInCandidate.comment,
                            candidateState: $rootScope.changeStateInCandidate.status,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                notificationService.success($filter('translate')('Candidate removed'));
                                updateHistory();
                                $location.path('/candidates');
                            } else {
                                if (resp.status === 'delete_limit' && resp.total === 0) {
                                    notificationService.error(
                                        $filter('translate')(
                                            'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                                        ),
                                    );
                                } else if (resp.message && resp.message === 'blockedDeletingCandidates') {
                                    $rootScope.showBlockDeletingCandidatesModal(resp.object);
                                    $scope.statusModel = {
                                        name: `candidate_status_assoc.${$scope.candidate.status}`,
                                        value: $scope.candidate.status,
                                    };
                                } else {
                                    notificationService.error(resp.message);
                                    $location.path('/candidates');
                                }
                            }
                            $rootScope.loading = false;
                            $rootScope.clickedSaveStatusOfCandidate = false;
                            $rootScope.closeModal();
                            $rootScope.changeStateInCandidate.status = '';
                            $rootScope.changeStateInCandidate.comment = '';
                            $rootScope.$$phase || $scope.$apply();
                        },
                        function (err) {
                            $('.changeStatusOfCandidate.modal').modal('hide');
                            $rootScope.clickedSaveStatusOfCandidate = false;
                            console.error(err);
                        },
                    );
                } else {
                    $rootScope.loading = true;
                    Candidate.changeState(
                        {
                            candidateId: $scope.candidate.candidateId,
                            comment: $rootScope.changeStateInCandidate.comment,
                            candidateState: $rootScope.changeStateInCandidate.status,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                $scope.candidate.status = resp.object.status;
                                $scope.statusModel = {
                                    name: `candidate_status_assoc.${resp.object.status}`,
                                    value: resp.object.status,
                                };

                                const userName = $rootScope.useAmericanNameStyle
                                    ? $scope.candidate.fullNameEn
                                    : $scope.candidate.fullName;
                                notificationService.success(
                                    $filter('translate')('set_status_1') +
                                        userName +
                                        $filter('translate')('set_status_2'),
                                );
                                updateHistory();
                            } else if (resp.status === 'delete_limit' && resp.total === 0) {
                                setStatusPrevValue();
                                notificationService.error(
                                    $filter('translate')(
                                        'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                                    ),
                                );
                            } else if (
                                resp.status === 'error' &&
                                resp.message &&
                                resp.message === 'blockedDeletingCandidates'
                            ) {
                                $rootScope.showBlockDeletingCandidatesModal(resp.object);
                                $scope.statusModel = {
                                    name: `candidate_status_assoc.${$scope.candidate.status}`,
                                    value: $scope.candidate.status,
                                };
                            } else {
                                notificationService.error(resp.message);
                            }
                            if (
                                $rootScope.changeStateInCandidate.status === 'employed' &&
                                $rootScope.me.orgParams.bambooHrIntegration === 'auto' &&
                                !$scope.candidate.existInBamboo
                            )
                                $scope.transferCandidateToBambooHR();
                            $rootScope.loading = false;
                            $rootScope.clickedSaveStatusOfCandidate = false;
                            $rootScope.closeModal();
                            $rootScope.changeStateInCandidate.status = '';
                            $rootScope.changeStateInCandidate.comment = '';
                            $rootScope.$$phase || $scope.$apply();
                        },
                        function (err) {
                            $('.changeStatusOfCandidate.modal').modal('hide');
                            $rootScope.clickedSaveStatusOfCandidate = false;
                            console.error(err);
                        },
                    );
                }
            }
        };

        const fetchMergeCandidates = (inputText, callback) => {
            if (inputText?.length && inputText?.length < inputSearchThreshold) return callback();
            Candidate.onGetAutocompleteWithDuplicates(
                $httpParamSerializer({
                    name: inputText.trim(),
                    candidateId: $rootScope.currentCandidateId,
                }),
            ).then((resp) => {
                let candidatesForMerge = [];
                let possibleDuplicatesForMerge = [];

                resp.objects.forEach(({ position, fullName, fullNameEn, candidateId: id, localId, duplicate }) => {
                    const candidateName = $rootScope.useAmericanNameStyle ? fullNameEn : fullName;
                    const label = position && position !== ' ' ? `${candidateName}, ${position}` : candidateName;

                    (duplicate ? possibleDuplicatesForMerge : candidatesForMerge).push({
                        label,
                        position,
                        fullName: candidateName,
                        id,
                        localId,
                    });
                });

                callback(
                    possibleDuplicatesForMerge.length
                        ? [
                              {
                                  label: $translate.instant('Possible doubles'),
                                  options: possibleDuplicatesForMerge,
                              },
                              {
                                  label: $translate.instant('other -s'),
                                  options: candidatesForMerge,
                              },
                          ]
                        : candidatesForMerge,
                );
            });
            $scope.$$phase || $scope.$apply();
        };

        $scope.toMergeModal = function (id) {
            $rootScope.candidateForMerge = undefined;
            if ($rootScope.me.recrutRole !== 'client') {
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/candidate-merge-modal.html',
                    windowClass: 'standart-modal',
                    controller: [
                        '$scope',
                        function ($scope) {
                            $scope.mergedCandidate = $stateParams.id;
                            $scope.onFetchMergeCandidates = $rootScope.makeDebounce(fetchMergeCandidates, 300);
                            $scope.onChangeCandidateForMerge = (value) => {
                                $scope.candidateForMerge = value || null;
                                $localStorage.set('candidateForMerge', $scope.candidateForMerge);
                                $rootScope.$$phase || $rootScope.$apply();
                            };
                        },
                    ],
                    resolve: {
                        items: function () {
                            return $scope.items;
                        },
                    },
                });
            } else {
                notificationService.error(
                    $filter('translate')('Only recruiters, admins and freelancers can adding candidates in vacancy'),
                );
            }
        };
        $rootScope.toMerge = function (id) {
            $rootScope.closeModal();
            if ($rootScope.me.recrutRole != 'client') {
                if ($scope.candidate.status != 'archived') {
                    Service.toMergeCandidate(id);
                } else {
                    notificationService.error($filter('translate')('Remote candidates can not be edited.'));
                }
            } else {
                notificationService.error(
                    $filter('translate')('Only recruiters, admins and freelancers can editing candidates'),
                );
            }
        };
        $scope.toEdit = function () {
            if ($rootScope.me.recrutRole != 'client') {
                if ($scope.candidate.status != 'archived') {
                    Service.toEditCandidate($stateParams.id, $stateParams.sliderDataId);
                } else {
                    notificationService.error($filter('translate')('Remote candidates can not be edited.'));
                }
            } else {
                notificationService.error(
                    $filter('translate')('Only recruiters, admins and freelancers can editing candidates'),
                );
            }
        };

        $rootScope.changeStatusOfInterviewInVacancy = {
            candidate: {},
            comment: '',
            status: '',
            date: null,
            exportgoogle: false,
        };
        $rootScope.addCandidateInInterview = {
            id: '',
            comment: '',
            status: 'longlist',
            buttonClicked: false,
            date: null,
            showSelect: '',
            showText: false,
            text: '',
            addedInVacancy: false,
            select2Obj: null,
        };

        $rootScope.addCommentInCandidate = function (out) {
            $rootScope.loading = true;
            let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorOneCandidate.getData());
            initTagsInModal();
            $rootScope.commentCandidate.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');
            if ($rootScope.commentCandidate.comment != undefined && $rootScope.commentCandidate.comment.length > 0) {
                $rootScope.loading = true;
                Candidate.setMessage(
                    {
                        comment: $rootScope.commentCandidate.comment,
                        candidateIds: [$scope.candidate.candidateId],
                    },
                    function (resp) {
                        $rootScope.closeModal();
                        $rootScope.commentCandidate.comment = null;
                        if (resp.status == 'ok') {
                            $rootScope.$emit('onUpdateCandidateHistory');
                        }
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                        notificationService.success($filter('translate')('Comment added'));
                    },
                    function (error) {
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                        notificationService.error(error.message);
                    },
                );
            } else {
                notificationService.error($filter('translate')('enter a comment'));
            }
        };
        $scope.toChangeStatusInterview = (stageModel, props) => {
            $rootScope.changeStatusOfInterviewInVacancy.status = stageModel;
            let status = stageModel;
            let vacancyId = props.vacancy;
            let interviewStatusNotTouchable = props.vacancyId.interviewStatusNotTouchable;
            let vacancyPosition = props.vacancyId.position;
            let vacancy = props.vacancyId;
            let responsibles = props.responsibles;
            $rootScope.changeCalendarTitle(vacancy);
            $rootScope.tempEmailTemplate = null;
            $rootScope.emailTemplateModel = '';
            getAllEmailTemplates(status);
            $scope.changeStageTemplate(vacancyId);
            $rootScope.googleMeet = false;
            $rootScope.outlookMeet = false;
            $scope.changeVacancyStage = true;
            $scope.showGoogleComment = false;
            $scope.meetUsers = [];
            $rootScope.showMeetusers = [];
            $scope.vacancyForTransfer = vacancy;
            $scope.meetParticipants = {};
            var array = interviewStatusNotTouchable.split(',');
            var vacancyStatus = Vacancy.interviewStatusNew();

            if ($scope.candidateProperties.responsible.fullName.length > 0) {
                $scope.initMeetingUsers([$scope.candidateProperties.responsible], $scope.candidate, responsibles);
            } else {
                $scope.initMeetingUsers([], $scope.candidate, responsibles);
            }

            if (vacancyStatus) {
                angular.forEach(vacancyStatus, function (v) {
                    if (v.status) {
                        angular.forEach(v.status, function (s) {
                            angular.forEach($scope.customStages, function (resp) {
                                if (resp.name == status) {
                                    angular.forEach(array, function (res) {
                                        if (res == resp.customInterviewStateId) {
                                            s = resp;
                                        }
                                    });
                                }
                            });
                            if (status.value == s.value) {
                                $rootScope.changeStatusOfInterviewInVacancy.status = s;
                            }
                        });
                    }
                });
            }
            if ($rootScope.changeStatusOfInterviewInVacancy.status == undefined) {
                $rootScope.changeStatusOfInterviewInVacancy.status = {
                    value: status,
                    withDate: false,
                    defaultS: true,
                    single: false,
                    added: true,
                    active_color: 'longlist_color',
                    useAnimation: false,
                    count: 0,
                    forAdd: true,
                };
            }

            $rootScope.changeStatusOfInterviewInVacancy.vacancyId = vacancyId;
            $rootScope.changeStatusOfInterviewInVacancy.position = vacancyPosition;
            $rootScope.changedStatusVacancy = vacancy;
            if ($scope.candidate.state == 'approved' && $rootScope.me.recrutRole != 'admin') {
                notificationService.error($filter('translate')('Transfer from the status of approved can only Admin'));
                return;
            }
            $rootScope.changeStatusOfInterviewInVacancy.candidate.candidateId = $scope.candidate;
            $rootScope.changeStatusOfInterviewInVacancy.approvedCount = $scope.approvedCount;
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-change-status-in-vacancy.html',
                controller: ['$scope', function ($scope) {}],
                scope: $scope,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });

            $scope.changeLetterSubject = (value) => {
                $rootScope.emailTemplateInModal.title = value;
                $scope.$$phase || $scope.$apply();
            };

            $scope.modalInstance.closed.then(function () {
                $scope.emptyRequiredInputs = [];
                $rootScope.candnotify.show = false;
                $rootScope.candnotify.name = $scope.candidate;
                $scope.changeStatusOfInterviewInVacancyPick = null;
                $scope.addCandidateInvacancyPicker = null;
                $rootScope.addCandidateInVacancy.date = null;
            });
            $scope.modalInstance.opened.then(function () {
                $scope.isCalendarTitleError = false;
                setTimeout(() => {
                    Service.createEmailTemplateFunc(
                        $scope,
                        $rootScope,
                        'changeStatusCandidateInVacancyMCE',
                        Mail,
                        $location,
                    );

                    $rootScope.changeTemplateInChangeStatusCandidate(
                        $rootScope.changeStatusOfInterviewInVacancy.status,
                    );

                    new Promise((resolve, reject) => {
                        let interval = setInterval(() => {
                            let changeStatusOfInterviewInVacancyPick = $('.changeStatusOfInterviewInVacancyPick'),
                                changeStatusOfInterviewEmployed = $('.changeStatusOfInterviewEmployed');

                            if (changeStatusOfInterviewInVacancyPick.length && changeStatusOfInterviewEmployed.length) {
                                clearInterval(interval);
                                resolve({
                                    changeStatusOfInterviewInVacancyPick,
                                    changeStatusOfInterviewEmployed,
                                });
                            }
                        }, 0);
                    }).then((elements) => {
                        $rootScope.changeStatusOfInterviewInVacancy.date = $scope.changeStatusOfInterviewInVacancyPick;

                        elements['changeStatusOfInterviewEmployed']
                            .datetimepicker({
                                format: 'dd/mm/yyyy',
                                startView: 2,
                                minView: 2,
                                autoclose: true,
                                weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                                language: $translate.use(),
                            })
                            .on('changeDate', function (data) {
                                $rootScope.changeStatusOfInterviewEmployed.date = data.date;
                            })
                            .on('hide', function () {
                                if ($('.changeStatusOfInterviewEmployed').val() == '') {
                                    $rootScope.changeStatusOfInterviewEmployed.date = null;
                                }
                                $('.changeStatusOfInterviewEmployed').blur();
                            });
                        $scope.$apply();
                    });
                }, 500);

                initUsersTags();
                $rootScope.ckEditorOneCandidateInModal = {
                    height: 140,
                    marginTop: '10px',
                    toolbar: [],
                    enterMode: CKEDITOR.ENTER_BR,
                    shiftEnterMode: CKEDITOR.ENTER_BR,
                    autoGrow_minHeight: 140,
                    autoGrow_maxHeight: 340,
                    extraPlugins: 'mentions, autocomplete, textmatch, textwatcher, xml, editorplaceholder, autogrow',
                    removePlugins: 'contextmenu,liststyle,tabletools,tableselection, elementspath',
                    editorplaceholder: $translate.instant('mention you'),
                    mentions: [
                        {
                            feed: dataCallback,
                            minChars: 0,
                            itemTemplate: '<li style="padding: 10px" data-id="{id}">{fullname}</li>',
                            outputTemplate: `<a href='#' style='text-decoration: none; line-height: 2; background-color: #DAECE1;border-radius: 10px;color: #202021;padding: 4px;border: 1px solid transparent;'>@{fullname}</a>&nbsp`,
                            marker: '@',
                            pattern: /@([\p{L}\p{M}]*(?:\s[\p{L}\p{M}]*)?)?$/u,
                        },
                    ],
                    on: {
                        paste: function (ev) {
                            ev.data.dataValue = '';
                            CKEDITOR.instances['ckEditorOneCandidateInModal'].insertText(
                                ev.data.dataTransfer._.data.Text,
                            );
                            setTimeout(() => {
                                let bodyHeight = ev.editor.document.$.children[0].offsetHeight;
                                if (bodyHeight > ev.editor.config.height) {
                                    if (bodyHeight < 605) {
                                        ev.editor.resize(null, bodyHeight);
                                    }
                                }
                            }, 0);
                        },
                        afterInsertHtml: function (ev) {
                            let user = ev.editor.data;
                            if (user) {
                                deleteFromTags(user.userId, 'userId');
                                $scope.deletedUsers.push(user);
                            }
                        },
                        contentDom: function (evt) {
                            var editable = evt.editor.editable();
                            editable.attachListener(editable, 'keyup', function (evt) {
                                if (evt.data.getKeystroke() === 1114125) {
                                    let data =
                                        $scope.addCandidateInvacancyPicker != null &&
                                        $rootScope.addCandidateInVacancy.status.withDate
                                            ? $scope.addCandidateInvacancyPicker
                                            : null;
                                    $rootScope.saveStatusInterviewInVacancy(data);
                                }
                            });
                        },
                        instanceReady: function (ev) {
                            $scope.deletedUsers = [];
                            $scope.afterDelete = [];

                            ev.editor.dataProcessor.writer.setRules('p', {
                                indent: false,
                                breakBeforeOpen: false,
                                breakAfterOpen: false,
                                breakBeforeClose: false,
                                breakAfterClose: false,
                            });
                            ev.editor.dataProcessor.writer.setRules('br', {
                                indent: false,
                                breakBeforeOpen: false,
                                breakAfterOpen: false,
                                breakBeforeClose: false,
                                breakAfterClose: false,
                            });
                        },
                    },
                };

                function dataCallback(opts, callback) {
                    opts.query = opts.query.replace(/\u00A0/g, ' ');
                    editUsersInTags('ckEditorOneCandidateInModal');

                    if (
                        opts.query.length === 0 &&
                        $scope.repsonsibleUsers.length > 0 &&
                        $scope.repsonsibleUsers[0].userId !== $rootScope.me.userId
                    ) {
                        setTimeout(function () {
                            callback(
                                $scope.repsonsibleUsers.filter(function (item) {
                                    return item.fullname;
                                }),
                            );
                        });
                    } else {
                        setTimeout(function () {
                            callback(
                                $scope.users.filter(function (item) {
                                    return item.fullname.toLowerCase().indexOf(opts.query.toLowerCase()) != -1;
                                }),
                            );
                        });
                    }
                }
            });

            $scope.showChangeStatusValue = null;
            $rootScope.candnotify = {};
            var email = '';
            angular.forEach($scope.candidate.contacts, function (c) {
                if (c.type === 'email' && c.default) {
                    email = c.value;
                }
            });
            $rootScope.candnotify.emails = email.replace(/ /gi, '').split(',');
            $rootScope.candnotify.sendMail = email && email.length ? email.split(/[',',' ']/gi)[0] : '';

            $rootScope.candnotify.show = false;
            $rootScope.candnotify.name = $scope.candidate;
            $rootScope.candnotify.fullName = $scope.candidate.fullName;

            if (status.value == 'approved') {
                $rootScope.showEmployedFields = true;
                $rootScope.probationaryPeriod = null;
            } else {
                $rootScope.showEmployedFields = false;
            }
        };

        function deleteFromTags(id, type) {
            $scope.users.filter((user, index) => {
                if (user[type] == id) {
                    $scope.users.splice(index, 1);
                }
            });
            $scope.repsonsibleUsers.forEach((user, index) => {
                if (user.userId == id) {
                    $scope.repsonsibleUsers.splice(index, 1);
                }
            });
        }

        function initUsersTags() {
            $scope.repsonsibleUsers = [];
            $scope.users = [];

            $rootScope.persons.forEach((i, index) => {
                if (i.userId === $scope.candidateProperties.responsible.userId && i.recrutRole !== 'researcher') {
                    $scope.repsonsibleUsers.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: true,
                    });
                }
                if (i.status === 'A') {
                    $scope.users.push({
                        id: index + 1,
                        fullname: $rootScope.useAmericanNameStyle ? i.fullNameEn : i.fullName,
                        userId: i.userId,
                        responsible: false,
                    });
                }
            });
            $scope.users.sort((a, b) => a.fullname.localeCompare(b.fullname));
        }

        $rootScope.changeStatusInAddCandidate = function () {
            if (!$rootScope.addCandidateInInterviewbuttonClicked) {
                var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
                var candidateObj = $rootScope.addCandidateInInterview;
                var date =
                    $('.addCandidateInInterviewPicker').datetimepicker('getDate') != null &&
                    candidateObj.status.withDate
                        ? $('.addCandidateInInterviewPicker').datetimepicker('getDate')
                        : null;

                if (changeObj.candidate.state == 'approved' && $rootScope.me.recrutRole != 'admin') {
                    $rootScope.errorMessageForAddCandidate.text = $filter('translate')(
                        'Transfer from the status of approved can only Admin',
                    );
                    $rootScope.errorMessageForAddCandidate.show = true;
                    return;
                }
                changeObj.status = candidateObj.status;
                changeObj.comment = candidateObj.comment;
                $rootScope.addCandidateInInterviewbuttonClicked = true;
                $rootScope.saveStatusInterviewInVacancy(date);
                $('.addCandidateInInterviewPicker').val('');
                $('.addCandidateInInterview.modal').modal('hide');
            }
        };

        $rootScope.transferCandidateInOtherVacancyStatus = function () {
            var status = $('#candidateAddToVacancy').select2('data').status;
            if (status == 'approved' && $rootScope.me.recrutRole != 'admin') {
                $rootScope.errorMessageForAddCandidate.text = $filter('translate')(
                    'Transfer from the status of approved can only Admin',
                );
                $rootScope.errorMessageForAddCandidate.show = true;
                return;
            }

            $rootScope.changeStatusOfInterviewInVacancy = $rootScope.addCandidateInVacancy;
            $rootScope.changeStatusOfInterviewInVacancy.vacancyId = $('#candidateAddToVacancy').select2('data').id;
            var data =
                $scope.addCandidateInvacancyPicker != null && $rootScope.addCandidateInVacancy.status.withDate
                    ? $scope.addCandidateInvacancyPicker
                    : null;
            $rootScope.saveStatusInterviewInVacancy(data);
            $('.addCandidateInVacancy').modal('hide');
            $rootScope.candidateAddedInVacancy = false;
            $scope.addCandidateInvacancyPicker = '';
        };
        $scope.initDirective = {};

        function checkRequiredInputs(sendEmail = false) {
            $scope.emptyRequiredInputs = [];
            if (sendEmail && !$rootScope.candnotify.sendMail) $scope.emptyRequiredInputs.push('candidateEmail');
            if (!$rootScope.calendarTitle && ($rootScope.googleMeet || $rootScope.outlookMeet))
                $scope.emptyRequiredInputs.push('calendarTitle');
            $scope.requiredInputs.forEach((inputId) => {
                if ($scope.emptyRequiredInputs.includes(inputId)) {
                    $rootScope.scrollToDomElement(`#${inputId}`, 'smooth');
                    notificationService.error($filter('translate')(inputId));
                }
            });

            return !!$scope.emptyRequiredInputs.length;
        }

        $rootScope.saveStatusInterviewInVacancy = function (customDate, flag, sendEmail, event) {
            if (checkRequiredInputs(sendEmail)) return;
            initUsersTags();

            const dateAndTimeText =
                $rootScope.changeStatusOfInterviewInVacancy.status.value === 'accept_offer'
                    ? 'Please enter a date and time'
                    : 'Please enter a date and time of interview';

            if (event && event.type === 'keypress' && !keyBoardActionWithCtrlEnterButtons(event)) return;

            if ($rootScope.googleMeet || $rootScope.outlookMeet) {
                $scope.createMapForMeeting();
                $scope.googleMeetFlag = $rootScope.googleMeet;
            }

            if (
                $rootScope.candnotify.show &&
                $rootScope.emailTemplateInModal?.text &&
                $rootScope.emailTemplateInModal?.title &&
                $rootScope.changeStatusOfInterviewInVacancy.status.type !== 'refuse' &&
                exisitingDateTimeOperators()
            ) {
                $rootScope.scrollToDomElement('#modalDateElement', 'smooth');
                notificationService.error($filter('translate')(dateAndTimeText));
                return;
            }

            if (flag && !$rootScope.addCandidateInVacancy.date) {
                if (
                    $rootScope.changeStatusOfInterviewInVacancy.status.type !== 'refuse' &&
                    exisitingDateTimeOperators()
                ) {
                    $rootScope.scrollToDomElement('#modalDateElement', 'smooth');
                    return notificationService.error($filter('translate')(dateAndTimeText));
                }
            }

            if (!$rootScope.clickedSaveStatusInterviewInVacancy) {
                $rootScope.clickedSaveStatusInterviewInVacancy = true;
                $rootScope.changeStatusOfInterviewInVacancy.errorMessage = false;
                var changeObj = $rootScope.changeStatusOfInterviewInVacancy;
                if (changeObj.status.value == 'declinedoffer' && changeObj.comment == '') {
                    $rootScope.changeStatusOfInterviewInVacancy.errorMessage = true;
                    $rootScope.clickedSaveStatusInterviewInVacancy = false;
                }
                if ($rootScope.showEmployedFields) {
                    changeObj.date =
                        $scope.changeStatusOfInterviewEmployed != null
                            ? $scope.changeStatusOfInterviewEmployed
                            : customDate != undefined
                            ? customDate
                            : null;
                } else {
                    changeObj.date =
                        $scope.addCandidateInvacancyPicker != null
                            ? $scope.addCandidateInvacancyPicker
                            : customDate != undefined
                            ? customDate
                            : null;
                }

                let text = $rootScope.replacerForTags(CKEDITOR.instances.ckEditorOneCandidateInModal.getData());
                changeObj.comment = text.replaceAll('<br />', ' &nbsp<br>&nbsp ');

                if (sendEmail == true && $rootScope.changeStatusOfInterviewInVacancy.vacancyId) {
                    Vacancy.changeInterviewAndNotify(
                        {
                            personId: $scope.personId,
                            vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                            candidateId: $scope.candidate.candidateId,
                            interviewState: changeObj.status.name
                                ? changeObj.status.customInterviewStateId
                                : changeObj.status.value,
                            comment: changeObj.comment,
                            date: changeObj.date,
                            lang: $translate.use(),
                            createGoogleMeet: $rootScope.googleMeet,
                            createTeams: $rootScope.outlookMeet,
                            minutesDuration: $rootScope.meetDuration.value,
                            summary: $rootScope.calendarTitle,
                            meetComment: $scope.changeStatusOfInterviewInVacancy.meetComment,
                            meetParticipants: $scope.meetParticipants,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                updBreadcrumbs($rootScope.changeStatusOfInterviewInVacancy);
                                if (changeObj.status.customInterviewStateId) {
                                    var id = resp.object.interviewId + changeObj.status.customInterviewStateId;
                                } else {
                                    var id = resp.object.interviewId + changeObj.status.value;
                                }

                                $scope.showChangeStatusValue = null;
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                if (
                                    $rootScope.candnotify.send &&
                                    $rootScope.candnotify.sendMail.length > 1 &&
                                    sendEmail
                                ) {
                                    var candnotify = $rootScope.candnotify;
                                    let templateForRequest = {
                                        type: $rootScope.emailTemplateInModal.type,
                                        title: $rootScope.emailTemplateInModal.title,
                                        text: $rootScope.emailTemplateInModal.text,
                                    };
                                    if ($rootScope.changeStatusOfInterviewInVacancy.status.value === 'accept_offer') {
                                        templateForRequest.type = 'offerAccepted';
                                        templateForRequest.filesIdName = Service.filesIdNameToObjectInString(
                                            $rootScope.fileForSave,
                                        );
                                    } else {
                                        templateForRequest.filesIdName = Service.filesIdNameToObjectInString(
                                            $rootScope.fileForSave,
                                        );
                                    }

                                    if ($scope.googleMeetFlag) {
                                        templateForRequest.text = templateForRequest.text.replace(
                                            '[[google meet link]]',
                                            `<a href='${resp.object.meetLink}' target='_blank'>${resp.object.meetLink}</a>`,
                                        );
                                    }

                                    Mail.sendMailByTemplateVerified(
                                        {
                                            toEmails: candnotify.sendMail,
                                            vacancyId: $rootScope.changedStatusVacancy
                                                ? $rootScope.changedStatusVacancy.vacancyId
                                                : $rootScope.VacancyAddedInCandidate.vacancyId,
                                            candidateId: $scope.candidate.candidateId,
                                            fullName: $scope.candidate.fullName,
                                            email:
                                                $rootScope.emailTemplateInModal.email || $rootScope.me.emails[0].email,
                                            date: changeObj.date,
                                            lang: $translate.use(),
                                            notNeedToSaveAction: true,
                                            template: templateForRequest,
                                        },
                                        function (resp) {
                                            if (resp.status == 'ok') {
                                                notificationService.success($filter('translate')('Letter sent'));
                                                $rootScope.closeModal();
                                            } else if (resp.code == 'emailAuthFailed') {
                                                notificationService.success($filter('translate')('Letter sent'));
                                                notificationService.error(
                                                    $filter('translate')(
                                                        'Email was not sent, please check mail integration settings',
                                                    ),
                                                );
                                            }
                                        },
                                        (err) => {
                                            notificationService.error($filter('translate')(err.message));
                                        },
                                    );
                                }
                                $rootScope.changeStatusOfInterviewInVacancy = {
                                    candidate: {},
                                    comment: '',
                                    status: '',
                                    date: null,
                                    exportgoogle: false,
                                };
                                $rootScope.addCandidateInInterviewbuttonClicked = false;
                                $rootScope.closeModal();
                                $('.changeStatusOfInterviewInVacancyPick').val('');
                                updateCandidateProperties();
                                $rootScope.$emit('onUpdateCandidateHistory');
                                let changeStagesText = 'You have transferred the candidate to the stage';
                                let reward = 'Reward';
                                if (resp.object.points != 0) {
                                    setTimeout(() => {
                                        gamificationNotify.showNotify(
                                            `${$filter('translate')(changeStagesText)}`,
                                            `${$filter('translateStage')(changeObj.status)}`,
                                            `${$filter('translate')(reward)} +${resp.object.points} ${$filter(
                                                'translate',
                                            )('of experience')}`,
                                        );
                                    }, 1500);
                                }
                            } else if (resp.status == 'error') {
                                if (resp.code === 'googleMeetError') {
                                    $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                    notificationService.error($filter('translate')('google_meet_error'));
                                    $rootScope.loading = false;
                                    return;
                                }
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                notificationService.error(resp.message);
                            }
                        },
                        function (err) {
                            $scope.showChangeStatusValue = null;
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            $rootScope.addCandidateInInterviewbuttonClicked = false;
                            console.error(err);
                        },
                    );
                } else if (
                    $rootScope.showEmployedFields &&
                    sendEmail == undefined &&
                    $rootScope.changeStatusOfInterviewInVacancy.vacancyId
                ) {
                    Vacancy.editInterview(
                        {
                            personId: $scope.personId,
                            vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                            candidateId: $scope.candidate.candidateId,
                            interviewId: changeObj.candidate.interviewId,
                            interviewState: changeObj.status.customInterviewStateId
                                ? changeObj.status.customInterviewStateId
                                : changeObj.status.value,
                            comment: changeObj.comment,
                            lang: $translate.use(),
                            probationaryPeriod: $rootScope.probationaryPeriod,
                            dateEmployee: $scope.addCandidateInvacancyPicker,
                            createGoogleMeet: $rootScope.googleMeet,
                            createTeams: $rootScope.outlookMeet,
                            minutesDuration: $rootScope.meetDuration.value,
                            summary: $rootScope.calendarTitle,
                            meetComment: $scope.changeStatusOfInterviewInVacancy.meetComment,
                            meetParticipants: $scope.meetParticipants,
                        },
                        // "dateEmployee": changeObj.date.date !== null ? changeObj.date: null},
                        function (resp) {
                            if (resp.status == 'ok') {
                                updBreadcrumbs($rootScope.changeStatusOfInterviewInVacancy);
                                if (resp.object && resp.object.showCloseVacancyPopup) initModalRemainder(changeObj);
                                if (changeObj.status.customInterviewStateId) {
                                    var id = resp.object.interviewId + changeObj.status.customInterviewStateId;
                                } else {
                                    var id = resp.object.interviewId + changeObj.status.value;
                                }
                                $scope.showChangeStatusValue = null;
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                if (
                                    (changeObj.status.withDate || changeObj.status.type == 'interview') &&
                                    changeObj.date &&
                                    $rootScope.candnotify.send &&
                                    $rootScope.candnotify.sendMail.length > 1
                                ) {
                                    var candnotify = $rootScope.candnotify;
                                    Vacancy.sendInterviewCreateMail(
                                        {
                                            email: candnotify.sendMail,
                                            vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                                            candidateId: changeObj.candidate.candidateId.candidateId,
                                            fullName: candnotify.fullName,
                                            date: changeObj.date,
                                            lang: $translate.use(),
                                        },
                                        function (resp) {},
                                    );
                                }
                                $rootScope.changeStatusOfInterviewInVacancy = {
                                    candidate: {},
                                    comment: '',
                                    status: '',
                                    date: null,
                                    exportgoogle: false,
                                };
                                $rootScope.addCandidateInInterviewbuttonClicked = false;
                                $rootScope.closeModal();
                                $('.changeStatusOfInterviewInVacancyPick').val('');
                                updateCandidateProperties();
                                $rootScope.$emit('onUpdateCandidateHistory');
                                let changeStagesText = 'You have transferred the candidate to the stage';
                                let reward = 'Reward';
                                if (resp.object.points != 0) {
                                    setTimeout(() => {
                                        gamificationNotify.showNotify(
                                            `${$filter('translate')(changeStagesText)}`,
                                            `${$filter('translateStage')(changeObj.status)}`,
                                            `${$filter('translate')(reward)} +${resp.object.points} ${$filter(
                                                'translate',
                                            )('of experience')}`,
                                        );
                                    }, 1500);
                                }
                            } else if (resp.status == 'error') {
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                notificationService.error(resp.message);
                            }
                        },
                        function (err) {
                            $scope.showChangeStatusValue = null;
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            $rootScope.addCandidateInInterviewbuttonClicked = false;
                        },
                    );
                } else if ($rootScope.changeStatusOfInterviewInVacancy.vacancyId) {
                    Vacancy.editInterview(
                        {
                            personId: $scope.personId,
                            vacancyId: $rootScope.changeStatusOfInterviewInVacancy.vacancyId,
                            candidateId: $scope.candidate.candidateId,
                            interviewState: changeObj.status.name
                                ? changeObj.status.customInterviewStateId
                                : changeObj.status.value,
                            comment: changeObj.comment,
                            date: changeObj.date,
                            lang: $translate.use(),
                            createGoogleMeet: $rootScope.googleMeet,
                            createTeams: $rootScope.outlookMeet,
                            minutesDuration: $rootScope.meetDuration.value,
                            summary: $rootScope.calendarTitle,
                            meetComment: $scope.changeStatusOfInterviewInVacancy.meetComment,
                            meetParticipants: $scope.meetParticipants,
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                updBreadcrumbs($rootScope.changeStatusOfInterviewInVacancy);
                                if (changeObj.status.customInterviewStateId) {
                                    var id = resp.object.interviewId + changeObj.status.customInterviewStateId;
                                } else {
                                    var id = resp.object.interviewId + changeObj.status.value;
                                }

                                $scope.showChangeStatusValue = null;
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                if (
                                    $rootScope.candnotify.send &&
                                    $rootScope.candnotify.sendMail.length > 1 &&
                                    sendEmail
                                ) {
                                    var candnotify = $rootScope.candnotify;

                                    Mail.sendMailByTemplateVerified(
                                        {
                                            toEmails: candnotify.sendMail,
                                            vacancyId: $rootScope.changedStatusVacancy
                                                ? $rootScope.changedStatusVacancy.vacancyId
                                                : $rootScope.VacancyAddedInCandidate.vacancyId,
                                            candidateId: $scope.candidate.candidateId,
                                            fullName: $scope.candidate.fullName,
                                            email:
                                                $rootScope.emailTemplateInModal.email || $rootScope.me.emails[0].email,
                                            date: changeObj.date,
                                            lang: $translate.use(),
                                            template: {
                                                type: $rootScope.emailTemplateInModal.type,
                                                title: $rootScope.emailTemplateInModal.title,
                                                text: $rootScope.emailTemplateInModal.text,
                                                fileId:
                                                    $rootScope.fileForSave.length > 0
                                                        ? $rootScope.fileForSave[0].fileId
                                                        : null,
                                                fileName:
                                                    $rootScope.fileForSave.length > 0
                                                        ? $rootScope.fileForSave[0].fileName
                                                        : null,
                                            },
                                        },
                                        function (resp) {
                                            if (resp.status == 'ok') {
                                                notificationService.success($filter('translate')('Letter sent'));
                                                $rootScope.closeModal();
                                            }
                                        },
                                    );
                                }
                                $rootScope.changeStatusOfInterviewInVacancy = {
                                    candidate: {},
                                    comment: '',
                                    status: '',
                                    date: null,
                                    exportgoogle: false,
                                };
                                $rootScope.addCandidateInInterviewbuttonClicked = false;
                                $rootScope.closeModal();
                                $('.changeStatusOfInterviewInVacancyPick').val('');
                                updateCandidateProperties();
                                $rootScope.$emit('onUpdateCandidateHistory');
                                let changeStagesText = 'You have transferred the candidate to the stage';
                                let reward = 'Reward';
                                if (resp.object.points != 0) {
                                    setTimeout(() => {
                                        gamificationNotify.showNotify(
                                            `${$filter('translate')(changeStagesText)}`,
                                            `${$filter('translateStage')(changeObj.status)}`,
                                            `${$filter('translate')(reward)} +${resp.object.points} ${$filter(
                                                'translate',
                                            )('of experience')}`,
                                        );
                                    }, 1500);
                                }
                            } else if (resp.status === 'error') {
                                if (resp.code === 'googleMeetError') {
                                    $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                    notificationService.error($filter('translate')('google_meet_error'));
                                    $rootScope.loading = false;
                                    return;
                                }
                                $rootScope.clickedSaveStatusInterviewInVacancy = false;
                                notificationService.error(resp.message);
                            }
                        },
                        function (err) {
                            $scope.showChangeStatusValue = null;
                            $rootScope.clickedSaveStatusInterviewInVacancy = false;
                            $rootScope.addCandidateInInterviewbuttonClicked = false;
                            console.error(err);
                        },
                    );
                }
            }
        };

        function setBreeadcrumbsStage(interviews) {
            if (!$scope.stageUrl) return;
            const stageUrl = angular.copy($scope.stageUrl);
            interviews.forEach((oneInterview) => {
                if (oneInterview.vacancy === stageUrl.vacancyId) {
                    let urlWithoutStage = stageUrl.url.split('&stage=')[0] + '&stage=';
                    stageUrl.stage = oneInterview.customStateName ? oneInterview.customStateName : oneInterview.state;
                    stageUrl.url = urlWithoutStage + oneInterview.state;

                    $scope.stageUrl = angular.copy(stageUrl);
                }
            });
        }

        function isContacts() {
            const contactFields = [
                'mphone',
                'email',
                'linkedin',
                'facebook',
                'telegram',
                'whatsApp',
                'viber',
                'skype',
                'github',
                'behance',
                'djinni',
                'homepage',
                'other',
            ];
            contactFields.forEach((contactName) => {
                $scope.contactAvailability[contactName] =
                    $scope.candidate.contacts.filter((item) => item.type === contactName).length > 0;
            });

            $scope.contactAvailability.showContactsBlock = $scope.candidate.contacts.every((contactField) =>
                contactFields.includes(contactField.type),
            );
        }

        function updBreadcrumbs(newStage) {
            if (!$scope.stageUrl || !newStage) return;
            let newStatus = newStage.status;
            let urlWithoutStage = $scope.stageUrl.url.split('&stage=')[0] + '&stage=';
            if ($scope.stageUrl.vacancyId && $scope.stageUrl.vacancyId === newStage.vacancyId) {
                if (newStatus.customInterviewStateId) {
                    $scope.stageUrl.url = urlWithoutStage + newStatus.customInterviewStateId;
                    $scope.stageUrl.stage = newStatus.name;
                } else {
                    $scope.stageUrl.url = urlWithoutStage + newStatus.value;
                    $scope.stageUrl.stage = newStatus.value;
                }
            }
        }

        $scope.changeTestStatus = function (appointmentId, status) {
            Test.editAppointment({ appointmentId: appointmentId, passed: status }, function (resp) {});
        };
        $scope.initDirectiveVar = {
            valiable: 'asdasd',
            test: 0,
        };
        $scope.close_modal = function () {
            $scope.show_modal = false;
        };
        $('.ui.dropdown').dropdown();
        $scope.showFastChangeStatus =
            $localStorage.get('showFastChangeStatus') != undefined ? $localStorage.get('showFastChangeStatus') : true;
        $scope.changeShowFastChangeStatus = function (change) {
            $scope.showFastChangeStatus = change;
            $localStorage.set('showFastChangeStatus', change);
        };
        $scope.changeCommentFlag = function (history) {
            history.editCommentFlag = !history.editCommentFlag;
            $scope.editComment = history.descr;
        };
        $rootScope.changeFlag = function (history) {
            history.editCommentFlag = !history.editCommentFlag;
            $scope.editComment = history.descr;
        };
        $scope.changeComment = function (action, comment) {
            Action.editAction({ comment: comment, actionId: action.actionId }, function (resp) {
                if (resp.status && angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                } else {
                    action.editCommentFlag = false;
                    action.descr = resp.object.descr;
                    action.new_komment = '';
                    action.dateEdit = resp.object.dateEdit;
                    notificationService.success($filter('translate')('Comment changed'));
                }
            });
        };
        $scope.changeComment = function (action, comment) {
            Action.editAction({ comment: comment, actionId: action.actionId }, function (resp) {
                if (resp.status && angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                } else {
                    action.editCommentFlag = false;
                    action.descr = resp.object.descr;
                    action.new_komment = '';
                    action.dateEdit = resp.object.dateEdit;
                    notificationService.success($filter('translate')('Comment changed'));
                }
            });
        };

        $rootScope.replaceComment = function (action, comment) {
            Action.editAction({ comment: comment, actionId: action.actionId }, function (resp) {
                if (resp.status && angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                } else {
                    action.editCommentFlag = false;
                    action.descr = resp.object.descr;
                    action.new_komment = '';
                    action.dateEdit = resp.object.dateEdit;
                    notificationService.success($filter('translate')('Comment changed'));
                }
            });
        };

        $scope.showDeleteComment = function (resp) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/delete-comment-candidate.html',
                scope: $scope,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });
            $scope.commentRemove = resp;
            $rootScope.commentRemoveId = resp.actionId;
        };

        $rootScope.deleteComment = function () {
            Action.removeMessageAction(
                {
                    actionId: $rootScope.commentRemoveId,
                },
                function (resp) {
                    if (resp.status === 'ok') {
                        $scope.showComments();
                        notificationService.success($filter('translate')('Comment removed'));
                        if (!$scope.onlyComments) {
                            $rootScope.$emit('onUpdateCandidateHistory');
                        }
                    } else {
                        console.error(resp);
                    }
                    $rootScope.closeModal();
                },
            );
        };

        $scope.changeUrlName = (value) => {
            $scope.addLinkToCandidate.name = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.changeUrl = (value) => {
            $scope.addLinkToCandidate.url = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.showAddLinkFunc = function () {
            $scope.showAddLink = true;
        };
        $scope.closeAddLinkFunc = function () {
            $scope.showAddLink = false;
            $scope.addLinkToCandidate.name = '';
            $scope.addLinkToCandidate.url = '';
            $scope.addLinkErrorShow = false;
        };
        $scope.addLinkInCandidate = function () {
            if ($scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
                if ($rootScope.me.recrutRole != 'client') {
                    $scope.addLinkToCandidate.url = $rootScope.addLinkPrefix($scope.addLinkToCandidate.url);
                    Candidate.addLink(
                        {
                            candidateId: $scope.candidate.candidateId,
                            name: $scope.addLinkToCandidate.name,
                            url: $scope.addLinkToCandidate.url,
                        },
                        function (resp) {
                            if (resp.status === 'ok') {
                                updateCandidateProperties();
                                $scope.addLinkToCandidate.name = '';
                                $scope.addLinkToCandidate.url = '';
                                $scope.addLinkErrorShow = false;
                                $scope.showAddLink = false;
                                notificationService.success($filter('translate')('history_info.added_link'));
                            } else {
                                notificationService.error(resp.message);
                            }
                        },
                    );
                } else {
                    notificationService.error(
                        $filter('translate')('This feature is available only to administrators and recruiters'),
                    );
                }
            } else {
                $scope.addLinkErrorShow = true;
                if (!$scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a title and URL'));
                }

                if ($scope.addLinkToCandidate.name && !$scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a URL'));
                }

                if (!$scope.addLinkToCandidate.name && $scope.addLinkToCandidate.url) {
                    notificationService.error($filter('translate')('Please enter a title'));
                }
            }
        };
        $scope.showCommentsFirstTime = function () {
            Service.history(
                {
                    page: { number: 0, count: 15 },
                    candidateId: $scope.candidate !== undefined ? $scope.candidate.candidateId : null,
                    onlyWithComment: ($scope.onlyWithComment = false),
                    ignoreType: ['interview_add_from_advice_v1', 'candidate_email_add'],
                },
                function (res) {
                    $scope.historyLimit = res.size;
                    $scope.historyTotal = res.total;
                    $scope.history = res.objects;

                    $scope.onlyComments = false;
                },
            );
        };
        $scope.showEditFileNameFunc = function (file) {
            file.showEditFileName = !file.showEditFileName;
            file.showMenuEdDelFile = !file.showMenuEdDelFile;
            $scope.showMenuEdDelFile = false;
        };
        $scope.onDownloadFile = function (file) {
            file.showMenuEdDelFile = !file.showMenuEdDelFile;
        };
        $scope.editFileName = function (data) {
            File.changeFileName(
                {
                    fileId: data.fileId,
                    name: data.fileName,
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        data.showEditFileName = false;
                    } else {
                        notificationService.error(resp.message);
                    }
                },
            );
        };
        $scope.onDownloadFile = function (file) {
            fetch(`${$window.location.origin}/hr/checkIfExists/${file.fileId}`)
                .then((res) => {
                    return res.json();
                })
                .then((data) => {
                    if (data.code === 'fileNotFound') notificationService.error(data.message);
                    if (data.status === 'ok') {
                        $rootScope.downloadFile(file.fileId, file.fileName);
                        updateCandidateProperties();
                    }
                });
            file.showMenuEdDelFile = false;
        };

        $scope.MenuEdDelFile = function (file) {
            file.showMenuEdDelFile = !file.showMenuEdDelFile;

            $('body').mouseup(function (e) {
                if (!e.target.className.includes('dropdown-toggle')) {
                    file.showMenuEdDelFile = false;
                }

                $scope.$$phase || $scope.$apply();
                $(document).off('mouseup');
            });
        };
        $scope.roundMinutes = function (date) {
            date.setHours(date.getHours());
            date.setMinutes(0);
            return date;
        };

        setTimeout(function () {
            $('.withoutTimeTask')
                .datetimepicker({
                    format: 'dd/mm/yyyy HH:00',
                    startView: 2,
                    minView: 1,
                    autoclose: true,
                    weekStart: $rootScope.currentLang == 'ru' || $rootScope.currentLang == 'ua' ? 1 : 7,
                    language: $translate.use(),
                    initialDate: new Date(),
                    startDate: new Date(),
                })
                .on('changeDate', function (data) {
                    $rootScope.editableTask.targetDate = $('.withoutTimeTask').datetimepicker('getDate');
                    $scope.roundMinutes($rootScope.editableTask.targetDate);
                    if ($rootScope.editableTask.targetDate) {
                        Task.changeTargetDate(
                            {
                                taskId: $rootScope.editableTask.taskId,
                                date: $rootScope.editableTask.targetDate,
                            },
                            function (resp) {
                                $scope.updateTasks();
                                $rootScope.$emit('onUpdateCandidateHistory');
                            },
                        );
                    }
                })
                .on('hide', function () {
                    if ($('.withoutTimeTask').val() == '') {
                        $rootScope.editableTask.date = '';
                    }
                    $('.withoutTimeTask').blur();
                });

            $scope.showModalConfirmationResumeUpdate = function () {
                $('.confirmationResumeUpdate.modal').modal('show');
            };
            $scope.showModalConfirmationResumeEdit = function (file) {
                if (file.showFiles) {
                    $rootScope.editResumeFile = file;
                    $scope.modalInstance = $uibModal.open({
                        animation: true,
                        windowClass: 'secondary-modal',
                        templateUrl: '../partials/modal/candidate-replace-with-resume.html',
                        size: '',
                    });
                } else {
                    $rootScope.loading = false;
                    notificationService.error($filter('translate')('wrong  file format '));
                }
            };
            $rootScope.changeCandidateFromExistingResume = function () {
                $rootScope.loading = true;
                Candidate.updateFromFile(
                    {
                        candidateId: $rootScope.candidateForUpdateResume.candidateId,
                        fileId: $rootScope.editResumeFile.fileId,
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            $scope.updateCandidate();
                            updateCandidateProperties();
                            $rootScope.loading = false;
                            $rootScope.closeModal();
                            notificationService.success($filter('translate')('candidate was updated by resume'));
                        } else {
                            notificationService.error(resp.message);
                            $rootScope.loading = false;
                            $rootScope.closeModal();
                        }
                    },
                );
            };
            $scope.showModalResume = function (file) {
                Service.showModalResume(file, $scope, $rootScope, $location, $sce, $uibModal);
            };
            $rootScope.closeModalResume = function () {
                $rootScope.closeModal();
            };
            $scope.downloadDoc = function () {
                $rootScope.loading = true;
                let docButton = $('#downloadDoc')[0];
                Candidate.getCV({ localId: $scope.candidate.localId }, function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.loading = false;
                        let pdfId = resp.object;
                        if (pdfId) {
                            docButton.href = '/hr/' + 'getapp?id=' + pdfId;
                            $timeout(() => {
                                docButton.click();
                            });
                        }
                    } else {
                        $rootScope.loading = false;
                        notificationService.error(resp.message);
                    }
                });
            };
            $rootScope.changeSearchType = function (param) {
                $window.location.replace('/!#/candidates');
                $rootScope.changeSearchTypeNotFromCandidates = param;
            };

            $rootScope.changeTabOnTask = function (val) {
                if (val == 'Task') {
                    $rootScope.editableTask.type = 'Task';
                } else if (val == 'Call') {
                    $rootScope.editableTask.type = 'Call';
                } else if (val == 'Meeting') {
                    $rootScope.editableTask.type = 'Meeting';
                }
                $rootScope.editNameTask(true);
            };
            $rootScope.changeTabOnTaskForNewTask = function (type) {
                $rootScope.newTask.type = type.value;
                $rootScope.typeOfTasksModel = type;
                $scope.$apply();
            };
            $rootScope.changeTabOnTaskForEditableTask = function (type) {
                $rootScope.editableTask.type = type;
                $scope.$apply();
            };
            $rootScope.saveEditableTask = () => {
                Task.edit(
                    {
                        taskId: $rootScope.editableTask.taskId,
                        status: $rootScope.editableTask.status.value.replace('tasksStatuses.', ''),
                        title: $rootScope.editableTask.title,
                        text: $rootScope.editableTask.text ? $rootScope.editableTask.text?.trim() : '',
                        targetDate: $rootScope.editableTask.targetDate,
                        responsibleIds: $rootScope.responsiblePersons.map(({ userId }) => userId),
                        type: $rootScope.editableTask.type.value,
                    },
                    (resp) => {
                        if (resp.status !== 'ok') {
                            notificationService.error(resp.message);
                        }
                        $scope.updateTasks();
                        $rootScope.closeTaskModal();
                        $rootScope.closePreviewModal && $rootScope.closePreviewModal();
                        notificationService.success($filter('translate')('Task saved'));
                    },
                );
            };
        });
        $scope.addOurEmployee = function (size) {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-add-candidate.html',
                size: size,
                resolve: {
                    items: function () {
                        return $scope.items;
                    },
                },
            });
        };

        $scope.removeCandidateFromVacancy = function (vacancyId) {
            $rootScope.loading = true;

            const payload = {
                vacancyId: vacancyId,
                candidateId: $scope.candidate.candidateId,
                comment: null,
            };

            Vacancy.onRemoveInterview(payload)
                .then((resp) => {
                    notificationService.success(
                        $filter('translate')('Candidate removed') + ' ' + $filter('translate')('from vacancy'),
                    );

                    updateCandidateProperties();
                    $rootScope.$emit('onUpdateCandidateHistory');
                })
                .catch((error) => console.error(error))
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $scope.$apply();
                });
        };

        function cascadeStages() {
            setTimeout(function () {
                angular.forEach($scope.candidateProperties.interviews, function (interview, index) {
                    var popWithstages = 'changeStatePop' + interview.vacancyId.vacancyId;
                    var popWithstagesInActive = 'changeStatePopInActive' + interview.vacancyId.vacancyId;

                    $('#' + popWithstages)
                        .children()
                        .each(function (index) {
                            var stageName = $(this).attr('id');
                            const isStandartStageInterview = [
                                'interview',
                                'telephone_interview',
                                'interview_with_the_boss',
                                'interview_with_the_client',
                                'tech_interview',
                                'hr_interview',
                            ];
                            var isStandartStageRefuse =
                                stageName == 'notafit' ||
                                stageName == 'declinedoffer' ||
                                stageName == 'is_not_looking_for_job' ||
                                stageName == 'no_response' ||
                                stageName == 'no_contacts' ||
                                stageName == 'accepted_counter_offer' ||
                                stageName == 'found_another_job' ||
                                stageName == 'offer_declined' ||
                                stageName == 'probation_failure';
                            if (isStandartStageRefuse) {
                                $(this).addClass('refusal');
                            } else {
                                if (stageName !== undefined) {
                                    for (var i = $scope.customStages.length; i--; i > 0) {
                                        if (
                                            $scope.customStages[i].type == 'refuse' &&
                                            $scope.customStages[i].name == stageName
                                        ) {
                                            $(this).addClass('refusal');
                                            break;
                                        }
                                    }
                                }
                            }
                            if (stageName === 'approved') $(this).addClass('approvedSpan');
                            if (isStandartStageInterview.includes(stageName)) {
                                $(this).addClass('interviewStage');
                            }
                            if (stageName !== undefined) {
                                $scope.customStages.forEach((item, index) => {
                                    if (item.type == 'interview' && $scope.customStages[index].name == stageName) {
                                        $(this).addClass('interviewStage');
                                    }
                                });
                            }
                        });

                    $('#' + popWithstagesInActive)
                        .children()
                        .each(function (index) {
                            var stageName = $(this).attr('id');
                            const isStandartStageInterview = [
                                'interview',
                                'telephone_interview',
                                'interview_with_the_boss',
                                'interview_with_the_client',
                                'tech_interview',
                                'hr_interview',
                            ];
                            var isStandartStageRefuse =
                                stageName == 'notafit' ||
                                stageName == 'declinedoffer' ||
                                stageName == 'is_not_looking_for_job' ||
                                stageName == 'no_response' ||
                                stageName == 'no_contacts' ||
                                stageName == 'accepted_counter_offer' ||
                                stageName == 'found_another_job' ||
                                stageName == 'offer_declined' ||
                                stageName == 'probation_failure';
                            if (isStandartStageRefuse) {
                                $(this).addClass('refusal');
                            } else {
                                if (stageName !== undefined) {
                                    for (var i = $scope.customStages.length; i--; i > 0) {
                                        if (
                                            $scope.customStages[i].type == 'refuse' &&
                                            $scope.customStages[i].name == stageName
                                        ) {
                                            $(this).addClass('refusal');
                                            break;
                                        }
                                    }
                                }
                            }
                            if (stageName === 'approved') $(this).addClass('approvedSpan');
                            if (isStandartStageInterview.includes(stageName)) {
                                $(this).addClass('interviewStage');
                            }
                            if (stageName !== undefined) {
                                $scope.customStages.forEach((item, index) => {
                                    if (item.type == 'interview' && $scope.customStages[index].name == stageName) {
                                        $(this).addClass('interviewStage');
                                    }
                                });
                            }
                        });
                });
            }, 0);
        }

        function cascadeStagesUnactive() {
            setTimeout(function () {
                angular.forEach($scope.candidateProperties.interviews, function (interview, index) {
                    var popWithstages = 'changeState' + interview.vacancyId.vacancyId;
                    $('#' + popWithstages)
                        .children()
                        .each(function (index) {
                            var stageName = $(this).attr('id').slice(1);
                            const isStandartStageInterview = [
                                'interview',
                                'telephone_interview',
                                'interview_with_the_boss',
                                'interview_with_the_client',
                                'tech_interview',
                                'hr_interview',
                            ];
                            var isStandartStageRefuse = [
                                'notafit',
                                'declinedoffer',
                                'is_not_looking_for_job',
                                'no_response',
                                'no_contacts',
                                'accepted_counter_offer',
                                'found_another_job',
                                'offer_declined',
                                'probation_failure',
                            ];
                            if (isStandartStageRefuse.includes(stageName)) $(this).addClass('refusalSpan');
                            if (stageName !== undefined) {
                                $scope.customStages.forEach((item, index) => {
                                    if (item.type == 'refuse' && $scope.customStages[index].name == stageName) {
                                        $(this).addClass('refusalSpan');
                                    }
                                });
                            }
                            if (stageName === 'approved') $(this).addClass('approvedSpan');
                            if (isStandartStageInterview.includes(stageName)) {
                                $(this).addClass('interviewStage');
                            }
                            if (stageName !== undefined) {
                                $scope.customStages.forEach((item, index) => {
                                    if (item.type == 'interview' && $scope.customStages[index].name == stageName) {
                                        $(this).addClass('interviewStage');
                                    }
                                });
                            }
                        });
                });
            }, 0);
        }

        // удалить потом
        // $scope.showRefusal = function (id, inActive) {
        //     let popWithstages = '';
        //     if (inActive) {
        //         popWithstages = 'changeStatePopInActive' + id;
        //     } else {
        //         popWithstages = 'changeStatePop' + id;
        //     }
        //
        //     $('#' + popWithstages)
        //         .find('.refusal')
        //         .each(function () {
        //             $('#' + popWithstages).append(this);
        //             $(this).css('marginBottom', '5px');
        //             $(this).animate({ height: '26px' }, 100);
        //         });
        // };
        //
        // $scope.hideRefusal = function (id) {
        //     var popWithstages = 'changeStatePop' + id;
        //     var popWithstagesInActive = 'changeStatePopInActive' + id;
        //     $('#' + popWithstages)
        //         .find('.refusal')
        //         .each(function () {
        //             $(this).css('marginBottom', '0');
        //             $(this).animate({ height: '0px' }, 100);
        //         });
        //
        //     $('#' + popWithstagesInActive)
        //         .find('.refusal')
        //         .each(function () {
        //             $(this).css('marginBottom', '0');
        //             $(this).animate({ height: '0px' }, 100);
        //         });
        // };

        ///////////////////////////////////////////////////////////////Email chat with candidate

        $scope.openChat = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-email-chat.html',
                size: '',
                resolve: {},
            });
        };
        $scope.gotoBottom = function () {
            document.getElementById('history').scrollIntoView();
        };
        $scope.limitedAccessCandidate = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                windowClass: 'secondary-modal',
                templateUrl: '../partials/modal/limited-access-candidate.html',
                size: '',
                backdrop: 'static',
                resolve: {},
            });
        };

        ////////////////////////////////////////////////////////////End of email chat with candidate
        ///////////////////////////////////////////////////////////////Sent Email candidate
        $rootScope.emailTemplateInModal = {
            text: 'Hi [[candidate name]]!<br/>',
        };

        $scope.restorePCDSTmpl = function () {
            $rootScope.loading = true;
            PDConsent.changeTemplate(
                { defaultTemplate: true, lang: $scope.PDCSTmplLang },
                (resp) => {
                    $rootScope.loading = false;
                    $scope.curentSavedPDconsentTemplate.text = resp.object.text.replace(
                        /\[candidate_name\]/g,
                        $scope.candidate.fullName,
                    );
                    $rootScope.emailTemplateInModal.title = resp.object.subject;
                    $scope.PDCSTmpltChanged = false;
                },
                (error) => {
                    $rootScope.loading = false;
                    notificationService.error('pdcs change template request status:' + error.status);
                },
            );
        };

        $scope.modifyPCDSTmpl = function () {
            $rootScope.loading = true;
            if ($rootScope.emailTemplateInModal.title && $rootScope.emailTemplateInModal.title.trim().length > 0) {
                let newText = $scope.curentSavedPDconsentTemplate.text.replace(
                    new RegExp($scope.candidate.fullName, 'g'),
                    '[candidate_name]',
                );
                PDConsent.changeTemplate(
                    {
                        defaultTemplate: false,
                        subject: $rootScope.emailTemplateInModal.title,
                        text: newText,
                        lang: $scope.PDCSTmplLang,
                    },
                    (resp) => {
                        $rootScope.loading = false;
                    },
                    (error) => {
                        $rootScope.loading = false;
                        notificationService.error('pdcs change template request status:' + error.status);
                    },
                );
            } else {
                $rootScope.loading = false;
                notificationService.error($filter('translate')('Enter the subject line'));
            }
        };

        FileInit.initVacancyTemplateFileOption($scope, '', '', false, $filter);
        $scope.callbackFileForTemplate = function (resp, names) {
            $scope.fileForSave.push({
                fileId: resp,
                fileName: names,
                fileResolution: Service.getFileResolutionFromName(names),
            });
            $rootScope.fileForSave.push({
                fileId: resp,
                fileName: names,
                fileResolution: Service.getFileResolutionFromName(names),
            });
        };
        $rootScope.addEmailFromWhatSend = function (email) {
            $rootScope.fromSendModel = email;

            if ($rootScope.emailThatAlreadyUsed) {
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    $rootScope.emailThatAlreadyUsed.email,
                    email.email,
                );
            }
            $rootScope.emailThatAlreadyUsed = email;
            localStorage.emailThatAlreadyUsed = email.email;
            $rootScope.emailTemplateInModal.email = email.email ? email.email : email;
            if ($scope.emailType !== 'pd_consent') {
                $rootScope.emailTemplateInModal.text = Email.insertSignatures(
                    $rootScope.emailTemplateInModal.text,
                    email.signature,
                );
            }

            $rootScope.$$phase || $rootScope.$apply();
        };
        $rootScope.sentEmail = function () {
            if (isFieldsFilled()) {
                Mail.sendMailByTemplateVerified(
                    {
                        toEmails: $rootScope.emailToSend,
                        candidateId: $scope.candidate.candidateId,
                        fullName: $scope.candidate.fullName,
                        email: $rootScope.emailTemplateInModal.email || $rootScope.me.emails[0].email,
                        date: null,
                        lang: $translate.use(),
                        template: {
                            type: $rootScope.emailTemplateInModal.type,
                            title: $rootScope.emailTemplateInModal.title,
                            text: $rootScope.emailTemplateInModal.text,
                            fileId: $rootScope.fileForSave.length > 0 ? $rootScope.fileForSave[0].fileId : null,
                            fileName: $rootScope.fileForSave.length > 0 ? $rootScope.fileForSave[0].fileName : null,
                        },
                    },
                    function (resp) {
                        if (resp.status == 'ok') {
                            notificationService.success($filter('translate')('Letter sent'));
                            $rootScope.closeModal();
                            $rootScope.emailToSend = null;
                            $rootScope.fileForSave = [];
                            $rootScope.emailTemplateInModal = {
                                text: 'Hi [[candidate name]]!<br/>',
                            };
                        }
                    },
                    (err) => {
                        notificationService.error($filter('translate')(err.message));
                    },
                );
            }

            function validateEmail(email) {
                return /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/.test(
                    email,
                );
            }

            function isFieldsFilled() {
                if (!$rootScope.emailToSend || $rootScope.emailToSend.trim().length === 0) {
                    notificationService.error($filter('translate')('Enter Email of recipient'));
                    $rootScope.emailToSendError = true;
                    return false;
                } else $rootScope.emailToSendError = false;

                if (!validateEmail($rootScope.emailToSend)) {
                    notificationService.error($filter('translate')('Wrong Email of recipient'));
                    $rootScope.emailToSendError = true;
                    return false;
                } else $rootScope.emailToSendError = false;

                if (
                    !$rootScope.emailTemplateInModal.title ||
                    $rootScope.emailTemplateInModal.title.trim().length === 0
                ) {
                    notificationService.error($filter('translate')('Enter the subject line'));
                    $rootScope.emailTemplateInModalSubjectError = true;
                    return false;
                } else $rootScope.emailTemplateInModalSubjectError = false;

                return true;
            }
        };

        $scope.changeTemplate = function (status) {
            return $rootScope.changeTemplateInAddCandidate.bind(this, status);
        };

        (function getPersonEmails() {
            Email.getMailboxes().then(
                (resp) => {
                    let permittedEmail = resp.filter((email) => email.personalMailing);
                    $rootScope.me.emails = permittedEmail;
                    $scope.personalMailingEmail = permittedEmail[0] ? permittedEmail[0].email : null;
                    Email.setDefaultMailbox($scope.personalMailingEmail);
                    if (!permittedEmail && resp.length) $scope.noAllowedMails = true;
                },
                (error) => notificationService.error(error),
            );
        })();

        $scope.deleteCandidateFromSystemModal = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/candidate-remove-from-system.html',
                size: '',
                scope: $scope,
                resolve: function () {},
            });
        };

        $scope.showCandidateSentEmail = function (emailType) {
            $rootScope.emailTemplateInModal.email = null;
            if (emailType === 'pd_consent') {
                if ($rootScope.isAccountTrial && !$rootScope.me.emails.length) {
                    notificationService.error($filter('translate')('block-autotests'));
                    return;
                }
                $scope.emailType = emailType;
                $rootScope.loading = true;
                PDConsent.getTemplate(
                    { lang: $scope.PDCSTmplLang },
                    (resp) => {
                        $rootScope.loading = false;
                        const filesIdName = resp.object.filesIdName;
                        $scope.fileGdprForSave = [];
                        if (filesIdName) {
                            const parse = JSON.parse(filesIdName);
                            for (const [key, value] of Object.entries(parse)) {
                                $scope.fileGdprForSave.push({ fileName: value, fileId: key });
                            }
                        }

                        $scope.curentSavedPDconsentTemplate.text = $scope.emailTemplate = resp.object.text;
                        $scope.curentSavedPDconsentTemplate.subject = $rootScope.emailTemplateInModal.title =
                            resp.object.subject;
                        setModal($scope.emailTemplate);
                    },
                    (error) => {
                        $rootScope.loading = false;
                        notificationService.error('Pdconsent get template request status:' + error.status);
                    },
                );

                PDConsent.getMailBox(
                    {},
                    (resp) => {
                        $rootScope.useSystemMailbox = resp.object.useUserMailbox;
                    },
                    (error) => {
                        $rootScope.loading = false;
                        notificationService.error(error.message);
                    },
                );
            } else {
                $scope.emailType = 'common';
                $rootScope.emailTemplateInModal.title = '';
                setModal();
            }

            function setModal(template) {
                $scope.onChangeTemplate = function () {
                    let newContent = $scope.curentSavedPDconsentTemplate.text;
                    if (!template) {
                        $rootScope.emailTemplateInModal.text = newContent;
                    } else {
                        if (newContent.indexOf('[allow_url]') === -1 || newContent.indexOf('[disallow_url]') === -1) {
                            $scope.curentSavedPDconsentTemplate.text = $scope.emailTemplate;
                            notificationService.error(
                                $filter('translate')(
                                    'You are able to change only the names of the buttons. Complete removal is impossible',
                                ),
                            );
                        } else {
                            $scope.emailTemplate = newContent;
                            $scope.PDCSTmpltChanged = true;
                        }
                    }
                    $rootScope.$$phase || $rootScope.$apply();
                };
                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/candidate-send-email.html',
                    windowClass: 'candidate-send-email',
                    size: '',
                    scope: $scope,
                    resolve: {},
                });
                $scope.modalInstance.closed.then(function () {
                    $rootScope.emailTemplateInModal = {
                        text: 'Hi [[candidate name]]!<br/>',
                    };
                });
                $scope.modalInstance.opened.then(function () {
                    setTimeout(function () {
                        setPdConsentTemplate(template);
                        $('.cke_button__insertcandidatename .cke_button__insertcandidatename_label').css(
                            'display',
                            'inline',
                        );
                        $('.cke_button__insertcandidatename .cke_button__insertcandidatename_icon').hide();
                    }, 100);
                });
                let isCandidateEmail = $scope.candidate.contacts.some((contact) => contact.type == 'email');
                $rootScope.candidateEmails = [];
                $rootScope.checkCandidateEmails = [];
                $rootScope.candidateModelEmail = {};

                if (isCandidateEmail) {
                    angular.forEach($scope.candidate.contacts, function (resp) {
                        if (resp.type == 'email') {
                            $rootScope.checkCandidateEmails.push(resp.value);
                            if (resp.default === true) {
                                $rootScope.emailToSend = resp.value;
                                $rootScope.candidateModelEmail = { value: resp.value, label: resp.value };
                            }
                            $rootScope.candidateEmails.push({ value: resp.value, label: resp.value });
                        }
                    });
                } else {
                    $rootScope.emailToSend = null;
                }

                if ($rootScope.emailTemplateInModal)
                    $rootScope.emailTemplateInModal.text = `${$translate.instant('hi')}, [[candidate name]]!<br/>`;
                if (Email.getDefaultMailbox()) {
                    $rootScope.emailTemplateInModal.email = Email.getDefaultMailbox().email;
                    Email.setDefaultSignature(Email.getDefaultMailbox());
                }
                if (!$rootScope.emailTemplateInModal.email && $rootScope.me.emails) {
                    $rootScope.me.emails.some((emailObj) => {
                        if (emailObj.permitSend) {
                            Email.setDefaultSignature(emailObj);
                            $rootScope.emailTemplateInModal.email = emailObj.email;
                            $rootScope.fromSendModel = emailObj;
                            return true;
                        }
                        return false;
                    });
                }
            }
        };

        $rootScope.handlerForCandidateEmail = function (value) {
            $rootScope.candidateModelEmail = value;
            $rootScope.emailToSend = value.label;
            $rootScope.$$phase || $rootScope.$apply();
        };

        $rootScope.changeCandidateEmailGdpr = (value) => {
            $rootScope.emailToSend = value;
            $rootScope.$$phase || $rootScope.$apply();
        };

        $rootScope.changeSubjectTittle = (value) => {
            $rootScope.emailTemplateInModal.title = value;

            if ($scope.emailType === 'pd_consent') {
                $scope.PDCSTmpltChanged = true;
            }

            $rootScope.$$phase || $rootScope.$apply();
        };

        $scope.deleteCandidateFromSystem = function () {
            Candidate.deleteCandidateFromSystem({
                candidateId: $scope.candidate.candidateId,
                comment: $rootScope.changeStateInCandidate.comment,
            }).then(
                (resp) => {
                    $scope.closeModal();
                    $rootScope.changeStateInCandidate.comment = '';
                    $location.path('/candidates');
                    notificationService.success(
                        $filter('translate')('Candidate name has been removed from the database', {
                            name: $rootScope.useAmericanNameStyle
                                ? $rootScope.changeStateInCandidate.candidate.fullNameEn
                                : $rootScope.changeStateInCandidate.candidate.fullName,
                        }),
                    );
                },
                (error) => {
                    if (error.object && error.object.limit === 0) {
                        notificationService.error(
                            $filter('translate')(
                                'deleteLimit.You_have_reached_the_daily_limit_for_removing_candidates_You_can_continue_tomorrow',
                            ),
                        );
                    } else if (error.message && error.message === 'blockedDeletingCandidates') {
                        $rootScope.showBlockDeletingCandidatesModal(error.object);
                    } else {
                        notificationService.error(error.message);
                    }
                    $scope.closeModal();
                    $rootScope.changeStateInCandidate.comment = '';
                },
            );
        };

        $scope.changePdConsentManual = function (status) {
            $rootScope.loading = true;
            PDConsent.changeConsentStatus(
                {
                    status: status,
                    userId: $rootScope.userId,
                    candidateId: $scope.candidate.candidateId,
                },
                (resp) => {
                    $scope.personalDataConsentStatus = status;
                    if ($scope.personalDataConsentStatus === 'user_confirmed')
                        $scope.pdcsConfirmer = Person.getPersonById($rootScope.persons, $rootScope.me.userId);
                    $rootScope.$emit('onUpdateCandidateHistory');
                    $rootScope.loading = false;
                    notificationService.success($filter('translate')('Personal Data Consent Status has been changed'));
                },
                (error) => {
                    $rootScope.loading = false;
                    notificationService.error('changeConsentStatus request response status:' + error.status);
                },
            );
        };

        $scope.initGdprFileAttache = function () {
            $scope.fileGdprForSave = [];
            FileInit.initVacancyTemplateFileOption($scope, '', '', false, $filter);

            $scope.callbackFileForTemplate = function (resp, names) {
                $scope.fileGdprForSave.push({ fileId: resp, fileName: names });
                $('#file').val('');
            };
        };

        $rootScope.removeFileGdpr = function (id) {
            $scope.fileGdprForSave = $scope.fileGdprForSave.filter((file) => file.fileId !== id);
        };

        $scope.sentPdConsentRequestEmail = function () {
            if (!$rootScope.emailToSend) {
                notificationService.error($filter('translate')('No email found'));
                return;
            }
            $scope.sendEmailFrom = null;
            $scope.gdprFiles = new Map();
            $scope.fileGdprForSave.forEach((file) => {
                $scope.gdprFiles.set(file.fileId, file.fileName);
            });
            if ($scope.gdprFiles.size === 0) {
                $scope.gdprFiles = null;
            } else {
                $scope.gdprFiles = JSON.stringify(Object.fromEntries($scope.gdprFiles.entries()));
            }

            if ($rootScope.useSystemMailbox && $rootScope.me.emails[0] && $rootScope.me.emails[0].email) {
                $scope.sendEmailFrom = $rootScope.me.emails[0].email;
            } else {
                $scope.sendEmailFrom = null;
            }

            if ($rootScope.emailTemplateInModal.title && $rootScope.emailTemplateInModal.title.trim().length > 0) {
                if ($scope.emailTemplate && $scope.emailTemplate.trim().length > 0) {
                    if (
                        $scope.emailTemplate.indexOf('[disallow_url]') !== -1 &&
                        $scope.emailTemplate.indexOf('[allow_url]') !== -1
                    ) {
                        $scope.emailTemplate = PDConsent.insertConsentTmplVars(
                            $scope.candidate.fullName,
                            $scope.curentSavedPDconsentTemplate.text,
                        );
                        $rootScope.loading = true;
                        PDConsent.sendRequestByEmail(
                            {
                                fromEmail: $scope.sendEmailFrom,
                                toEmails: $rootScope.emailToSend,
                                candidateId: $scope.candidate.candidateId,
                                filesIdName: $scope.gdprFiles,
                                template: { filesIdName: $scope.gdprFiles },
                                allowUrl: $location.$$protocol + '://' + $location.$$host + '/i/pd-consent-allow/',
                                disallowUrl: $location.$$protocol + '://' + $location.$$host + '/i/pd-consent-deny/',
                                subject: $rootScope.emailTemplateInModal.title,
                                text: $scope.emailTemplate,
                                lang: $scope.PDCSTmplLang,
                            },
                            (resp) => {
                                $rootScope.loading = false;
                                if (resp.status != 'ok') {
                                    notificationService.error(resp.message);
                                } else {
                                    $scope.modalInstance.close();
                                    $scope.fileGdprForSave = [];
                                    $scope.personalDataConsentStatus = 'email_sent';
                                    $rootScope.$emit('onUpdateCandidateHistory');
                                    notificationService.success(
                                        $filter('translate')('Email with the request for') +
                                            ' ' +
                                            $scope.candidate.fullName +
                                            ' ' +
                                            $filter('translate')('personal data processing has been sent'),
                                    );
                                }
                            },
                            (error) => {
                                $rootScope.loading = false;
                                notificationService.error('sendRequestByEmail request response status:' + error.status);
                            },
                        );
                    } else {
                        notificationService.error('Please not remove "Provide" & "Not provide" buttons');
                    }
                } else {
                    notificationService.error($filter('translate')('Enter the text of the letter'));
                }
            } else {
                notificationService.error($filter('translate')('Enter the subject line'));
            }
        };

        function updateHistory() {
            if ($scope.onlyComments) {
                $scope.showCommentsFirstTime();
            } else {
                $scope.showDetails();
            }
        }

        $scope.showDetails = function () {
            Service.history(
                {
                    page: { number: 0, count: 15 },
                    candidateId: $scope.candidate ? $scope.candidate.candidateId : null,
                    onlyWithComment: ($scope.onlyWithComment = false),
                    ignoreType: ['interview_add_from_advice_v1', 'candidate_email_add'],
                },
                function (res) {
                    var keepGoing = true;
                    angular.forEach($scope.history, function (val) {
                        if (keepGoing) {
                            if (
                                val.type === 'vacancy_message' ||
                                val.type === 'candidate_message' ||
                                val.type === 'interview_message' ||
                                val.type === 'client_message'
                            ) {
                                $scope.showHistoryForPrint = true;
                                keepGoing = false;
                            }
                        }
                    });
                    $scope.historyLimit = res.size;
                    $scope.historyTotal = res.total;
                    $scope.history = res.objects;
                    $scope.objectSize = res['objects'] ? res['total'] : 0;
                    $scope.onlyComments = false;
                    $('.showCommentSwitcher').prop('checked', !$scope.onlyComments);
                },
            );
        };

        function setPdConsentTemplate(template) {
            if (template) {
                if ($rootScope.useAmericanNameStyle) {
                    $scope.emailTemplate = template = template.replace(
                        /\[candidate_name\]/g,
                        $scope.candidate.firstName,
                    );
                } else {
                    $scope.emailTemplate = template = template.replace(
                        /\[candidate_name\]/g,
                        $scope.candidate.firstName,
                    );
                }
            } else {
                if ($rootScope.useAmericanNameStyle) {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[candidate name\]\]/g,
                        $scope.candidate.firstName,
                    );
                } else {
                    $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                        /\[\[candidate name\]\]/g,
                        $scope.candidate.firstName,
                    );
                }
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text
                    .replace(/\[\[recruiterr&#39;s name\]\]/g, $rootScope.me.fullName)
                    .replace(
                        /\[\[recruiter's name\]\]/g,
                        $rootScope.useAmericanNameStyle ? $rootScope.me.fullNameEn : $rootScope.me.fullName,
                    );
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\[\[vacancy name\]\]/g,
                    '',
                );
                $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace(
                    /\[\[interview date and time\]\]/g,
                    '',
                );
                // $rootScope.emailTemplateInModal.text = $rootScope.emailTemplateInModal.text.replace("You scheduled for an interview on vacancy", "").replace("at", "").replace("// See you in (skype / google hangouts etc).", "").replace("// We'll wait for you at our office: (write company address, floor, office number)", "").replace("// Please see how to find our office on the map in the attached file.", "");
            }
        }

        function setPdConsentStatus(consentStatus) {
            let consentObject = consentStatus ? consentStatus : {};
            $scope.personalDataConsentStatus = consentObject.status;
            if ($scope.personalDataConsentStatus === 'user_confirmed')
                $scope.pdcsConfirmer = Person.getPersonById($rootScope.persons, consentObject.userId);
        }

        $scope.toSentPreview = function (mailing) {
            Mailing.showSentCompaignById(mailing);
        };

        function keyBoardActionWithCtrlEnterButtons(event) {
            return event.ctrlKey && (event.code === 'Enter' || event.originalEvent.code === 'Enter');
        }

        function changePDCSTemplateLang(switchedLang) {
            $scope.PDCSTmplLang = switchedLang;
            $rootScope.loading = true;
            PDConsent.getTemplate(
                { lang: switchedLang },
                (resp) => {
                    $rootScope.loading = false;
                    $scope.curentSavedPDconsentTemplate.text = $scope.emailTemplate = resp.object.text;
                    $scope.curentSavedPDconsentTemplate.subject = $rootScope.emailTemplateInModal.title =
                        resp.object.subject;
                    // tinyMCE.get('modalMCE').setContent(resp.object.text.replace(/\[candidate_name\]/g, $scope.candidate.fullName));
                },
                (error) => {
                    $rootScope.loading = false;
                    notificationService.error('Pdconsent get template request status:' + error.status);
                },
            );
        }

        $rootScope.saveVacancyStatus = function () {
            Vacancy.changeState(
                {
                    vacancyId: $rootScope.changedStatusVacancy.vacancyId,
                    comment: $rootScope.changeStateObject.comment ? $rootScope.changeStateObject.comment : null,
                    vacancyState: 'completed',
                },
                function (resp) {
                    if (resp.status == 'ok') {
                        $rootScope.changeStateObject.comment = '';
                        $rootScope.changeStateObject.status = null;
                        notificationService.success($filter('translate')('vacancy change status'));
                        $scope.closeModalChangeStatusOfVacancy();
                        $rootScope.$emit('onUpdateCandidateHistory');
                        //Service.showCongratulationsHandler(resp.object.dc, $scope.hideCongratulation);
                        //resetHideCongratulationValue();
                    } else if (resp.message) {
                        notificationService.error(resp.message);
                    }
                },
                function (err) {
                    notificationService.error($filter('translate')('service temporarily unvailable'));
                },
            );
        };

        function resetHideCongratulationValue() {
            $scope.hideCongratulation = true;
        }

        function initModalRemainder(changeObj) {
            let modalConfig = {
                animation: true,
                templateUrl: '../partials/modal/vacancy_close_reminder.html',
                size: '',
                scope: $scope,
                backdrop: 'static',
            };

            if (changeObj.status.value === 'approved') {
                $scope.candidateData = {
                    name: changeObj.candidate.candidateId.fullName || changeObj[0].candidate.candidateId.fullName,
                };
            }

            $scope.modalReminderCloseTheVacancy = $uibModal.open(modalConfig);
            $scope.currentCloseVacancy = $rootScope.changeStatusOfInterviewInVacancy.position;

            $scope.modalReminderCloseTheVacancy.opened.then((arguments) => {
                document.querySelector('body').classList.add('modal-close-vacancies');
            });

            $scope.modalReminderCloseTheVacancy.closed.then((arguments) => {
                document.querySelector('body').classList.remove('modal-close-vacancies');
            });
        }

        $scope.showChangeStatusOfVacancyModal = function () {
            $rootScope.modalChangeStatusVacancy = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/vacancy-change-status.html?111',
                size: '',
            });

            $scope.modalChangeStatusVacancy.opened.then((arguments) => {
                $scope.closeModalReminder();
            });
        };

        $rootScope.showChangeStatusOfVacancy = function (status) {
            $rootScope.isModalRemainderOpen = true;
            $rootScope.changeStateObject = {
                status: 'completed',
                status_old: $rootScope.changedStatusVacancy.status,
            };
            Vacancy.onGetCounts({
                vacancyId: $rootScope.changedStatusVacancy
                    ? $rootScope.changedStatusVacancy.vacancyId
                    : $rootScope.VacancyAddedInCandidate.vacancyId,
            }).then(
                (resp) => {
                    $scope.hideCongratulation = resp.objects.length === 1 && resp.objects[0].item === 'approved';
                    $scope.showChangeStatusOfVacancyModal();
                },
                (err) => console.error(err),
            );
        };

        $scope.closeModalReminder = function () {
            $scope.modalReminderCloseTheVacancy.close();
        };

        $rootScope.closeModalChangeStatusOfVacancy = function () {
            $rootScope.modalChangeStatusVacancy.close();
        };

        $scope.getTestById = function (id) {
            return $scope.allTests.filter((test) => {
                return test.test.id === id;
            })[0];
        };

        function getCandidateContact(type) {
            return $scope.candidate.contacts.filter(
                (contact) => contact.type === type && contact.value && contact.value.length,
            )[0];
        }

        $scope.goToTags = () => {
            $location.path('/company/tags');
        };
        $scope.clickInRegionMap = function () {
            if ($scope.candidate.region) {
                $scope.map = {
                    center: {
                        latitude: $scope.candidate.region.lat,
                        longitude: $scope.candidate.region.lng,
                    },
                    zoom: 1000,
                };

                $scope.options = { scrollwheel: false };
            }
            $scope.showRegion2Map = !$scope.showRegion2Map;

            // $('body').mouseup((e) => {
            //     let element = $(".clever-window");
            //     if (!element.is(e.target) && element.has(e.target).length === 0) {
            //         vm.showRegion2Map = false;
            //         $(document).off('mouseup');
            //     }
            // });
        };
        $scope.inviteToVCV = function () {
            if (!$scope.vcv.vacancy || !$scope.vcv.vacancy.vacancyId) {
                notificationService.error($filter('translate')('Select a vacancy'));
                return;
            }
            $rootScope.loading = true;
        };

        $scope.allListDisplayToogle = function () {
            $scope.allListDisplayed = !$scope.allListDisplayed;
        };

        $scope.goToTestsPage = function () {
            $location.path('/candidate/tests');
        };
        $scope.setParticipationInVacancies = function () {
            Candidate.onParticipationInVacancies({
                candidateId: $scope.candidate.candidateId,
            }).then((resp) => {
                if (resp.status === 'ok') {
                    if (resp.objects.length) $scope.vacanciesOfCandidate = [...resp.objects];
                    else $scope.vacanciesOfCandidate = [];
                }
            });
        };
        $scope.updateCandidateLinksNew = function () {
            if ($scope.candidateProperties.candidateLinks !== undefined) {
                $scope.linkedCandidate = $scope.candidateProperties.candidateLinks;
                angular.forEach($scope.linkedCandidate, function (res) {
                    $scope.linkedOneCandidate = res;
                    if (
                        res.resourseType == 'linkedin' ||
                        res.resourseType == 'superJob' ||
                        res.resourseType == 'workua' ||
                        res.resourseType == 'trelloCardId' ||
                        res.resourseType == 'robotaua' ||
                        res.resourseType == 'recruforce' ||
                        res.resourseType == 'hh' ||
                        res.resourseType == 'zoho_id' ||
                        res.resourseType == 'firebird_id' ||
                        res.resourseType == 'linkedin_d' ||
                        res.resourseType == 'cvlv' ||
                        res.resourseType == 'estaffId'
                    ) {
                        $scope.linked = true;
                    } else if (res.resourseType == 'linkedinNew') {
                        res.resourseType = 'linkedinNew' ? 'linkedin' : 'linkedin';
                        $scope.linked = true;
                    }
                });
            }
        };

        function onSanitizeCandidateFields(candidate) {
            if (!candidate) return;

            if (candidate.descr) candidate.descr = Service.sanitizeStringFromXSS(candidate.descr);
            if (candidate.educationList)
                candidate.educationList.forEach(
                    (educationItem) =>
                        (educationItem.description = Service.sanitizeStringFromXSS(educationItem.description)),
                );
            if (candidate.workExperiences)
                candidate.workExperiences.forEach(
                    (workExperience) =>
                        (workExperience.description = Service.sanitizeStringFromXSS(workExperience.description)),
                );
            if (candidate.customFields)
                candidate.customFields.forEach((customField) => {
                    if (customField.type === 'string' && customField.fieldValue) {
                        customField.fieldValue.value = Service.sanitizeStringFromXSS(customField.fieldValue.value);
                    }
                });
        }

        function updateCandidateProperties() {
            Candidate.onGetCandidateProperties({
                localId: $stateParams.id,
            }).then(
                (props) => setCandidatesProperties(props),
                (error) => console.error(error.message),
            );
        }

        function getCandidatesProperties() {
            Promise.all([
                Candidate.onGetCandidateProperties({
                    localId: $stateParams.id.split('&')[0],
                }),
                vacancyStages.requestVacancyStages(),
            ]).then(
                ([props, stages]) => {
                    $scope.jobCannonLink = props.object.candidateExternalIntegration?.externalUrl;
                    $scope.isHotStatusCandidate = Boolean(
                        $scope.candidate.isOpenToWork ||
                            props.object.candidateExternalIntegration?.isOpenToWork ||
                            props.object.candidateExternalIntegration?.isSelfRegistered ||
                            new Date() - 1209600000 <= props.object.candidateExternalIntegration?.lastUpdatedDate, // 14 days limit
                    );

                    setCustomStages(stages.object);
                    setCandidatesProperties(props);
                },
                (error) => {
                    console.error(error.message);
                },
            );
        }

        $rootScope.getCandidatesPropertiesForLang = function () {
            Promise.all([
                Candidate.onGetCandidateProperties({
                    localId: $stateParams.id.split('&')[0],
                }),
                vacancyStages.requestVacancyStages(),
            ]).then(
                ([props, stages]) => {
                    setCustomStages(stages.object);
                    setCandidatesProperties(props);
                },
                (error) => {
                    console.error(error.message);
                },
            );
        };

        function setCandidatesProperties(res) {
            $rootScope.candidateInterviews = res.object.interviews;
            $scope.candidateProperties = res.object;
            $scope.inactiveVacancies = [];

            $timeout(() => {
                $scope.candidate.recalls = res.object?.replies;

                if ($scope.candidate.recalls.length) {
                    $scope.candidate.recalls?.forEach((item) => {
                        let counterLength = 0;
                        item.fields = JSON.parse(item.additionalInfo);
                        if (!item.fields.custom) item.fields.custom = [];
                        if (item.fields && item.fields.custom) {
                            item.fields.custom.forEach((custom) => {
                                if (custom.type === 'textFile' || custom.type === 'selectFile') {
                                    if (!custom.value && !custom.files) {
                                        counterLength = +counterLength + 1;
                                    }
                                } else if (custom.type === 'file') {
                                    if (!custom.files) {
                                        counterLength = +counterLength + 1;
                                    }
                                } else {
                                    if (!custom.value) {
                                        counterLength = +counterLength + 1;
                                    }
                                }
                            });
                            item.dontShow = counterLength === item.fields.custom.length;
                        }
                    });
                }
                $scope.candidate.recalls?.forEach((vacancy) => {
                    vacancy.vacancy = $scope.candidateProperties.interviews.find(
                        (item) => item.vacancy === vacancy.vacancyId,
                    );
                });

                if ($scope.candidate.recalls.length === 1 && !$scope.candidate.recalls[0].dontShow) {
                    $scope.candidate.recalls[0].showRecall = true;
                }

                $scope.candidate.recalls?.forEach((item) => {
                    let saveCount = 0;
                    if (item.dontShow) {
                        saveCount += 1;
                    }
                    $rootScope.dontShowRecall = saveCount === $scope.candidate.recalls.length;
                });
            }, 0);

            $scope.oldResponsibleUserInCandidate = $scope.candidateProperties.responsible || null;

            setBreeadcrumbsStage($scope.candidateProperties.interviews);
            cascadeStages();
            setPdConsentStatus($scope.candidateProperties.pcsData);

            if ($scope.candidateProperties.files) {
                if (
                    $scope.candidateProperties.files.length != undefined &&
                    $scope.candidateProperties.files.length != 0
                ) {
                    angular.forEach($scope.candidateProperties.files, function (val) {
                        if (val.url) {
                            $scope.showAddedLinks = true;
                        }
                        if (!val.url) {
                            $scope.showAddedFiles = true;
                        }
                        val.fileResolution = Service.getFileResolutionFromName(val.fileName);
                        Service.initDocuments(val);
                    });
                }
            } else {
                $scope.showAddedLinks = false;
                $scope.showAddedFiles = false;
            }

            if ($scope.candidateProperties.interviews !== undefined) {
                angular.forEach($scope.candidateProperties.interviews, function (item) {
                    if (item.vacancyId.interviewStatus === undefined) {
                        item.vacancyId.interviewStatus = 'longlist,shortlist,interview,approved,notafit,declinedoffer';
                    }
                });
            }

            angular.forEach($scope.candidateProperties.interviews, function (value) {
                const refuseTypes = [
                    'notafit',
                    'declinedoffer',
                    'is_not_looking_for_job',
                    'no_response',
                    'no_contacts',
                    'accepted_counter_offer',
                    'found_another_job',
                    'offer_declined',
                    'probation_failure',
                ];

                value.vacancyId.interviewStatusNotTouchable = value.vacancyId.interviewStatus;
                const array = value.vacancyId.interviewStatus ? value.vacancyId.interviewStatus.split(',') : [];
                value.stateOptions = [];

                array.forEach((state) => {
                    if (state !== 'applied_people') {
                        value.stateOptions.push({ value: state, label: state });
                    }
                });

                angular.forEach($scope.customStages, function (resp) {
                    if (value.state == resp.customInterviewStateId) {
                        value.state = resp.name;
                        value.customInterviewStateId = resp.customInterviewStateId;
                    }
                    angular.forEach(array, function (res) {
                        if (resp.customInterviewStateId == res) {
                            array[array.indexOf(resp.customInterviewStateId)] = resp.name;
                        }
                    });

                    value.stateOptions.forEach((state) => {
                        if (state.value && state.value === resp.customInterviewStateId) {
                            state.itsCustom = true;
                            state.value = resp.name;
                            state.label = resp.name;
                            state.name = resp.name;
                            state.type = resp.type;
                            state.customInterviewStateId = resp.customInterviewStateId;
                        }

                        if (state.value && refuseTypes.includes(state.value)) {
                            state.type = 'refuse';
                        }
                    });
                });

                value.stateOptions.forEach((stage) => {
                    if (stage.value === 'approved') stage.color = '#77B472';
                    if (stage.type === 'refuse') stage.color = '#d67354';

                    if (value.state === 'applied_people') {
                        value.stateModel = { value: 'applied_people', label: 'applied_people' };
                    }

                    if (value.state === stage.value) {
                        value.stateModel = { value: stage.value, label: stage.value, color: stage.color };
                    }
                });
                value.vacancyId.interviewStatus = array;

                if (
                    value.vacancyId.status != 'completed' &&
                    value.vacancyId.status != 'deleted' &&
                    value.vacancyId.status != 'canceled' &&
                    value.vacancyId.status != 'recommendation'
                ) {
                    $scope.participationInVacancy = true;
                    value.inactive = false;
                } else {
                    $rootScope.inactiveVacancies = true;
                    value.inactive = true;
                }

                if (value.inactive) {
                    $scope.inactiveVacancies.push(value);
                }
            });

            $rootScope.$$phase || $rootScope.$apply();

            if (
                $rootScope.me.recrutRole == 'admin' ||
                $rootScope.me.recrutRole == 'recruter' ||
                $rootScope.me.recrutRole == 'client' ||
                $rootScope.me.recrutRole == 'researcher' ||
                $rootScope.me.recrutRole == 'freelancer'
            ) {
                if ($scope.candidateProperties.testAppointmentContents !== undefined) {
                    $scope.totalTestsCount = $scope.candidateProperties.testTestTotaElementsl;
                    $scope.tests = [];
                    for (var iter = 0; iter < $scope.candidateProperties.testAppointmentContents.length; iter++) {
                        $scope.tests[iter] = $scope.candidateProperties.testAppointmentContents[iter];
                    }
                } else if (angular.equals(resp.status, 'error')) {
                    notificationService.error(resp.message);
                }
            }

            $scope.updateCandidateLinksNew();

            $scope.showModalEditTaskToCandidate = function (task) {
                const taskText = task.text;
                $scope.modalEditTaskToCandidateOpened = false;
                $location.$$absUrl = $location.$$absUrl.split('&')[0];
                let newUrl = $location.$$absUrl.split('?')[0];
                $scope.urlDescription = $location.$$absUrl.split('?')[1] || $scope.urlDescription;

                $location.$$absUrl = newUrl + '&task=' + task.taskId;
                $rootScope.responsiblePersons = [];
                $rootScope.responsiblePersonsEdit = [];
                $rootScope.historyComment = [];
                $rootScope.historyAction = [];
                $rootScope.editableTask = task;
                $rootScope.showNewText = false;
                $scope.standartLength = $rootScope.editableTask.text;
                $rootScope.editedTaskText = function (text) {
                    if ($scope.standartLength != text && $rootScope.editableTask.text.length > 0) {
                        $rootScope.showNewText = true;
                    } else {
                        $rootScope.showNewText = false;
                    }
                };
                angular.forEach($rootScope.persons, function (res) {
                    res.notShown = false;
                });
                angular.forEach($rootScope.editableTask.responsiblesPerson, function (resp) {
                    angular.forEach($rootScope.persons, function (res) {
                        if (resp.responsible.userId == res.userId) {
                            $rootScope.responsiblePersons.push(res);
                            res.notShown = true;
                            //$rootScope.persons.splice($rootScope.persons.indexOf(res), 1);
                        }
                    });
                });

                $scope.modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/edit-task.html',
                    size: '',
                    scope: $scope,
                    resolve: function () {},
                });
                $scope.modalInstance.closed.then(function () {
                    $scope.modalEditTaskToCandidateOpened = true;
                    if ($rootScope.showNewText) {
                        $rootScope.editableTask.text = taskText;
                    }
                    if ($rootScope.addComment) {
                        $rootScope.addComment.comment = '';
                    }
                    $rootScope.responsiblePersons = [];
                    if ($scope.urlDescription) {
                        if ($location.$$absUrl.indexOf('&task=') == -1) {
                            $location.$$absUrl += '?' + $scope.urlDescription;
                        } else {
                            $location.$$absUrl = $location.$$absUrl.split('&task=')[0] + '?' + $scope.urlDescription;
                        }
                    }
                });
                $rootScope.getMoreHistory = function (number) {
                    Task.getHistory(
                        {
                            taskId: task.taskId,
                            page: {
                                number: 0,
                                count: $rootScope.historyLimit + number,
                            },
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                $rootScope.historyLimit = resp.objects !== undefined ? resp.size : null;
                                $rootScope.historyTotal = resp.objects !== undefined ? resp.total : null;
                                if ($rootScope.historyTotal < $rootScope.historyLimit) {
                                    $('.more-history').css('display', 'none');
                                }
                                $rootScope.historyTask = resp.objects;
                            } else {
                                notificationService.error(resp.message);
                            }
                        },
                    );
                };
                $rootScope.getHistoryForComment = function () {
                    Task.getHistory(
                        {
                            taskId: task.taskId,
                            type: 'comment',
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                $rootScope.historyComment = [];
                                $('.taskSwitcher').prop('checked', true);
                                $rootScope.historyTask = resp.objects;
                                angular.forEach($rootScope.historyTask, function (resp) {
                                    if (resp.type == 'comment') {
                                        $rootScope.historyComment.push(resp);
                                    }
                                });
                            } else {
                                notificationService.error(resp.message);
                            }
                        },
                    );
                };
                $rootScope.getHistoryForAllActions = function () {
                    Task.getHistory(
                        {
                            taskId: task.taskId,
                            page: { number: 0, count: $rootScope.historyLimit },
                        },
                        function (resp) {
                            if (resp.status == 'ok') {
                                $('.taskSwitcher').prop('checked', false);
                                $rootScope.historyLimit = resp.objects !== undefined ? resp.size : null;
                                $rootScope.historyTotal = resp.objects !== undefined ? resp.total : null;
                                $rootScope.historyTask = resp.objects;
                            } else {
                                notificationService.error(resp.message);
                            }
                        },
                    );
                };
                $rootScope.getHistoryForTask = function (val) {
                    if (val == 'all actions') {
                        $rootScope.showAllActions = true;
                        $('.taskSwitcher').prop('checked', true);
                        $rootScope.getHistoryForAllActions();
                    } else if (val == 'comment') {
                        $rootScope.getHistoryForComment();
                        $rootScope.showAllActions = false;
                        $('.taskSwitcher').prop('checked', false);
                    } else if (val == 'switch') {
                        $rootScope.showAllActions = !$rootScope.showAllActions;
                        if ($rootScope.showAllActions == false) {
                            $rootScope.getHistoryForComment();
                            $('.taskSwitcher').prop('checked', true);
                        } else {
                            $rootScope.getHistoryForAllActions();
                            $('.taskSwitcher').prop('checked', false);
                        }
                    }
                };
                if ($rootScope.getHistoryForTask) $rootScope.getHistoryForTask('all actions');
                $scope.modalInstance.result.then(
                    function (selectedItem) {
                        //$scope.selected = selectedItem;
                    },
                    function () {
                        $location.$$absUrl = $location.$$absUrl.split('&')[0];
                    },
                );
            };

            $scope.candidateTasks = $scope.candidateProperties.taskContents;
            $rootScope.candidateTasks = $scope.candidateTasks;

            const personIds = $rootScope.candidateTasks.reduce(
                (arr, curr) => {
                    return curr.responsibleIds.length > arr.responsibleIds.length ? curr : arr;
                },
                { responsibleIds: [] },
            );

            $rootScope.personsLength = personIds.responsibleIds.length.toString();

            $rootScope.candidateTasks.forEach((val, idx) => {
                if (val.status === 'open') val.status = 'inwork';
                val.status = {
                    status: 'tasksStatuses.' + $scope.candidateTasks[idx].status,
                    color: val.status === 'inwork' ? '#77b472' : '#eaa24d',
                    value: 'tasksStatuses.' + $scope.candidateTasks[idx].status,
                };
                val.editableTaskOptions = [
                    {
                        value: 'inwork',
                        name: 'inwork',
                        taskId: $scope.candidateTasks[idx].taskId,
                        status: 'inwork',
                        sendStatus: 'open',
                        color: '#77B472',
                    },
                    {
                        value: 'completed',
                        name: 'completed',
                        taskId: $scope.candidateTasks[idx].taskId,
                        status: 'completed',
                        sendStatus: 'completed',
                        color: '#eaa24d',
                    },
                    {
                        value: 'Cancelled',
                        name: 'Cancelled',
                        taskId: $scope.candidateTasks[idx].taskId,
                        status: 'Cancelled',
                        sendStatus: 'Cancelled',
                        color: '#eaa24d',
                    },
                ];

                val.statusModel = {
                    status: val.status,
                    color: val.status === 'tasksStatuses.inwork' ? '#77B472' : '#eaa24d',
                };
            });

            if ($scope.urlTaskId) {
                $rootScope.responsiblePersonsEdit = [];
                angular.forEach($scope.candidateTasks, function (resp) {
                    if (resp.taskId == $scope.urlTaskId) {
                        $rootScope
                            .waitForCondition(200, 100, () => $rootScope.persons)
                            .then(() => {
                                $rootScope.editableTask = resp;
                                $location.$$absUrl = $location.$$absUrl.split('?')[0];
                                $state.go('candidate.tasks', { candidateObj: $scope.candidate });
                                $timeout(() => $scope.$broadcast('openTaskFromEmailRedirect', $rootScope.editableTask));
                            });
                    }
                });
                if ($rootScope.editableTask && $location.$$absUrl.indexOf('&task=') == -1) {
                    $location.$$absUrl = $location.$$absUrl + '&task=' + $scope.urlTaskId;
                    angular.forEach($rootScope.editableTask.responsiblesPerson, function (resp) {
                        angular.forEach($rootScope.persons, function (res) {
                            if (resp.responsible.userId == res.userId) {
                                $rootScope.responsiblePersonsEdit.push(res);
                                res.notShown = true;
                            }
                        });
                    });
                    $('.editTaskInCandidate')
                        .modal('setting', {
                            onHide: function () {
                                $scope.urlTaskId = null;
                                $location.$$absUrl = $location.$$absUrl.split('&')[0];
                                // $scope.$apply();
                            },
                        })
                        .modal('show');
                }
            }
            $scope.$$phase || $scope.$apply();
        }

        function setStatusPrevValue() {
            $scope.statusModel = {
                value: $rootScope.changeStateInCandidate.status_old,
                name: `candidate_status_assoc.${$rootScope.changeStateInCandidate.status_old}`,
            };
        }

        function setIUser() {
            let iUser = null;
            for (let i = 0; i <= $scope.persons.length - 1; i++) {
                if ($rootScope.me.userId == $scope.persons[i].userId) {
                    iUser = $scope.persons[i];
                    $scope.persons.splice(i, 1);
                    break;
                }
            }

            if (iUser) {
                $scope.persons.unshift(iUser);
            }
        }

        function setCustomStages(stages) {
            $scope.customStages = stages.interviewStates;
            $rootScope.customStages = stages.interviewStates;
        }

        function initHistoryTable() {
            $scope.tableParamsCandidateHistory = new ngTableParams(
                {
                    page: 1,
                    count: 15,
                },
                {
                    total: 0,
                    getData: function ($defer, params) {
                        let pageNumber = 0;
                        let pageCount = 15;

                        function getHistory(page, count) {
                            if (page || count) {
                                pageNumber = page;
                                pageCount = count;
                                $scope.candidateSearchOptions.page.number = page;
                                $scope.candidateSearchOptions.page.count = count;
                                $scope.candidateSearchOptions = {
                                    candidateId: $scope.candidate !== undefined ? $scope.candidate.candidateId : null,
                                    page: {
                                        number: pageNumber,
                                        count: pageCount,
                                    },
                                    onlyWithComment: true,
                                    ignoreType: ['interview_add_from_advice_v1', 'candidate_email_add'],
                                };
                            } else {
                                pageNumber = params.$params.page;
                                pageCount = params.$params.count;
                                $scope.candidateSearchOptions = {
                                    candidateId: $scope.candidate !== undefined ? $scope.candidate.candidateId : null,
                                    page: {
                                        number: pageNumber - 1,
                                        count: pageCount,
                                    },
                                    onlyWithComment: true,
                                    ignoreType: ['interview_add_from_advice_v1', 'candidate_email_add'],
                                };
                                $scope.isShowMore = false;
                                if (document.getElementById('scrollup'))
                                    document.getElementById('scrollup').style.display = 'none';
                                if (pageNumber > 1) {
                                    $timeout(function () {
                                        $anchorScroll('mainTable');
                                    });
                                }
                            }

                            Service.history($scope.candidateSearchOptions, function (response) {
                                if (page) {
                                    $scope.history = $scope.history.concat(response['objects']);
                                } else {
                                    $scope.history = response['objects'];
                                }
                                $scope.history.forEach((item) => {
                                    if (
                                        item.type === 'candidate_merge_group' ||
                                        item.type === 'candidate_edit_group' ||
                                        item.type === 'merge_group'
                                    )
                                        item.descr = JSON.parse(item.descr);
                                });
                                var array = [];
                                angular.forEach($scope.history, function (value) {
                                    if (value.stateNew && value.type == 'set_interview_status') {
                                        array = value.stateNew.split(',');
                                        angular.forEach($scope.customStages, function (val) {
                                            angular.forEach(array, function (resp) {
                                                if (val.customInterviewStateId == resp) {
                                                    array[array.indexOf(val.customInterviewStateId)] = val.name;
                                                }
                                            });
                                        });
                                        value.stateNew = array.toString();
                                    }
                                });
                                $scope.objectSize = response['objects'] ? response['total'] : 0;
                                $scope.showHistory = response.objects != undefined;
                                params.total(response['total']);
                                $scope.paginationParams = {
                                    currentPage: $scope.candidateSearchOptions.page.number,
                                    totalCount: $scope.objectSize,
                                };
                                $scope.pagesCount = Math.ceil(
                                    response['total'] / $scope.candidateSearchOptions.page.count,
                                );
                                $defer.resolve($scope.history);
                                $rootScope.loading = false;
                                $scope.displayShowMore =
                                    $scope.candidateSearchOptions.page.number <
                                    params.total() / $scope.candidateSearchOptions.page.count - 1;
                            });
                        }

                        getHistory();
                        $scope.showMore = function () {
                            $scope.isShowMore = true;
                            $scope.displayShowMore = Service.dynamicTableLoading(
                                params.total(),
                                $scope.candidateSearchOptions.page.number,
                                $scope.candidateSearchOptions.page.count,
                                getHistory,
                            );
                        };
                    },
                },
            );
        }

        function getTests() {
            Test.getTests(
                function (resp) {
                    if (resp.status == 'ok') {
                        $scope.allTests = resp.objects;
                    } else {
                        notificationService.error(resp.message);
                    }
                },
                function (err) {
                    notificationService.error(err.message);
                },
            );
        }

        if ($rootScope.me.recrutRole === 'admin' || $rootScope.me.recrutRole === 'recruter') getTests();

        $scope.validationOfEmailField = function (email) {
            if (email.value.length > 0) {
                const re =
                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                email.isError = !re.test(String(email.value).toLowerCase());
                if (email.value.length > 50) {
                    email.isError = true;
                    email.value = email.value.slice(0, 50);
                }
            } else {
                email.isError = false;
            }
        };
        $scope.validationOfContactField = function (value, field) {
            if (field.type === 'mphone') {
                if (value && value.length > 20) {
                    field.isError = true;
                    field.value = value.slice(0, 20);
                } else {
                    field.isError = false;
                    field.value = value;
                }
            }
            if (field.type === 'facebook' || field.type === 'linkedin' || field.type === 'djinni') {
                if (value && value.length >= 2048) {
                    field.isError = true;
                    field.value = value.slice(0, 2048);
                } else {
                    field.isError = false;
                    field.value = value;
                }
            }
            if (
                field.type === 'other' ||
                field.type === 'github' ||
                field.type === 'behance' ||
                field.type === 'homepage'
            ) {
                if (value && value.length >= 2048) {
                    field.isError = true;
                    field.value = value.slice(0, 2048);
                } else {
                    field.isError = false;
                    field.value = value;
                }
            }
            if (field.type === 'email') {
                if (value && value.length > 50) {
                    field.isError = true;
                    field.toMuchCharacters = true;
                    field.value = value.slice(0, 50);
                } else {
                    field.toMuchCharacters = false;
                    field.value = value;
                }
            }

            if (field.type === 'whatsApp' || field.type === 'viber') {
                if (value && value.length > 50) {
                    field.isError = true;
                    field.value = value.slice(0, 50).replace(/[^+0-9]/g, '');
                } else {
                    field.isError = false;
                    field.value = value.replace(/[^+0-9]/g, '');
                }
            }

            if (field.type === 'telegram' || field.type === 'skype') {
                if (value && value.length > 50) {
                    field.isError = true;
                    field.value = value.slice(0, 50);
                } else {
                    field.isError = false;
                    field.value = value;
                }
            }

            if (!value || (value && value.length > 5)) {
                const changes = debounce(() => $scope.checkDuplicatesByNameAndContacts());
                changes();
            }

            $rootScope.$$phase || $rootScope.$apply();
        };

        function debounce(func, timeout = 300) {
            let timer;
            return (...args) => {
                clearTimeout(timer);
                timer = setTimeout(() => {
                    func.apply(this, args);
                }, timeout);
            };
        }

        $scope.clearEmailValidationError = function (email) {
            email.isError = false;
        };
        $scope.validateCandidateName = (name, type) => {
            if (type === 'currentWorkPlace' || type === 'currentPosition') {
                if (name.length >= 100) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return Candidate.validatePosition(name);
            } else {
                if (name.length >= 50) $scope.errorFields[type] = true;
                else $scope.errorFields[type] = false;
                return Candidate.validateName(name);
            }
        };
        $scope.removeInputField = (param, contacts, index) => contacts[param].splice(index, 1);
        $scope.selectFavoriteContacts = function ($scope, event, contact, index) {
            Candidate.selectFavoriteContacts($scope, event, contact, index);
        };
        $scope.addContact = (opt) => {
            if (opt.value === 'E-mail') opt.value = 'email';
            $scope.addInputField(opt.value, $scope.contacts);
            $scope.contactTypesModel = { value: '', label: '' };

            $rootScope.$$phase || $rootScope.$apply();
        };

        $scope.contactTypes = [
            { value: 'mphone', pathToIcon: 'phone' },
            { value: 'email', pathToIcon: 'mail' },
            { value: 'linkedin', pathToIcon: 'linkedin' },
            { value: 'facebook', pathToIcon: 'facebook' },
            { value: 'telegram', pathToIcon: 'telegram' },
            { value: 'whatsApp', pathToIcon: 'whatsApp' },
            { value: 'viber', pathToIcon: 'viber' },
            { value: 'skype', pathToIcon: 'skype' },
            { value: 'github', pathToIcon: 'github' },
            { value: 'behance', pathToIcon: 'behance' },
            { value: 'homepage', pathToIcon: 'homepage' },
            { value: 'djinni', pathToIcon: 'djinni' },
            { value: 'other', pathToIcon: 'other' },
        ].map((item) => ({ ...item, pathToIcon: '/images/redesign/candidate/' + item.pathToIcon + '.svg' }));

        $scope.contactTypesModel = { value: '', label: '' };
        $scope.addInputField = (param, contacts) => {
            contacts[param].push({ value: '', type: param });
            Object.keys(contacts).forEach((item) => {
                contacts[item].isLast = false;
            });
            contacts[param].isLast = true;
        };

        $scope.changeLinkName = (value, file) => {
            file.fileName = value;

            $rootScope.$$phase || $rootScope.$apply();
        };

        $scope.contactsLength = function (contact) {
            let contactIsExist = false;
            contact.forEach((item) => {
                if (item && item.value) contactIsExist = true;
            });
            return contactIsExist;
        };
        $scope.onShowEditContacts = function () {
            $scope.showEditContacts = true;
            $('.first-navbar').css({ 'pointer-events': 'none' });
            $('.second-navbar').css({ 'pointer-events': 'none' });
            $('.second-navbar').css({ opacity: '.6' });
            $('.first-navbar').css({ opacity: '.6' });
            $('#navbar-bgc').css({ 'background-color': 'transparent' });
            $scope.checkDuplicatesByNameAndContacts();
            $rootScope.$$phase || $rootScope.$apply();
        };
        $scope.unblurPage = function () {
            $scope.showEditContacts = false;
            $('.first-navbar').css({ 'pointer-events': 'auto' });
            $('.second-navbar').css({ 'pointer-events': 'auto' });
            $('.second-navbar').css({ opacity: '1' });
            $('.first-navbar').css({ opacity: '1' });
            $('#navbar-bgc').css({ 'background-color': '#F8FAFB' });
            setContacts();
            $rootScope.$$phase || $scope.$apply();
        };
        $scope.onSaveContacts = function () {
            let contacts = [];
            let isErrorField = false;
            Object.keys($scope.contacts).forEach((contact) => {
                $scope.contacts[contact].forEach((item) => {
                    if (item.isError) {
                        isErrorField = true;
                    }
                    if (item.value) {
                        contacts.push(item);
                    }
                });
            });

            if (!isErrorField) {
                $rootScope.loading = true;
                Candidate.onSaveContacts({
                    candidateId: $scope.candidate.candidateId,
                    contacts,
                })
                    .then((resp) => {
                        if (resp.status === 'ok') {
                            $scope.candidate.contacts = resp.object;
                            $rootScope.$emit('onUpdateCandidateHistory');

                            $scope.updateCandidate();
                            updateCandidateProperties();
                        }
                    })
                    .catch((err) => console.error(err))
                    .finally(() => {
                        $scope.unblurPage();
                        isContacts();
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                    });
            }
        };
        $scope.checkDuplicatesByNameAndContacts = function () {
            Candidate.checkDuplicatesByNameAndContacts($scope, 'candidate');
        };

        function setContacts() {
            $scope.contacts = {
                skype: [],
                mphone: [],
                email: [],
                telegram: [],
                linkedin: [],
                facebook: [],
                homepage: [],
                github: [],
                behance: [],
                viber: [],
                whatsApp: [],
                djinni: [],
                other: [],
            };
            $scope.candidate.contacts.forEach((item) => {
                let type = item.type;
                $scope.contacts[type].push({ ...item });
            });
            if ($scope.contacts.email.length === 0)
                $scope.contacts.email.push({
                    type: 'email',
                    value: '',
                    default: false,
                });
            if ($scope.contacts.linkedin.length === 0)
                $scope.contacts.linkedin.push({
                    type: 'linkedin',
                    value: '',
                    default: false,
                });
            if ($scope.contacts.mphone.length === 0)
                $scope.contacts.mphone.push({
                    type: 'mphone',
                    value: '',
                    default: false,
                });
        }

        $scope.transferCandidateToBambooHR = function (event) {
            $rootScope.loading = true;
            BambooHrIntegrationService.integrateCandidate($scope.candidate)
                .then(() => {
                    $scope.candidate.existInBamboo = true;
                    if (event) notificationService.success($filter('translate')('Candidate sent to BambooHR'));
                })
                .catch((error) => {
                    if (error.code === 'bambooEmployeeExists' || error.code === 'errorConnectingToBambooHr')
                        notificationService.error(error.message);
                    console.error(error.message || error.statusText);
                })
                .finally(() => {
                    $rootScope.loading = false;
                    $rootScope.$$phase || $rootScope.$apply();
                });
        };

        $rootScope.onChangeCalendarTitle = (value) => {
            $scope.emptyRequiredInputs = $scope.emptyRequiredInputs.filter((field) => field !== 'calendarTitle');
            $rootScope.calendarTitle = value;
            $scope.$apply();
        };

        $rootScope.onChangeMeetDuration = (value) => {
            $rootScope.meetDuration = value;
            $scope.$apply();
        };

        $rootScope.changeCandidateEmail = (value) => {
            $scope.emptyRequiredInputs = $scope.emptyRequiredInputs.filter((field) => field !== 'candidateEmail');
            $rootScope.candnotify.sendMail = value;
            $scope.$$phase || $scope.$apply();
        };

        $rootScope.changeLetterSubject = (value) => {
            $rootScope.emailTemplateInModal.title = value;
            $scope.$$phase || $scope.$apply();
        };

        $scope.onChangeProbationaryPeriods = (value) => {
            $scope.probationaryPeriod = value;
            $rootScope.probationaryPeriod = value.value;
            $scope.$$phase || $scope.$apply();
        };
    },
]);
