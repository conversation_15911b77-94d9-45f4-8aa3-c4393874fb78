angular
    .module('RecruitingApp', [
        'ngRoute',
        'ui.router',
        'ngCookies',
        'RecruitingApp.filters',
        'services',
        'RecruitingApp.directives',
        'ngTable',
        'once',
        'infinite-scroll',
        'oi.file',
        'oi.list',
        'ckeditor',
        'ui.notify',
        'LocalStorageModule',
        'pascalprecht.translate',
        'pasvaz.bindonce',
        'tmh.dynamicLocale',
        'uiGmapgoogle-maps',
        'googlechart',
        'googleApi',
        'circularTimepicker',
        'dndLists',
        'controller',
        'components',
        'constant',
        'ng-sortable',
        'angulartics',
        'angulartics.google.analytics',
        'ngQuickDate',
        'ui.bootstrap',
        'outlookApi',
        'ngMaterial',
        'ngAnimate',
        'ngAria',
        'ngMessages',
        'mdPickers',
        'ngImgCrop',
    ])
    .value('notificationData', {})
    .value('routeData', {})
    .config([
        '$routeProvider',
        '$locationProvider',
        '$analyticsProvider',
        '$stateProvider',
        '$urlRouterProvider',
        function ($routeProvider, $locationProvider, $analyticsProvider, $stateProvider, $urlRouterProvider) {
            $locationProvider.hashPrefix('');
            let states = [
                {
                    url: '/organizer',
                    name: 'organizer',
                    controller: 'ActivityFutureController',
                    templateUrl: 'partials/future.html',
                    data: {
                        title: 'Organizer',
                        pageName: 'Activity',
                        navName: 'future',
                    },
                },
                {
                    url: '/organizer/calendar',
                    name: 'calendar',
                    templateUrl: 'partials/GoogleCalendar.html',
                    controller: 'ActivityCalendarController',
                    data: {
                        title: 'Google Calendar',
                        pageName: 'Activity Calendar',
                        navName: 'future',
                    },
                },
                {
                    url: '/organizer/dashboard',
                    name: 'dashboard',
                    component: 'dashboardComponent',
                    data: {
                        title: 'Overview',
                        pageName: 'Activity Dashboard',
                        navName: 'future',
                    },
                },
                // {
                //     url: '/onboarding?stage',
                //     name: 'onboarding',
                //     component: 'onboarding',
                //     data: {
                //         title: 'onboarding',
                //         pageName: 'onboarding',
                //     },
                // },
                {
                    name: 'onboarding.organizer',
                    component: 'onboardingOrganizer',
                },
                {
                    name: 'onboarding.vacancies',
                    component: 'onboardingVacancies',
                },
                {
                    name: 'onboarding.candidates',
                    component: 'onboardingCandidates',
                },
                {
                    name: 'onboarding.candidate',
                    component: 'onboardingCandidate',
                },
                {
                    name: 'onboarding.vacancyAdd',
                    component: 'onboardingVacancyAdd',
                },
                {
                    name: 'onboarding.candidateAdd',
                    component: 'onboardingCandidateAdd',
                },
                {
                    name: 'onboarding.vacancy',
                    component: 'onboardingVacancy',
                },
                {
                    url: '/mailing',
                    name: 'mailing',
                    component: 'mailing',
                    data: {
                        title: 'Mailing',
                        pageName: 'Mailings',
                        navName: 'vacancy',
                    },
                },
                {
                    name: 'mailing.details',
                    component: 'mailingDetails',
                },
                {
                    name: 'mailing.editor',
                    component: 'mailingEditor',
                },
                {
                    name: 'mailing.preview',
                    component: 'mailingPreview',
                },
                {
                    url: '/mailings',
                    name: 'mailings',
                    component: 'mailings',
                    data: {
                        title: 'My mailings',
                        pageName: 'Mailings',
                        navName: 'vacancy',
                    },
                },
                {
                    url: '/prepared',
                    name: 'mailings.saved',
                    component: 'saved',
                },
                {
                    url: '/sent',
                    name: 'mailings.sent',
                    component: 'mailingsSent',
                },
                {
                    url: '/sent-mailing',
                    name: 'sent-mailing',
                    component: 'mailingSent',
                    data: {
                        title: 'My mailings',
                        pageName: 'Mailings',
                        navName: 'vacancy',
                    },
                },
                {
                    url: '/users/{id}',
                    name: 'user',
                    component: 'user',
                    data: {
                        title: 'User',
                        pageName: 'User',
                    },
                },
                {
                    url: '/candidates?ids',
                    templateUrl: 'partials/candidates/candidates.html',
                    controller: 'CandidateController',
                    name: 'candidates',
                    data: {
                        title: 'Candidates',
                        pageName: 'Candidates',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidates/{id}',
                    name: 'candidate',
                    templateUrl: 'partials/candidate.html',
                    controller: 'CandidateOneController',
                    data: {
                        title: 'Candidate',
                        pageName: 'Candidate',
                        navName: 'candidates',
                    },
                    params: {
                        isFromVacancyToEvaluate: false,
                        isFromNotificationsLetters: false,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/candidates/slide/{sliderDataId}/{id}?isFromVacancyToEvaluate?vacancyName?isFromVacancyToCandidate?scoreCardId?vacancyId',
                    name: 'candidate-slide',
                    templateUrl: 'partials/candidate.html',
                    controller: 'CandidateOneController',
                    data: {
                        title: 'Candidate',
                        pageName: 'Candidate',
                        navName: 'candidates',
                    },
                    params: {
                        isFromVacancyToEvaluate: false,
                        isFromNotificationsLetters: false,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                        scoreCardId: '',
                        vacancyId: '',
                    },
                    reloadOnSearch: false,
                },
                {
                    name: 'candidate-slide.mainInfo',
                    component: 'candidateMainInfo',
                    params: {
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.mainInfo',
                    component: 'candidateMainInfo',
                    params: {
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate-slide.personalMailing',
                    component: 'candidatePersonalMailing',
                    params: {
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.personalMailing',
                    component: 'candidatePersonalMailing',
                    params: {
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate-slide.evaluate',
                    component: 'candidateEvaluateComponent',
                    params: {
                        infoList: {},
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.evaluate',
                    component: 'candidateEvaluateComponent',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate-slide.tasks',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate-slide.history',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate-slide.comments',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.comments',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.history',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    name: 'candidate.tasks',
                    component: 'candidateMainInfo',
                    params: {
                        candidateObj: null,
                        vacancyName: null,
                        isFromVacancyToCandidate: null,
                    },
                },
                {
                    url: '/candidates/{id}/{test}',
                    name: 'candidate-test-one',
                    templateUrl: 'partials/candidate.html',
                    controller: 'CandidateOneController',
                    data: {
                        title: 'Candidate',
                        pageName: 'Candidates',
                        navName: 'candidates',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/candidate/add',
                    name: 'candidate-add',
                    templateUrl: 'partials/candidate-add.html',
                    controller: 'CandidateAddController',
                    data: {
                        title: 'Add candidate',
                        pageName: 'Candidate add',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/edit/{id}',
                    name: 'candidate-edit',
                    templateUrl: 'partials/candidate-add.html',
                    controller: 'CandidateEditController',
                    data: {
                        title: 'Edit candidate',
                        pageName: 'Candidate edit',
                        navName: 'candidates',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/candidate/edit/slide/{sliderDataId}/{id}',
                    name: 'candidate-edit-slide',
                    templateUrl: 'partials/candidate-add.html',
                    controller: 'CandidateEditController',
                    data: {
                        title: 'Edit candidate',
                        pageName: 'Candidate edit',
                        navName: 'candidates',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/candidate/merge/{id}',
                    name: 'candidate-merge',
                    templateUrl: 'partials/candidate-merge.html',
                    controller: 'CandidateMergeController',
                    data: {
                        title: 'Merge candidate',
                        pageName: 'Candidate merge',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/candidate/tests',
                    name: 'candidate-tests',
                    templateUrl: 'partials/testModule/tests.html',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Tests and forms',
                        pageName: 'Tests and forms',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/tests/add',
                    name: 'candidate-tests-add',
                    templateUrl: 'partials/testModule/tests.html?b1',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Tests and forms',
                        pageName: 'Tests and forms',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/tests/edit/{id}',
                    name: 'candidate-tests-edit',
                    templateUrl: 'partials/testModule/tests.html?b1',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Tests and forms',
                        pageName: 'Tests and forms',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/tests/results/:id',
                    name: 'test-results-one',
                    templateUrl: 'partials/testModule/test-result.html',
                    controller: 'testResults',
                    data: {
                        title: 'Tests results',
                        pageName: 'Tests results',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/test/{id}',
                    name: 'candidate-test',
                    templateUrl: 'partials/testModule/test.html',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Tests and forms',
                        pageName: 'Test page',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/test/results/{id}',
                    name: 'candidate-test-result',
                    templateUrl: 'partials/testModule/test-result.html',
                    controller: 'testResults',
                    data: {
                        title: 'Test results',
                        pageName: 'Test results',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/test/details/{id}',
                    name: 'candidate-test-details',
                    templateUrl: 'partials/testModule/test-detail.html',
                    controller: 'testResults',
                    data: {
                        title: 'Detailed test results',
                        pageName: 'Detailed test results',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/send-test-candidate-to-email/{id}',
                    name: 'candidate-send-test',
                    templateUrl: 'partials/testModule/sendTestCandidateToEmail.html',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Send test candidate to email',
                        pageName: 'Send test candidate to email',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/send-test-candidate-to-email-from-vacancy',
                    templateUrl: 'partials/testModule/sendTestCandidateToEmail.html',
                    name: 'candidate-send-test-from-vacancy',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Send test candidate to email',
                        pageName: 'Send test candidate to email from vacancy',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/send-test-candidate-to-email-from-candidate',
                    name: 'candidate-send-test-from-candidate',
                    templateUrl: 'partials/testModule/sendTestCandidateToEmail.html',
                    controller: 'testsAndForms',
                    data: {
                        title: 'Send test candidate to email',
                        pageName: 'Send test candidate to email from candidate',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/candidate/add/email',
                    name: 'candidate-add-from-email',
                    templateUrl: 'partials/candidateAddFromEmail.html',
                    controller: 'CandidateAddFromEmailController',
                    data: {
                        title: 'Get candidates from email',
                        pageName: 'Candidates add from email',
                    },
                },
                {
                    url: '/candidate/add/zip',
                    name: 'candidate-add-zip',
                    templateUrl: 'partials/candidateAddFromZip.html',
                    controller: 'CandidateAddFromZipController',
                    data: {
                        title: 'Get candidates from zip',
                        pageName: 'Zip',
                    },
                },
                {
                    url: '/candidates_link',
                    name: 'candidate-link',
                    templateUrl: 'partials/candidateslink.html',
                    controller: 'CandidateLINKController',
                    data: {
                        title: 'Candidates',
                        pageName: 'Candidates',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/efficiency',
                    name: 'efficiency',
                    templateUrl: 'partials/efficiency.html',
                    controller: 'EfficiencyController',
                    data: {
                        title: 'Efficiency',
                        pageName: 'Efficiency',
                    },
                },
                {
                    url: '/reports/statistics',
                    name: 'statistics',
                    component: 'activityStatisticsComponent',
                    data: {
                        title: 'statistics',
                        pageName: 'Statistics client',
                    },
                },

                {
                    url: '/company',
                    name: 'company',
                    templateUrl: 'partials/company-layout.html',
                },
                {
                    url: '/settings/{param}',
                    name: 'company.company-settings-param',
                    templateUrl: 'partials/companysettings.html',
                    controller: 'ActivityCompanySettingsController',
                    data: {
                        title: 'Company settings',
                        pageName: 'Company settings',
                        navName: 'company',
                    },
                },
                {
                    url: '/settings',
                    name: 'company.company-settings',
                    templateUrl: 'partials/companysettings.html',
                    controller: 'ActivityCompanySettingsController',
                    data: {
                        title: 'Company settings',
                        pageName: 'Company settings',
                        navName: 'company',
                    },
                },
                {
                    url: '/custom-fields',
                    name: 'company.custom-fields',
                    templateUrl: 'partials/customFields.html',
                    controller: 'CustomFieldController',
                    data: {
                        title: 'Custom fields',
                        pageName: 'Custom fields',
                        navName: 'company',
                    },
                },
                {
                    url: '/tags',
                    name: 'company.Tags',
                    templateUrl: 'partials/tags.html',
                    controller: 'tagsController',
                    data: {
                        title: 'Tags',
                        pageName: 'Tags',
                        navName: 'company',
                    },
                },
                {
                    url: '/sources-setting',
                    name: 'company.sources-setting',
                    component: 'sourcesSettingComponent',
                    data: {
                        title: 'List of sources',
                        pageName: 'List of sources',
                        navName: 'company',
                    },
                },
                {
                    url: '/scorecards',
                    name: 'company.scorecards',
                    component: 'scoreCardsMainComponent',
                    data: {
                        title: 'Scorecards',
                        pageName: 'scorecards',
                        navName: 'company',
                    },
                },
                {
                    url: '/email-templates',
                    name: 'company.email-templates',
                    templateUrl: 'partials/emailTemplates/emailTemplates.html',
                    controller: 'emailTemplatesController',
                    data: {
                        title: 'Email_templates',
                        pageName: 'Email_templates',
                        navName: 'company',
                    },
                },

                {
                    url: '/sms-templates',
                    name: 'company.sms-templates',
                    templateUrl: 'partials/smsTemplates/smsTemplates.html',
                    controller: 'smsTemplatesController',
                    data: {
                        title: 'sms_templates',
                        pageName: 'sms_templates',
                        navName: 'company',
                    },
                },
                {
                    url: '/gdpr',
                    name: 'company.gdpr',
                    component: 'gdprComponent',
                    data: {
                        title: 'GDPR Settings',
                        pageName: 'GDPR Settings',
                        navName: 'company',
                    },
                },
                {
                    url: '/history',
                    name: 'company.history',
                    templateUrl: 'partials/history.html',
                    controller: 'ActivityGlobalHistoryController',
                    data: {
                        title: 'Activity History',
                        pageName: 'Company History',
                        navName: 'company',
                    },
                },
                {
                    url: '/additionally',
                    name: 'company.Additionally',
                    templateUrl: 'partials/additionally.html',
                    controller: 'ActivityCompanySettingsController',
                    data: {
                        title: 'Additionally',
                        pageName: 'Additionally',
                        navName: 'company',
                    },
                },

                {
                    url: '/clients/statistics',
                    name: 'statistics-clients',
                    templateUrl: 'partials/statisticsС.html',
                    controller: 'ClientsStatisticsController',
                    data: {
                        title: 'Clients',
                        pageName: 'Statistics client',
                    },
                },
                {
                    url: '/notices',
                    name: 'notices',
                    templateUrl: 'partials/notices.html',
                    controller: 'ActivityNoticesController',
                    data: {
                        title: 'Notifications',
                        pageName: 'ActivityNotice',
                    },
                },
                {
                    url: '/notifications',
                    name: 'notifications',
                    templateUrl: 'partials/notification.html',
                    controller: 'NotificationController',
                    data: {
                        title: 'Email_notifications',
                        pageName: 'Notification',
                    },
                },
                {
                    url: '/pay-corporate',
                    name: 'pay-corporate',
                    templateUrl: 'partials/pay-corporate.html',
                    controller: 'payWay4PayCorporateController',
                    data: {
                        title: 'Balance and payment',
                        pageName: 'pay-corporate',
                    },
                },
                {
                    url: '/payment',
                    name: 'payment',
                    templateUrl: 'partials/payment.html',
                    controller: 'payWay4PayPaymentController',
                    data: {
                        title: 'Balance and payment',
                        pageName: 'payment',
                    },
                },
                {
                    url: '/pay',
                    name: 'pay',
                    templateUrl: 'partials/pay.html',
                    controller: 'payWay4PayController',
                    data: {
                        title: 'Balance and payment',
                        pageName: 'pay',
                    },
                },
                {
                    name: 'pay.regular-payment',
                    component: 'regularPaymentsSettings',
                },
                {
                    name: 'pay.bank-transfer',
                    component: 'bankTransferSettings',
                },
                {
                    name: 'pay.crypto',
                    component: 'crypto',
                },
                {
                    name: 'pay.paypal',
                    component: 'paypal',
                },
                {
                    url: '/blocked',
                    name: 'blocked-account',
                    templateUrl: 'partials/blocked-account.html',
                    controller: 'payWay4PayController',
                    data: {
                        title: 'Blocked',
                        pageName: 'Blocked',
                    },
                },
                {
                    url: '/blocked-corporate',
                    name: 'blocked-account-corporate',
                    templateUrl: 'partials/blocked-account-corporate.html',
                    controller: 'payWay4PayCorporateController',
                    data: {
                        title: 'BlockedCorporate',
                        pageName: 'BlockedCorporate',
                    },
                },
                {
                    name: 'blocked-account.regular-payment',
                    component: 'regularPaymentsSettings',
                },
                {
                    name: 'blocked-account.bank-transfer',
                    component: 'bankTransferSettings',
                },
                {
                    name: 'blocked-account.crypto',
                    component: 'crypto',
                },
                {
                    name: 'blocked-account.paypal',
                    component: 'paypal',
                },
                {
                    url: '/xray_link',
                    name: 'xray-link',
                    templateUrl: 'partials/xraylink.html',
                    controller: 'CandidateXRayLinkController',
                    data: {
                        title: 'Candidates X-Ray LinkedIn Search',
                        pageName: 'Candidates',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/clients',
                    name: 'clients',
                    templateUrl: 'partials/clients.html',
                    controller: 'ClientsController',
                    data: {
                        title: 'Clients',
                        pageName: 'Clients',
                        navName: 'clients',
                    },
                },
                {
                    url: '/client/add/',
                    name: 'client-add',
                    templateUrl: 'partials/client-add.html',
                    controller: 'ClientAddController',
                    data: {
                        title: 'New client',
                        pageName: 'Client add',
                        navName: 'clients',
                    },
                },
                {
                    url: '/client/edit/{id}',
                    name: 'client-edit',
                    templateUrl: 'partials/client-add.html',
                    controller: 'ClientEditController',
                    data: {
                        title: 'Edit client',
                        pageName: 'Client edit',
                        navName: 'clients',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/clients/{id}',
                    name: 'clients-id',
                    templateUrl: 'partials/client.html',
                    controller: 'ClientOneController',
                    reloadOnSearch: false,
                    data: {
                        title: 'Client',
                        pageName: 'Clients',
                        navName: 'clients',
                    },
                },
                {
                    url: '/contacts/{id}',
                    name: 'client-contacts',
                    templateUrl: 'partials/contacts.html',
                    controller: 'ContactsOneController',
                    reloadOnSearch: false,
                    data: {
                        title: 'Contacts',
                        pageName: 'Clients',
                        navName: 'clients',
                    },
                },
                {
                    url: '/contact/add/{id}',
                    name: 'client-contact-add',
                    templateUrl: 'partials/contact-add.html',
                    controller: 'ContactAddController',
                    data: {
                        title: 'Add contact',
                        pageName: 'Clients',
                        navName: 'clients',
                    },
                },
                {
                    url: '/contact/edit/{contactId}',
                    name: 'contact-edit',
                    templateUrl: 'partials/contact-add.html',
                    controller: 'ContactEditController',
                    data: {
                        title: 'Edit contact',
                        pageName: 'Contact Edit',
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/vacancies',
                    name: 'vacancies',
                    templateUrl: 'partials/vacancies.html',
                    controller: 'vacanciesController',
                    data: {
                        title: 'Vacancies',
                        pageName: 'Vacancies',
                        navName: 'vacancy',
                    },
                    params: {
                        status: null,
                    },
                    reloadOnSearch: false,
                },
                {
                    url: '/vacancies/{id}',
                    name: 'vacancy',
                    component: 'vacancyComponent',
                    reloadOnSearch: false,
                    data: {
                        title: 'Vacancy',
                        pageName: 'Vacancies',
                        navName: 'vacancy',
                    },
                },
                {
                    name: 'vacancy.description',
                    component: 'vacancyDescription',
                },
                {
                    name: 'vacancy.stages',
                    component: 'vacancyStages',
                },
                {
                    name: 'vacancy.pipeline',
                    component: 'pipelineComponent',
                },
                {
                    name: 'vacancy.suggestions',
                    component: 'vacancySuggestions',
                },
                {
                    name: 'vacancy.tasks',
                    component: 'vacancyTasks',
                },
                {
                    name: 'vacancy.history',
                    component: 'vacancyHistory',
                },
                {
                    name: 'vacancy.templates',
                    component: 'vacancyTemplates',
                },
                {
                    url: '/vacancy/add?isApplication',
                    name: 'vacancy-add',
                    templateUrl: 'partials/vacancy/vacancy-add.html',
                    controller: 'vacancyAddController',
                    data: {
                        title: 'Add vacancy',
                        pageName: 'Vacancy add',
                        navName: 'vacancy',
                    },
                    resolve: {
                        CustomFieldList: function (CustomField) {
                            return new Promise((resolve, reject) => {
                                CustomField.getFullFields(
                                    { objectType: 'vacancy' },
                                    (resp) => resolve(resp),
                                    (error) => reject(error),
                                );
                            });
                        },
                    },
                },
                {
                    url: '/vacancy/edit/{id}',
                    name: 'vacancy-edit',
                    templateUrl: 'partials/vacancy/vacancy-add.html',
                    controller: 'vacancyEditController',
                    reloadOnSearch: false,
                    data: {
                        title: 'Edit vacancy',
                        pageName: 'Vacancy edit',
                        navName: 'vacancy',
                    },
                    params: {
                        isToValidateVacancy: null,
                    },
                },
                {
                    url: '/vacancy/request?localId',
                    name: 'vacancy-application',
                    templateUrl: 'partials/vacancy-application/vacancy-application.html',
                    controller: 'vacancyApplicationController',
                    data: {
                        title: 'Vacancy application',
                        pageName: 'Vacancy application',
                        navName: 'vacancy',
                    },
                    resolve: {},
                },
                {
                    url: '/vacancy/report/{id}',
                    name: 'vacancy-report-id',
                    templateUrl: 'partials/vacancy-reports.html',
                    controller: 'vacancyReportController',
                    data: {
                        title: 'Vacancy report',
                        pageName: 'Vacancies',
                    },
                },
                {
                    url: '/reports/vacancy',
                    name: 'vacancy-report',
                    templateUrl: 'partials/vacancy-report.html',
                    controller: 'reportsController',
                    data: {
                        title: 'Vacancy report',
                        pageName: 'Vacancy report',
                    },
                },
                {
                    url: '/reports/vacancy-funnel',
                    name: 'vacancy-report-funnel-all',
                    templateUrl: 'partials/vacancy-report-funnel-all.html',
                    controller: 'vacancyFunnelReportController',
                    data: {
                        title: 'Vacancies report',
                        pageName: 'Vacancies report',
                    },
                },
                {
                    url: '/reports/vacancy-histogram',
                    name: 'vacancy-histogram',
                    templateUrl: '/partials/vacancy-stages-histogram.html',
                    controller: 'vacancyStagesHistogramController',
                    data: {
                        title: 'Vacancies report',
                        pageName: 'Vacancies report',
                    },
                },
                {
                    url: '/reports/vacancy/{id}',
                    name: 'report-vacancy-id',
                    templateUrl: 'partials/vacancy-reports.html',
                    controller: 'vacancyReportController',
                    data: {
                        title: 'Vacancy report',
                        pageName: 'Vacancy report',
                    },
                },
                {
                    url: '/reports/reportall',
                    name: 'reportall',
                    templateUrl: 'partials/report-all.html',
                    controller: 'reportAllController',
                    data: {
                        title: 'Report',
                        pageName: 'Report all',
                    },
                },
                {
                    url: '/linkedinIntegration',
                    name: 'linkedinIntegration',
                    templateUrl: 'partials/linkedinIntegration.html',
                    controller: 'linkedinIntegrationController',
                    data: {
                        pageName: 'linkedin integration',
                    },
                },
                {
                    url: '/company/users',
                    name: 'users',
                    templateUrl: 'partials/users.html',
                    controller: 'usersController',
                    data: {
                        title: 'Users',
                        pageName: 'All users',
                    },
                },
                {
                    url: '/recalls/{id}',
                    name: 'recalls-id',
                    templateUrl: 'partials/recall.html',
                    controller: 'recallController',
                    data: {
                        title: 'Recalls Info',
                        pageName: 'Recalls Info',
                    },
                },
                {
                    url: '/email/vacancy/{vacancyId}',
                    name: 'vacancy-send-to-client',
                    templateUrl: 'partials/vacancy-send-candidates-to-client.html',
                    controller: 'CandidateEmailSend',
                    data: {
                        title: 'Send email',
                        pageName: 'Vacancies',
                        navName: 'vacancy',
                    },
                },
                {
                    url: '/excelHistory',
                    name: 'excel-history',
                    templateUrl: 'partials/excel-history.html',
                    controller: 'excelHistoryController',
                    data: {
                        title: 'Excel History',
                        pageName: 'Excel History',
                        navName: 'candidates',
                    },
                },
                {
                    url: '/company/employees',
                    name: 'employees',
                    templateUrl: 'partials/employees.html',
                    controller: 'EmployeesController',
                    data: {
                        title: 'employees',
                        pageName: 'Company employees',
                    },
                },
                {
                    url: '/company/employees/{id}',
                    name: 'employee',
                    templateUrl: 'partials/employee.html',
                    controller: 'EmployeeOneController',
                    data: {
                        title: 'employees',
                        pageName: 'Employee User',
                        // navName: 'company'
                    },
                },
                {
                    url: '/company/employee/add',
                    name: 'employee-add',
                    templateUrl: 'partials/employee-add.html',
                    controller: 'EmployeeAddController',
                    data: {
                        title: 'Adding an employee',
                        pageName: 'Employee add',
                        // navName: 'company'
                    },
                },
                {
                    url: '/company/employee/add/{candidateId}',
                    name: 'company-employee-add',
                    templateUrl: 'partials/employee-add-from-candidate.html',
                    controller: 'EmployeeAddFromCandidateController',
                    data: {
                        title: 'Adding an employee',
                        pageName: 'Users',
                    },
                },
                {
                    url: '/company/employees/edit/{employeeId}',
                    name: 'employee-edit',
                    templateUrl: 'partials/employee-add.html',
                    controller: 'EmployeeEditController',
                    data: {
                        title: 'Edit candidate',
                        pageName: 'Employee Edit User',
                        // navName: 'company'
                    },
                },
                {
                    url: '/company/departmentCatalog',
                    name: 'department',
                    templateUrl: 'partials/departmentCatalog.html',
                    controller: 'DepartmentCatalogController',
                    data: {
                        title: 'Department catalog',
                        pageName: 'Department Catalog',
                    },
                },
                {
                    url: '/faq',
                    name: 'faq',
                    templateUrl: 'partials/faq.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'FAQ',
                        pageName: 'FAQ',
                    },
                },
                {
                    url: '/ask_question',
                    name: 'ask-question',
                    templateUrl: 'partials/feedback-page.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Ask question',
                        pageName: 'Ask question',
                    },
                },
                {
                    url: '/report_problem_on_this_page',
                    name: 'report-problem',
                    templateUrl: 'partials/feedback-page.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Report problem on this page',
                        pageName: 'Report problem on this page',
                    },
                },
                {
                    url: '/suggest_improvement_or_request_feature',
                    name: 'suggest-improvement',
                    templateUrl: 'partials/feedback-page.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Suggest improvement or request feature',
                        pageName: 'Suggest improvement or request feature',
                    },
                },
                {
                    url: '/feedback/thanks',
                    name: 'thanks',
                    templateUrl: 'partials/feedback-page-thanks.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Thanks for feedback',
                        pageName: 'Thanks for feedback',
                    },
                },
                {
                    url: '/feedback-new-design',
                    name: 'feedback-new-design',
                    templateUrl: 'partials/feedback-page-new-design.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Feedback for new design',
                        pageName: 'Feedback for new design',
                    },
                },
                {
                    url: '/feedback-new-design-thanks',
                    name: 'feedback-new-design-thanks',
                    templateUrl: 'partials/feedback-page-new-design-thanks.html',
                    controller: 'FeedbackController',
                    data: {
                        title: 'Thanks for feedback',
                        pageName: 'Thanks for feedback',
                    },
                },
                {
                    url: '/email-integration',
                    name: 'email-integration',
                    component: 'mailIntegrationMainComponent',
                    data: {
                        title: 'Integration with email',
                        pageName: 'Integration with email',
                    },
                    resolve: {
                        mailboxes: function (Email) {
                            return Email.getMailboxes(true);
                        },
                    },
                },
                {
                    name: 'email-integration.no-mailboxes',
                    component: 'mailIntegrationFirstTimeComponent',
                },
                {
                    name: 'email-integration.no-mailboxes.integrate-new',
                    views: {
                        addMailbox: 'integrateNewEmailComponent',
                    },
                },
                {
                    name: 'email-integration.integrated-mailboxes',
                    component: 'integratedMailboxesComponent',
                },
                {
                    name: 'email-integration.integrated-mailboxes.edit-settings',
                    views: {
                        editSettings: 'editSettingsComponent',
                    },
                },
                {
                    name: 'email-integration.integrated-mailboxes.integrate-new',
                    views: {
                        addMailbox: 'integrateNewEmailComponent',
                    },
                },
                {
                    url: '/cloud-admin',
                    name: 'cloud-admin',
                    templateUrl: 'partials/cloud-admin.html',
                    controller: 'cloudAdminController',
                    data: {
                        title: 'CLoud Admin',
                        pageName: 'Cloud Admin',
                    },
                },
                {
                    url: '/cloud-admin/{orgId}',
                    name: 'cloud-admin-account-information',
                    templateUrl: 'partials/cloud-admin-account-information.html',
                    controller: 'cloudAdminController',
                    data: {
                        title: 'Cloud Admin Account Information',
                        pageName: 'Cloud Admin Account Information',
                    },
                },
                {
                    url: '/cloud-admin/{orgId}/{userId}',
                    name: 'cloud-admin-account-information-with-referral',
                    templateUrl: 'partials/cloud-admin-account-information.html',
                    controller: 'cloudAdminController',
                    data: {
                        title: 'Cloud Admin Account Information',
                        pageName: 'Cloud Admin Account Information',
                    },
                },
                {
                    url: '/reports/pipeline',
                    name: 'pipeline',
                    templateUrl: 'partials/pipeline.html',
                    controller: 'pipelineController',
                    data: {
                        title: 'Pipeline',
                        pageName: 'Pipeline',
                    },
                },
                {
                    url: '/reports',
                    name: 'reports',
                    templateUrl: 'partials/reports.html',
                    controller: 'MyReportsCtrl',
                    controllerAs: 'myReportsCtrl',
                    data: {
                        title: 'Reports',
                        pageName: 'Reports',
                    },
                },
                {
                    url: '/reports/custom-reports',
                    name: 'custom-reports',
                    templateUrl: 'partials/custom-reports.html',
                    controller: 'CustomReports',
                    controllerAs: 'ctrlReport',
                    data: {
                        title: 'Custom Reports',
                        pageName: 'Custom Reports',
                    },
                },
                {
                    url: '/reports/edit-reports',
                    name: 'edit-reports',
                    templateUrl: 'partials/edit-custom-report.html',
                    controller: 'CustomReportEditCtrl',
                    controllerAs: 'editReport',
                    data: {
                        title: 'Edit Reports',
                        pageName: 'Edit Reports',
                    },
                },
                {
                    url: '/constructor-reports',
                    name: 'constructor-reports',
                    templateUrl: 'partials/constructor-reports.html',
                    controller: 'constructorReports',
                    data: {
                        title: 'Reports constructor',
                        pageName: 'Reports constructor',
                    },
                },
                {
                    url: '/reports/performance',
                    name: 'performance-report',
                    component: 'userPerformanceComponent',
                    data: {
                        title: 'User performance',
                        pageName: 'User performance',
                    },
                },
                {
                    url: '/reports/candidates-source',
                    name: 'candidates-source',
                    component: 'candidatesSourceComponent',
                    data: {
                        title: 'Source report',
                        pageName: 'Source report',
                    },
                },
                {
                    url: '/reports/candidates-source/statistics',
                    name: 'candidates-source-statistics',
                    component: 'candidatesSourceStatComponent',
                    data: {
                        title: 'Number of candidate sources',
                        pageName: 'Number of candidate sources',
                    },
                },
                {
                    url: '/reports/candidates-source/efficiency',
                    name: 'candidates-source-efficiency',
                    component: 'candidatesSourceEfficiencyComponent',
                    data: {
                        title: 'Sources efficiency',
                        pageName: 'Sources efficiency',
                    },
                },
                {
                    url: '/settings',
                    name: 'scope-settings',
                    templateUrl: 'partials/settings.html',
                    controller: 'ScopeSettingsController',
                    data: {
                        title: 'Account data visibility',
                        pageName: 'Settings',
                    },
                },
                {
                    url: '/invoice',
                    name: 'invoice',
                    templateUrl: 'partials/invoice.html',
                    controller: 'invoiceController',
                    data: {
                        title: 'Invoice',
                        pageName: 'Invoice',
                    },
                },
                {
                    url: '/referral',
                    name: 'referral',
                    component: 'referralMainComponent',
                    data: {
                        title: 'Referral program',
                        pageName: 'Referral program',
                    },
                },
                {
                    name: 'referral.general',
                    component: 'referralGeneralComponent',
                },
                {
                    name: 'referral.how',
                    component: 'referralHowComponent',
                },
                {
                    name: 'referral.earnings',
                    component: 'referralEarningsComponent',
                },
                {
                    url: '/score-cards',
                    name: 'score-cards',
                    component: 'scoreCardsMainComponent',
                    data: {
                        title: 'Scorecards',
                        pageName: 'Score cards',
                    },
                },
                {
                    url: '/integration-page',
                    name: 'integration-page',
                    component: 'integrationPageComponent',
                    data: {
                        title: 'Integration with work sites',
                        pageName: 'Integration with work sites',
                    },
                },
                {
                    url: '/achievements/results',
                    component: 'resultsComponent',
                    name: 'results',
                    data: {
                        title: 'Results',
                        pageName: 'Results',
                        navName: 'achievements',
                    },
                },
                {
                    url: '/achievements/collections',
                    component: 'collectionsComponent',
                    name: 'collections',
                    data: {
                        title: 'Collections',
                        pageName: 'Collection',
                        navName: 'achievements',
                    },
                },
                {
                    url: '/achievements/awards',
                    component: 'awardsComponent',
                    name: 'awards',
                    data: {
                        title: 'Awards',
                        pageName: 'Awards',
                        navName: 'achievements',
                    },
                },
            ];
            states.forEach((state) => {
                $stateProvider.state(state);
            });
            $urlRouterProvider.otherwise('/organizer');
        },
    ])
    .config([
        '$httpProvider',
        function ($httpProvider) {
            // Interceptors for every request and response
            $httpProvider.interceptors.push(...['requestObserver', 'responseObserver']);
        },
    ])
    .run(function (
        $rootScope,
        $templateCache,
        $window,
        $transitions,
        $q,
        $location,
        $http,
        serverAddress,
        $filter,
        notificationService,
        Service,
        $uibModalStack,
        $state,
        $translate,
    ) {
        $rootScope.topNavBarState = true;

        /**
         * @param {function} fn
         * @param {number} ms
         * @return {(function(): void)|*}
         */
        $rootScope.makeDebounce = (fn, ms = 700) => {
            let timeout;
            return function () {
                const fnCall = () => fn.apply(this, arguments);
                clearTimeout(timeout);
                timeout = setTimeout(fnCall, ms);
            };
        };

        function searchInFaqDB() {
            Person.onSearchInFaq();
        }

        const debounce = (fn, ms) => {
            let timeout;
            return function () {
                const fnCall = () => fn.apply(this, arguments);
                clearTimeout(timeout);
                timeout = setTimeout(fnCall, ms);
            };
        };

        let searchInFaq = debounce(searchInFaqDB, 500);

        $rootScope.searchInFaq = function () {
            searchInFaq();
        };

        $rootScope.isHiddenFromHM = function () {
            return (
                $rootScope.me.recrutRole === 'client' &&
                $rootScope.me.personParams.restrictInterviewStageChangeHM === 'true'
            );
        };

        $rootScope.TimeMinusTimeZone = function (milliseconds) {
            if (!milliseconds) return;

            const dateObj = new Date(milliseconds);

            return dateObj.getTime() - dateObj.getTimezoneOffset() * 60000;
        };

        $rootScope.textToLengthPx = function (text) {
            const element = document.createElement('canvas');
            const context = element.getContext('2d');
            context.font = '14px Helvetica-Normal';

            return Math.floor(context.measureText($filter('translate')(text)).width);
        };

        $rootScope.scrollToDomElement = function (selector, behavior = 'auto') {
            const element = document.querySelector(selector);
            element &&
                element.scrollIntoView({
                    block: 'center',
                    behavior,
                });
        };

        /**
         * Use instead of timeouts. Wait for some condition that passes as callback
         * <AUTHOR> Ilnitskyi
         * @throws error of iterations limit
         */
        $rootScope.waitForCondition = (intervalTime = 300, iterationsLimit = 100, conditionCallback) => {
            return new Promise((resolve, reject) => {
                let iterations = 0;
                const interval = setInterval(() => {
                    if (Boolean(conditionCallback())) {
                        resolve();
                        clearInterval(interval);
                    } else if (iterations >= iterationsLimit) {
                        reject(new Error('Iterations limit'));
                        clearInterval(interval);
                    }
                    iterations++;
                }, intervalTime);
            });
        };

        $rootScope.checkIfOnlyContainsLink = (text) => {
            if (!text) return;
            return text.trim().split(' ').length === 1 && $filter('linkify3')(text.trim()).endsWith('</a>');
        };

        $rootScope.isMyProfilePage = () => {
            return $location.$$path.includes($rootScope.me?.userId);
        };

        $rootScope.checkClass = function () {
            if ($('#hide-popup').attr('aria-expanded') === 'true') {
                $rootScope.topNavBarState = false;
            } else {
                $rootScope.topNavBarState = true;
            }
        };

        $rootScope.addLinkPrefix = function (link) {
            let newLink = link;
            if (!link.startsWith('http')) {
                newLink = `https://${link}`;
            }
            return newLink;
        };

        $rootScope.downloadFile = async (fileId, fileName) => {
            try {
                const response = await fetch(
                    `/hr/getapp/${fileId}/${Service.getFileResolutionFromName(fileName).substring(1)}`,
                );
                if (response.status !== 200) {
                    const errorResponse = await response.json();
                    throw errorResponse.code;
                }
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                Promise.resolve().then(() => a.remove());
            } catch (error) {
                notificationService.error($translate.instant(error));
            }
        };

        $rootScope.downloadReport = async (reportId, tagId) => {
            try {
                const resp = await fetch(`/hr/getapp?id=${reportId}`);
                if (resp.status !== 200) {
                    const errorResponse = await resp.json();
                    throw errorResponse.code;
                }
                $(tagId)[0].href = `/hr/getapp?id=${reportId}`;
                $(tagId)[0].click();
            } catch (error) {
                notificationService.error($translate.instant(error));
            }
        };

        $rootScope.addClientAndLocationToVacancyLabels = function (vacancies) {
            return vacancies.map((vacancy) => {
                let location = '';
                const region = vacancy.region;
                const isRemote = vacancy.employmentType === 'remote';
                const isClientHidden = $rootScope.me.personParams.clientAccessLevel === 'hide';
                if (isRemote) {
                    location = $translate.instant('remote_1');
                }
                if (region) {
                    location = `${isRemote ? `${location}, ` : ''}${
                        region[`${region.hasOwnProperty('displayCity') ? 'displayCity' : 'displayCountry'}`]
                    }`;
                }

                const label = `${$filter('limitToEllipse')(vacancy.position, 72)} (${
                    isClientHidden ? '' : `${vacancy.clientId.name}. `
                }${location})`;

                return {
                    label,
                    ...vacancy,
                };
            });
        };

        $rootScope.testIsPhotoLink = function (link) {
            const regex = /(http|https):\/\/\S+/gi;
            return regex.test(link);
        };

        $rootScope.checkUserForTagsList = function (actionName, areClientsHidden) {
            return !(
                ['client_message', 'client_change_state', 'client_set_responsible'].includes(actionName) &&
                areClientsHidden
            );
        };

        $transitions.onBefore({}, function (transition) {
            $('.first-navbar').css({ 'pointer-events': 'auto' });
            $('.first-navbar').css({ opacity: '1' });
            if (transition.to() !== undefined && transition.to().url !== undefined) {
                $uibModalStack.dismissAll();
                const transitionTo = transition.to();
                $rootScope.currentLocation = transitionTo.url;
                if (transitionTo && transitionTo.data && !$state.$current.name.includes('blocked-account')) {
                    $rootScope.activePage = transitionTo.data.pageName;
                    $rootScope.navName = transitionTo.data.navName;
                    hardReloadAfterReleaseHandler($rootScope, transition);
                }
            } else if (transition.to().name.includes('blocked') && $rootScope.me.orgParams.tarif !== 'corporate') {
                $rootScope.currentLocation = '/blocked';
            } else if (transition.to().name.includes('blocked') && $rootScope.me.orgParams.tarif === 'corporate') {
                $rootScope.currentLocation = '/blocked-corporate';
            }

            const transitionFrom = transition.from();
            if (transitionFrom !== undefined && transitionFrom.url !== undefined) {
                $rootScope.previousLocation = transitionFrom.url;
            }
            var defer = $q.defer();
            SecurityFilter(
                $rootScope,
                defer,
                $location,
                $http,
                serverAddress,
                $filter,
                notificationService,
                $rootScope.currentLocation,
            );
            return defer.promise;
        });
    })
    .run(function (
        $location,
        $rootScope,
        $window,
        $filter,
        $localStorage,
        Vacancy,
        notificationService,
        translateWords,
        $transitions,
        $state,
        $stateParams,
        Service,
        Nps,
    ) {
        $rootScope.$state = $state;
        $rootScope.$stateParams = $stateParams;
        //Redirect on rejected resolve
        $state.defaultErrorHandler(function (error) {
            if (error.detail === 'blocked') {
                if (!$rootScope.$state.$current.name.includes('blocked-account')) {
                    if ($rootScope.me.orgParams.tarif !== 'corporate')
                        $state.go('blocked-account', {
                            errorMessage: 'Account blocked, transition denied',
                        });
                    else if ($rootScope.me.orgParams.tarif === 'corporate')
                        $state.go('blocked-account-corporate', {
                            errorMessage: 'Account blocked, transition denied',
                        });
                }
            } else {
                $state.go('organizer', { errorMessage: error.message });
            }
        });
        //start--Some bad code from test module

        // Function for save to GLOBAL SCOPE variable for All Funnel
        $rootScope.pushedLocalIdVacancies = function () {
            $rootScope.pushedVacancies = [];
        };

        $rootScope.sendTestFromVacancyStage = function (stage, longlist, dataForVacancy) {
            if (stage == undefined) {
                stage = longlist;
            }
            $localStorage.set('activeCustomStageId', stage);
            $rootScope.vacancySearchParams = {
                candidateFilter: {
                    displayContacts: true,
                    displayLanguages: true,
                    displayRelocatedRegion: true,
                    ...($rootScope.currentSelectScope === 'onlyMy' && {
                        responsibleId: $rootScope.me.userId,
                    }),
                },
                state: stage,
                vacancyId: $rootScope.vacancy.vacancyId,
                withCandidates: true,
                withVacancies: true,
            };
            Vacancy.getCandidatesInStages($rootScope.vacancySearchParams, function (resp) {
                if (resp.objects.length == 0) {
                    notificationService.error($filter('translate')('add candidates to the stage'));
                } else {
                    $rootScope.candidatesInStages = resp.objects;
                    $localStorage.set('vacancyForTest', $rootScope.candidatesInStages);
                    $location.path('/candidate/send-test-candidate-to-email-from-vacancy');
                }
            });
        };

        var is_safari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

        if (is_safari === false) {
            $rootScope.NpsOnPost = function (rate, recommendation, opinion) {
                return Nps.onNpsPost({ rate, recommendation, opinion });
            };

            localStorage.setItem('showOnce', 'false');

            const bc = new BroadcastChannel('dcode');

            bc.addEventListener('message', (e) => {
                localStorage.setItem('showOnce', e.data);
            });

            setInterval(() => {
                if (localStorage.getItem('showOnce') == 'false') {
                    Nps.OnNpsGet().then((resp) => {
                        $rootScope.numberOfQuestionForNPS = resp.object.numberOfQuestion;
                        if (resp.object.needToShow === true) {
                            if (localStorage.getItem('showOnce') == 'false') {
                                bc.postMessage('true');
                                localStorage.setItem('showOnce', 'true');
                                $rootScope.showNPS();
                            }
                        }
                    });
                }
            }, 1800000); // 1800000 - 30 min // 300000 - 5 min // 120000 - 2 min
        }

        if (is_safari === true) {
            $rootScope.NpsOnPost = function (rate, recommendation, opinion) {
                return Nps.onNpsPost({ rate, recommendation, opinion });
            };

            localStorage.setItem('showOnce', 'false');

            setInterval(() => {
                if (localStorage.getItem('showOnce') == 'false') {
                    Nps.OnNpsGet().then((resp) => {
                        $rootScope.numberOfQuestionForNPS = resp.object.numberOfQuestion;
                        if (resp.object.needToShow === true) {
                            if (localStorage.getItem('showOnce') == 'false') {
                                // bc.postMessage('true')
                                localStorage.setItem('showOnce', 'true');
                                $rootScope.showNPS();
                            }
                        }
                    });
                }
            }, 1800000); // 1800000 - 30 min // 300000 - 5 min // 120000 - 2 min
        }

        $rootScope.sendCandidateToTest = function (candidate, count) {
            if (count == 0) {
                notificationService.error(
                    $filter('translate')('Please add an email before sending a test to this candidate'),
                );
            } else {
                if (candidate) {
                    $localStorage.set('candidateForTest', candidate);
                    $rootScope.candidateToTest = JSON.parse($localStorage.get('candidateForTest'));
                    $location.path('/candidate/send-test-candidate-to-email-from-candidate');
                    $rootScope.fromCandidate = [candidate];
                    $rootScope.emailCandidateId = candidate.candidateId;
                    if (candidate.contacts.length > 0) {
                        angular.forEach(candidate.contacts, function (nval) {
                            if (nval.type === 'email' && nval.default) {
                                delete $rootScope.emailCandidate;
                                var email = nval.value.split(' ')[0];
                                $rootScope.emailCandidate = email.replace(/,/g, '');
                            }
                        });
                    } else {
                        delete $rootScope.emailCandidate;
                        notificationService.error(
                            $filter('translate')('Please add an email before sending a test to this candidate'),
                        );
                    }
                }
            }
        };
        //end--Some bad code from test module
        $transitions.onSuccess({}, function (transition) {
            if (transition.from() && transition.from().url != undefined) {
                var firstPage = $location.$$protocol + '://' + $location.$$host + '/!#/ask_question';
                var secondPage = $location.$$protocol + '://' + $location.$$host + '/!#/report_problem_on_this_page';
                var thirdPage =
                    $location.$$protocol + '://' + $location.$$host + '/!#/suggest_improvement_or_request_feature';
                $rootScope.previousHistoryFeedback = $location.$$absUrl;
                if (
                    $rootScope.previousHistoryFeedback != firstPage &&
                    $rootScope.previousHistoryFeedback != secondPage &&
                    $rootScope.previousHistoryFeedback != thirdPage
                ) {
                    $localStorage.set('previousHistoryFeedback', $rootScope.previousHistoryFeedback);
                }
                if (transition.to() != undefined) {
                    if (transition.to().url != undefined && transition.to().url == '/cloud-admin') {
                        if ($rootScope.me) {
                            if ($rootScope['me']['personParams']['domainAdmin'] == 'all') {
                                document.title = 'Admin Panel of all accounts | CleverStaff';
                            } else {
                                document.title =
                                    'Admin Panel of domain' + $rootScope['me']['personParams']['domainAdmin'];
                            }
                        }
                    }
                }
                $rootScope.stageName = '';
                $('.showCustomStage').hide();
                $('#candidate_preview').css({
                    top: 0,
                    left: 0,
                    display: 'none',
                });
            }
        });
    })
    .run(function ($rootScope, $uibModal) {
        $rootScope.$on('unAuthorized', showAuthModal);

        function showAuthModal() {
            if (!$rootScope.modalLoginForm) {
                $rootScope.modalLoginForm = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/no-access-modal.html',
                    size: '',
                    backdrop: 'static',
                    keyboard: false,
                    controller: 'noAccessModalController',
                    windowClass: 'modal-access-wrapper',
                    resolve: function () {},
                });
            }
        }
    })
    .run(function ($rootScope, $uibModal, $filter) {
        $rootScope.showBlockDeletingCandidatesModal = function (blocker) {
            const modalText = $filter('translate')('Administrator name has prohibited you from deletion candidates', {
                name: $rootScope.useAmericanNameStyle ? blocker.fullNameEn : blocker.fullName,
            });

            $rootScope.blockDeletingCandidateModal = $uibModal.open({
                animation: true,
                templateUrl: '../partials/modal/blocked-candidates-deleting.html',
                backdrop: 'static',
                keyboard: false,
                controller: [
                    '$scope',
                    function ($scope) {
                        $scope.closeModal = function () {
                            $rootScope.blockDeletingCandidateModal.close();
                        };
                        $scope.modalText = modalText;
                    },
                ],
            });
        };
    })
    .run(function ($rootScope, $uibModal, notificationData, $translate) {
        $rootScope.$on('showPopup', showNotificationPopup);

        function showNotificationPopup() {
            if (!$rootScope.notificationPopup) {
                const popupVersions = { ...notificationData['popup_versions'] };

                for (let version in popupVersions) {
                    if (
                        popupVersions[version].roles.some(
                            (role) => role === $rootScope.me.recrutRole || role === 'all',
                        ) &&
                        popupVersions[version].countries.some(
                            (country) => country === $rootScope.me.orgParams.registrationCountry || country === 'all',
                        ) &&
                        popupVersions[version].languages.some(
                            (language) => language === $rootScope.currentLang || language === 'all',
                        ) &&
                        popupVersions[version].sex.some((sex) => sex === $rootScope.me.sex || sex === 'all')
                    ) {
                        notificationData['popupToDisplay'] = popupVersions[version].content;
                        break;
                    }
                }

                if (!notificationData['popupToDisplay']) return;

                $rootScope.notificationPopup = $uibModal.open({
                    animation: true,
                    templateUrl: '../partials/modal/notification-pop-up.html',
                    controller: 'notificationPopupController',
                    windowClass: 'notification-pop-up',
                });
                clearInterval($rootScope.popupIntervalCheck);
            }
        }
    })
    .run(function ($rootScope, $translate) {
        //Section for helper functions

        /**
         * Returns the verb variant based on the provided gender.
         *
         * @param {boolean} gender - The gender to determine the verb variant (true for male, false for female).
         * @param {{ male: string; female: string }} verbVariants - An object containing the verb variants for male and female.
         * @param {boolean} [translate=true] - Whether to translate the verb variant using $translate.
         * @returns {string} The verb variant corresponding to the provided gender.
         */
        $rootScope.getVerbByGender = (gender, verbVariants, translate = true) => {
            if (translate) {
                return gender ? $translate.instant(verbVariants.male) : $translate.instant(verbVariants.female);
            }

            return gender ? verbVariants.male : verbVariants.female;
        };

        /**
         * Sets CKEditor height to fit the content. Fixes scroll overflow issue on small screen height
         * @param {object} event CKEditor event
         */
        $rootScope.adjustCKEditorHeight = (event) => {
            const { container, config } = event.editor;
            const height = container.$.clientHeight;

            if (config.resizeCount > 20) {
                throw new Error('resizeCount > 20, probably infinite loop');
            }

            if (event.data.contentsHeight === height) return;

            //prevents infinite loop and call stack overflow
            config.resizeCount = ++config.resizeCount || 0;
            config.autoGrow_maxHeight = height;
            event.editor.resize(null, height, true);
        };

        $rootScope.capitalize = (text) => {
            if (!text) return;
            return text.charAt(0).toUpperCase() + text.slice(1);
        };

        $rootScope.getSortedLangOptions = (langOptions) => {
            const english = langOptions.find(({ value }) => value.english === 'english');

            const currentLangKey = $rootScope.getCurrentLangKey($rootScope.currentLang);

            langOptions.sort((a, b) => a.value[currentLangKey].localeCompare(b.value[currentLangKey]));

            if (english) {
                return [english, ...langOptions.filter(({ value }) => value.english !== 'english')];
            }
            return langOptions;
        };

        /**
         * @param { 'en' | 'ua' | 'ru' | 'pl' } currentLang
         * @return { 'english' | 'ukrainian' | 'russian' | 'polish' }
         */
        $rootScope.getCurrentLangKey = (currentLang) => {
            switch (currentLang) {
                case 'en':
                    return 'english';
                case 'ua':
                    return 'ukrainian';
                case 'ru':
                    return 'russian';
                case 'pl':
                    return 'polish';
            }
        };

        $rootScope.getCandidateWord = function (count, lang) {
            const few = [2, 3, 4, 22, 23, 24].includes(count);
            switch (lang) {
                case 'pl':
                    return few ? 'kandydata' : 'kandydatów';
                case 'ua':
                    return few ? 'кандидата' : 'кандидатів';
                case 'ru':
                    return few ? 'кандидата' : 'кандидатов';
                case 'en':
                    return 'candidates';
                default:
                    return '';
            }
        };

        $rootScope.getItemByLang = (prefix, itemObj, lang) => {
            switch (lang) {
                case 'ru':
                    return itemObj[`${prefix}Ru`];
                case 'en':
                    return itemObj[`${prefix}En`];
                case 'ua':
                    return itemObj[`${prefix}Ua`];
                case 'pl':
                    return itemObj[`${prefix}Pl`];
            }
        };

        /**
         * Returns fullName or fullNameEn depending on $rootScope.useAmericanNameStyle
         * @param { {fullName: string, fullNameEn: string} } personLikeObj
         * @return { string }
         */
        $rootScope.getFullNameDueToStyle = (personLikeObj) => {
            return $rootScope?.useAmericanNameStyle ? personLikeObj?.fullNameEn : personLikeObj?.fullName;
        };

        /**
         * @param {string} selector
         */
        $rootScope.onScrollToBlockBySelector = (selector) => {
            const elem = document.querySelector(selector);
            elem?.scrollIntoView({ block: 'center', behavior: 'smooth' });
        };

        //For using in react components instead of getService()
        $rootScope.translate = $translate.instant;

        //Another helper function
        $rootScope._objectToFormData = (obj) => {
            const formData = new FormData();

            Object.entries(obj).forEach(([key, value]) => {
                formData.append(key, value);
            });

            return formData;
        };

        //Debounce
        $rootScope.makeDebounce = (fn, ms) => {
            let timeout;
            return function () {
                const fnCall = () => fn.apply(this, arguments);
                clearTimeout(timeout);
                timeout = setTimeout(fnCall, ms);
            };
        };

        $rootScope.isToday = (date) => {
            if (typeof date === 'string' || typeof date === 'number') {
                date = new Date(date);
            }

            const today = new Date();
            return (
                date.getDate() === today.getDate() &&
                date.getMonth() === today.getMonth() &&
                date.getFullYear() === today.getFullYear()
            );
        };

        /**
         * To scroll to the end of the column after opening dropdown
         *
         * @param {string} selector
         */
        $rootScope.columnScrollDown = (selector) => {
            const column = document.querySelector(selector);
            setTimeout(() => {
                column && column.scrollBy(0, 1000);
            }, 50);
        };

        // Filter russia from countries list
        $rootScope.filterCountriesList = (countries) => {
            const countryToFilter = {
                ua: 'Росія',
                en: 'Russia',
                pl: 'Rosja',
                ru: 'Россия',
            };

            return $rootScope.currentLang in countryToFilter
                ? countries.filter((item) => item !== countryToFilter[$rootScope.currentLang])
                : countries;
        };
    })
    .config(function (googleServiceProvider, $logProvider, $translateProvider, tmhDynamicLocaleProvider) {
        /************************************/
        googleServiceProvider.configure({
            clientIdT: '195081582460-eo4qmmi7o6hii0ckmrc004lhkh9m3596.apps.googleusercontent.com',
            clientIdW: apiKey.google.client_id,
            calendarName: 'CleverStaff events',
            scopes: ['https://www.googleapis.com/auth/userinfo.email', 'https://www.googleapis.com/auth/calendar'],
            gmailScopes: [
                'https://www.googleapis.com/auth/userinfo.email',
                'https://www.googleapis.com/auth/gmail.readonly',
            ],
        });
        /************************************/
        $logProvider.debugEnabled(false);
        /************************************/
        $translateProvider.useStaticFilesLoader({
            prefix: 'languange/locale-',
            suffix: '.json?v=916',
        });
        $translateProvider.translations('en');
        $translateProvider.translations('ru');
        $translateProvider.translations('ua');
        $translateProvider.translations('pl');

        const userLang = getUserLang();
        const lST = userLang.substring(0, 2);

        if (lST === 'ru' || lST === 'be' || lST === 'uk' || lST === 'ua' || lST === 'pl') {
            if (lST === 'uk' || lST === 'ua') {
                $translateProvider.preferredLanguage('ua');
                localStorage.setItem('lang', 'ua');
                localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ua');
            } else if (lST === 'pl') {
                $translateProvider.preferredLanguage('pl');
                localStorage.setItem('lang', 'pl');
                localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'pl');
            } else {
                $translateProvider.preferredLanguage('ru');
                localStorage.setItem('lang', 'ru');
                localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ru');
            }
        } else {
            $translateProvider.preferredLanguage('en');
            localStorage.setItem('lang', 'en');
            localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'en');
        }

        $translateProvider.useLocalStorage();
        /************************************/
        tmhDynamicLocaleProvider.localeLocationPattern('lib/angular/i18n/angular-locale_{{locale}}.js');
        tmhDynamicLocaleProvider.useCookieStorage();
        /************************************/
        if (!navigator.saysWho) {
            navigator.saysWho = (function () {
                var ua = navigator.userAgent,
                    tem,
                    M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
                if (/trident/i.test(M[1])) {
                    tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
                    return 'IE ' + (tem[1] || '');
                }
                if (M[1] === 'Chrome') {
                    tem = ua.match(/\bOPR\/(\d+)/);
                    if (tem != null) return 'Opera ' + tem[1];
                }
                M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
                if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
                return M.join(' ');
            })();
        }

        // Set CKEditor scrollbar style for all editors including React
        window.CKEDITOR.addCss(
            '.cke_editable::-webkit-scrollbar { width: 8px!important;height:5px!important;max-height: 5px; }',
        );
        window.CKEDITOR.addCss('.cke_editable::-webkit-scrollbar-track { background-color: transparent; }');
        window.CKEDITOR.addCss(
            '.cke_editable::-webkit-scrollbar-thumb { background-color: #c7c7c7!important;border-radius: 4px;}',
        );
        window.CKEDITOR.addCss('.cke_editable::-webkit-scrollbar-thumb:hover { background-color: #909090; }');
    });

var controller = angular.module('controller', []);
var component = angular.module('components', []);

// TODO: 5008 remove function below
function checkUrl(serverAddress, url, method) {
    if (
        url == 'partials/client.html' ||
        url == 'partials/candidate.html' ||
        url == 'partials/vacancy/vacancy.html' ||
        url == 'partials/user.html' ||
        url == 'partials/candidates.html' ||
        url == 'partials/vacancies.html' ||
        url == 'partials/future.html' ||
        url == 'partials/employees.html' ||
        url == 'partials/statistics.html' ||
        url == 'partials/pay.html' ||
        url == 'partials/payPlaton.html' ||
        url == 'partials/clients.html' ||
        url == 'partials/users.html'
    ) {
        return true;
    } else if (
        (url == serverAddress + '/candidate/get' && method == 'GET') ||
        (url == serverAddress + '/vacancy/get' && method == 'GET') ||
        (url == serverAddress + '/client/get' && method == 'GET') ||
        (url == serverAddress + '/contact/get' && method == 'GET') ||
        (url == serverAddress + '/person/getPerson' && method == 'GET')
    ) {
        return true;
    } else {
        return false;
    }
}

var defaultRoute = '/organizer';

function checkUrlByRole(url, Role, accessLevel, $location, serverAddress, $http, $filter, notificationService) {
    if (Role === 'researcher') {
        if (url === '/company/settings') {
            $location.path('/company/users');
        }
    }
    if (Role == 'client') {
        if (url == '/candidate/add' || url == '/candidate/edit/{id}' || url == '/client/add') {
            $location.path(defaultRoute);
        } else if (url == '/company/statistics') {
            $location.path('/clients/statistics');
        } else {
            return true;
        }
    } else if (
        accessLevel == 'hide' &&
        (url == '/vacancy/add' ||
            url == '/vacancy/edit/{id}' ||
            url == '/clients' ||
            url == '/client/add/' ||
            url == '/client/edit/{id}' ||
            url == '/clients/{id}' ||
            url == '/contacts/{id}' ||
            url == '/vacancy/add?isApplication' ||
            url == '/email/vacancy/{vacancyId}')
    ) {
        notificationService.error($filter('translate')('This function is not available'));
        $location.path('/organizer');
    } else {
        return true;
    }
}

function setPersonParams($http, userId, paramName, paramValue, serverAddress) {
    const URL = serverAddress + '/person/changeUserParam';
    $http
        .post(URL, {
            userId,
            name: paramName,
            value: paramValue,
        })
        .then(function (resp) {});
}

function SecurityFilter($rootScope, deffer, $location, $http, serverAddress, $filter, notificationService, urlTo) {
    if (urlTo != undefined) {
        var routeName = urlTo;
        var meChecked = false;
        $rootScope.$watch('me', function meWatch(newValue, oldValue) {
            if (newValue != undefined) {
                if (
                    newValue.personParams == undefined ||
                    newValue.personParams.timeZoneOffset == undefined ||
                    newValue.personParams.timeZoneOffset != new Date().getTimezoneOffset()
                ) {
                    setPersonParams(
                        $http,
                        newValue.userId,
                        'timeZoneOffset',
                        new Date().getTimezoneOffset(),
                        serverAddress,
                    );
                }
                if (newValue.status === 'error') {
                    //$window.location.replace('/');
                } else {
                    if (
                        checkUrlByRole(
                            routeName,
                            newValue.recrutRole,
                            newValue.personParams.clientAccessLevel,
                            $location,
                            serverAddress,
                            $http,
                            $filter,
                            notificationService,
                        )
                    ) {
                        if (deniedForBlocked($rootScope.me.orgParams, routeName)) deffer.reject('blocked');
                        else deffer.resolve();
                    } else {
                        deffer.reject();
                    }
                }
            }
        });
        if ($rootScope.me && $rootScope.me.personParams && !meChecked) {
            if (
                checkUrlByRole(
                    routeName,
                    $rootScope.me.recrutRole,
                    $rootScope.me.personParams.clientAccessLevel,
                    $location,
                    serverAddress,
                    $http,
                    $filter,
                    notificationService,
                )
            ) {
                if (deniedForBlocked($rootScope.me.orgParams, routeName)) deffer.reject('blocked');
                else deffer.resolve();
            } else {
                deffer.reject();
            }
        }
    } else {
        deffer.resolve();
    }
}

function deniedForBlocked(orgParams, urlTo) {
    if (
        orgParams.block === 'Y' &&
        urlTo !== '/blocked' &&
        urlTo !== '/blocked-corporate' &&
        urlTo !== '/settings' &&
        urlTo !== '/invoice' &&
        urlTo !== '/company/users' &&
        !urlTo.includes('/users/') &&
        urlTo !== '/payment'
    ) {
        return true;
    } else {
        return false;
    }
}

function hardReloadAfterReleaseHandler($rootScope, transition) {
    if ($rootScope.isOutdatedVersion) hardReloadByURL($rootScope, transition);
}

function hardReloadByURL($rootScope, transition) {
    const toState = transition.to();
    const toStateName = toState.name;
    const toStateParams = transition.params();

    const link = transition.router.stateService.href(toStateName, toStateParams);

    $rootScope.isOutdatedVersion = false;
    $rootScope.$$phase || $rootScope.$apply();

    location.replace = `${location.$$protocol + '://' + location.$$host}/!${link}`;
    setTimeout(() => location.reload(true), 100);
}

function getUserLang() {
    const browserLang = navigator.language || navigator.userLanguage;
    const prevLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');

    return prevLang || browserLang;
}
