angular
    .module('services.interceptorHandler', [])
    .factory('requestObserver', function () {
        return {
            request: function (config) {
                if (config.url.includes('partials')) {
                    config.url = `${config.url}?v=918`;
                }
                return config;
            },
        };
    })
    .factory('responseObserver', [
        '$q',
        '$window',
        'notificationService',
        '$translate',
        '$rootScope',
        function responseObserver($q, $window, notificationService, $translate, $rootScope) {
            return {
                response: function (response) {
                    if ($rootScope.noConnection) {
                        PNotify.removeAll();
                        $rootScope.noConnection = false;
                    }
                    return response;
                },
                responseError: function (errorResponse) {
                    switch (errorResponse.status) {
                        case 401:
                            $rootScope.loading = false;
                            if (errorResponse.config.url !== '/hr/person/authping')
                                $rootScope.$broadcast('unAuthorized');
                            break;
                        case 403:
                            if (errorResponse.config.url !== '/hr/person/authping')
                                notificationService.error($translate.instant('Access is denied'));
                            break;
                        case 502:
                            $rootScope.haveError = 'error';
                            setTimeout(() => {
                                $('.ui-pnotify-text').each(function () {
                                    this.parentElement.style.display = 'none';
                                });
                            }, 0);
                            $rootScope.callMeForModal();
                            break;
                        case 504:
                            break;
                        default:
                            if (!$window.navigator.onLine || errorResponse.status === -1) {
                                if (!errorResponse.status || errorResponse.config.url === 'js/Version.json') return;
                                var exists = false;
                                setTimeout(() => {
                                    $('.ui-pnotify-text').each(function () {
                                        if (
                                            $(this).html() == $translate.instant('You don’t have internet connection')
                                        ) {
                                            exists = true;
                                        } else {
                                            this.parentElement.style.display = 'none';
                                        }
                                    });

                                    if (!exists) {
                                        new PNotify({
                                            styling: 'jqueryui',
                                            type: 'error',
                                            hide: false,
                                            text: $translate.instant('You don’t have internet connection'),
                                        });
                                    }
                                }, 0);

                                let timerId = setTimeout(function tick() {
                                    fetch(`${$window.location.origin}/hr/person/authping`)
                                        .then((res) => {
                                            if (res.status === 404) {
                                                timerId = setTimeout(tick, 3000);
                                            } else {
                                                PNotify.removeAll();
                                                clearTimeout(timerId);
                                            }
                                        })
                                        .catch(() => {
                                            timerId = setTimeout(tick, 3000);
                                        });
                                }, 2000);
                            } else {
                                if (!errorResponse.status || errorResponse.config.url === 'js/Version.json') return;
                                var exists = false;
                                $('.ui-pnotify-text').each(function () {
                                    if ($(this).html() == $translate.instant('service temporarily unvailable')) {
                                        exists = true;
                                    }
                                });
                                if (!exists) {
                                    notificationService.error($translate.instant('service temporarily unvailable'));
                                }
                                return $q.reject(errorResponse);
                            }
                    }
                },
            };
        },
    ]);
