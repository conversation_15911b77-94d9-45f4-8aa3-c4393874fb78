angular.module('services.vacancy', ['ngResource']).factory('Vacancy', [
    '$resource',
    'serverAddress',
    '$rootScope',
    '$q',
    '$http',
    'ScopeService',
    'Service',
    '$location',
    function ($resource, serverAddress, $rootScope, $q, $http, ScopeService, Service, $location) {
        var options;
        var vacancy = $resource(
            serverAddress + '/vacancy/:param',
            { param: '@param' },
            {
                all: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'get',
                    },
                },
                add: {
                    method: 'PUT',
                    params: {
                        param: 'add',
                    },
                },
                tab: {
                    method: 'GET',
                    params: {
                        param: 'tab',
                    },
                },
                setInterviewStatus: {
                    method: 'POST',
                    params: {
                        param: 'setInterviewStatus',
                    },
                },
                setDatePaymentOrFinish: {
                    method: 'POST',
                    params: {
                        param: 'setDatePaymentOrFinish',
                    },
                },
                edit: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'edit',
                    },
                },
                getAllCategories: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getCategories',
                    },
                },
                editForRecommendation: {
                    method: 'POST',
                    params: {
                        param: 'editForRecommendation',
                    },
                },
                one: {
                    method: 'GET',
                    params: {
                        param: 'get',
                    },
                },
                editInterview: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeInterview',
                    },
                },
                editInterviews: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeInterviews',
                    },
                },
                addInterview: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'setInterview',
                    },
                },
                addResponsible: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'setResponsible',
                    },
                },
                removeResponsible: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'removeResponsible',
                    },
                },
                changeState: {
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    method: 'POST',
                    params: {
                        param: 'changeState',
                    },
                },
                addFile: {
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    method: 'POST',
                    params: {
                        param: 'addFile',
                    },
                },
                removeFile: {
                    method: 'GET',
                    params: {
                        param: 'removeFile',
                    },
                },
                recalls: {
                    method: 'GET',
                    params: {
                        param: 'recalls',
                    },
                },
                oneRecall: {
                    method: 'GET',
                    params: {
                        param: 'recall',
                    },
                },
                getEvents: {
                    method: 'POST',
                    params: {
                        param: 'getEvents',
                    },
                },
                recallRewieved: {
                    method: 'GET',
                    params: {
                        param: 'recallRewieved',
                    },
                },
                setMessage: {
                    method: 'POST',
                    params: {
                        param: 'setMessage',
                    },
                },
                addFileFromCache: {
                    method: 'GET',
                    params: {
                        param: 'addFile',
                    },
                },
                addPublish: {
                    method: 'GET',
                    params: {
                        param: 'addPublish',
                    },
                },
                deletePublish: {
                    method: 'GET',
                    params: {
                        param: 'deletePublish',
                    },
                },
                getAdvices: {
                    method: 'GET',
                    params: {
                        param: 'getAdvices',
                    },
                },
                changeInterviewDate: {
                    method: 'POST',
                    params: {
                        param: 'changeInterviewDate',
                    },
                },
                changeInterviewMessage: {
                    method: 'POST',
                    params: {
                        param: 'changeInterviewMessage',
                    },
                },
                createMeetEvent: {
                    method: 'POST',
                    params: {
                        param: 'createMeetEvent',
                    },
                },
                createGoogleMeetEvent: {
                    method: 'POST',
                    params: {
                        param: 'createGoogleMeetEvent',
                    },
                },
                sendInterviewCreateMail: {
                    method: 'POST',
                    params: {
                        param: 'sendInterviewCreateMail',
                    },
                },
                sendInterviewUpdateMail: {
                    method: 'POST',
                    params: {
                        param: 'sendInterviewUpdateMail',
                    },
                },
                getVacancyResponsibles: {
                    method: 'POST',
                    params: {
                        param: 'getResponsibles',
                    },
                },
                getVacancyExampleForLogoDemo: {
                    method: 'GET',
                    params: {
                        param: 'getVacancyExampleForLogoDemo',
                    },
                },
                removeInterview: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'removeInterview',
                    },
                },
                setMessageToCandidate: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'messageInterview',
                    },
                },
                changeInterviewEmployeeDetail: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'changeInterviewEmployeeDetail',
                    },
                },
                getWithLastAction: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getWithLastAction',
                    },
                },
                getCandidatesInStages: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'interview/get',
                    },
                },
                getCounts: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'interview/getCounts',
                    },
                },
                getSentInvites: {
                    method: 'GET',
                    params: {
                        param: 'interview/getSentInvites',
                    },
                },
                setInterviewList: {
                    method: 'POST',
                    params: {
                        param: 'setInterviewList',
                    },
                },
                copyCandidates: {
                    method: 'POST',
                    params: {
                        param: 'copyCandidates',
                    },
                },
                deleteCandidates: {
                    method: 'POST',
                    params: {
                        param: 'removeInterviewList',
                    },
                },
                hideState: {
                    method: 'GET',
                    params: {
                        param: 'hideState',
                    },
                },
                openHideState: {
                    method: 'GET',
                    params: {
                        param: 'openHideState',
                    },
                },
                getVacanciesForReport: {
                    method: 'POST',
                    params: {
                        param: 'getVacanciesForReport',
                    },
                },
                changeVacanciesForCandidatesAccess: {
                    method: 'POST',
                    params: {
                        param: 'changeVacanciesForCandidatesAccess',
                    },
                },
                setInterviewAndNotify: {
                    method: 'POST',
                    params: {
                        param: 'setInterviewAndNotify',
                    },
                },
                changeInterviewAndNotify: {
                    method: 'POST',
                    params: {
                        param: 'changeInterviewAndNotify',
                    },
                },
                saveImg: {
                    method: 'POST',
                    params: {
                        param: 'saveImg',
                    },
                },
                autocompleteVacancies: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'autocomplete',
                    },
                },
                getSkillsByCategory: {
                    method: 'GET',
                    params: {
                        param: 'getSkillsByCategory',
                    },
                },
                getSkillAutocomplete: {
                    method: 'POST',
                    params: {
                        param: 'skill-autocomplete',
                    },
                },
                autocompleteVacancies: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'autocomplete',
                    },
                },
                removeImg: {
                    method: 'POST',
                    params: {
                        param: 'removeImg',
                    },
                },
                addCopy: {
                    method: 'POST',
                    params: {
                        param: 'addCopy',
                    },
                },
                hideVacancy: {
                    method: 'GET',
                    params: {
                        param: 'hideVacancy',
                    },
                },
                saveVacancyFilterValues: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'saveVacancyFilterValues',
                    },
                },
                getVacancyFilterTemplate: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'getVacancyFilterTemplate',
                    },
                },
                getVacanciesWithActivityByPeriod: {
                    method: 'POST',
                    params: {
                        param: 'getVacanciesWithActivityByPeriod',
                    },
                },
                getByLocalId: {
                    method: 'POST',
                    params: {
                        param: 'getByLocalId',
                    },
                },
                getByIds: {
                    method: 'POST',
                    params: {
                        param: 'getByIds',
                    },
                },
                setPriority: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'setPriority',
                    },
                },
                validate: {
                    method: 'POST',
                    params: {
                        param: 'validate',
                    },
                },
                markRecalls: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'markRecalls',
                    },
                },
                getSortField: {
                    method: 'GET',
                    params: {
                        param: 'sortField',
                    },
                },
                sortField: {
                    method: 'POST',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'sortField',
                    },
                },
                getPipeline: {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'kanban/interviews',
                    },
                },
                changePipelineIndex: {
                    method: 'PUT',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    params: {
                        param: 'kanban/interviews/changeIndex',
                    },
                },
            },
        );

        vacancy.requestHideState = function (params) {
            $rootScope.loading = true;

            return new Promise((resolve, reject) => {
                vacancy.hideState(
                    params,
                    (resp) => resolve(resp),
                    (error) => reject(error),
                );
            });
        };

        vacancy.requestOpenHideState = function (params) {
            $rootScope.loading = true;

            return new Promise((resolve, reject) => {
                vacancy.openHideState(
                    params,
                    (resp) => resolve(resp),
                    (error) => reject(error),
                );
            });
        };

        vacancy.requestChangeVacanciesForCandidatesAccess = (access, vacancyId) =>
            vacancy.changeVacanciesForCandidatesAccess(
                {
                    vacancyId,
                    vacanciesForCandidatesAccess: access ? 'privateAccess' : 'publicAccess',
                },
                (resp) => {},
            );
        vacancy.onGetSkillsByCategory = function (param) {
            return new Promise((resolve, reject) => {
                vacancy.getSkillsByCategory(
                    { category: param },
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.onGetSkillAutocomplete = function (param) {
            const withImported = $location.$$url === '/candidates' && param?.length >= 3;
            return new Promise((resolve, reject) => {
                vacancy.getSkillAutocomplete(
                    { skillName: param, withImported },
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.onAddVacancy = function (param) {
            return new Promise((resolve, reject) => {
                vacancy.add(
                    param,
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.onAddCopy = function (param) {
            return new Promise((resolve, reject) => {
                vacancy.addCopy(
                    param,
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.onGetAllCategories = function () {
            return new Promise((resolve, reject) => {
                vacancy.getAllCategories(
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.interviewStatusNew = function () {
            return [
                {
                    vacancyType: 'simpleVacancy',
                    used: true,
                    status: [
                        {
                            value: 'applied_people',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: true,
                            count: 0,
                            forAdd: true,
                            movable: false,
                        },
                        {
                            value: 'longlist',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: true,
                            active_color: 'longlist_color',
                            count: 0,
                            googleCalendarPrefix: '',
                            forAdd: true,
                            movable: false,
                        },
                        {
                            value: 'shortlist',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: true,
                            active_color: 'shortlist_color',
                            count: 0,
                            forAdd: true,
                            movable: true,
                        },
                        {
                            value: 'test_task',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            single: true,
                            added: false,
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },

                        {
                            value: 'interview',
                            forHistory: true,
                            withDate: true,
                            defaultS: false,
                            single: true,
                            added: true,
                            active_color: 'interview_color',
                            count: 0,
                            forAdd: true,
                            movable: true,
                        },
                        {
                            value: 'interview_with_the_boss',
                            forHistory: true,
                            withDate: true,
                            single: false,
                            defaultS: false,
                            added: false,
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },

                        {
                            value: 'security_check',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: false,
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'tech_screen',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'hr_interview',
                            forHistory: true,
                            withDate: true,
                            defaultS: false,
                            added: false,
                            single: true,
                            active_color: 'interview_color',
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'tech_interview',
                            forHistory: true,
                            withDate: true,
                            defaultS: false,
                            added: false,
                            single: true,
                            active_color: 'interview_color',
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'interview_with_the_client',
                            forHistory: true,
                            withDate: true,
                            defaultS: false,
                            single: true,
                            added: false,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'sent_offer',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            active_color: 'interview_color',
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },
                        {
                            value: 'accept_offer',
                            forHistory: true,
                            withDate: true,
                            defaultS: false,
                            added: false,
                            single: true,
                            active_color: 'interview_color',
                            count: 0,
                            forAdd: false,
                            movable: true,
                        },

                        {
                            value: 'approved',
                            forHistory: false,
                            withDate: false,
                            defaultS: true,
                            single: false,
                            added: true,
                            active_color: 'approved_color',
                            count: 0,
                            forAdd: false,
                            movable: false,
                        },
                        {
                            value: 'notafit',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: true,
                            active_color: 'notafit_color',
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'declinedoffer',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            single: false,
                            added: true,
                            active_color: 'declinedoffer_color',
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'no_response',
                            forHistory: false,
                            withDate: false,
                            defaultS: false,
                            added: true,
                            single: false,
                            active_color: 'no_response_color',
                            count: 0,
                            forAdd: true,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'no_contacts',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'is_not_looking_for_job',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'accepted_counter_offer',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'found_another_job',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'offer_declined',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                        {
                            value: 'probation_failure',
                            forHistory: true,
                            withDate: false,
                            defaultS: false,
                            added: false,
                            single: true,
                            isEnclosed: true,
                            count: 0,
                            forAdd: false,
                            movable: false,
                            type: 'refuse',
                        },
                    ],
                },
            ];
        };
        vacancy.standardInterviewStatus = function (type) {
            let stages;
            if (type === 'simple_vacancy') {
                stages = [
                    {
                        value: 'longlist',
                        active_color: 'longlist_color',
                        name: 'long_list',
                    },
                    {
                        value: 'shortlist',
                        active_color: 'shortlist_color',
                        name: 'short_list',
                    },
                    {
                        value: 'interview',
                        active_color: 'interview_color',
                        name: 'interview',
                    },
                    {
                        value: 'approved',
                        active_color: 'approved_color',
                        name: 'approved',
                    },
                    {
                        value: 'notafit',
                        active_color: 'notafit_color',
                        name: 'not_a_fit',
                    },
                    {
                        value: 'declinedoffer',
                        active_color: 'declinedoffer_color',
                        name: 'declined_offer',
                    },
                ];
            } else if (type === 'it_vacancy') {
                stages = [
                    {
                        value: 'longlist',
                        active_color: 'longlist_color',
                        name: 'long_list',
                    },
                    {
                        value: 'shortlist',
                        active_color: 'shortlist_color',
                        name: 'short_list',
                    },
                    {
                        value: 'tech_interview',
                        active_color: 'interview_color',
                        name: 'tech_interview',
                    },
                    {
                        value: 'hr_interview',
                        active_color: 'interview_color',
                        name: 'hr_interview',
                    },
                    {
                        value: 'job_offer',
                        active_color: 'interview_color',
                        name: 'job_offer',
                    },
                    {
                        value: 'approved',
                        active_color: 'approved_color',
                        name: 'approved',
                    },
                    {
                        value: 'notafit',
                        active_color: 'notafit_color',
                        name: 'not_a_fit',
                    },
                    {
                        value: 'declinedoffer',
                        active_color: 'declinedoffer_color',
                        name: 'declined_offer',
                    },
                ];
            }
            return stages;
        };

        vacancy.getInterviewStatus = function () {
            return [
                { name: 'Long list', value: 'longlist' },
                { name: 'Short list', value: 'shortlist' },
                { name: 'Interview', value: 'interview' },
            ];
        };

        vacancy.getInterviewStatusFull = function () {
            return [
                { name: 'Long list', value: 'longlist' },
                { name: 'Short list', value: 'shortlist' },
                { name: 'Interview', value: 'interview' },
                { name: 'notafit', value: 'notafit' },
                { name: 'declinedoffer', value: 'declinedoffer' },
            ];
        };

        vacancy.status = function () {
            return [
                { value: 'open', name: 'open' },
                { value: 'inwork', name: 'in work' },
                { value: 'completed', name: 'completed' },
                { value: 'onhold', name: 'wait' },
                { value: 'payment', name: 'payment' },
                { value: 'replacement', name: 'replacement' },
                { value: 'canceled', name: 'canceled' },
                { value: 'deleted', name: 'deleted' },
                { value: 'recommendation', name: 'recommendation' },
            ];
        };

        vacancy.getStatusAssociated = function () {
            return {
                open: 'open',
                onhold: 'wait',
                inwork: 'in work',
                payment: 'payment',
                completed: 'completed',
                canceled: 'canceled',
                deleted: 'deleted',
                recommendation: 'recommendation',
            };
        };

        vacancy.searchOptions = function () {
            return options;
        };
        vacancy.searchStatus = function () {
            return statusing;
        };
        vacancy.setOptions = function (name, value) {
            options[name] = value;
        };

        vacancy.init = function () {
            options = {
                state: null,
                id: null,
                creator: null,
                regions: null,
                org: null,
                responsible: null,
                sort: null,
                sortOrder: null,
                city: null,
                country: null,
                personId: null,
                ids: null,
                searchFullTextType: null,
                page: { number: 0, count: 15 },
                words: null,
                position: null,
                clientId: null,
            };
        };
        vacancy.init();

        vacancy.getAllVacansies = (params) =>
            $q((resolve, reject) =>
                vacancy.getVacanciesForReport(
                    params,
                    (response) => resolve(response),
                    (error) => reject(error),
                ),
            );
        vacancy.onAddResponsible = function (params) {
            $rootScope.loading = true;
            return new Promise((resolve, reject) => {
                vacancy.addResponsible(
                    params,
                    (response) => {
                        if (response.status === 'ok') {
                            resolve(response);
                        } else {
                            reject(response);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };
        vacancy.requestGetCandidatesInStages = function (params) {
            $rootScope.loading = true;
            return new Promise((resolve, reject) => {
                vacancy.getCandidatesInStages(
                    params,
                    (response) => {
                        vacancy.candidateLastRequestParams = params;
                        vacancy.getCandidate = response.objects.map((item) => item.candidateId.localId);
                        localStorage.setItem('candidateLastRequestParams', JSON.stringify(params));
                        localStorage.setItem('getAllCandidates', JSON.stringify(vacancy.getCandidate));
                        resolve(response, params);
                    },
                    () => {
                        reject();
                    },
                );
            });
        };
        vacancy.uploadPromoLogo = function (fileUp) {
            var FD = new FormData();
            var blobBin = atob(fileUp.split(',')[1]);
            var array = [];
            for (var i = 0; i < blobBin.length; i++) {
                array.push(blobBin.charCodeAt(i));
            }
            var file = new Blob([new Uint8Array(array)], { type: 'image/png' });
            FD.append('image', file);
            return $http({
                url: serverAddress + '/vacancy/saveImg/' + $rootScope.vacancy.vacancyId,
                method: 'POST',
                data: FD,
                withCredentials: true,
                headers: { 'Content-Type': undefined },
                transformRequest: angular.identity,
            });
        };

        vacancy.getAllVacancies = function () {
            return new Promise((resolve, reject) => {
                vacancy.all(
                    vacancy.searchOptions(),
                    (response) => {
                        resolve(response);
                    },
                    (error) => reject(error),
                );
            });
        };

        vacancy.getSortStatus = function () {
            return new Promise((resolve, reject) => {
                vacancy.getSortField(
                    (response) => {
                        resolve(response);
                    },
                    (error) => reject(error),
                );
            });
        };

        vacancy.getVacancy = function (params) {
            return new Promise((resolve, reject) => {
                vacancy.one(
                    params,
                    (response) => {
                        resolve(response);
                    },
                    (error) => reject(error),
                );
            });
        };

        vacancy.experience = function () {
            return [
                { value: 'e00_no_experience' },
                { value: 'e01_less_than1year' },
                { value: 'e1_1year' },
                { value: 'e2_2years' },
                { value: 'e3_3years' },
                { value: 'e4_4years' },
                { value: 'e5_5years' },
                { value: 'e6_10years' },
            ];
        };
        vacancy.currency = Service.currency;

        vacancy.employmentType = function () {
            return [
                'fullEmployment',
                'underemployment',
                'remote',
                'trainingAndPractice',
                'projectWork',
                'temporaryWork',
                'relocate',
                'shiftWork',
            ];
        };

        vacancy.changeVisibility = function (state, id) {
            return new $q((resolve, reject) => {
                vacancy.hideVacancy(
                    {
                        vacancyId: id,
                        hide: state,
                    },
                    function (resp) {
                        resolve(resp);
                    },
                    function (error) {
                        reject(error);
                    },
                );
            });
        };

        function setSortOption(val) {
            if (val === options['sort']) {
                switchSortOrder();
            } else {
                options['sort'] = val;
                options['sortOrder'] = 'ASC';
            }
        }
        vacancy.onGetVacanciesWithActivityByPeriod = function (params) {
            return new Promise((resolve, reject) => {
                vacancy.getVacanciesWithActivityByPeriod(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') resolve(resp);
                        else reject(resp);
                    },
                    (error) => reject(error),
                );
            });
        };

        vacancy.onGetByLocalId = function (params) {
            return new Promise((resolve, reject) => {
                vacancy.getByLocalId(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => {
                        reject(error);
                    },
                );
            });
        };

        vacancy.onGetByLocalIds = function (params) {
            return new Promise((resolve, reject) => {
                vacancy.getByIds(
                    params,
                    (resp) => {
                        if (resp.status === 'ok') {
                            resolve(resp);
                        } else {
                            reject(resp);
                        }
                    },
                    (error) => reject(error),
                );
            });
        };

        vacancy.onGetEvents = new PromiseWrapper('getEvents');
        vacancy.onGetTab = new PromiseWrapper('tab');
        vacancy.onGetWithLastAction = new PromiseWrapper('getWithLastAction');
        vacancy.onSetPriority = new PromiseWrapper('setPriority');
        vacancy.onMarkRecalls = new PromiseWrapper('markRecalls');
        vacancy.onGetVacancyFilterTemplate = new PromiseWrapper('getVacancyFilterTemplate');
        vacancy.onSaveVacancyFilterValues = new PromiseWrapper('saveVacancyFilterValues');
        vacancy.onGetCounts = new PromiseWrapper('getCounts');
        vacancy.onEditVacancy = new PromiseWrapper('edit');
        vacancy.onGetCandidatesInStages = new PromiseWrapper('getCandidatesInStages');
        vacancy.onValidateVacancy = new PromiseWrapper('validate');
        vacancy.onRemoveInterview = new PromiseWrapper('removeInterview');
        vacancy.onGetVacancyResponsibles = new PromiseWrapper('getVacancyResponsibles');
        vacancy.onGetSentInvites = new PromiseWrapper('getSentInvites');
        vacancy.onGetPipeline = new PromiseWrapper('getPipeline');
        vacancy.changeIndex = new PromiseWrapper('changePipelineIndex');
        vacancy.onChangeInterview = new PromiseWrapper('editInterview');
        vacancy.onChangeInterviewAndNotify = new PromiseWrapper('changeInterviewAndNotify');
        vacancy.onChangeInterviews = new PromiseWrapper('editInterviews');
        vacancy.onSetInterviewAndNotify = new PromiseWrapper('setInterviewAndNotify');
        vacancy.onSetInterview = new PromiseWrapper('addInterview');
        vacancy.onMessageInterview = new PromiseWrapper('setMessageToCandidate');
        vacancy.onCopyCandidates = new PromiseWrapper('copyCandidates');
        vacancy.onDeleteCandidates = new PromiseWrapper('deleteCandidates');
        vacancy.onGetCandidatesInStages = new PromiseWrapper('getCandidatesInStages');
        vacancy.onGetAllVacancies = new PromiseWrapper('autocompleteVacancies');

        function PromiseWrapper(request) {
            return function (params) {
                return new Promise((resolve, reject) => {
                    vacancy[request](
                        params,
                        (resp) => {
                            if (resp.status === 'ok') {
                                resolve(resp);
                            } else {
                                reject(resp);
                            }
                        },
                        (error) => {
                            reject(error);
                        },
                    );
                });
            };
        }

        vacancy.search = {
            _params: {
                city: null,
                clientId: null,
                country: null,
                countries: null,
                scopeCountry: null,
                creator: null,
                id: null,
                ids: null,
                org: null,
                page: { number: 0, count: 15 },
                personId: null,
                position: null,
                regions: null,
                responsible: null,
                responsibleIds: null,
                searchFullTextType: null,
                state: null,
                states: null,
                sort: null,
                containsWord: true,
                words: null,
            },

            resetPagination: false,

            _getUrlParams() {
                const urlParams = Service.getUrlVars($location.$$absUrl);
                if (urlParams.hasOwnProperty('clientId')) this.setParam('clientId', urlParams['clientId']);
                if (urlParams.hasOwnProperty('states')) {
                    let states = urlParams['states'].split(',');
                    this.setParam('states', states);
                }
            },

            _setScopeParams() {
                const scopeParam = ScopeService.getActiveScopeObject();
                if (scopeParam.name === 'onlyMy') {
                    this.setParam('personId', $rootScope.me.userId);
                    this.setParam('scopeCountry', null);
                } else if (scopeParam.value) {
                    this.setParam('personId', null);
                    this.setParam('scopeCountry', scopeParam.value.value);
                    this.setParam('country', scopeParam.value.value);
                } else {
                    this.setParam('personId', null);
                    this.setParam('scopeCountry', null);
                }
            },

            setParam(param, value) {
                switch (param) {
                    case 'sort':
                        this.setSortOption(value);
                        break;
                    default:
                        this._params[param] = value;
                }
                this._params[param] = value;
            },

            set params(params) {
                if (params.customFields) {
                    params.customFields.forEach((field) => {
                        if (field.field.type === 'date') {
                            field.dateTimeValue = $rootScope.TimeMinusTimeZone(field.dateTimeValue);
                        }
                    });
                }

                this._params = Object.assign(this._params, params);
            },

            get params() {
                this._setScopeParams();
                this._getUrlParams();
                return this._params;
            },

            _searchValues: {},
            _customFieldsValues: {},

            set searchValues(values) {
                this._searchValues = angular.copy(values);
            },

            get searchValues() {
                return angular.equals({}, this._searchValues) ? null : this._searchValues;
            },

            get customFieldsValues() {
                return angular.equals({}, this._customFieldsValues) ? null : this._customFieldsValues;
            },

            set customFieldsValues(values) {
                this._customFieldsValues = angular.copy(values);
            },

            resetFieldsValues() {
                this._searchValues = {};
                this._customFieldsValues = {};
            },

            setSortOption(val) {
                if (val === this._params['sort']) {
                    this.switchSortOrder();
                } else {
                    this._params['sort'] = val;
                    this._params['sortOrder'] = $rootScope.sortType;
                }
            },

            switchSortDate(sortParam) {
                this._params['sort'] = sortParam;
                if (this._params['sortOrder'] === 'DESC') {
                    this._params['sortOrder'] = 'ASC';
                    vacancy.sortField({
                        sortField: 'openDate',
                        sortOrder: 'DESC',
                    });
                } else {
                    $rootScope.resetTable();
                }
            },

            switchStartDc() {
                if ($rootScope.sortType === 'ASC') {
                    this._params['sortOrder'] = 'DESC';
                    this._params['sort'] = 'dc';
                } else {
                    this._params['sortOrder'] = 'ASC';
                    this._params['sort'] = 'dc';
                }
            },

            switchSortOrder() {
                if (this._params['sortOrder'] === 'DESC') {
                    this._params['sortOrder'] = 'ASC';
                } else {
                    this._params['sortOrder'] = 'DESC';
                }
            },

            resetParams() {
                for (let param in this._params) {
                    switch (param) {
                        case 'page':
                            this._params[param] = { number: 0, count: 15 };
                            break;
                        case 'sort':
                            this._params['sort'] = 'dc';
                            this._params['sortOrder'] = 'ASC';
                            break;
                        default:
                            this._params[param] = null;
                    }
                }
            },
        };

        vacancy.isLoadVacancies = true;

        vacancy.setIsLoadVacancies = function (condition) {
            vacancy.isLoadVacancies = condition;
        };

        vacancy.getIsLoadVacancies = function () {
            return vacancy.isLoadVacancies;
        };

        vacancy.experienceOptionsMap = (item) => {
            if (item.value === 'e00_no_experience') {
                return { label: 'experience_assoc.can_be_e00_no_experience', value: 'e00_no_experience' };
            }
            return item;
        };

        return vacancy;
    },
]);
