angular.module('services.search.searchField', []).factory('searchField', [
    'Service',
    '$filter',
    function (Service, $filter) {
        return class Field {
            constructor({ name, type, state, data, placeholder, path }) {
                this.name = name;
                this.type = type;
                this.data = data;
                this.state = state;
                this.path = path;
                this.placeholder = placeholder;
                this.value = null;
                this.noValue = false;
            }

            set value(value) {
                if (this.name === 'employmentType') {
                    console.log('2 - set value', this);
                }
                this._value = value;
            }

            get value() {
                return this._value;
            }

            isLoaded() {
                return this.placeholder;
            }

            reset({ name, callback = function () {} }) {
                console.log('1 - reset');
                this.value = null;
                this.noValue = false;
                callback(this.value);
            }

            changeState({ name, callback }) {
                this.state.isSelected = !this.state.isSelected;
                this.reset({ callback });
            }

            changeActiveState({ name, callback }) {
                this.state.isActive = !this.state.isActive;
                this.reset({ name, callback });
            }

            toggleNoValue() {
                this.noValue = !this.noValue;
                this.value = null;
            }

            getLabelValue() {
                if (this.noValue) return 'no_value';

                return this._isNotEqualPlaceholder()
                    ? Service.getObjectPropertyValueByPath(this, this.path.label)
                    : null;
            }

            getValue() {
                if (this.name === 'employmentType') {
                    console.log('3 - getValue');
                    // console.log('getValue', this);
                    // console.log(
                    //     this._isNotEqualPlaceholder()
                    //         ? Service.getObjectPropertyValueByPath(this, this.path.value)
                    //         : null,
                    // );
                }

                return this._isNotEqualPlaceholder()
                    ? Service.getObjectPropertyValueByPath(this, this.path.value)
                    : null;
            }

            _isNotEqualPlaceholder() {
                return (
                    this.value !== this.placeholder &&
                    this.value &&
                    this.value !== $filter('translate')(this.placeholder) &&
                    Service.getObjectPropertyValueByPath(this, this.path.label) !== this.placeholder
                );
            }
        };
    },
]);
