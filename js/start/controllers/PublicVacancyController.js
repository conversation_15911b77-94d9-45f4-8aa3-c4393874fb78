controller.controller('PublicVacancyController', [
    '$rootScope',
    '$scope',
    '$filter',
    '$location',
    '$routeParams',
    '$sce',
    '$translate',
    'Service',
    'notificationService',
    'FileInit',
    'serverAddress',
    '$window',
    'Company',
    '$uibModal',
    'Person',
    '$anchorScroll',
    function (
        $rootScope,
        $scope,
        $filter,
        $location,
        $routeParams,
        $sce,
        $translate,
        Service,
        notificationService,
        FileInit,
        serverAddress,
        $window,
        Company,
        $uibModal,
        Person,
        $anchorScroll,
    ) {
        $rootScope.activePublicController = 'PublicVacancyController';

        setUserLang();

        $rootScope.closeModal = function () {
            $scope.modalInstance.close();
            $('body').removeClass('modal-open-public-vacancy-form');
        };
        $scope.message = 'def';
        $rootScope.header = 'two';
        $scope.serverAddress = serverAddress;
        $scope.request = {
            name: null,
            lastName: null,
            phone: null,
            message: null,
            vacancyId: $scope.vacancyId,
            fileId: null,
        };

        $.getScript('https://platform.linkedin.com/in.js?async=true', function success() {
            if (IN) {
                IN.init({
                    api_key: apiKey.linkedIn.api_key,
                    scope: 'r_emailaddress w_share',
                });
            }
        });
        $.getScript('//connect.facebook.net/en_UK/all.js', function () {
            if (FB) {
                FB.init({
                    appId: apiKey.facebook.appId,
                    version: 'v2.9',
                });
            }
        });

        $(window).scroll(function () {
            applyBtnEvent();
        });

        function applyBtnEvent() {
            if ($('.vacancy-info').offset() && $(window).scrollTop() >= $('.vacancy-info').offset().top - 10) {
                $('.apply-buttons').addClass('fixed');
            } else {
                if ($('.apply-buttons').hasClass('fixed')) {
                    $('.apply-buttons').removeClass('fixed');
                }
            }
        }

        $scope.share = function (sourse) {
            if ($scope.companyLogo != undefined && $scope.companyLogo !== '') {
                $scope.publicImgLink = $scope.companyLogo
                    ? $location.$$protocol +
                      '://' +
                      $location.$$host +
                      $scope.serverAddress +
                      '/getlogo?id=' +
                      $scope.companyLogo
                    : null;
            } else if ($scope.vacancy.imageId) {
                $scope.publicImgLink = $scope.vacancy.imageId
                    ? $location.$$protocol +
                      '://' +
                      $location.$$host +
                      $scope.serverAddress +
                      '/getlogo?id=' +
                      $scope.vacancy.imageId
                    : null;
            } else {
                $scope.publicImgLink = 'https://cleverstaff.net/images/sprite/icon_128_128_png.png';
            }
            $scope.publicDescr = '';
            let linkForFacebook =
                $location.$$protocol + '://' + $location.$$host + '/i/vacancy-' + $scope.vacancy.localId;
            let link = $location.$$protocol + '://' + $location.$$host + '/i/pv/' + $scope.vacancy.localId;

            if (!$scope.vacancy.descr.match(/<[^>]*/)) {
                $scope.vacancy.descr = '<p>' + $scope.vacancy.descr + '</p>';
            }
            angular.forEach(
                angular.element($scope.vacancy.descr).text().replace('\r\n', '\n').split('\n'),
                function (val) {
                    if (val !== undefined && val !== '') {
                        $scope.publicDescr += val + ' ';
                    }
                },
            );

            if ($scope.serverAddress === '/hrdemo') {
                link = $location.$$protocol + '://' + $location.$$host + '/di#/pv/' + $scope.vacancy.localId;
            }
            if (sourse === 'linkedin') {
                if (!IN.User.isAuthorized()) {
                    IN.User.authorize(function () {
                        IN.API.Raw('/people/~/shares')
                            .method('POST')
                            .body(
                                JSON.stringify({
                                    content: {
                                        title: $filter('translate')('Vacancy') + ' ' + $scope.vacancy.position,
                                        description: $scope.publicDescr,
                                        'submitted-url': link,
                                        'submitted-image-url': $scope.publicImgLink,
                                    },
                                    visibility: {
                                        code: 'anyone',
                                    },
                                    comment: '',
                                }),
                            )
                            .result(function (r) {
                                notificationService.success($filter('translate')('Vacancy posted on your LinkedIn'));
                            })
                            .error(function (r) {
                                notificationService.error(r.message);
                            });
                    }, 'w_share');
                } else {
                    IN.API.Raw('/people/~/shares')
                        .method('POST')
                        .body(
                            JSON.stringify({
                                content: {
                                    title: $filter('translate')('Vacancy') + ' ' + $scope.vacancy.position,
                                    description: $scope.publicDescr,
                                    'submitted-url': link,
                                    'submitted-image-url': $scope.publicImgLink,
                                },
                                visibility: {
                                    code: 'anyone',
                                },
                                comment: '',
                            }),
                        )
                        .result(function (r) {
                            notificationService.success($filter('translate')('Vacancy posted on your LinkedIn'));
                        })
                        .error(function (r) {
                            notificationService.error(r.message);
                        });
                }
            }
            if (sourse === 'facebook') {
                if (FB) {
                    FB.getLoginStatus(function (response) {
                        if (response.status === 'connected') {
                            FB.ui(
                                {
                                    method: 'share_open_graph',
                                    action_type: 'og.shares',
                                    action_properties: JSON.stringify({
                                        object: {
                                            'og:url': linkForFacebook,
                                            'og:title': $filter('translate')('Vacancy') + ' ' + $scope.vacancy.position,
                                            'og:description': $filter('limitTo')($scope.publicDescr, 100, 0),
                                            'og:image': $scope.publicImgLink,
                                        },
                                    }),
                                },
                                function (response) {
                                    if (response.error_message) {
                                        console.error($filter('translate')('Vacancy was not shared'));
                                    } else {
                                        notificationService.success(
                                            $filter('translate')('Vacancy posted on your Facebook'),
                                        );
                                    }
                                },
                            );
                        } else {
                            FB.login(function () {
                                FB.ui(
                                    {
                                        method: 'share_open_graph',
                                        action_type: 'og.shares',
                                        action_properties: JSON.stringify({
                                            object: {
                                                'og:url': linkForFacebook,
                                                'og:title':
                                                    $filter('translate')('Vacancy') + ' ' + $scope.vacancy.position,
                                                'og:description': $filter('limitTo')($scope.publicDescr, 100, 0),
                                                'og:image': $scope.publicImgLink,
                                            },
                                        }),
                                    },
                                    function (response) {
                                        if (response.error_message) {
                                            console.error($filter('translate')('Vacancy was not shared'));
                                        } else {
                                            notificationService.success(
                                                $filter('translate')('Vacancy posted on your Facebook'),
                                            );
                                        }
                                    },
                                );
                            });
                        }
                    });
                }
            }
        };

        $scope.textToLengthPx = function (text) {
            const element = document.createElement('canvas');
            const context = element.getContext('2d');
            context.font = '14px Helvetica-Normal';

            return Math.floor(context.measureText($translate.instant(text)).width + 2);
        };

        $scope.addRecallFromLinkedIn = function () {
            IN.User.authorize(function () {
                IN.API.Profile('me')
                    .fields([
                        'site-standard-profile-request',
                        'public-profile-url',
                        'first-name',
                        'last-name',
                        'email-address',
                        'phone-numbers',
                        'bound-account-types',
                        'headline',
                        'summary',
                        'specialties',
                        'positions',
                        'educations',
                    ])
                    .result(function (me) {
                        parseLinkedInInformationForRecall(me, $scope);
                        $scope.showRecallFromModal();
                    });
            });
        };
        $scope.show = true;
        FileInit.initFileOptionForPublicVacancy(
            $scope,
            'public',
            {
                allowedType: ['docx', 'doc', 'pdf', 'odt', 'jpg', 'jpeg'],
                maxSize: 5242880,
            },
            $filter,
        );

        $scope.selectFile = (value, event) => {
            event.target.value = '';
            $scope.currentField = value;
        };

        $scope.deleteFile = (id, field) => {
            field.files = field.files.filter((file) => file.attId !== id);
            if (field.files.length === 0) {
                field.errorFile = true;
            }
        };

        $scope.callbackFile = (var1, var2) => {
            let duplicate = null;
            if ($scope.currentField.type === 'file' && $scope.currentField.fileLimit === 1) {
                $scope.currentField.files = [];
            }

            if (!$scope.currentField.files) $scope.currentField.files = [];
            $scope.currentField?.files.forEach((file) => {
                if (file.name === var2) {
                    duplicate = true;
                }
            });
            if (!duplicate) {
                $scope.currentField.files.push({ name: var2, attId: var1 });
                notificationService.success($filter('translate')('history_info.added_file'));
            } else {
                notificationService.error($filter('translate')('history_info.duplicated_file'));
            }
            $scope.currentField.errorFile = false;
        };

        $scope.goToCompanyPage = function (flag = null) {
            if (flag) {
                $location.path(`/i/${$scope.vacancy.alias}-vacancies/description`);
            } else if (!flag && $scope.vacancy.alias) {
                $location.path(`/i/${$scope.vacancy.alias}-vacancies`);
            } else {
                $location.path(`/`);
            }
        };

        $scope.callbackFileError = function () {
            $scope.message = 'error_file';
        };
        var vacancyId = $location.$$path.split('vacancy-')[1];

        $scope.orgName = null;
        $scope.loadStatusForPublicVacancy = false;
        $scope.onTransformExperience = function (experience) {
            if (experience && experience.length) {
                let willPlusSkills = experience
                    .filter((item) => !item.mustHave)
                    .sort((a, b) => (a.experience < b.experience ? 1 : b.experience < a.experience ? -1 : 0));
                let requiredSkills = experience
                    .filter((item) => item.mustHave)
                    .sort((a, b) => (a.experience < b.experience ? 1 : b.experience < a.experience ? -1 : 0));
                if (requiredSkills && requiredSkills.length) {
                    $scope.requiredSkills = requiredSkills.reduce((prev, cur) => {
                        prev.push({
                            skill: cur.skill,
                            experience:
                                $rootScope.currentLang === 'ru'
                                    ? Service.onGetDesiredSkills[cur.experience].ru
                                    : Service.onGetDesiredSkills[cur.experience].en,
                            mustHave: cur.mustHave,
                        });
                        return prev;
                    }, []);
                }
                if (willPlusSkills && willPlusSkills.length) {
                    $scope.willPlusSkills = willPlusSkills.reduce((prev, cur) => {
                        prev.push({
                            skill: cur.skill,
                            experience:
                                $rootScope.currentLang === 'ru'
                                    ? Service.onGetDesiredSkills[cur.experience].ru
                                    : Service.onGetDesiredSkills[cur.experience].en,
                            mustHave: cur.mustHave,
                        });
                        return prev;
                    }, []);
                }
            }
        };

        function settingsLanguage() {
            if ($scope.googleRegion.region) {
                let location = $scope.googleRegion.region.googlePlaceId;
                let lang = $rootScope.currentLang;
                switch (lang) {
                    case 'ru':
                        $scope.translateRegion =
                            (location.countryRu ? location.countryRu : '') +
                            (location.countryRu && location.cityRu ? ', ' : '') +
                            (location.cityRu ? location.cityRu : '');
                        break;
                    case 'en':
                        $scope.translateRegion =
                            (location.countryEn ? location.countryEn : '') +
                            (location.countryEn && location.cityEn ? ', ' : '') +
                            (location.cityEn ? location.cityEn : '');
                        break;
                    case 'ua':
                        $scope.translateRegion =
                            (location.countryUa ? location.countryUa : '') +
                            (location.countryUa && location.cityUa ? ', ' : '') +
                            (location.cityUa ? location.cityUa : '');
                        break;
                }
            }
        }

        function setUserLang(lang) {
            let defaultLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;

            if (defaultLang.length > 2) {
                defaultLang.substr(0, 2);
            }

            $rootScope.currentLang = lang || defaultLang;
            $translate.use($rootScope.currentLang);
        }

        Person.onAuthPing()
            .then((resp) => {
                $rootScope.isActiveSession = resp.status === 'ok';
            })
            .then(Person.me)
            .then((resp) => {
                $rootScope.me = resp.object;
                setUserLang($rootScope.me.personParams.lang);
            })
            .catch((resp) => {
                console.error(resp);
            })
            .finally(() => {
                $rootScope.$$phase || $scope.$apply();
            });
        $scope.datepickerOfCustomEdit = '';

        // const removeLinkPrefix = function (link) {
        //     return link.replace(/^https?:\/\/(www\.)?/, '');
        // };
        //
        // const formatCompanyFacebookLink = function (link) {
        //     this.headerCompanyDetails = document.querySelector('.header-company-details');
        //
        //     const newLink = removeLinkPrefix(link);
        //     return newLink.length > Math.floor(this.headerCompanyDetails.offsetWidth / 10)
        //         ? `facebook ${$filter('translate')('page')}`.toLowerCase()
        //         : newLink;
        // };
        //
        // const formatCompanyWebSiteLink = function (link) {
        //     this.headerCompanyDetails = document.querySelector('.header-company-details');
        //
        //     const newLink = removeLinkPrefix(link);
        //     return newLink.length > Math.floor(this.headerCompanyDetails.offsetWidth / 10)
        //         ? `${newLink.split('/')[0]} ${$filter('translate')('page')}`.toLowerCase()
        //         : newLink;
        // };

        setTimeout(() => {
            Promise.all([getLangs(), getVacancies()])
                .then(([langs, resp]) => {
                    $scope.vacancyId = resp.object.vacancyId;
                    $scope.googleRegion = resp.object;
                    $scope.request.vacancyId = resp.object.vacancyId;
                    $rootScope.title =
                        resp.object.position + ' - ' + $filter('translate')('vacancy_in') + ' ' + resp.object.orgName;
                    $rootScope.vacancyName = resp.object.position;
                    if (resp.object.region != undefined) {
                        $rootScope.region = resp.object.region.fullName;
                    }
                    $scope.vacancy = resp.object;
                    $scope.recallTemp = JSON.parse(resp.object.recallTemplate.additionalInfo);
                    $scope.systems = JSON.parse(resp.object.recallTemplate.additionalInfo).system;
                    $scope.firstName = $scope.systems[0];
                    $scope.lastName = $scope.systems[1];
                    $scope.phoneSystem = $scope.systems[2];
                    $scope.emailSystem = $scope.systems[3];
                    $scope.vacancy.languages = transformLanguages(resp.object.languages);

                    if (!$scope.recallTemp.custom) {
                        $scope.recallTemp.custom = [];
                    }

                    $scope.recallTemp?.custom?.forEach((item) => {
                        if (item.type === 'file' || item.type === 'selectFile' || item.type === 'textFile') {
                            item.files = [];
                        }
                    });

                    $scope.similarVacancies =
                        (resp.objects &&
                            resp.objects.filter((vacancy) => vacancy.descr && vacancy.descr.length > 30)) ||
                        [];
                    $scope.orgName = resp.object.orgName || '';
                    $scope.onTransformExperience(resp.object.desiredSkills);

                    $rootScope.accountInformation = $rootScope.accountInformation
                        ? {
                              ...$rootScope.accountInformation,
                              ...resp.object.accountInformation,
                              companyName: resp.object.orgName,
                          }
                        : {
                              ...resp.object.accountInformation,
                              companyName: resp.object.orgName,
                          };

                    // if ($rootScope.accountInformation.companyFacebookPage) {
                    //     $rootScope.accountInformation.companyFacebookPageFormatted = formatCompanyFacebookLink(
                    //         $rootScope.accountInformation.companyFacebookPage,
                    //     );
                    // }
                    // if ($rootScope.accountInformation.companyWebSite) {
                    //     $rootScope.accountInformation.companyWebSiteFormatted = formatCompanyWebSiteLink(
                    //         $rootScope.accountInformation.companyWebSite,
                    //     );
                    // }

                    if ($rootScope.accountInformation.companyLocation) {
                        const parsedLocation = JSON.parse($rootScope.accountInformation.companyLocation);

                        $rootScope.accountInformation.companyLocation = Object.values(parsedLocation)
                            .filter((location) => location)
                            .join(', ');
                    }

                    $scope.isVacancyAvailable = !(
                        $scope.vacancy.status == 'deleted' ||
                        $scope.vacancy.status == 'canceled' ||
                        ($scope.vacancy.descr && $scope.vacancy.descr.length <= 30) ||
                        $scope.vacancy.status == 'payment' ||
                        $scope.vacancy.status == 'completed' ||
                        !$scope.vacancy.descr
                    );
                    $scope.companyPublicInfo = {};
                    $scope.companyPublicInfo.fb = $scope.vacancy.linkToCompanyFaceBookPage;
                    $scope.companyPublicInfo.companyWebSite = $scope.vacancy.linkToCompanySite;
                    $scope.companyPublicInfo.orgName = $scope.vacancy.orgName;
                    $scope.companyPublicInfo.alias = $scope.vacancy.alias;
                    $scope.vacancyFound = true;
                    $scope.loadStatusForPublicVacancy = true;
                })
                .then(() => {
                    settingsLanguage();
                })
                .catch((error) => {
                    console.error(error);
                    $scope.vacancyFound = false;
                })
                .finally(() => {
                    setTimeout(() => {
                        $rootScope.loading = false;
                        $rootScope.$$phase || $scope.$apply();
                        $anchorScroll('content-block');
                    }, 100);
                });
        }, 1000);
        $scope.to_trusted = function (html_code) {
            return $sce.trustAsHtml(html_code);
        };

        $scope.showErrorEmailMessage = false;
        $scope.incorrectPhoneNumber = false;
        $rootScope.changeEmail = function (email) {
            if (email.length === 0) {
                $scope.showErrorEmailMessage = false;
                $scope.emptyEmail = true;
            } else {
                $scope.emptyEmail = false;
            }
        };
        $scope.showErrorPhoneMessage = false;
        $('#phone').on('input', function () {
            $scope.showErrorPhoneMessage = false;
            if (!$scope.$$phase) {
                $scope.$apply();
            }
        });
        $scope.changeEmail = function () {
            if ($scope.request.email == undefined) {
                $scope.showErrorEmailMessage = true;
            } else $scope.showErrorEmailMessage = $scope.request.email.length == 0;
        };
        $scope.enterPhoneNumber = false;
        $scope.changePhone = function (phone) {
            if (phone == undefined) {
                $scope.enterPhoneNumber = true;
                $scope.incorrectPhoneNumber = false;
                $scope.showErrorPhoneMessage = true;
            } else if (phone.length > 0) {
                $scope.showErrorPhoneMessage = false;
            }
        };
        $scope.$watch('request.phone', function (newVal, oldVal) {
            if (newVal != undefined && oldVal != newVal) {
                $scope.showErrorPhoneMessage = false;
            }
        });

        $scope.showModalInfoAboutVacancy = function () {
            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: 'partials/modal/vacancy-added-response.html',
                size: '',
                scope: $scope,
                resolve: {},
            });
        };

        $scope.editVacancy = function () {
            location.href = `${$location.$$protocol + '://' + $location.$$host}/!#/vacancy/edit/${
                $scope.vacancy.localId
            }`;
        };

        $scope.showRecallFromModal = function () {
            $scope.recallForm = {
                name: {},
                last_name: {},
                email: {},
                email2: {},
                phone: {},
            };

            $scope.showErrorEmailMessage = false;
            $scope.showErrorPhoneMessage = false;
            $scope.showErrorCvFileMessage = false;
            $scope.emptyEmail = false;
            $('body').addClass('modal-open-public-vacancy-form');
            $rootScope.orgId = $scope.vacancy.orgId;

            $scope.modalInstance = $uibModal.open({
                animation: true,
                templateUrl: 'partials/modal/public-vacancy-form.html',
                size: '',
                scope: $scope,
                resolve: {},
            });

            $scope.recallTemp = JSON.parse($scope.vacancy.recallTemplate.additionalInfo);

            $scope.recallTemp?.custom?.forEach((item) => {
                if ((item.type === 'select' || item.type === 'selectFile') && item.params?.length) {
                    item['options'] = item.params.map((param) => ({ label: param, value: param }));
                }
            });
        };

        $scope.onChangeFirstName = (value) => {
            $scope.request.name = value;
            if ($scope.firstName.mandatory && $scope.recallForm.name.invalid && value?.length) {
                $scope.recallForm.name.invalid = false;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeLastName = (value) => {
            $scope.request.lastName = value;
            if ($scope.lastName.mandatory && $scope.recallForm.last_name.invalid && value?.length) {
                $scope.recallForm.last_name.invalid = false;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeEmail = (value) => {
            $scope.request.email = value;
            $rootScope.changeEmail($scope.request.email);
            if ($scope.emailSystem.mandatory && $scope.recallForm.email.invalid && value?.length) {
                $scope.emptyEmail = false;
                $scope.showErrorEmailMessage = false;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangePhone = (value) => {
            $scope.request.phone = value.replace(/[^0-9\+\s]/g, '');
            if ($scope.phoneSystem.mandatory && $scope.recallForm.phone.invalid && value?.length) {
                $scope.showErrorPhoneMessage = false;
                $scope.recallForm.phone.invalid = false;
            }
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeAccessPerson = (event) => {
            $scope.accessPerson = event.target.checked;
            $rootScope.$$phase || $scope.$apply();
        };

        $scope.onChangeCustomSelectField = (value, field, third) => {
            field.value = value;

            if (!value && field.mandatory) {
                field.error = true;
            } else if (value && field.mandatory) {
                field.error = false;
            }

            $rootScope.$$phase || $scope.$apply();
        };

        $scope.errorHandler = (field, value) => {
            field.value = value;
            field.error = false;
        };

        $scope.dateErrorHandle = (field) => {
            if (field?.value) {
                field.error = false;
            }
        };

        $scope.removeDate = (field) => {
            field.value = '';
            if (field.mandatory) field.error = true;
        };

        $scope.sendRequest = function (accessPerson) {
            $scope.recallForm = {
                name: {},
                last_name: {},
                email: {},
                email2: {},
                phone: {},
            };
            $scope.globalError = false;
            $scope.showErrorCvFileMessage = true;

            // systems
            if (
                validEmail($scope.request.email) &&
                $scope.emailSystem.mandatory &&
                !$scope.emailSystem.disabled &&
                !$scope.emptyEmail &&
                $scope.request.email
            ) {
                $scope.showErrorEmailMessage = true;
                $scope.globalError = true;
            } else {
                $scope.showErrorEmailMessage = false;
            }

            if (
                $scope.emailSystem.mandatory &&
                !$scope.emailSystem.disabled &&
                (!$scope.request.email || !$scope.request.email.length)
            ) {
                $scope.emptyEmail = true;
                $scope.globalError = true;
            }

            if ($scope.emailSystem.mandatory && $scope.emptyEmail && !$scope.emailSystem.disabled) {
                $scope.globalError = true;
            }

            if (!$scope.request.phone && $scope.phoneSystem.mandatory && !$scope.phoneSystem.disabled) {
                $scope.showErrorPhoneMessage = true;
                $scope.enterPhoneNumber = true;
                $scope.globalError = true;
            }

            if (!$scope.request.name && $scope.firstName.mandatory && !$scope.firstName.disabled) {
                $scope.globalError = true;
                $scope.recallForm.name.pristine = false;
                $scope.recallForm.name.invalid = true;
            }

            if (!$scope.request.lastName && $scope.lastName.mandatory && !$scope.lastName.disabled) {
                $scope.globalError = true;
                $scope.recallForm.last_name.pristine = false;
                $scope.recallForm.last_name.invalid = true;
            }

            //customs
            $scope.recallTemp?.custom?.forEach((item) => {
                if (item.mandatory && !item.value && !item.files && !item.disabled) {
                    item.error = true;
                    $scope.globalError = true;
                }

                if (item.mandatory && item?.files?.length <= 0 && !item.disabled) {
                    item.errorFile = true;
                    $scope.globalError = true;
                }

                if (item.type === 'file' || item.type === 'textFile' || item.type === 'selectFile') {
                    item.error = false;
                    item.errorFile = false;
                    if (item.mandatory && item.type !== 'file' && !item.disabled) {
                        if (!item?.files?.length) {
                            item.errorFile = true;
                            $scope.globalError = true;
                        }
                        if (!item.value) {
                            item.error = true;
                            $scope.globalError = true;
                        }
                    }

                    if (item.mandatory && item.type === 'file' && !item.disabled && !item?.files?.length) {
                        item.error = true;
                        item.errorFile = true;
                        $scope.globalError = true;
                    }
                }
            });

            if ($scope.globalError) {
                notificationService.error($filter('translate')('You should fill all obligatory fields.'));
                return;
            }

            if (!accessPerson) {
                notificationService.error(
                    $translate.instant('You need to give your consent for your personal data to processing proceed'),
                );
                return;
            }

            $scope.request.lang = $translate.use();

            if (!$scope.request.phone) {
                $scope.request.phone = '';
            }

            $scope.request.phone = String($scope.request.phone);

            $scope.request.firstName = $scope.request.name;
            let customRequest = angular.copy($scope.recallTemp.custom);

            $scope.request.fields = {
                system: $scope.systems,
                custom: customRequest,
            };

            $scope.request.recallTemplateId = $scope.vacancy.recallTemplate.recallTemplateId;

            customRequest?.forEach((item) => {
                if (item.type === 'date' || item.type === 'datetime') {
                    const date = new Date(item.value);
                    item.value = date.getTime();
                }

                if (item.type === 'select' || (item.type === 'selectFile' && item.value.value)) {
                    item.value = item.value.value;
                }
            });

            $rootScope.loading = true;
            Service.addCandidateReply(
                $scope.request,
                function (resp) {
                    $rootScope.loading = false;
                    if (resp.status && resp.status === 'error' && resp.message) {
                        $scope.message = 'error';
                        notificationService.error(resp.message);
                    } else if (resp.status && resp.status === 'was_send') {
                        $rootScope.finalText = 'Thanks! You’ve updated your application on this vacancy.';
                        $scope.message = 'success';
                        $scope.request = {
                            name: '',
                            lastName: '',
                            phone: '',
                            message: '',
                            vacancyId: $scope.vacancyId,
                            fileId: null,
                        };

                        // $("#email2").val("");
                        $scope.filesForRecall = [];
                        $scope.recallForm.name.pristine = true;
                        $scope.recallForm.last_name.pristine = true;
                        $scope.recallForm.phone.pristine = true;
                        $scope.recallForm.phone.invalid = false;
                        $scope.recallForm.email2.invalid = false;
                        $scope.recallForm.email2.pristine = false;
                        $scope.showErrorEmailMessage = false;
                        $('body').removeClass('modal-open-public-vacancy-form');
                        $rootScope.loading = false;
                        $rootScope.closeModal();
                        $scope.showModalInfoAboutVacancy();

                        setTimeout(() => {
                            $rootScope.closeModal();
                            delete $rootScope.finalText;
                        }, 2500);
                    } else {
                        $scope.message = 'success';
                        $scope.request = {
                            name: '',
                            lastName: '',
                            phone: '',
                            message: '',
                            vacancyId: $scope.vacancyId,
                            fileId: null,
                        };
                        // $("#email2").val("");
                        $scope.filesForRecall = [];
                        $scope.recallForm.name.pristine = true;
                        $scope.recallForm.last_name.pristine = true;
                        $scope.recallForm.phone.pristine = true;
                        $scope.recallForm.phone.invalid = false;
                        $scope.recallForm.email2.invalid = false;
                        $scope.recallForm.email2.pristine = false;
                        $scope.showErrorEmailMessage = false;
                        $('body').removeClass('modal-open-public-vacancy-form');
                        $rootScope.loading = false;
                        $rootScope.closeModal();
                        $rootScope.finalText = 'Thanks! The employer has got your application.';
                        $scope.showModalInfoAboutVacancy();

                        setTimeout(() => {
                            $rootScope.closeModal();
                            delete $rootScope.finalText;
                        }, 2500);
                    }
                },
                function (err) {
                    $rootScope.loading = false;
                    $scope.message = 'error';
                    notificationService.error(err.message);
                },
            );
        };

        function transformLanguages(langs) {
            if (!langs) return;
            return langs.map((lang) => {
                return {
                    ...lang,
                    name: $rootScope.allLanguages[lang.name][$rootScope.currentLang],
                };
            });
        }

        function getLangs() {
            return Service.onGetLanguages().then((resp) => {
                $rootScope.allLanguages = {};

                resp.objects.forEach((item) => {
                    $rootScope.allLanguages[item.key] = {
                        ru: item.translation.russian || null,
                        en: item.translation.english || null,
                        ua: item.translation.ukrainian || null,
                        pl: item.translation.polish || null,
                    };
                });
            });
        }

        function getVacancies() {
            let defaultLang;
            if (navigator.language == 'ru-RU' || navigator.language == 'ua-UA' || navigator.language == 'en-EN') {
                defaultLang = navigator.language.split('-')[1].toLowerCase();
            } else {
                defaultLang = $rootScope.currentLang;
            }

            let host = document.referrer || 'open_by_link';
            if (vacancyId) {
                return Service.requestGetVacancy({
                    id: vacancyId,
                    host,
                    lang: defaultLang,
                });
            }
        }

        // function validEmail(email, notificationService) {
        //     if (email == undefined) return true;
        //     var r =
        //         /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/gi;
        //     return !email.match(r);
        // }

        function validEmail(email, notificationService) {
            if (email == undefined) return true;
            const r = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,16}$/;
            return !email.match(r);
        }
    },
]);
