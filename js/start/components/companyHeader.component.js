class companyHeaderComponentCtrl {
    constructor(
        $uibModal,
        $translate,
        notificationService,
        $state,
        $rootScope,
        $scope,
        Candidate,
        Company,
        serverAddress,
        $filter,
        $document,
        $window,
        $location,
    ) {
        this.$uibModal = $uibModal;
        this.$translate = $translate;
        this.notificationService = notificationService;
        this.$state = $state;
        this.$rootScope = $rootScope;
        this.$scope = $scope;
        this.candidateService = Candidate;
        this.companyService = Company;
        this.serverAddress = serverAddress;
        this.$filter = $filter;
        this.$document = $document;
        this.$window = $window;
        this.$location = $location;
    }

    $onInit() {
        this.wallpaperBlock = document.querySelector('.company-wallpaper-block');

        if (
            this.$rootScope.accountInformation &&
            (this.$rootScope.accountInformation.companyWallpaper ||
                this.$rootScope.accountInformation.companyBackground)
        ) {
            this.setCompanyWallpaper(
                this.$rootScope.accountInformation.companyWallpaper,
                this.$rootScope.accountInformation.companyWallpaperPosition,
            );
            this.setCompanyBackground(this.$rootScope.accountInformation.companyBackground);
        }

        this.$scope.$watch(
            () => this.$rootScope.accountInformation,
            () => {
                if (this.$rootScope.accountInformation && this.$rootScope.accountInformation.companyWallpaper) {
                    this.setCompanyWallpaper(
                        this.$rootScope.accountInformation.companyWallpaper,
                        this.$rootScope.accountInformation.companyWallpaperPosition,
                    );
                }
                if (this.$rootScope.accountInformation && this.$rootScope.accountInformation.companyBackground) {
                    this.setCompanyBackground(this.$rootScope.accountInformation.companyBackground);
                }
            },
        );

        this.uploadCompanyCover = this.uploadCompanyCover.bind(this);
        this.closeCustomizeView = this.closeCustomizeView.bind(this);
        this.openCustomizeView = this.openCustomizeView.bind(this);
        this.clickToStartMoveCursor = this.clickToStartMoveCursor.bind(this);
        this.moveCursor = this.moveCursor.bind(this);
    }

    goToAccount() {
        location.href = `${this.$location.$$protocol + '://' + this.$location.$$host}/!#/company/settings`;
    }
    addLinkPrefix = function (link) {
        let newLink = link;
        if (!link.startsWith('http')) {
            newLink = `https://${link}`;
        }
        return newLink;
    };

    moveWallpaperModeEnable() {
        this.closeCustomizeView();

        this.clickPosition = null;
        this.startCompanyWallpaperPosition = this.wallpaperBlock.style.backgroundPositionY;
        this.moveWallpaperModeActive = true;

        this.wallpaperBlock.style.cursor = 'move';
        this.wallpaperBlock.style.userSelect = 'none';
        this.wallpaperBlock.addEventListener('mousedown', this.clickToStartMoveCursor);
    }

    moveWallpaperModeDisable() {
        this.wallpaperBlock.style.cursor = 'default';
        this.wallpaperBlock.style.userSelect = 'select';

        this.wallpaperBlock.removeEventListener('mousedown', this.clickToStartMoveCursor);
    }

    clickToStartMoveCursor(event) {
        this.clickPosition = event.offsetY;

        this.wallpaperBlock.addEventListener('mousemove', this.moveCursor);

        this.wallpaperBlock.addEventListener('mouseup', (e) => {
            this.wallpaperBlock.removeEventListener('mousemove', this.moveCursor);
        });

        this.wallpaperBlock.addEventListener('mouseleave', (e) => {
            this.wallpaperBlock.removeEventListener('mousemove', this.moveCursor);
        });
    }

    moveCursor(event) {
        const cursorPosition = event.offsetY - this.clickPosition;

        this.clickPosition = event.offsetY;

        this.changedCompanyWallpaperPosition =
            parseInt(this.wallpaperBlock.style.backgroundPositionY) - cursorPosition >= 100
                ? 100 + '%'
                : parseInt(this.wallpaperBlock.style.backgroundPositionY) - cursorPosition <= 0
                ? 0 + '%'
                : parseInt(this.wallpaperBlock.style.backgroundPositionY) - cursorPosition + '%';

        this.wallpaperBlock.style.backgroundPositionY = this.changedCompanyWallpaperPosition;
    }

    saveCompanyWallpaperPosition() {
        if (!this.changedCompanyWallpaperPosition) return;

        this.companyService
            .onChangeAccountInformationParam({
                param: 'companyWallpaperPosition',
                value: this.changedCompanyWallpaperPosition,
            })
            .then((resp) => {
                if (resp.status === 'ok') {
                    this.$rootScope.accountInformation.companyWallpaperPosition = resp.object.companyWallpaperPosition;

                    this.moveWallpaperModeActive = false;
                    this.moveWallpaperModeDisable();

                    this.notificationService.success(this.$filter('translate')('Changes saved'));
                }
            })
            .catch((error) => {
                console.error(error);
            })
            .finally(() => {
                this.$rootScope.loading = false;
                this.$rootScope.$$phase || this.$scope.$apply();
            });
    }

    setPrevCompanyWallpaperPosition() {
        this.wallpaperBlock.style.backgroundPositionY = this.startCompanyWallpaperPosition || 0;
        this.moveWallpaperModeActive = false;
        this.moveWallpaperModeDisable();

        this.$rootScope.$$phase || this.$scope.$apply();
    }

    openCustomizeView(event) {
        event && event.stopPropagation();
        this.openedCustomizeView = !this.openedCustomizeView;
    }

    closeCustomizeView(event) {
        event && event.stopImmediatePropagation();
        this.openedCustomizeView = false;
    }

    confirmChangeBackgroundColor() {
        if (this.backgroundColor) {
            this.companyService
                .onChangeAccountInformationParam({
                    param: 'companyBackground',
                    value: this.backgroundColor,
                })
                .then((resp) => {
                    if (resp.status === 'ok') {
                        this.notificationService.success(this.$filter('translate')('Changes saved'));
                    }
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.$rootScope.loading = false;
                    this.$rootScope.$$phase || this.$scope.$apply();
                });
        }
    }

    changeBackgroundColor(backgroundColor) {
        if (!this.backgroundColor) return;

        this.backgroundColor = backgroundColor;
        this.setCompanyBackground(this.backgroundColor);
    }

    uploadCompanyCover(event) {
        this.$rootScope.loading = true;
        let file = event.target.files[0];
        if (file && file.type.includes('image')) {
            let imageReader = new FileReader();
            imageReader.onload = (image) => {
                this.$scope.$apply(($scope) => {
                    this.companyService
                        .uploadCompanyCover(image.target.result)
                        .then((resp) => {
                            if (resp.data.status === 'ok') {
                                this.setCompanyWallpaper(resp.data.object);
                            } else {
                                this.notificationService.error(resp.data.message);
                            }
                        })
                        .catch((error) => {
                            console.error(error.message);
                        })
                        .finally(() => {
                            this.$rootScope.loading = false;
                            this.$rootScope.$$phase || this.$scope.$apply();
                        });
                });
            };
            imageReader.readAsDataURL(file);
        } else {
            this.notificationService.error(this.$filter('translate')('Uploaded file should have format png or jpeg'));
            return;
        }
    }

    setCompanyBackground(backgroundColor = '') {
        const backgroundBlock = document.querySelector('.public-page-content');
        backgroundBlock.style.backgroundColor = backgroundColor;

        this.$rootScope.accountInformation.companyBackground = backgroundColor;
        this.backgroundColor = backgroundColor;
    }

    setCompanyWallpaper(wallpaperId = '', position) {
        this.$rootScope.accountInformation.companyWallpaper = wallpaperId;

        this.wallpaperBlock.style.backgroundImage = `url('${
            this.$location.$$protocol + '://' + this.$location.$$host
        }/hr/getlogo?id=${wallpaperId}')`;
        this.wallpaperBlock.style.backgroundSize = 'cover';
        this.wallpaperBlock.style.backgroundRepeat = 'no-repeat';
        this.wallpaperBlock.style.backgroundPositionX = 'center';
        if (position) {
            this.wallpaperBlock.style.backgroundPositionY = position;
        } else {
            this.wallpaperBlock.style.backgroundPositionY = 'center';
        }
    }

    deleteCompanyCover() {
        this.closeCustomizeView();
        class deleteCover {
            constructor($rootScope, $scope, Service) {
                this.$rootScope = $rootScope;
                this.$scope = $scope;
                this.globalService = Service;
            }

            $onInit() {
                this.wallpaperBlock = document.querySelector('.company-wallpaper-block');
            }

            closeModal() {
                modalInstance.close();
            }
            resetDefaultWallpaper() {
                this.$rootScope.accountInformation.companyWallpaper = null;

                this.wallpaperBlock.style.backgroundImage = '';
                this.wallpaperBlock.style.backgroundSize = 'center';
                this.wallpaperBlock.style.backgroundRepeat = 'no-repeat';
                this.wallpaperBlock.style.backgroundPositionX = 'center';
                this.wallpaperBlock.style.backgroundPositionY = 'center';
            }
            deleteCover() {
                this.globalService
                    .onRemoveWallpaper()
                    .then((resp) => {
                        this.resetDefaultWallpaper();
                        this.closeModal();
                    })
                    .catch((error) => {
                        console.error(error);
                    })
                    .finally(() => {
                        this.$rootScope.loading = false;
                        this.$rootScope.$$phase || this.$scope.$apply();
                    });
            }
        }
        const modalInstance = this.$uibModal.open({
            templateUrl: '../partials/modal/deleteCover.html',
            controller: ['$rootScope', '$scope', 'Service', deleteCover],
            controllerAs: 'vm',
            resolve: {
                closeModal: () => {
                    return this.closeModal;
                },
                deleteCover: () => {
                    return this.deleteCover;
                },
            },
        });
    }
}

const companyHeaderComponentDefinition = {
    templateUrl: 'partials/public/components/companyHeader.component.html',
    controller: companyHeaderComponentCtrl,
    controllerAs: 'vm',
    bindings: {},
};
component.component('companyHeaderComponent', companyHeaderComponentDefinition);
