const requireDir = require('require-dir');
requireDir('./gulp/', { recurse: true });

const gulp = require('gulp'),
    replace = require('gulp-replace'),
    pug = require('gulp-pug'),
    prettify = require('gulp-jsbeautifier'),
    sass = require('gulp-sass')(require('sass')),
    csso = require('gulp-csso'),
    sourcemaps = require('gulp-sourcemaps'),
    runSequence = require('run-sequence'),
    newer = require('gulp-newer'),
    imagemin = require('gulp-imagemin'),
    imageminWebp = require('imagemin-webp');
concat = require('gulp-concat');
minify = require('gulp-minify');
extReplace = require('gulp-ext-replace');
(pngquant = require('imagemin-pngquant')),
    (rename = require('gulp-rename')),
    (plumber = require('gulp-plumber')),
    (clean = require('gulp-clean')),
    (browsersync = require('browser-sync'));

// Path object
var path = {
    watch: {
        html: './landing/templates/*.pug',
        html_lang: './landing/templates/ru/*.pug',
        html_lang_ua: './landing/templates/ua/*.pug',
        html_lang_pl: './landing/templates/pl/*.pug',
        html_layouts: './landing/templates/layouts/*.pug',
        html_includes: './landing/templates/includes/*.pug',
        html_components: './landing/templates/components/*.pug',
        html_elements: './landing/templates/elements/*.pug',
        css: './landing/sass/**/*.scss',
        js: './landing/js/**/*.js',
        img: './landing/images/**/*.*',
    },
    src: {
        html: './landing/templates/*.pug',
        html_lang: './landing/templates/ru/*.pug',
        html_lang_ua: './landing/templates/ua/*.pug',
        html_lang_pl: './landing/templates/pl/*.pug',
        css: './landing/sass/*.scss',
        js: './landing/js/*.js',
        img: './landing/images/**/*.*',
        files: './landing/files/*.*',
        root_files: './landing/root_files/*.*',
        sitemap: './landing/templates/sitemap.xml',
    },
    build: {
        html: './external/',
        html_lang: './external/ru/',
        html_lang_ua: './external/ua/',
        html_lang_pl: './external/pl/',
        css: './external/css/',
        js: './external/js/',
        img: './external/images/',
        files: './external/files/',
        sitemap: './external/',
    },
    clean: {
        build: [
            './external/*.*',
            './external/css',
            './external/js',
            './external/images',
            '!./external/fonts',
            '!./external/files',
            '!./external/php',
            '!./external/video',
            '!./external/modules',
            '!./external/node_modules',
            '!./external/package.json',
            '!./external/yarn.lock',
        ],
    },
    versions: [
        'i.html',
        'home.html',
        './js/main/app.js',
        './js/main/service/interceptorService.js',
        './landing/templates/**/*.pug',
    ],
};

// HTML
gulp.task('build_html', () => {
    return gulp
        .src(path.src.html)
        .pipe(plumber())
        .pipe(
            pug({
                pretty: true,
            }),
        )
        .pipe(
            prettify({
                indent_size: 2,
                inline: 'span, mark, strong, em',
                extra_liners: 'head, body, header, footer, main, section, aside, article, dialog',
            }),
        )
        .pipe(gulp.dest(path.build.html))
        .pipe(browsersync.stream());
});

gulp.task('build_html_lang', () => {
    return gulp
        .src(path.src.html_lang)
        .pipe(plumber())
        .pipe(
            pug({
                pretty: true,
            }),
        )
        .pipe(
            prettify({
                indent_size: 2,
                inline: 'span, mark, strong, em',
                extra_liners: 'head, body, header, footer, main, section, aside, article, dialog',
            }),
        )
        .pipe(gulp.dest(path.build.html_lang))
        .pipe(browsersync.stream());
});

gulp.task('build_html_lang_ua', () => {
    return gulp
        .src(path.src.html_lang_ua)
        .pipe(plumber())
        .pipe(
            pug({
                pretty: true,
            }),
        )
        .pipe(
            prettify({
                indent_size: 2,
                inline: 'span, mark, strong, em',
                extra_liners: 'head, body, header, footer, main, section, aside, article, dialog',
            }),
        )
        .pipe(gulp.dest(path.build.html_lang_ua))
        .pipe(browsersync.stream());
});

// For pl pages
gulp.task('build_html_lang_pl', () => {
    return gulp
        .src(path.src.html_lang_pl)
        .pipe(plumber())
        .pipe(
            pug({
                pretty: true,
            }),
        )
        .pipe(
            prettify({
                indent_size: 2,
                inline: 'span, mark, strong, em',
                extra_liners: 'head, body, header, footer, main, section, aside, article, dialog',
            }),
        )
        .pipe(gulp.dest(path.build.html_lang_pl))
        .pipe(browsersync.stream());
});

// CSS (change maping from sass to css)
gulp.task('build_css', () => {
    return gulp
        .src(path.src.css)
        .pipe(plumber())

        .pipe(sass())
        .pipe(gulp.dest(path.build.css))

        .pipe(csso())
        .pipe(
            rename(function (path) {
                path.basename += '.min';
            }),
        )
        .pipe(gulp.dest(path.build.css))

        .pipe(browsersync.stream());
});
gulp.task('build_bootstrap_css', () => {
    return gulp
        .src('./vendor/bootstrap-4.6.2/scss/bootstrap.scss')
        .pipe(plumber())

        .pipe(sass())
        .pipe(gulp.dest(path.build.css))

        .pipe(csso())
        .pipe(
            rename(function (path) {
                path.basename += '.min';
            }),
        )
        .pipe(gulp.dest(path.build.css))

        .pipe(browsersync.stream());
});

// JS
gulp.task('build_js', () => {
    return gulp
        .src(path.src.js)
        .pipe(sourcemaps.init())

        .pipe(sourcemaps.write())
        .pipe(gulp.dest(path.build.js))

        .pipe(browsersync.stream());
});

gulp.task('build_index_js', () => {
    return gulp
        .src([
            './lib/swiper-5.4.5/swiper.js',
            './lib/jquery/jquery.slim.min.js',
            './lib/popper/popper.min.js',
            './lib/bootstrap/bootstrap.bundle.js',
            './landing/js/api.key.const.js',
            './landing/js/cs.js',
            './landing/js/google-consent-mode.js',
            './landing/js/cs.home.js',
        ])
        .pipe(concat('index-scripts.js'))
        .pipe(minify({ ext: { min: '.min.js' } }))
        .pipe(gulp.dest(path.build.js));
});

// Images
gulp.task('build_image', () => {
    return gulp
        .src(path.src.img)
        .pipe(newer(path.build.img))
        .pipe(
            imagemin([
                pngquant({
                    speed: 1,
                    quality: [0.89, 0.9],
                }),
                imagemin.jpegtran({ progressive: true }),
                // imagemin.svgo({
                //     plugins: [
                //         { removeViewBox: true },
                //         { cleanupIDs: false }
                //     ]
                // }),
                imagemin.gifsicle({ interlaced: true }),
            ]),
        )
        .pipe(gulp.dest(path.build.img));
});

gulp.task('webp', () => {
    return gulp
        .src(path.src.img)
        .pipe(imagemin([imageminWebp({ quality: 80 })]))
        .pipe(extReplace('.webp'))
        .pipe(gulp.dest(path.build.img));
});

//Files
gulp.task('build_files', () => {
    return gulp.src(path.src.files).pipe(gulp.dest(path.build.files));
});

//Root Files
gulp.task('build_root_files', () => {
    return gulp.src(path.src.root_files).pipe(gulp.dest(path.build.html));
});

//Sitemap
gulp.task('build_sitemap', () => {
    return gulp.src(path.src.sitemap).pipe(gulp.dest(path.build.sitemap));
});

// Clean
gulp.task('clean', function () {
    return gulp.src(path.clean.build).pipe(clean());
});

// Copy
gulp.task('copy-fonts', function () {
    return gulp.src('./landing/fonts/**/*.*').pipe(gulp.dest('./landing/fonts/'));
});

//Build
gulp.task('build-landing', (done) => {
    if (process.env.npm_config_no_landing) {
        return done();
    }
    return gulp.parallel(
        'build_html',
        'build_html_lang',
        'build_html_lang_ua',
        'build_html_lang_pl',
        'build_css',
        'build_bootstrap_css',
        'build_js',
        'build_index_js',
        'build_image',
        'build_files',
        'build_root_files',
        'copy-fonts',
        'build_sitemap',
    )(done);
});

gulp.task('watch-landing', function () {
    gulp.watch('landing/js/*.js', gulp.series(['build_js', 'build_index_js']));
    gulp.watch(
        'landing/templates/**/*.pug',
        gulp.series(['build_html', 'build_html_lang', 'build_html_lang_ua', 'build_html_lang_pl']),
    );
    gulp.watch('landing/sass/**/*.scss', gulp.series(['build_css']));
    gulp.watch('landing/images/**/*.+(png|svg|jpg|)', gulp.series(['build_image']));
    gulp.watch('landing/files/*.pdf', gulp.series(['build_files']));
    gulp.watch('landing/root_files/*.pdf', gulp.series(['build_root_files']));
});

gulp.task('up-versions', () => {
    const versionFile = require('./js/version.json');
    return gulp
        .src(process.env.npm_config_no_landing ? path.versions.slice(0, -1) : path.versions)
        .pipe(replace(/(\?v=)(\d+)/g, (_, prefix) => `${prefix}${versionFile.version}`))
        .pipe(gulp.dest((file) => file.base));
});
