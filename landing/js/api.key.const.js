var facebookAppId = '***************',
    apiKey = {
        facebook: { appId: facebookAppId },
        linkedIn: { api_key: '75zhzlvnn4p5on' },
        vk: { apiId: 4592828 },
        google: {
            client_id: '************-eo4qmmi7o6hii0ckmrc004lhkh9m3596.apps.googleusercontent.com',
        },
        outlook: { client_id: '********-454c-4fb7-8ef7-c799b62005c3' },
    },
    GOOGLE_URL =
        'https://accounts.google.com/o/oauth2/auth' +
        '?client_id=' +
        apiKey.google.client_id +
        '&scope=email%20profile' +
        '&state=/profile' +
        '&redirect_uri=' +
        location.protocol +
        '//' +
        document.domain +
        '/white.html' +
        '&response_type=code%20token',
    COEF_ANIMATION = 0.6;

if (document.domain === 'dev.cleverstaff.net' || document.domain === 'dev2.cleverstaff.net') {
    facebookAppId = '***************';
}

// Storage Message for Russian language
localStorage.setItem(
    '[CS] LOCALIZATION MESSAGES RU',
    JSON.stringify({
        redirect: 'Переадресация...',
        forbidden: 'Вы исчерпали лимит в 5 регистраций в день. Попробуйте, пожалуйста, завтра.',
        busyLogin: 'Этот email уже используется. Вы можете запросить смену пароля, либо использовать другой email.',
        wrongPromocode: 'Промокод не найден',
        terms_accept: 'Пожалуйста, прочитайте и примите Политику Приватности и Публичную Оферту',
        registrationOrInviteIsNotConfirmed:
            'Подтвердите регистрацию или примите приглашение в аккаунт, чтобы авторизоваться',
        phoneIsIncorrect: 'Пожалуйста, укажите полный номер телефона',
        suchAccountAlreadyExist: 'Такой аккаунт уже существует',
        GoogleError: 'Ошибка доступа через Google. Пожалуйста, попробуйте ещё раз',
        unknown: 'Сервис временно недоступен',
    }),
);

// Storage Message for Ukrainian language
localStorage.setItem(
    '[CS] LOCALIZATION MESSAGES UA',
    JSON.stringify({
        redirect: 'Переадресація...',
        forbidden: 'Ви вичерпали ліміт у 5 реєстрацій на добу. Спробуйте, будь ласка, завтра.',
        busyLogin: 'Цей email вже використовується. Ви можете запросити зміну паролю, або використати інший email.',
        wrongPromocode: 'Промокод не знайдений',
        terms_accept: 'Будь ласка, прочитайте та прийміть Політику Приватності та Публічну Оферту',
        registrationOrInviteIsNotConfirmed:
            'Підтвердіть реєстрацію або прийміть запрошення в аккаунт, щоб авторизоватися',
        phoneIsIncorrect: 'Будь ласка, вкажіть повний номер телефону',
        suchAccountAlreadyExist: 'Такий аккаунт вже існує',
        GoogleError: 'Помилка доступу через Google. Будь ласка, спопробуйте ше раз',
        unknown: 'Сервіс тимчасово недоступний',
    }),
);

// Storage Message for English language
localStorage.setItem(
    '[CS] LOCALIZATION MESSAGES EN',
    JSON.stringify({
        redirect: 'Redirect...',
        forbidden: 'You have reached the limit of 5 registrations per day. Please try again tomorrow.',
        busyLogin: 'Someone already has that email try another one',
        wrongPromocode: 'Promo code not found',
        terms_accept: 'Please read and accept the Privacy Policy and the Public Offer',
        registrationOrInviteIsNotConfirmed: 'Сonfirm registration or accept the invitation to your account to log in',
        phoneIsIncorrect: 'Please enter your full phone number',
        suchAccountAlreadyExist: 'Such account already exist',
        GoogleError: 'Access error through Google. Please try again',
        unknown: 'Service is temporarily unavailable',
    }),
);

// Storage Message for Polish language
localStorage.setItem(
    '[CS] LOCALIZATION MESSAGES PL',
    JSON.stringify({
        redirect: 'Przekierowanie...',
        forbidden: 'Wyczerpałeś limit 5 rejestracji dziennie. Spróbuj jutro.',
        busyLogin: 'Ten email jest już w użyciu. Możesz poprosić o zmianę hasła lub użyć innego adresu e-mail.',
        wrongPromocode: 'Nie znaleziono kodu promocyjnego',
        terms_accept: 'Prosimy o zapoznanie się i zaakceptowanie Polityki Prywatności oraz Oferty Publicznej',
        registrationOrInviteIsNotConfirmed:
            'Potwierdź rejestrację lub zaakceptuj zaproszenie do konta, aby się zalogować',
        phoneIsIncorrect: 'Podaj swój pełny numer telefonu',
        suchAccountAlreadyExist: 'Takie konto już istnieje',
        GoogleError: 'Błąd podczas uzyskiwania dostępu przez Google. Proszę spróbuj ponownie',
        unknown: 'Usługa jest chwilowo niedostępna',
    }),
);
