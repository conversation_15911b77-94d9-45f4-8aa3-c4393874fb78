// Global
var checkForUserLang;
var userLang;
var redirectFromBlog;
var DOMAIN = 'https://' + location.hostname + '/hr/';
var isTestServer = location.hostname !== 'cleverstaff.net';

if (isTestServer) {
    const firstChild = document.head.firstChild;
    const metaTag = document.createElement('meta');
    metaTag.setAttribute('name', 'robots');
    metaTag.setAttribute('content', 'noindex, nofollow');
    document.head.insertBefore(metaTag, firstChild);
}

let base_easing = null;

if (this.Power2) {
    base_easing = Power2.easeOut;
}

const pathName = window.location.pathname;

const header = document.querySelector('.header');

if (
    pathName.includes('/signin.html') ||
    pathName.includes('/signup.html') ||
    pathName.includes('/request.html') ||
    pathName.includes('/make-request.html')
) {
    if (header) {
        header.style.opacity = 1;
    }
}

//set language without ua ip
function setLagnuageWithOutIp() {
    userLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY') || navigator.language;
    if (window.location.href.includes('webinar.html')) {
        return;
    }

    if (userLang === 'en') {
        let temp = window.location.pathname;
        if (window.location.href.indexOf('ua') !== -1) {
            temp = temp.replace('/ua', '');
        }

        if (window.location.href.indexOf('ru') !== -1) {
            temp = temp.replace('/ru', '');
        }
        if (temp !== window.location.pathname) {
            window.location.href = `${temp}${window.location.search}`;
        }
        return;
    }
    if (userLang === 'ru') {
        if (window.location.href.indexOf('ru') === -1) {
            window.location.href = `/ru${window.location.pathname.replace('/ua', '').replace('/pl', '')}${
                window.location.search
            }`;
        }
        return;
    }
    if (userLang === 'ua') {
        if (window.location.href.indexOf('ua') === -1) {
            window.location.href = `/ua${window.location.pathname.replace('/ru', '').replace('/pl', '')}${
                window.location.search
            }`;
        }
        return;
    }
    if (userLang === 'pl') {
        if (window.location.href.indexOf('pl') === -1) {
            window.location.href = `/pl${window.location.pathname.replace('/ua', '').replace('/ru', '')}${
                window.location.search
            }`;
        }
        return;
    }
}

// clear language of landing
checkForUserLang = localStorage.getItem('SET_LANG');
if (!checkForUserLang) {
    window.localStorage.removeItem('NG_TRANSLATE_LANG_KEY');
}

// set language of landing
userLang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');
// redirect from blog
if (window.location.href.includes('redirect=false')) {
    redirectFromBlog = true;
} else {
    redirectFromBlog = false;
}

function setLanguage() {
    if (redirectFromBlog) {
        return;
    }

    if (window.location.href.includes('90day')) {
        localStorage.setItem('SET_LANG', 'true');
        if (window.location.href.includes('/pl')) {
            localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'pl');
        } else {
            localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'en');
        }
        return;
    }

    if (!userLang && !redirectFromBlog) {
        initLanguage();
    } else {
        setLagnuageWithOutIp();
    }
}

function initLanguage() {
    const pagesToExcludeLangRedirect = ['/signin.html'];

    if (!pagesToExcludeLangRedirect.some((page) => window.location.href.includes(page))) {
        getUserBy();
    }
}

setLanguage();

// Detect element in viewport
var isInViewport = function (elem) {
    if (!elem) return;
    var distance = elem.getBoundingClientRect();

    return (
        distance.top >= 0 &&
        distance.left >= 0 &&
        distance.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        distance.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
};

function anim_start(tween) {
    tween.play();
}

function anim_back(tween) {
    tween.reverse();
}

function onSetToStorage(param, value) {
    localStorage.setItem(param, value);
}

document.addEventListener('DOMContentLoaded', function (event) {
    'use strict';

    // Language change
    const toggleLang = document.querySelectorAll('.lang__option');

    if (location.href.toLowerCase().includes('api')) document.getElementById('change-lang').style.display = 'none';

    toggleLang &&
        toggleLang.forEach((item) => {
            item.addEventListener('click', function (e) {
                e.preventDefault();

                switch (e.target.innerText) {
                    case 'Ru':
                        location.href =
                            location.origin + '/ru' + location.pathname.replace('/ua', '').replace('/pl', '');
                        localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ru');
                        break;
                    case 'Ua':
                        location.href =
                            location.origin + '/ua' + location.pathname.replace('/ru', '').replace('/pl', '');
                        localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ua');
                        break;
                    case 'Pl':
                        location.href =
                            location.origin + '/pl' + location.pathname.replace('/ua', '').replace('/ru', '');
                        localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'pl');
                        break;
                    case 'En':
                        location.href =
                            location.origin +
                            location.pathname.replace('/ru', '').replace('/ua', '').replace('/pl', '');
                        localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'en');
                        break;
                    default:
                        break;
                }
            });
        });

    // Navigation
    const nav_box = document.querySelector('.nav-box'),
        menu_toggle = document.querySelector('.menu-toggle');

    if (menu_toggle) {
        const toggleMenu = function (e) {
            e.preventDefault();

            nav_box.classList.toggle('reveal');
            this.classList.toggle('change');
        };

        menu_toggle.addEventListener('click', toggleMenu, false);
    }

    const menu_close = document.querySelector('.nav-box__close');

    if (menu_close) {
        const closeMenu = function (e) {
            e.preventDefault();

            nav_box.classList.remove('reveal');
            menu_toggle.classList.remove('change');
        };

        menu_close.addEventListener('click', closeMenu, false);
    }

    // Scroll top button
    const scroll_top = document.querySelector('.scroll-top');

    if (scroll_top) {
        const scrollTop = function (e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        scroll_top.addEventListener('click', scrollTop, false);
    }

    // Scroll links
    const scroll_links = document.querySelectorAll('.scroll-link');

    if (scroll_links.length) {
        const scrollDestination = function (e) {
            e.preventDefault();

            const elementPosition = document.querySelector(this.dataset.destination).offsetTop;
            window.scrollTo({ top: elementPosition - this.dataset.offset, behavior: 'smooth' });
        };

        for (let i = 0; i < scroll_links.length; i++) {
            scroll_links[i].addEventListener('click', scrollDestination, false);
        }
    }

    const closeIeModal = document.getElementById('close-ie-modal');

    if (closeIeModal) {
        closeIeModal.addEventListener('click', () => hideIeModal());
    }

    const ieModal = document.getElementById('ie-modal'),
        loginBtn = document.querySelector('.login-link');

    // if (loginBtn) {
    //     loginBtn.addEventListener('click', function (e) {
    //         e.preventDefault();
    //         if (isIE()) {
    //             showIeModal();
    //         } else {
    //             window.location.replace('/signin.html');
    //         }
    //     });
    // }

    function onCheckActiveSession() {
        fetch(DOMAIN + 'person/authping')
            .then((resp) => {
                if (resp.status === 401) return;

                window.location.replace('/!#/organizer');
            })
            .catch((e) => console.error(e));
    }

    if (
        window.location.href.includes('cleverstaff.net') &&
        (window.location.pathname === '/' ||
            window.location.pathname === '/ru/' ||
            window.location.pathname === '/ua/' ||
            window.location.pathname === '/pl/')
    ) {
        setTimeout(onCheckActiveSession, 500);
    }

    function showIeModal() {
        const imagesFromModal = document.querySelectorAll('.is-ie-lazyLoading');
        imagesFromModal.forEach((item) => {
            item.classList.add('visible');
        });
        const coverDiv = document.createElement('div');

        coverDiv.id = 'cover-div';
        document.body.style.overflowY = 'hidden';
        document.body.append(coverDiv);
        ieModal.style.display = 'flex';
    }

    function hideIeModal() {
        document.body.removeChild(document.getElementById('cover-div'));
        document.body.style.overflowY = 'auto';
        ieModal.style.display = 'none';
    }

    /*
        Lazy loading for images with tag img, source
     */

    let lazyImgs = [].slice.call(document.querySelectorAll('img.lazy'));
    let lazySources = [].slice.call(document.querySelectorAll('source.lazy'));

    if ('IntersectionObserver' in window && 'IntersectionObserverEntry' in window) {
        lazyLoad(lazyImgs);
        lazyLoad(lazySources);
    }

    function lazyLoad(lazyImages) {
        if (lazyImages.length > 0) {
            let lazySourceObserver = new IntersectionObserver(function (entries, observer) {
                entries.forEach(function (entry) {
                    if (entry.isIntersecting || entry.isIntersecting) {
                        let lazyImage = entry.target;
                        if (lazyImage.dataset.srcset) lazyImage.srcset = lazyImage.dataset.srcset;
                        if (lazyImage.dataset.src) lazyImage.src = lazyImage.dataset.src;
                        lazyImage.classList.remove('lazy');
                        lazySourceObserver.unobserve(lazyImage);
                    }
                });
            });

            lazyImages.forEach(function (lazyImage) {
                lazySourceObserver.observe(lazyImage);
            });
        }
    }

    /*
        End of Lazy loading
     */
});

function getUserBy() {
    fetch(DOMAIN + 'public/getUserLocation')
        .then((res) => res.json())
        .then(function (res) {
            localStorage.setItem('SET_LANG', 'true');

            if (res.countryCode == 'UA') {
                userLang = localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ua');
                if (window.location.href.indexOf('ua') === -1) {
                    window.location.href = `/ua${window.location.pathname.replace('/ru', '').replace('/pl', '')}${
                        window.location.search
                    }`;
                }
            } else if (res.countryCode == 'PL') {
                if (window.location.href.indexOf('pl') === -1) {
                    window.location.href = `/pl${window.location.pathname.replace('/ua', '').replace('/ru', '')}${
                        window.location.search
                    }`;
                }
            } else if (
                res.countryCode === 'BY' ||
                res.countryCode === 'KZ' ||
                res.countryCode === 'MD' ||
                res.countryCode === 'UZ' ||
                res.countryCode === 'KG'
            ) {
                userLang = localStorage.setItem('NG_TRANSLATE_LANG_KEY', 'ru');
                if (window.location.href.indexOf('ru') === -1) {
                    window.location.href = `/ru${window.location.pathname.replace('/ua', '').replace('/pl', '')}${
                        window.location.search
                    }`;
                }
            } else {
                setLagnuageWithOutIp();
            }
        })
        .catch(function (error) {});
}

(function () {
    if (document.referrer.indexOf('cleverstaff') === -1) {
        var sessions = [],
            session = '',
            myStorage = window.localStorage,
            url = window.location.href,
            searchSites = ['google', 'yandex', 'yahoo', 'bing', 'tut', 'Baidu'],
            utms = ['utm_source=', 'utm_medium='];
        if (myStorage.getItem('session')) {
            sessions = myStorage.getItem('session').split(' | ');
            if (url.indexOf('utm') !== -1) {
                for (var i = 0; i < utms.length; i++) {
                    var slash = i === utms.length - 1 ? '' : ' / ',
                        start = url.indexOf(utms[i]) + utms[i].length,
                        end = url.substr(start).indexOf('&');
                    session += url.substr(start, end) + slash;
                }
            } else {
                var referrer = document.referrer;
                if (referrer) {
                    if (checkSites(searchSites)) {
                        var searcher;
                        for (var i = 0; i < searchSites.length; i++) {
                            if (referrer.indexOf(searchSites[i]) !== -1) {
                                searcher = searchSites[i];
                            }
                        }
                        session = searcher + ' / organic';
                    } else {
                        session = referrer.replace(/(^\w+:|^)\/\//, '');
                    }
                } else {
                    session = 'direct';
                }
            }
            sessions.push(session);
            myStorage.setItem('session', sessions.join(' | '));
        }
        function checkSites(searchArray) {
            for (var i = 0; i < searchArray.length; i++) {
                if (referrer.indexOf(searchArray[i]) !== -1) {
                    return referrer.indexOf(searchArray[i]) !== -1;
                }
            }
        }
    }
})();

const showTextBtn = document.getElementById('showTextBtn');
const hideTextBtn = document.getElementById('hideTextBtn');
const textBlock = document.getElementById('textBlock');
const arrowBtn = document.getElementById('arrowBtn');
const toggleTextBtn = document.getElementById('toggleTextBtn');

if (toggleTextBtn) {
    toggleTextBtn.addEventListener('click', () => {
        if (!showTextBtn.classList.contains('hidden')) {
            textBlock.classList.remove('hidden');
            hideTextBtn.classList.remove('hidden');
            showTextBtn.classList.add('hidden');
            arrowBtn.classList.add('rotated');
        } else {
            showTextBtn.classList.remove('hidden');
            textBlock.classList.add('hidden');
            hideTextBtn.classList.add('hidden');
            arrowBtn.classList.remove('rotated');
        }
    });
}

//only for Black Friday banner
const bfPromoCode = document.querySelector('.bf-banner__content_promocode');
const bfPromoCodeHint = document.querySelector('.bf-banner__content_hint');
if (bfPromoCode && bfPromoCodeHint) {
    bfPromoCode.addEventListener('click', () => {
        let copiedText;

        switch (userLang) {
            case 'ua':
                copiedText = 'Скопійовано';
                break;
            case 'en':
                copiedText = 'Copied';
                break;
            case 'pl':
                copiedText = 'Skopiowano';
                break;
            case 'ru':
                copiedText = 'Скопировано';
                break;
        }

        navigator.clipboard.writeText(bfPromoCode.innerText);

        bfPromoCodeHint.classList.add('bf-banner__content_hint-animated');
        bfPromoCodeHint.innerText = copiedText;

        const timeout = setTimeout(() => {
            bfPromoCodeHint.classList.remove('bf-banner__content_hint-animated');
            clearTimeout(timeout);
        }, 700);
    });
}
