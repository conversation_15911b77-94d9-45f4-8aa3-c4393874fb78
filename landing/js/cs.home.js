document.addEventListener('DOMContentLoaded', function (event) {
    'use strict';

    // Global
    var header_box = document.querySelector('.header');
    var headerInner = document.querySelector('.header__inner');
    const headerLogoClever = document.querySelector('.header-logo .clever');
    const headerLogoText = document.querySelector('.header-logo .logo-text');
    var passive_button = document.querySelector('.btn-passive');
    var nav_box = document.querySelector('.nav-box');
    var hero = document.querySelector('.hero');
    var artemisImage = document.querySelector('.artemis');
    var artemisMobileImage = document.querySelector('.artemis-mobile');
    var coefficient = COEF_ANIMATION;
    const mobileWidth = 768;
    const isMobile = window.innerWidth <= mobileWidth;
    const timeCoefficient = isMobile ? 0.1 : 1;

    const header = document.querySelector('.header');

    // const preloaderEl = document.querySelector('.preloader');

    initAdditionalLogoText();

    // Preloader
    const initialLoading = () => {
        // if (!isMobile) {
        //     preloaderEl.querySelector('.preloader__brand').style.width = '411px';
        //     setTimeout(() => {
        //         preloaderEl.classList.add('_hidden');
        //     }, 900);
        // }

        let coefficient = 0;

        setTimeout(() => {
            headerInner.querySelectorAll('.brand nav .nav__link').forEach((item) => {
                setTimeout(() => {
                    item.classList.add('_visible');
                }, 80 * coefficient);

                coefficient++;
            });

            headerInner.querySelector('.lang').classList.add('_visible');
            headerInner.querySelector('.login-link').classList.add('_visible');
            headerInner.querySelector('.btn-passive').classList.add('_visible');
            headerInner.querySelector('.btn-group--basic').classList.add('_visible');

            if (document.querySelector('#lobbyTop')) {
                document.querySelector('#lobbyTop').style.opacity = '1';
            }
            if (document.querySelector('#lobbyTop.lobby-top-ua-ru')) {
                document.querySelector('#lobbyTop.lobby-top-ua-ru').style.opacity = '1';
            }
        }, 100 * timeCoefficient);
    };

    function getUserLocation() {
        fetch(DOMAIN + 'public/getUserLocation')
            .then((res) => res.json())
            .then(function (res) {
                localStorage.setItem('SET_LANG', 'true');
                // if (res.countryCode === 'PL') {
                //     document.querySelector('#lobbyTop').style.display = 'block';
                //     window.scrollTo({ top: 0 });
                // }
            })
            .catch(function (error) {
                localStorage.removeItem('fromPL');
            })
            .finally(() => {
                // if (document.querySelector('#lobbyTop.lobby-top-ua-ru')) {
                //     document.querySelector('#lobbyTop.lobby-top-ua-ru').style.display = 'flex';
                //     document.querySelector('#lobbyTop.lobby-top-ua-ru').style.opacity = '0';
                // }
            });
    }

    getUserLocation();

    window.onload = function () {
        const lobbyClose = document.querySelector('#lobbyTop .close-icon');
        lobbyClose &&
            lobbyClose.addEventListener('click', () => {
                document.querySelector('#lobbyTop').style.display = 'none';
            });

        // document.querySelector('.preloader').classList.add('done');
        initialLoading();

        setTimeout(() => {
            // header.style.opacity = 1;
            nav_box.style.display = 'block';
            interactive_hero();
        }, 1000);

        if (window.outerWidth < 1200) {
            passive_button.text = passive_button.dataset.rename;
        }
    };

    // Toggle answers
    const toggleAnswer = (event) => {
        const questionWrapper = event.target.closest('.question-wrapper');
        const questionAnswer = questionWrapper.querySelector('.question-answer');
        const arrowImg = questionWrapper.querySelector('.question-arrow img');
        questionAnswer.classList.toggle('active');
        arrowImg.src = questionAnswer.classList.contains('active')
            ? arrowImg.src.replace('-down-', '-up-')
            : arrowImg.src.replace('-up-', '-down-');
    };

    document.querySelectorAll('.question-arrow').forEach((arrow) => {
        arrow.addEventListener('click', toggleAnswer);
    });

    //Artemis animation
    const interactiveRemarks = () => {
        if (artemisAnimationProcess) return;

        const remark1 = document.querySelector('.remark-1');
        const remark2 = document.querySelector('.remark-2');
        const youTube = document.querySelector('.youtube-button');

        remark1.style.display = 'block';

        setTimeout(() => {
            remark1.classList.add('_visible');
        }, 1000);
        setTimeout(() => {
            remark1.classList.remove('_visible');
        }, 5000);

        setTimeout(() => {
            remark1.style.display = 'none';
            remark2.style.display = 'block';
        }, 6000);

        setTimeout(() => {
            remark2.classList.add('_visible');
        }, 6300);
        setTimeout(() => {
            remark2.classList.remove('_visible');
        }, 10300);
        setTimeout(() => {
            remark2.style.display = 'none';
            youTube.style.display = 'block';
        }, 11000);

        setTimeout(() => {
            youTube.classList.add('_visible');
        }, 11700);

        artemisAnimationProcess = true;
    };

    let artemisAnimationProcess = false;
    if (window.innerWidth <= mobileWidth) {
        setTimeout(() => {
            startArtemisAnimation();
            interactiveRemarks();
        }, 2000 * timeCoefficient);
    } else {
        document.body.addEventListener('click', () => {
            startArtemisAnimation();
            interactiveRemarks();
        });

        artemisImage.ondragstart = () => {
            return false;
        };
        artemisImage.oncontextmenu = () => {
            return false;
        };

        artemisImage.onmouseover = () => {
            startArtemisAnimation();
            interactiveRemarks();
        };
    }

    function startArtemisAnimation() {
        if (artemisAnimationProcess) return;

        artemisMobileImage.style.filter = 'blur(0)';
        artemisImage.style.filter = 'blur(0)';
    }

    //Animated logo
    // const logoTimeout = setTimeout(() => {
    //     headerLogoClever.src = headerLogoClever.src.replace('.png', '.gif');
    //
    //     setTimeout(() => {
    //         headerLogoText.src = headerLogoText.src.replace('.png', '.gif');
    //     }, 2500);
    //
    //     setTimeout(() => {
    //         setInterval(() => {
    //             headerLogoClever.src = headerLogoClever.src.replace('.gif', '.png');
    //             headerLogoClever.src = headerLogoClever.src.replace('.png', '.gif');
    //         }, 6000);
    //     }, 4000);
    // }, 2000);

    //Modal with video
    let videoSrc;
    $('.youtube-button').click(function () {
        videoSrc = $(this).data('src');
    });
    // when the modal is opened autoplay it
    $('#myModal').on('shown.bs.modal', function (e) {
        // set the video src to autoplay and not to show related video
        $('#video').attr('src', videoSrc + '?autoplay=1&amp;modestbranding=1&amp;showinfo=0');
    });

    // stop playing the YouTube video when I close the modal
    $('#myModal').on('hide.bs.modal', function (e) {
        // a poor man's stop video
        $('#video').attr('src', videoSrc);
    });

    // actions fire after reaching target element
    function current_segment(element, offset) {
        var stats = element.getBoundingClientRect();

        if (stats.bottom - offset > 0) {
            // anim_back(header_button);
            headerInner.querySelector('.btn-group-login .btn-active').classList.remove('_visible');
            headerInner.querySelector('.brand .logo').style.width = '173px';
            // anim_back(header_logo);

            if (window.outerWidth < 1200) {
                passive_button.text = passive_button.dataset.rename;
            } else {
                passive_button.text = passive_button.dataset.name;
            }

            header_box.classList.remove('reveal');
            nav_box.classList.add('nav-top');
        } else {
            header_box.classList.add('reveal');
            nav_box.classList.remove('nav-top');

            // anim_start(header_button);
            headerInner.querySelector('.btn-group-login .btn-active').classList.add('_visible');
            headerInner.querySelector('.brand .logo').style.width = '50px';

            // anim_start(header_logo);
            passive_button.text = passive_button.dataset.rename;
        }
    }

    // Init hero animation
    const interactive_hero = () => {
        hero.querySelector('.hero__title').classList.add('_visible');

        setTimeout(() => {
            hero.querySelectorAll('.hero__content p').forEach((element) => {
                element.classList.add('_visible');
            });
        }, 200);
        setTimeout(() => {
            hero.querySelector('.hero__content a.btn').classList.add('_visible');
        }, 900);
    };

    // Init additional logo text animation
    function initAdditionalLogoText() {
        const additionalHeaderText = localStorage.getItem('additionalHeaderText') || 'ai';
        const additionalHeaderImg = document.querySelector(`.${additionalHeaderText}`);
        additionalHeaderImg.style.display = 'inline-block';
        setTimeout(() => additionalHeaderImg.parentNode.classList.add('show'), 1500);
        localStorage.setItem('additionalHeaderText', additionalHeaderText === 'ai' ? 'support' : 'ai');
    }

    const storyGamificationEl = document.querySelector('.story-gamification');
    const storyGamification = () => {
        if (window.innerWidth <= mobileWidth) {
            storyGamificationEl.querySelector('.mobile-image').classList.add('_visible');
        } else {
            let coefficient = 0;
            storyGamificationEl.querySelectorAll('.info__media .media-item').forEach((item) => {
                item.style.display = 'block';

                setTimeout(() => {
                    item.classList.add('_visible');
                }, 100 * coefficient);

                coefficient++;
            });
        }

        setTimeout(() => {
            storyGamificationEl.querySelector('.info__title').classList.add('_visible');
        }, 10);
        setTimeout(() => {
            storyGamificationEl.querySelector('.info__content').classList.add('_visible');
            storyGamificationEl.querySelector('.gamification-link').classList.add('_visible');
        }, 20);
    };

    const storyAnimation = (storyClass, imagesTimeout) => {
        const story = document.querySelector(storyClass);
        story.querySelector('.info__content').classList.add('_visible');
        story.querySelector('.info__media .bg-area').classList.add('_visible');
        story.querySelector('.info__title').classList.add('_visible');
        if (storyClass === '.story-5') {
            story.querySelectorAll('.testimonial-link').forEach((block) => block.classList.add('_visible'));
            story.querySelectorAll('.info__content').forEach((block) => block.classList.add('_visible'));
            story.querySelectorAll('.referrer').forEach((block) => block.classList.add('_visible'));
        }

        if (window.innerWidth <= mobileWidth) {
            if (storyClass === '.story-5') return;
            story.querySelector('.info__title--mobile').classList.add('_visible');
            story.querySelector('.mobile-image').classList.add('_visible');
        } else {
            let coefficient = 0;
            story.querySelectorAll('.info__media .media-item').forEach((item) => {
                item.style.display = 'block';
                setTimeout(() => {
                    item.classList.add('_visible');
                }, imagesTimeout * coefficient);

                coefficient++;
            });
        }
    };

    // Check animation conditions
    function animationInViewport() {
        if (isInViewport(document.querySelector('.hero__title'))) {
            interactive_hero();
        }

        if (isInViewport(document.querySelector('.heading-partners'))) {
            document.querySelector('.heading-features--basic').classList.add('_visible');
        }

        if (isInViewport(document.querySelector('.heading-features--basic'))) {
            document.querySelector('.heading-features--basic').classList.add('_visible');
        }

        if (isInViewport(document.querySelector('.heading-features--alt'))) {
            document.querySelector('.heading-features--alt').classList.add('_visible');
        }

        if (isInViewport(document.querySelector('.heading-pricing'))) {
            document.querySelector('.heading-pricing ').classList.add('_visible');
        }

        // story sections
        if (isInViewport(document.querySelector('.story-gamification .info__title'))) {
            storyGamification();
        }

        if (isInViewport(document.querySelector('.story-1 .info__title'))) {
            storyAnimation('.story-1', 100);
        }

        if (isInViewport(document.querySelector('.story-2 .info__title'))) {
            storyAnimation('.story-2', 300);
        }

        if (isInViewport(document.querySelector('.story-3 .info__title'))) {
            storyAnimation('.story-3', 100);
        }

        if (isInViewport(document.querySelector('.story-4 .info__title'))) {
            storyAnimation('.story-4', 200);
        }

        if (isInViewport(document.querySelector('.story-5 .info__title'))) {
            storyAnimation('.story-5', 200);
            reviews_slider.autoplay.start();
        }

        // slider autoplay
        if (isInViewport(document.querySelector('.clients-slider'))) {
            clients_slider.autoplay.start();
        }

        if (isInViewport(document.querySelector('.blog-heading'))) {
            blog_slider.autoplay.start();
        }
    }

    window.onscroll = function (event) {
        current_segment(hero, 50);
        animationInViewport();
    };

    initialLoading();
    nav_box.style.display = 'block';
    interactive_hero();

    // onload functions call
    current_segment(hero, 50);

    // Clients slider
    var clients_slider = new Swiper('.clients-slider', {
        speed: 600,
        slidesPerView: 2,
        slidesPerColumn: 1,

        slidesPerColumnFill: 'row',

        autoplay: {
            delay: 3000 * coefficient,
            disableOnInteraction: false,
        },

        pagination: {
            el: '.swiper-pagination',
            type: 'fraction',
            renderFraction: function (currentClass, totalClass) {
                return (
                    '0<span class="' +
                    currentClass +
                    '"></span>' +
                    '<span class="swiper-pagination-divider">/</span>' +
                    ' 0<span class="' +
                    totalClass +
                    '"></span>'
                );
            },
        },

        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        breakpoints: {
            320: {
                slidesPerView: 2,
                slidesPerColumn: 3,
            },
            480: {
                slidesPerView: 2,
                slidesPerColumn: 3,
            },
            768: {
                slidesPerView: 3,
                slidesPerColumn: 2,
            },
            1199: {
                slidesPerView: 3,
                slidesPerColumn: 2,
            },
        },
    });

    clients_slider.autoplay.stop();

    const reviews_slider = new Swiper('.reviews-slider', {
        speed: 600,
        slidesPerView: 1,
        slidesPerColumn: 1,

        slidesPerColumnFill: 'row',

        autoplay: {
            delay: 6000 * coefficient,
            disableOnInteraction: false,
        },

        pagination: {
            el: '.swiper-pagination',
            type: 'fraction',
            renderFraction: function (currentClass, totalClass) {
                return (
                    '0<span class="' +
                    currentClass +
                    '"></span>' +
                    '<span class="swiper-pagination-divider">/</span>' +
                    ' 0<span class="' +
                    totalClass +
                    '"></span>'
                );
            },
        },

        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    });

    reviews_slider.autoplay.stop();

    const blog_slider = new Swiper('.blog-slider', {
        speed: 600,
        slidesPerView: 1,
        spaceBetween: 30,
        slidesPerColumnFill: 'row',

        autoplay: {
            delay: 6000 * coefficient,
            disableOnInteraction: false,
        },

        pagination: {
            el: '.swiper-pagination',
            type: 'fraction',
            renderFraction: function (currentClass, totalClass) {
                return (
                    '0<span class="' +
                    currentClass +
                    '"></span>' +
                    '<span class="swiper-pagination-divider">/</span>' +
                    ' 0<span class="' +
                    totalClass +
                    '"></span>'
                );
            },
        },

        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        breakpoints: {
            991: {
                slidesPerView: 3,
                slidesPerColumn: 1,
            },
        },
    });

    blog_slider.autoplay.stop();

    const features_slider = new Swiper('.features-group', {
        speed: 600,
        slidesPerView: 2,
        slidesPerColumn: 1,

        initialSlide: 0,

        centeredSlides: true,

        slidesPerColumnFill: 'row',

        pagination: {
            el: '.swiper-pagination',
            type: 'fraction',
            renderFraction: function (currentClass, totalClass) {
                return (
                    '0<span class="' +
                    currentClass +
                    '"></span>' +
                    '<span class="swiper-pagination-divider">/</span>' +
                    ' 0<span class="' +
                    totalClass +
                    '"></span>'
                );
            },
        },

        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        breakpoints: {
            320: {
                slidesPerView: 1,
                slidesPerColumn: 3,
            },
            480: {
                slidesPerView: 1,
                slidesPerColumn: 3,
            },
            // 768: {
            //     slidesPerView: 1,
            //     slidesPerColumn: 3,
            // },
            // 1199: {
            //     slidesPerView: 3,
            //     slidesPerColumn: 2,
            // },
        },
    });

    var pricing_slider = new Swiper('.pricing-group', {
        slidesPerView: 'auto',

        centeredSlides: false,
        initialSlide: 0,

        simulateTouch: false,

        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },

        breakpoints: {
            1199: {
                slidesPerView: 0,
                spaceBetween: 0,
                watchOverflow: true,
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 0,
                watchOverflow: false,
            },
            400: {
                slidesPerView: 1,
                spaceBetween: 200,
                watchOverflow: false,
            },
            300: {
                slidesPerView: 1,
                spaceBetween: 200,
                watchOverflow: false,
            },
        },
    });
});
