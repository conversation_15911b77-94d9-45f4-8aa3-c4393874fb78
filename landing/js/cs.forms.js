document.addEventListener('DOMContentLoaded', function (event) {
    'use strict';
    window.fbAsyncInit = function () {
        FB.init({
            appId: apiKey.facebook.appId,
            oauth: true,
            status: true, // check login status
            cookie: true, // enable cookies to allow the server to access the session
            xfbml: true, // parse XFBML
            version: 'v2.9',
        });
    };
    (function (d, s, id) {
        let js,
            fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = '//connect.facebook.net/en_US/sdk.js#xfbml=1&appId=305967169564826&version=v2.9';
        fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
    const DOMAIN = 'https://' + location.hostname + '/hr/',
        DOMAIN_FROM_SOCIAL = 'https://' + location.hostname + '/!#/organizer/',
        base_easing = Power2.easeOut,
        coefficient = COEF_ANIMATION,
        MESSAGES_RU = JSON.parse(localStorage.getItem('[CS] LOCALIZATION MESSAGES RU') || '{}'),
        MESSAGES_EN = JSON.parse(localStorage.getItem('[CS] LOCALIZATION MESSAGES EN') || '{}'),
        MESSAGES_PL = JSON.parse(localStorage.getItem('[CS] LOCALIZATION MESSAGES PL') || '{}'),
        MESSAGES_UA = JSON.parse(localStorage.getItem('[CS] LOCALIZATION MESSAGES UA') || '{}'),
        is90DayPage = window.location.href.includes('90day');

    let LANG = document.documentElement.lang;
    if (LANG === 'uk') LANG = 'ua';
    window.dataLayer = window.dataLayer || [];

    function animationInViewport() {
        TweenMax.to('.page-title .text-box span', 1, {
            y: '-80px',
            delay: 0.1,
            ease: base_easing,
        });
        TweenMax.to('.banner__title .text-box span', 1, {
            y: '-80px',
            delay: 0.5,
            ease: base_easing,
        });
        TweenMax.to('.banner .btn', 0.3, {
            opacity: '1',
            delay: 0.5,
            ease: base_easing,
        });
        TweenMax.staggerTo(
            '.banner .banner__text',
            1,
            { opacity: '1', y: '-80px', delay: 0.2, ease: base_easing },
            0.4,
        );
    }

    // Promocode Logic

    const promocodeField = document.getElementById('promocode');

    if (promocodeField && window.location.search.includes('promocode')) {
        let promocode = window.location.search.split('promocode=')[1];
        promocodeField.value = promocode;
    }

    //Country code select
    const countryCode = document.querySelector('.country-code');
    if (countryCode) {
        const choiceInstance = new Choices(countryCode, {
            shouldSort: false,
            searchEnabled: true,
            searchFields: ['label', 'value'],
            searchChoices: true,
            itemSelectText: '',
            placeholder: true,
            placeholderValue: 'Phone',
            noResultsText: LANG === 'ru' ? 'Нет результатов' : 'No results found',
            choices: [
                {
                    value: 'Afghanistan',
                    label: '+93',
                    customProperties: {
                        shortcode: 'af',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Ukraine',
                    label: '+380',
                    selected: true,
                    customProperties: {
                        shortcode: 'ua',
                        mask: '(99) 999-99-99',
                    },
                },
                {
                    value: 'Belarus',
                    label: '+375',
                    customProperties: {
                        shortcode: 'by',
                        mask: '(9) 99-99-99-99',
                    },
                },
                {
                    value: 'Kazakhstan',
                    label: '+7',
                    customProperties: {
                        shortcode: 'kz',
                        mask: '(999) 999-99-99',
                    },
                },
                {
                    value: 'Uzbekistan',
                    label: '+998',
                    customProperties: {
                        shortcode: 'uz',
                        mask: '(9) 999-99-999',
                    },
                },
                {
                    value: 'Aland Islands',
                    label: '+358',
                    customProperties: {
                        shortcode: 'ax',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Albania',
                    label: '+355',
                    customProperties: {
                        shortcode: 'al',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Algeria',
                    label: '+213',
                    customProperties: {
                        shortcode: 'dz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'American Samoa',
                    label: '+1',
                    customProperties: {
                        shortcode: 'as',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Andorra',
                    label: '+376',
                    customProperties: {
                        shortcode: 'ad',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Angola',
                    label: '+244',
                    customProperties: {
                        shortcode: 'ao',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Anguilla',
                    label: '+1264',
                    customProperties: {
                        shortcode: 'ai',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Antarctica',
                    label: '+672',
                    customProperties: {
                        shortcode: 'aq',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Antigua And Barbuda',
                    label: '+1268',
                    customProperties: {
                        shortcode: 'ag',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Argentina',
                    label: '+54',
                    customProperties: {
                        shortcode: 'ar',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Armenia',
                    label: '+374',
                    customProperties: {
                        shortcode: 'am',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Aruba',
                    label: '+297',
                    customProperties: {
                        shortcode: 'aw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Australia',
                    label: '+61',
                    customProperties: {
                        shortcode: 'au',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Austria',
                    label: '+43',
                    customProperties: {
                        shortcode: 'at',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Azerbaijan',
                    label: '+994',
                    customProperties: {
                        shortcode: 'az',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bahamas',
                    label: '+1242',
                    customProperties: {
                        shortcode: 'bs',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bahrain',
                    label: '+973',
                    customProperties: {
                        shortcode: 'bh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bangladesh',
                    label: '+880',
                    customProperties: {
                        shortcode: 'bd',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Barbados',
                    label: '+1246',
                    customProperties: {
                        shortcode: 'bb',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Belgium',
                    label: '+32',
                    customProperties: {
                        shortcode: 'be',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Belize',
                    label: '+501',
                    customProperties: {
                        shortcode: 'bz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Benin',
                    label: '+229',
                    customProperties: {
                        shortcode: 'bj',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bermuda',
                    label: '+1441',
                    customProperties: {
                        shortcode: 'bm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bhutan',
                    label: '+975',
                    customProperties: {
                        shortcode: 'bt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bolivia',
                    label: '+591',
                    customProperties: {
                        shortcode: 'bo',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bosnia And Herzegovina',
                    label: '+387',
                    customProperties: {
                        shortcode: 'ba',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Botswana',
                    label: '+267',
                    customProperties: {
                        shortcode: 'bw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bouvet Island',
                    label: '+47',
                    customProperties: {
                        shortcode: 'bv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Brazil',
                    label: '+55',
                    customProperties: {
                        shortcode: 'br',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'British Indian Ocean Territory',
                    label: '+246',
                    customProperties: {
                        shortcode: 'io',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Brunei Darussalam',
                    label: '+673',
                    customProperties: {
                        shortcode: 'bn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Bulgaria',
                    label: '+359',
                    customProperties: {
                        shortcode: 'bg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Burkina Faso',
                    label: '+226',
                    customProperties: {
                        shortcode: 'bf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Burundi',
                    label: '+257',
                    customProperties: {
                        shortcode: 'bi',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cambodia',
                    label: '+855',
                    customProperties: {
                        shortcode: 'kh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cameroon',
                    label: '+237',
                    customProperties: {
                        shortcode: 'cm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Canada',
                    label: '+1',
                    customProperties: {
                        shortcode: 'ca',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cape Verde Islands',
                    label: '+238',
                    customProperties: {
                        shortcode: 'cv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cayman Islands',
                    label: '+1345',
                    customProperties: {
                        shortcode: 'ky',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Central African Republic',
                    label: '+236',
                    customProperties: {
                        shortcode: 'cf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Chad',
                    label: '+235',
                    customProperties: {
                        shortcode: 'td',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Chile',
                    label: '+56',
                    customProperties: {
                        shortcode: 'cl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'China',
                    label: '+86',
                    customProperties: {
                        shortcode: 'cn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Christmas Island',
                    label: '+672',
                    customProperties: {
                        shortcode: 'cx',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cocos (Keeling) Islands',
                    label: '+225',
                    customProperties: {
                        shortcode: 'cc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Colombia',
                    label: '+57',
                    customProperties: {
                        shortcode: 'co',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Comoros',
                    label: '+269',
                    customProperties: {
                        shortcode: 'km',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Congo',
                    label: '+242',
                    customProperties: {
                        shortcode: 'cg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Congo, Democratic Republic',
                    label: '+243',
                    customProperties: {
                        shortcode: 'cd',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cook Islands',
                    label: '+682',
                    customProperties: {
                        shortcode: 'ck',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Costa Rica',
                    label: '+506',
                    customProperties: {
                        shortcode: 'cr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: "Cote D'Ivoire",
                    label: '+225',
                    customProperties: {
                        shortcode: 'ci',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Croatia',
                    label: '+385',
                    customProperties: {
                        shortcode: 'hr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cuba',
                    label: '+53',
                    customProperties: {
                        shortcode: 'cu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Cyprus',
                    label: '+357',
                    customProperties: {
                        shortcode: 'cy',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Czech Republic',
                    label: '+420',
                    customProperties: {
                        shortcode: 'cz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Denmark',
                    label: '+45',
                    customProperties: {
                        shortcode: 'dk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Djibouti',
                    label: '+253',
                    customProperties: {
                        shortcode: 'dj',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Dominica',
                    label: '+1809',
                    customProperties: {
                        shortcode: 'dm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Dominican Republic',
                    label: '+1809',
                    customProperties: {
                        shortcode: 'do',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Ecuador',
                    label: '+593',
                    customProperties: {
                        shortcode: 'ec',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Egypt',
                    label: '+20',
                    customProperties: {
                        shortcode: 'eg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'El Salvador',
                    label: '+503',
                    customProperties: {
                        shortcode: 'sv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Equatorial Guinea',
                    label: '+240',
                    customProperties: {
                        shortcode: 'gq',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Eritrea',
                    label: '+291',
                    customProperties: {
                        shortcode: 'er',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Estonia',
                    label: '+372',
                    customProperties: {
                        shortcode: 'ee',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Ethiopia',
                    label: '+251',
                    customProperties: {
                        shortcode: 'et',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Falkland Islands',
                    label: '+500',
                    customProperties: {
                        shortcode: 'fk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Faroe Islands',
                    label: '+298',
                    customProperties: {
                        shortcode: 'fo',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Fiji',
                    label: '+679',
                    customProperties: {
                        shortcode: 'fj',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Finland',
                    label: '+358',
                    customProperties: {
                        shortcode: 'fi',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'France',
                    label: '+33',
                    customProperties: {
                        shortcode: 'fr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'French Guiana',
                    label: '+594',
                    customProperties: {
                        shortcode: 'gf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'French Polynesia',
                    label: '+689',
                    customProperties: {
                        shortcode: 'pf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'French Southern Territories',
                    label: '+689',
                    customProperties: {
                        shortcode: 'tf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Gabon',
                    label: '+241',
                    customProperties: {
                        shortcode: 'ga',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Gambia',
                    label: '+220',
                    customProperties: {
                        shortcode: 'gm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Georgia',
                    label: '+995',
                    customProperties: {
                        shortcode: 'ge',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Germany',
                    label: '+49',
                    customProperties: {
                        shortcode: 'de',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Ghana',
                    label: '+233',
                    customProperties: {
                        shortcode: 'gh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Gibraltar',
                    label: '+350',
                    customProperties: {
                        shortcode: 'gi',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Greece',
                    label: '+30',
                    customProperties: {
                        shortcode: 'gr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Greenland',
                    label: '+299',
                    customProperties: {
                        shortcode: 'gl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guadeloupe',
                    label: '+590',
                    customProperties: {
                        shortcode: 'gp',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guam',
                    label: '+671',
                    customProperties: {
                        shortcode: 'gu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guatemala',
                    label: '+502',
                    customProperties: {
                        shortcode: 'gt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guernsey',
                    label: '+44',
                    customProperties: {
                        shortcode: 'gg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guinea',
                    label: '+224',
                    customProperties: {
                        shortcode: 'gn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guinea-Bissau',
                    label: '+245',
                    customProperties: {
                        shortcode: 'gw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Guyana',
                    label: '+592',
                    customProperties: {
                        shortcode: 'gy',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Haiti',
                    label: '+509',
                    customProperties: {
                        shortcode: 'ht',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Heard Island & Mcdonald Islands',
                    label: '+61',
                    customProperties: {
                        shortcode: 'hm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Holy See (Vatican City State)',
                    label: '+39',
                    customProperties: {
                        shortcode: 'va',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Honduras',
                    label: '+504',
                    customProperties: {
                        shortcode: 'hn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Hong Kong',
                    label: '+852',
                    customProperties: {
                        shortcode: 'hk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Hungary',
                    label: '+36',
                    customProperties: {
                        shortcode: 'hu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Iceland',
                    label: '+354',
                    customProperties: {
                        shortcode: 'is',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'India',
                    label: '+91',
                    customProperties: {
                        shortcode: 'in',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Indonesia',
                    label: '+62',
                    customProperties: {
                        shortcode: 'id',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Iran, Islamic Republic Of',
                    label: '+98',
                    customProperties: {
                        shortcode: 'ir',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Iraq',
                    label: '+964',
                    customProperties: {
                        shortcode: 'iq',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Ireland',
                    label: '+353',
                    customProperties: {
                        shortcode: 'ie',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Isle Of Man',
                    label: '+44',
                    customProperties: {
                        shortcode: 'im',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Israel',
                    label: '+972',
                    customProperties: {
                        shortcode: 'il',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Italy',
                    label: '+39',
                    customProperties: {
                        shortcode: 'it',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Jamaica',
                    label: '+1876',
                    customProperties: {
                        shortcode: 'jm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Japan',
                    label: '+81',
                    customProperties: {
                        shortcode: 'jp',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Jersey',
                    label: '+44-1534',
                    customProperties: {
                        shortcode: 'je',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Jordan',
                    label: '+962',
                    customProperties: {
                        shortcode: 'jo',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Kenya',
                    label: '+254',
                    customProperties: {
                        shortcode: 'ke',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Kiribati',
                    label: '+686',
                    customProperties: {
                        shortcode: 'ki',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Korea - North',
                    label: '+850',
                    customProperties: {
                        shortcode: 'kp',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Korea - South',
                    label: '+82',
                    customProperties: {
                        shortcode: 'kr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Kuwait',
                    label: '+965',
                    customProperties: {
                        shortcode: 'kw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Kyrgyzstan',
                    label: '+996',
                    customProperties: {
                        shortcode: 'kg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: "Lao People's Democratic Republic",
                    label: '+856',
                    customProperties: {
                        shortcode: 'la',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Latvia',
                    label: '+371',
                    customProperties: {
                        shortcode: 'lv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Lebanon',
                    label: '+961',
                    customProperties: {
                        shortcode: 'lb',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Lesotho',
                    label: '+266',
                    customProperties: {
                        shortcode: 'ls',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Liberia',
                    label: '+231',
                    customProperties: {
                        shortcode: 'lr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Libyan Arab Jamahiriya',
                    label: '+218',
                    customProperties: {
                        shortcode: 'ly',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Liechtenstein',
                    label: '+417',
                    customProperties: {
                        shortcode: 'li',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Lithuania',
                    label: '+370',
                    customProperties: {
                        shortcode: 'lt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Luxembourg',
                    label: '+352',
                    customProperties: {
                        shortcode: 'lu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Macao',
                    label: '+853',
                    customProperties: {
                        shortcode: 'mo',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Macedonia',
                    label: '+389',
                    customProperties: {
                        shortcode: 'mk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Madagascar',
                    label: '+261',
                    customProperties: {
                        shortcode: 'mg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Malawi',
                    label: '+265',
                    customProperties: {
                        shortcode: 'mw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Malaysia',
                    label: '+60',
                    customProperties: {
                        shortcode: 'my',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Maldives',
                    label: '+960',
                    customProperties: {
                        shortcode: 'mv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mali',
                    label: '+223',
                    customProperties: {
                        shortcode: 'ml',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Malta',
                    label: '+356',
                    customProperties: {
                        shortcode: 'mt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Marshall Islands',
                    label: '+692',
                    customProperties: {
                        shortcode: 'mh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Martinique',
                    label: '+596',
                    customProperties: {
                        shortcode: 'mq',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mauritania',
                    label: '+222',
                    customProperties: {
                        shortcode: 'mr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mauritius',
                    label: '+230',
                    customProperties: {
                        shortcode: 'mu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mayotte',
                    label: '+269',
                    customProperties: {
                        shortcode: 'yt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mexico',
                    label: '+52',
                    customProperties: {
                        shortcode: 'mx',
                        mask: '9999999999',
                    },
                },
                {
                    value: 'Micronesia, Federated States Of',
                    label: '+691',
                    customProperties: {
                        shortcode: 'fm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Moldova',
                    label: '+373',
                    customProperties: {
                        shortcode: 'md',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Monaco',
                    label: '+377',
                    customProperties: {
                        shortcode: 'mc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mongolia',
                    label: '+976',
                    customProperties: {
                        shortcode: 'mn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Montenegro',
                    label: '+382',
                    customProperties: {
                        shortcode: 'me',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Montserrat',
                    label: '+1664',
                    customProperties: {
                        shortcode: 'ms',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Morocco',
                    label: '+212',
                    customProperties: {
                        shortcode: 'ma',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Mozambique',
                    label: '+258',
                    customProperties: {
                        shortcode: 'mz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Myanmar',
                    label: '+95',
                    customProperties: {
                        shortcode: 'mm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Namibia',
                    label: '+264',
                    customProperties: {
                        shortcode: 'na',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Nauru',
                    label: '+674',
                    customProperties: {
                        shortcode: 'nr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Nepal',
                    label: '+977',
                    customProperties: {
                        shortcode: 'np',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Netherlands',
                    label: '+31',
                    customProperties: {
                        shortcode: 'nl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Netherlands Antilles',
                    label: '+599',
                    customProperties: {
                        shortcode: 'an',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'New Caledonia',
                    label: '+687',
                    customProperties: {
                        shortcode: 'nc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'New Zealand',
                    label: '+64',
                    customProperties: {
                        shortcode: 'nz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Nicaragua',
                    label: '+505',
                    customProperties: {
                        shortcode: 'ni',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Niger',
                    label: '+227',
                    customProperties: {
                        shortcode: 'ne',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Nigeria',
                    label: '+234',
                    customProperties: {
                        shortcode: 'ng',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Niue',
                    label: '+683',
                    customProperties: {
                        shortcode: 'nu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Norfolk Island',
                    label: '+672',
                    customProperties: {
                        shortcode: 'nf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Northern Mariana Islands',
                    label: '+670',
                    customProperties: {
                        shortcode: 'mp',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Norway',
                    label: '+47',
                    customProperties: {
                        shortcode: 'no',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Oman',
                    label: '+968',
                    customProperties: {
                        shortcode: 'om',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Pakistan',
                    label: '+92',
                    customProperties: {
                        shortcode: 'pk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Palau',
                    label: '+680',
                    customProperties: {
                        shortcode: 'pw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Palestinian Territory',
                    label: '+970',
                    customProperties: {
                        shortcode: 'ps',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Panama',
                    label: '+507',
                    customProperties: {
                        shortcode: 'pa',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Papua New Guinea',
                    label: '+675',
                    customProperties: {
                        shortcode: 'pg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Paraguay',
                    label: '+595',
                    customProperties: {
                        shortcode: 'py',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Peru',
                    label: '+51',
                    customProperties: {
                        shortcode: 'pe',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Philippines',
                    label: '+63',
                    customProperties: {
                        shortcode: 'ph',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Pitcairn',
                    label: '+64',
                    customProperties: {
                        shortcode: 'pn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Poland',
                    label: '+48',
                    customProperties: {
                        shortcode: 'pl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Portugal',
                    label: '+351',
                    customProperties: {
                        shortcode: 'pt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Puerto Rico',
                    label: '+1787',
                    customProperties: {
                        shortcode: 'pr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Qatar',
                    label: '+974',
                    customProperties: {
                        shortcode: 'qa',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Reunion',
                    label: '+262',
                    customProperties: {
                        shortcode: 're',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Romania',
                    label: '+40',
                    customProperties: {
                        shortcode: 'ro',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Rwanda',
                    label: '+250',
                    customProperties: {
                        shortcode: 'rw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Barthelemy',
                    label: '+590',
                    customProperties: {
                        shortcode: 'bl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Helena',
                    label: '+94',
                    customProperties: {
                        shortcode: 'sh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Kitts And Nevis',
                    label: '+94',
                    customProperties: {
                        shortcode: 'kn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'SSaint Lucia',
                    label: '+94',
                    customProperties: {
                        shortcode: 'lc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Martin',
                    label: '+590',
                    customProperties: {
                        shortcode: 'mf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Pierre And Miquelon',
                    label: '+508',
                    customProperties: {
                        shortcode: 'pm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saint Vincent And Grenadines',
                    label: '+1-784',
                    customProperties: {
                        shortcode: 'vc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Samoa',
                    label: '+685',
                    customProperties: {
                        shortcode: 'ws',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'San Marino',
                    label: '+378',
                    customProperties: {
                        shortcode: 'sm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Sao Tome And Principe',
                    label: '+239',
                    customProperties: {
                        shortcode: 'st',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Saudi Arabia',
                    label: '+966',
                    customProperties: {
                        shortcode: 'sa',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Senegal',
                    label: '+221',
                    customProperties: {
                        shortcode: 'sn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Serbia',
                    label: '+381',
                    customProperties: {
                        shortcode: 'rs',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Seychelles',
                    label: '+248',
                    customProperties: {
                        shortcode: 'sc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Sierra Leone',
                    label: '+232',
                    customProperties: {
                        shortcode: 'sl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Singapore',
                    label: '+65',
                    customProperties: {
                        shortcode: 'sg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Slovakia',
                    label: '+421',
                    customProperties: {
                        shortcode: 'sk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Slovenia',
                    label: '+386',
                    customProperties: {
                        shortcode: 'si',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Solomon Islands',
                    label: '+677',
                    customProperties: {
                        shortcode: 'sb',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Somalia',
                    label: '+252',
                    customProperties: {
                        shortcode: 'so',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'South Africa',
                    label: '+27',
                    customProperties: {
                        shortcode: 'za',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'South Georgia And Sandwich Isl.',
                    label: '+500',
                    customProperties: {
                        shortcode: 'gs',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Spain',
                    label: '+34',
                    customProperties: {
                        shortcode: 'es',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Sri Lanka',
                    label: '+94',
                    customProperties: {
                        shortcode: 'lk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Sudan',
                    label: '+249',
                    customProperties: {
                        shortcode: 'sd',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Suriname',
                    label: '+597',
                    customProperties: {
                        shortcode: 'sr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Svalbard And Jan Mayen',
                    label: '+47',
                    customProperties: {
                        shortcode: 'sj',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Swaziland',
                    label: '+268',
                    customProperties: {
                        shortcode: 'sz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Sweden',
                    label: '+46',
                    customProperties: {
                        shortcode: 'se',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Switzerland',
                    label: '+41',
                    customProperties: {
                        shortcode: 'ch',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Syrian Arab Republic',
                    label: '+963',
                    customProperties: {
                        shortcode: 'sy',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Taiwan',
                    label: '+886',
                    customProperties: {
                        shortcode: 'tw',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tajikistan',
                    label: '+992',
                    customProperties: {
                        shortcode: 'tj',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tanzania',
                    label: '+255',
                    customProperties: {
                        shortcode: 'tz',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Thailand',
                    label: '+66',
                    customProperties: {
                        shortcode: 'th',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Timor-Leste',
                    label: '+670',
                    customProperties: {
                        shortcode: 'tl',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Togo',
                    label: '+228',
                    customProperties: {
                        shortcode: 'tg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tokelau',
                    label: '+690',
                    customProperties: {
                        shortcode: 'tk',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tonga',
                    label: '+676',
                    customProperties: {
                        shortcode: 'to',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Trinidad And Tobago',
                    label: '+1868',
                    customProperties: {
                        shortcode: 'tt',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tunisia',
                    label: '+216',
                    customProperties: {
                        shortcode: 'tn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Turkey',
                    label: '+90',
                    customProperties: {
                        shortcode: 'tr',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Turkmenistan',
                    label: '+993',
                    customProperties: {
                        shortcode: 'tm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Turks And Caicos Islands',
                    label: '+1649',
                    customProperties: {
                        shortcode: 'tc',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Tuvalu',
                    label: '+668',
                    customProperties: {
                        shortcode: 'tv',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Uganda',
                    label: '+256',
                    customProperties: {
                        shortcode: 'ug',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'United Arab Emirates',
                    label: '+971',
                    customProperties: {
                        shortcode: 'ae',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'United Kingdom',
                    label: '+44',
                    customProperties: {
                        shortcode: 'gb',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'United States',
                    label: '+1',
                    customProperties: {
                        shortcode: 'us',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'United States Outlying Islands',
                    label: '+1-340',
                    customProperties: {
                        shortcode: 'um',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Uruguay',
                    label: '+598',
                    customProperties: {
                        shortcode: 'uy',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Vanuatu',
                    label: '+678',
                    customProperties: {
                        shortcode: 'vu',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Venezuela',
                    label: '+58',
                    customProperties: {
                        shortcode: 've',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Vietnam',
                    label: '+84',
                    customProperties: {
                        shortcode: 'vn',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Virgin Islands, British',
                    label: '+1',
                    customProperties: {
                        shortcode: 'vg',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Virgin Islands, U.S.',
                    label: '+1',
                    customProperties: {
                        shortcode: 'vi',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Wallis And Futuna',
                    label: '+681',
                    customProperties: {
                        shortcode: 'wf',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Western Sahara',
                    label: '+212',
                    customProperties: {
                        shortcode: 'eh',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Yemen (North)',
                    label: '+969',
                    customProperties: {
                        shortcode: 'ye',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Yemen (South)',
                    label: '+967',
                    customProperties: {
                        shortcode: 'ye',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Zambia',
                    label: '+260',
                    customProperties: {
                        shortcode: 'zm',
                        mask: '99999999999999999',
                    },
                },
                {
                    value: 'Zimbabwe',
                    label: '+263',
                    customProperties: {
                        shortcode: 'zw',
                        mask: '99999999999999999',
                    },
                },
            ],
            callbackOnInit: function () {
                const loads = document.getElementsByClassName('onloading');

                if (loads && loads.length) {
                    Array.prototype.forEach.call(loads, function (v) {
                        v.classList.remove('onloading');
                    });
                }

                animationInViewport();
            },
            callbackOnCreateTemplates: function (template) {
                const itemSelectText = this.config.itemSelectText,
                    countryInput = document.querySelector('[name="country"]'),
                    prefixPhoneInput = document.querySelector('[name="prefixPhone"]'),
                    phoneInputLabel = document.querySelector('.user-tel__label'),
                    phoneInput = document.querySelector('[name="phone"]');
                let onRebuild,
                    count = 1;

                return {
                    item: function (classNames, data) {
                        countryInput.focus();
                        countryInput.value = data.value;
                        countryInput.blur();
                        prefixPhoneInput.value = data.label;
                        phoneInputLabel.innerText = data.label;

                        // set padding left for phone input depending on the width of the country code.
                        phoneInput.style.paddingLeft = phoneInputLabel.offsetWidth + 15 + 'px';

                        // phoneInput.setAttribute('placeholder', data.customProperties.mask);
                        phoneInput.setAttribute('mask', data.customProperties.mask);
                        phoneInput.value = '';

                        onSuperMask();

                        // if (!onRebuild) {
                        //     onRebuild = onSuperMask();
                        // } else {
                        //     count++ && !(count % 3) && onRebuild();
                        //     // !(count % 3) && onLazyLoad();
                        // }

                        return template(`
                                <div  class="${classNames.item} ${
                            data.highlighted ? classNames.highlightedState : classNames.itemSelectable
                        }" data-item data-id="${data.id}" data-value="${data.value}" ${
                            data.active ? 'aria-selected="true"' : ''
                        } ${data.disabled ? 'aria-disabled="true"' : ''}>
                                    <span class="flag flag--${data.customProperties.shortcode} visible"></span>
                                    <span>${data.value}</span>
                                </div>
                            `);
                    },
                    choice: function (classNames, data) {
                        return template(`
                                <div class="${classNames.item} ${classNames.itemChoice} ${
                            data.disabled ? classNames.itemDisabled : classNames.itemSelectable
                        }" data-select-text="${itemSelectText}" data-choice ${
                            data.disabled ? 'data-choice-disabled aria-disabled="true"' : 'data-choice-selectable'
                        } data-id="${data.id}" data-value="${data.value}" ${
                            data.groupId > 0 ? 'role="treeitem"' : 'role="option"'
                        }>
                                    <span class="flag flag--${data.customProperties.shortcode} lazy-background"></span>
                                    <span>${data.value} (${data.label})</span>
                                </div>
                            `);
                    },
                };
            },
        });

        countryCode.addEventListener('showDropdown', onLazyLoad, false);
        // lazy load for country input
        document.querySelector('.choices__input.choices__input--cloned').addEventListener('input', () => {
            setTimeout(() => {
                let lazyImages = [].slice.call(document.querySelectorAll('.lazy-background'));
                lazyImages.forEach(function (lazyImage) {
                    lazyImage.classList.add('visible');
                    lazyImage.classList.remove('lazy-background');
                });
            }, 300);
        });

        getUserBy('country').then(function (country) {
            choiceInstance.setChoiceByValue(country);
        });
    } else {
        animationInViewport();
    }
    // Password visibility
    const toggle_pass = document.querySelectorAll('.toggle-pass');

    if (toggle_pass.length) {
        const togglePass = function (e) {
            e.preventDefault();
            var password = this.parentNode.querySelectorAll('[id*="password"]');

            if (password.length > 0 && this.dataset.reveal !== 'reveal') {
                this.dataset.reveal = 'reveal';
                for (let i = 0; i < password.length; i++) {
                    password[i].type = 'text';
                }
            } else {
                this.dataset.reveal = 'hide';
                for (let i = 0; i < password.length; i++) {
                    password[i].type = 'password';
                }
            }

            this.classList.toggle('change');
        };

        for (let i = 0; i < toggle_pass.length; i++) {
            toggle_pass[i].addEventListener('click', togglePass, false);
        }
    }

    //Password error
    const passwordField = document.getElementById('user-password');
    const passwordConfirmField = document.getElementById('user-re-password');
    const passwordError = document.querySelector('.password-error');
    const passwordErrorObj = {
        passwordLength: {
            ru: 'Пароль должен содержать не менее 8 символов',
            en: 'The password must be at least 8 characters',
            ua: 'Пароль має містити не менше 8 символів',
            pl: 'Hasło musi zawierać co najmniej 8 znaków',
        },
        passwordDoesNotNumber: {
            ru: 'Пароль должен содержать хотя бы одну цифру',
            en: 'Password must contain at least one number',
            ua: 'Пароль має містити хоча б одну цифру',
            pl: 'Hasło musi zawierać co najmniej jedną cyfrę',
        },
        passwordDoesNotWord: {
            ru: 'Пароль должен содержать хотя бы одну букву',
            en: 'Password must contain at least one word',
            ua: 'Пароль має містити хоча б одну букву',
            pl: 'Hasło musi zawierać co najmniej jedną literę',
        },
        passwordIncludesCyrillic: {
            ru: 'Пароль не может содержать кириллицу',
            en: 'The password cannot contain Cyrillic',
            ua: 'Пароль не може містити кирилицю',
            pl: 'Hasło nie może zawierać cyrylicy',
        },
        passwordsDoesNotMatches: {
            ru: 'Пароли не совпадают',
            en: 'Passwords mismatch',
            ua: 'Паролі не співпадають',
            pl: 'Hasła nie pasują do siebie',
        },
    };
    const validatePassword = function () {
        if (passwordConfirmField) {
            if (passwordField.value.length < 8) {
                passwordError.innerText = passwordErrorObj.passwordLength[LANG];
            } else if (!/^[A-Za-z.!#$%@&"'*+/=?^_`{|(),-;:\\}~\d]{8,}$/.test(passwordField.value)) {
                passwordError.innerText = passwordErrorObj.passwordIncludesCyrillic[LANG];
            } else if (
                passwordField.value.length >= 8 &&
                /^[A-Za-z.!#$%@&"'*+/=?^_`{|(),-;:\\}~\d]{8,}$/.test(passwordField.value) &&
                passwordConfirmField.value.length > 0 &&
                passwordConfirmField.value !== passwordField.value
            ) {
                passwordError.innerText = passwordErrorObj.passwordsDoesNotMatches[LANG];
            } else if (!/^(?=.*\d)(?=.*[a-zA-Z0-9!,.?%$#@*_\-+=\\|/[\]{}()]).{8,30}$/.test(passwordField.value)) {
                passwordError.innerText = passwordErrorObj.passwordDoesNotNumber[LANG];
            } else if (/[0-9]+/i.test(passwordField.value) && !/[a-zA-Z]/.test(passwordField.value)) {
                passwordField.classList.add('invalid');
                passwordError.innerText = passwordErrorObj.passwordDoesNotWord[LANG];
            } else {
                passwordError.innerText = '';
            }
        } else {
            if (passwordField.value.length < 8) {
                passwordError.innerText = passwordErrorObj.passwordLength[LANG];
            } else if (!/^[A-Za-z.!#$%@&"'*+/=?^_`{|(),-;:\\}~\d]{8,}$/.test(passwordField.value)) {
                passwordError.innerText = passwordErrorObj.passwordIncludesCyrillic[LANG];
            } else if (
                passwordField.value.length >= 8 &&
                /^[A-Za-z.!#$%@&"'*+/=?^_`{|(),-;:\\}~\d]{8,}$/.test(passwordField.value)
            ) {
            } else if (!/^(?=.*\d)(?=.*[a-zA-Z0-9!,.?%$#@*_\-+=\\|/[\]{}()]).{8,30}$/.test(passwordField.value)) {
                passwordError.innerText = passwordErrorObj.passwordDoesNotNumber[LANG];
            } else if (/[0-9]+/i.test(passwordField.value) && !/[a-zA-Z]/.test(passwordField.value)) {
                passwordError.innerText = passwordErrorObj.passwordDoesNotWord[LANG];
            } else {
                passwordError.innerText = '';
            }
        }
    };
    passwordField && passwordField.addEventListener('blur', validatePassword);
    passwordConfirmField && passwordConfirmField.addEventListener('blur', validatePassword);

    //Duplicate company
    const companyField = document.getElementById('companyName');
    const companyDuplicateErrorForm = document.querySelector('.alert-company-icon');
    const companyErrorForm = document.querySelector('#companyName ~ .form__error');
    const errorMessage = document.getElementById('error-message');
    function onErrorIcon() {
        errorMessage.style.display = 'block';
    }
    function offErrorIcon() {
        errorMessage.style.display = 'none';
    }
    const debounce = (fn, ms) => {
        let timeout;
        return function () {
            const fnCall = () => fn.apply(this, arguments);
            clearTimeout(timeout);
            timeout = setTimeout(fnCall, ms);
        };
    };
    function checkCompany(e) {
        companyDuplicateErrorForm.style.display = 'none';
        return new Promise(function (resolve) {
            fetchHTTP({
                url: DOMAIN + `public/company/nameDuplicate`,
                method: 'post',
                dataType: 'json',
                data: {
                    orgName: e.target.value,
                    lang: LANG,
                },
            })
                .then((resp) => {
                    if (resp.message) {
                        companyErrorForm.style.display = 'none';
                        companyDuplicateErrorForm.style.display = 'inline-block';
                        companyDuplicateErrorForm.addEventListener('mouseover', onErrorIcon);
                        companyDuplicateErrorForm.addEventListener('mouseout', offErrorIcon);
                        // companyField.classList.add('invalid')
                        document.querySelector('.tooltip').innerHTML = resp.message;
                        if (!companyField.value) {
                            companyDuplicateErrorForm.removeEventListener('mouseover', onErrorIcon);
                            companyDuplicateErrorForm.removeEventListener('mouseout', offErrorIcon);
                            companyDuplicateErrorForm.style.display = 'none';
                            companyErrorForm.style.display = 'block';
                        }
                    }
                    if (resp.message === '') {
                        companyDuplicateErrorForm.removeEventListener('mouseover', onErrorIcon);
                        companyDuplicateErrorForm.removeEventListener('mouseout', offErrorIcon);
                        companyDuplicateErrorForm.style.display = 'none';
                        if (!companyField.value) {
                            companyErrorForm.style.display = 'block';
                        }
                    }
                })
                .catch((error) => {
                    resolve(error);
                });
        });
    }

    let checkCompanyDebounce = debounce(checkCompany, 300);
    if (companyField !== null && !is90DayPage) {
        companyField.addEventListener('keyup', checkCompanyDebounce);
    }
    // Referral link logic
    function initReferralInfo() {
        let location = window.location.href;
        let url = new URL(location);
        let referralCode = url.searchParams.get('aff_code');
        if (referralCode) {
            localStorage.setItem('referralCode', JSON.stringify(referralCode));
            getReferralInfo(referralCode, location);
        } else {
            let existedCode = JSON.parse(localStorage.getItem('referralCode'));
            if (existedCode) getReferralInfo(existedCode, location);
        }

        function getReferralInfo(referral) {
            fetchHTTP({
                url: DOMAIN + `referral/getReferrerInfo?userId=${referral}`,
                method: 'get',
                dataType: 'json',
                data: { userId: referral },
            })
                .then((data) => {
                    if (data.status === 'ok' && data.object.orgId && data.object.userId) {
                        let elmsIds = ['referralInfo', 'referralInfoGoogle', 'referralInfoFb'];
                        elmsIds.forEach(function (oneId) {
                            let oneElm = document.getElementById(oneId);
                            if (oneElm) {
                                let text = oneElm.innerHTML;
                                text = text
                                    .replace('{Referrer_name}', data.object.userName)
                                    .replace('{Referrer_company}', data.object.orgName);
                                oneElm.innerHTML = text;
                                oneElm.style.display = 'inline-block';
                            }
                        });
                    }
                })
                .catch((error) => {
                    console.error('get referral info response: ', error);
                });
        }
    }

    initReferralInfo();

    /**
     * Здеся будем вызывать функцию конструктор The Валидатор
     */
    var btnSubmit = document.querySelector('[type="submit"]'),
        errorServer = document.getElementById('server_error'),
        value,
        validator,
        signUp = {
            facebook: false,
            google: false,
        };

    /**
     * Page Request
     * */
    if (hasPage('request')) {
        value = [
            {
                name: 'firstName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'lastName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'phone',
                rules: 'required|phone_number',
            },
            {
                name: 'email',
                rules: 'required|valid_email',
            },
            {
                name: 'companyName',
                rules: 'required',
            },
            {
                name: 'requirements',
                rules: 'min_length[0]',
            },
        ];
        validator = new FormValidator('form-request', value, onValidator);
    } else if (hasPage('signup')) {
        /**
         * Page Sign Up
         * */
        value = [
            {
                name: 'firstName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'lastName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'country',
                rules: 'required',
            },
            {
                name: 'prefixPhone',
                rules: 'required',
            },
            {
                name: 'phone',
                rules: 'required|phone_number',
            },
            {
                name: 'email',
                rules: 'required|valid_email',
            },
            {
                name: 'companyName',
                rules: 'required',
            },
            {
                name: 'userPassword',
                rules: 'required|valid_pass',
            },
            {
                name: 'userRepassword',
                rules: 'required|matches[userPassword]',
            },
            {
                name: 'promocode',
                rules: 'min_length[1]',
            },
            {
                name: 'agreement',
                rules: 'required',
            },
        ];
        validator = new FormValidator('form-register', value, onValidator);
    } else if (hasPage('signup-bf')) {
        /**
         * Page Sign Up for Black Friday
         * */
        value = [
            {
                name: 'firstName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'lastName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'country',
                rules: 'required',
            },
            {
                name: 'prefixPhone',
                rules: 'required',
            },
            {
                name: 'phone',
                rules: 'required|phone_number',
            },
            {
                name: 'email',
                rules: 'required|valid_email',
            },
            {
                name: 'companyName',
                rules: 'required',
            },
            {
                name: 'userPassword',
                rules: 'required|valid_pass',
            },
            {
                name: 'userRepassword',
                rules: 'required|matches[userPassword]',
            },
            {
                name: 'promocode',
                rules: 'required|min_length[1]',
            },
            {
                name: 'agreement',
                rules: 'required',
            },
        ];
        validator = new FormValidator('form-register', value, onValidator);
    } else if (hasPage('make-request')) {
        /**
         * Page Make request
         * */
        value = [
            {
                name: 'firstName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'lastName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'phone',
                rules: 'required|phone_number',
            },
            {
                name: 'email',
                rules: 'required|valid_email',
            },
            {
                name: 'companyName',
                rules: 'required',
            },
            {
                name: 'requirements',
                rules: 'min_length[0]',
            },
        ];
        validator = new FormValidator('form-make-request', value, onValidator);
    } else if (hasPage('90day')) {
        /**
         * Page 90 day
         * */
        value = [
            {
                name: 'fullName',
                rules: 'required|alpha|min_length[3]|max_length[50]',
            },
            {
                name: 'country',
                rules: 'required',
            },
            {
                name: 'prefixPhone',
                rules: 'required',
            },
            {
                name: 'phone',
                rules: 'required|phone_number',
            },
            {
                name: 'email',
                rules: 'required|valid_email',
            },
            {
                name: 'companyName',
                rules: 'required',
            },
            {
                name: 'userPassword',
                rules: 'required|valid_pass',
            },
            {
                name: 'agreement',
                rules: 'required',
            },
        ];
        validator = new FormValidator('form90day', value, onValidator);
    }

    function getFormParams(fields) {
        const params = {};
        Object.keys(fields).reduce(function (acum, param) {
            if (!fields[param]) return acum;

            acum[param] = fields[param].value;
            return acum;
        }, params);

        return params;
    }

    function onValidator(error, event) {
        event.preventDefault();

        btnSubmit.disabled = Boolean(error.length);
        if (passwordError && passwordError.innerText) {
            btnSubmit.disabled = true;
        }

        if (is90DayPage) {
            btnSubmit.disabled = false;
        }

        if (event.type === 'submit') {
            if (is90DayPage) {
                btnSubmit.disabled = false;
                validator.validateAllFields();
                if (validator.errors.length) return;
            }

            onValidatorSubmit();
        }
    }

    function onValidatorSubmit() {
        var fields = getFormParams(validator.fields);
        var prefixPhoneField = document.querySelector('[name="prefixPhone"]');
        var countryField = document.querySelector('[name="country"]');

        // Script for get cookie Google Analytics
        var match = document.cookie.match('(?:^|;)\\s*_ga=([^;]*)');
        var raw = match ? decodeURIComponent(match[1]) : null;
        if (raw) {
            match = raw.match(/(\d+\.\d+)$/);
        }
        var gacid = match ? match[1] : null;

        try {
            /**
             * [Page] Request
             */
            if (hasPage('request')) {
                // onSubmit(validator, 'public/newLead');
                fetchHTTP({
                    url: DOMAIN + 'public/newLead',
                    method: 'post',
                    dataType: 'json',
                    data: {
                        firstName: fields.firstName,
                        lastName: fields.lastName,
                        phone: prefixPhoneField.value + fields.phone,
                        email: fields.email,
                        companyName: fields.companyName,
                        country: localStorage.getItem('country') || countryField.value,
                        keyRequirements: fields.requirements,
                        utms: localStorage.getItem('UTMS'),
                        sessions: localStorage.getItem('session'),
                        ga: gacid,
                        intention: 'Request demo',
                    },
                })
                    .then(function (res) {
                        // Сделать что то после ответа
                        setTimeout(appendCodeTrackingCapterra);
                        onToggleModal(true);
                        onResetForm(validator.fields);
                        sentAnalyticEvents('Personal_present', 'Submit', 'Features');

                        // prettier-ignore
                        window.dataLayer.push({
                            'event': 'registration',
                            'form_name': 'demo',
                        });
                    })
                    .catch(function (error) {
                        innerErrorMessage(errorServer, error.code);
                    });
            } else if (hasPage('90day')) {
                fetchHTTP({
                    url: DOMAIN + 'person/registration',
                    method: 'post',
                    dataType: 'json',
                    data: {
                        fullName: fields.fullName,
                        countryCustom: fields.prefixPhone,
                        phone: fields.prefixPhone + fields.phone,
                        login: fields.email,
                        orgName: fields.companyName,
                        password: fields.userPassword,
                        password2: fields.userPassword,
                        country: fields.country,
                        lang: LANG,
                        terms: fields.agreement,
                        ga: gacid,
                        intention: localStorage.getItem('tarifParams'),
                        utms: localStorage.getItem('UTMS'),
                        sessions: localStorage.getItem('session'),
                        source: JSON.parse(localStorage.getItem('referralCode')),
                        offer: '90dayTrial',
                    },
                })
                    .then(function (res) {
                        if (res.personId) {
                            localStorage.removeItem('referralCode');
                        }

                        localStorage.removeItem('UTMS');
                        localStorage.removeItem('session');

                        if (res.status === 'forbidden') {
                            innerErrorMessage(errorServer, res.status);
                        } else {
                            _redirectToFinish('finish90.html');
                        }
                    })
                    .catch(function (error) {
                        innerErrorMessage(errorServer, error.code);
                    });
            } else if (hasPage('make-request')) {
                /**
                 * [Page] Make request
                 */
                fetchHTTP({
                    url: DOMAIN + 'public/newLead',
                    method: 'post',
                    dataType: 'json',
                    data: {
                        firstName: fields.firstName,
                        lastName: fields.lastName,
                        phone: prefixPhoneField.value + fields.phone,
                        email: fields.email,
                        companyName: fields.companyName,
                        country: localStorage.getItem('country') || countryField.value,
                        keyRequirements: fields.requirements,
                        utms: localStorage.getItem('UTMS'),
                        sessions: localStorage.getItem('session'),
                        intention: localStorage.getItem('intention'),
                    },
                })
                    .then(function (res) {
                        // Сделать что то после ответа
                        var blocks = document.querySelectorAll('.section--block');

                        // Block length 2!
                        // На странице с формой, выделенно две секции <section>
                        // После успешного ответа от сервера, секцию с формой скрываем,
                        // вторую показываем.
                        // prettier-ignore
                        window.dataLayer.push({
                            'event': 'registration',
                            'form_name': 'demo',
                        });

                        if (blocks && blocks.length === 2) {
                            blocks[0].classList.remove('active');
                            blocks[1].classList.add('active');
                            TweenMax.to('.page-title .text-box span', 1.2 * coefficient, {
                                y: '-80px',
                                ease: base_easing,
                            });
                            TweenMax.to('.page-figure', 1.2 * coefficient, {
                                y: '-30px',
                                ease: base_easing,
                            });
                        }
                    })
                    .catch(function (error) {
                        innerErrorMessage(errorServer, error.code);
                    });
            } else if (hasPage('signup') || hasPage('signup-bf')) {
                /**
                 * [Page] Sign up
                 */
                /**
                 * Google
                 */
                if (signUp.google) {
                    fetchHTTP({
                        url: DOMAIN + 'person/registration/google',
                        method: 'post',
                        dataType: 'json',
                        data: {
                            login: fields.email,
                            firstName: fields.firstName,
                            lastName: fields.lastName,
                            orgName: fields.companyName,
                            country: fields.country,
                            phone: fields.prefixPhone + fields.phone,
                            password: fields.userPassword,
                            password2: fields.userRepassword,
                            promocode: fields.promocode || '',
                            terms: fields.agreement,
                            systemLink: signUp.code,
                            social: 'google',
                            lang: LANG,
                            ga: gacid,
                            intention: localStorage.getItem('tarifParams'),
                            utms: localStorage.getItem('UTMS'),
                            sessions: localStorage.getItem('session'),
                            source: JSON.parse(localStorage.getItem('referralCode')),
                        },
                    })
                        .then(function (res) {
                            if (res.personId) {
                                localStorage.removeItem('referralCode');
                            }
                            // Сделать что то после ответа регистрации
                            localStorage.removeItem('UTMS');
                            localStorage.removeItem('session');
                            localStorage.setItem('dataLayer', 'true');

                            _redirectToFinishFromSocial();
                        })
                        .catch(function (error) {
                            innerErrorMessage(errorServer, error.code);
                        });
                } else if (signUp.facebook) {
                    /**
                     * Facebook
                     */
                    fetchHTTP({
                        url: DOMAIN + 'person/registration/facebook',
                        method: 'post',
                        dataType: 'json',
                        data: {
                            login: fields.email,
                            firstName: fields.firstName,
                            lastName: fields.lastName,
                            orgName: fields.companyName,
                            country: fields.country,
                            phone: fields.prefixPhone + fields.phone,
                            password: fields.userPassword,
                            password2: fields.userRepassword,
                            promocode: fields.promocode || '',
                            terms: fields.agreement,
                            ga: gacid,
                            lang: LANG,
                            systemLink: signUp.code,
                            social: 'facebook',
                            intention: localStorage.getItem('tarifParams'),
                            utms: localStorage.getItem('UTMS'),
                            sessions: localStorage.getItem('session'),
                            source: JSON.parse(localStorage.getItem('referralCode')),
                        },
                    })
                        .then(function (res) {
                            if (res.personId) {
                                localStorage.removeItem('referralCode');
                            }
                            // Сделать что то после ответа регистрации
                            localStorage.removeItem('UTMS');
                            localStorage.removeItem('session');
                            localStorage.setItem('dataLayer', 'true');

                            _redirectToFinishFromSocial();
                        })
                        .catch(function (error) {
                            innerErrorMessage(errorServer, error.code);
                        });
                } else {
                    /**
                     * Clever staff
                     */
                    fetchHTTP({
                        url: DOMAIN + 'person/registration',
                        method: 'post',
                        dataType: 'json',
                        data: {
                            firstName: fields.firstName,
                            lastName: fields.lastName,
                            countryCustom: fields.prefixPhone,
                            phone: fields.prefixPhone + fields.phone,
                            login: fields.email,
                            orgName: fields.companyName,
                            password: fields.userPassword,
                            password2: fields.userRepassword,
                            promocode: fields.promocode || '',
                            country: fields.country,
                            lang: LANG,
                            terms: fields.agreement,
                            ga: gacid,
                            intention: localStorage.getItem('tarifParams'),
                            utms: localStorage.getItem('UTMS'),
                            sessions: localStorage.getItem('session'),
                            source: JSON.parse(localStorage.getItem('referralCode')),
                        },
                    })
                        .then(function (res) {
                            if (res.personId) {
                                localStorage.removeItem('referralCode');
                            }
                            // Сделать что то после ответа регистрации
                            localStorage.removeItem('UTMS');
                            localStorage.removeItem('session');
                            if (res.status === 'forbidden') {
                                innerErrorMessage(errorServer, res.status);
                            } else {
                                localStorage.setItem('dataLayer', 'true');
                                _redirectToFinish();
                            }
                        })
                        .catch(function (error) {
                            innerErrorMessage(errorServer, error.code);
                        });
                }
            }
        } catch (error) {
            innerErrorMessage(errorServer, error.code);
        }

        function _redirectToFinish(target = 'finishreg.html') {
            let lang = localStorage.getItem('NG_TRANSLATE_LANG_KEY');
            localStorage.setItem('lang_when_register', lang);
            // var target = 'finishreg.html',
            let link = '';

            switch (LANG) {
                case 'en':
                    link = target;
                    break;
                case 'ua':
                    link = 'ua/' + target;
                    break;
                case 'ru':
                    link = 'ru/' + target;
                    break;
                case 'pl':
                    link = 'pl/' + target;
                    break;
                default:
                    link = 'ua/' + target;
            }

            redirectTo(DOMAIN.replace('hr/', '') + link);
        }

        function _redirectToFinishFromSocial() {
            var target = 'finishregsocial.html',
                link = '';

            switch (LANG) {
                case 'en':
                    link = target;
                    break;
                case 'ua':
                    link = 'ua/' + target;
                    break;
                case 'ru':
                    link = 'ru/' + target;
                    break;
                case 'pl':
                    link = 'pl/' + target;
                    break;
                default:
                    link = 'ua/' + target;
            }

            redirectTo(DOMAIN.replace('hr/', '') + link);
        }
    }

    function hasPage(name) {
        var path = location.pathname;
        return (
            path === '/' + name + '.html' ||
            path === '/ru/' + name + '.html' ||
            path === '/ua/' + name + '.html' ||
            path === '/pl/' + name + '.html'
        );
    }

    function innerErrorMessage(target, code) {
        if (!target) return message;

        if (LANG === 'ru') {
            return (target.innerHTML = getMessageLocal(code, 'ru'));
        } else if (LANG === 'en') {
            return (target.innerHTML = getMessageLocal(code, 'en'));
        } else if (LANG === 'ua') {
            return (target.innerHTML = getMessageLocal(code, 'ua'));
        } else if (LANG === 'pl') {
            return (target.innerHTML = getMessageLocal(code, 'pl'));
        }
    }

    function redirectTo(link) {
        window.location.href = link;
    }

    /**
     * Здеся логика модального окна
     */

    var modalWindow = document.getElementById('modal'),
        modalClose;

    if (modalWindow) {
        modalClose = modalWindow.querySelector('.close');
        if (modalClose) {
            modalClose.addEventListener('click', onToggleModal.bind(null, false), false);
        }
    }

    function onToggleModal(state) {
        state ? modalWindow.classList.add('active') : modalWindow.classList.remove('active');
    }

    function onResetForm(fields) {
        if (!fields) return;

        for (let key in fields) {
            const field = fields[key];

            if (!field) continue;

            field.value = field.element.value = '';
        }
    }

    function onUpdateForm(fields, options) {
        if (!fields && !options) return;

        for (let key in options) {
            const field = fields[key];

            if (!field) continue;

            field.value = field.element.value = options[key];
        }
    }

    /**
     * Здеся логика связанная с метрикой стороних сервисов
     */

    function appendCodeTrackingCapterra() {
        // Вставка кода отслеживания Capterra T-3849
        const key = 'bc970293055597d3198446f1c3ace5cf',
            id = '2096814',
            prefix = 'https:' == document.location.protocol ? 'https://ct.capterra.com' : 'http://ct.capterra.com';

        (function () {
            const ct = document.createElement('script');
            ct.type = 'text/javascript';
            ct.async = true;
            ct.src = prefix + '/capterra_tracker.js?vid=' + id + '&vkey=' + key;
            const s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ct, s);
        })();
    }

    function sentAnalyticEvents(eventCategory, eventAction) {
        try {
            if (location.hostname.search('cleverstaff.net') > -1) {
                ga('send', 'event', eventCategory, eventAction);
                fbq('track', eventCategory);
            }
        } catch (error) {
            console.error('Analytic Events', error);
        }
    }

    /**
     * Sign Up with Google & Facebook
     */

    var btnGoogleUp = document.getElementById('signUpGoogle'),
        btnFacebookUp = document.getElementById('signUpFacebook');

    btnGoogleUp && btnGoogleUp.addEventListener('click', onSignUpGoogle, false);
    btnFacebookUp && btnFacebookUp.addEventListener('click', onSignUpFacebook, false);

    function onSignUpGoogle(e) {
        e.preventDefault();

        var win = window.open(GOOGLE_URL, 'windowname1', getPopupParams());
        var pollTimer = window.setInterval(function () {
            try {
                if (!win.opener) {
                    window.clearInterval(pollTimer);
                }
                if (win.document.URL.indexOf(gup(GOOGLE_URL, 'redirect_uri')) !== -1) {
                    window.clearInterval(pollTimer);
                    var url = win.document.URL,
                        code = gup(url, 'code'),
                        access_token = gup(url, 'access_token');

                    win.close();

                    fetchHTTP({
                        url: 'https://www.googleapis.com/oauth2/v1/userinfo?access_token=' + access_token,
                        data: null,
                        dataType: 'json',
                        method: 'get',
                    })
                        .then(function (res) {
                            if (location.hostname.search('cleverstaff.net') > -1) {
                                fbq('track', 'Registration');
                                onUpdateForm(validator.fields, {
                                    firstName: res.given_name,
                                    lastName: res.family_name,
                                    email: res.email,
                                });
                                signUp.google = true;
                                signUp.facebook = false;
                                signUp.code = code;
                            }
                        })
                        .catch(function (error) {
                            innerErrorMessage(errorServer, error.code);
                        });
                }
            } catch (e) {}
        }, 500);
    }

    function onSignUpFacebook(e) {
        e.preventDefault();

        FB.login(
            function (response) {
                if (response.authResponse) {
                    var code = response.authResponse.accessToken;

                    FB.api('/me?fields=email,first_name,last_name', function (user) {
                        try {
                            if (location.hostname.search('cleverstaff.net') > -1) {
                                fbq('track', 'Registration');
                                onUpdateForm(validator.fields, {
                                    firstName: user.first_name,
                                    lastName: user.last_name,
                                    email: user.email,
                                });
                                signUp.google = false;
                                signUp.facebook = true;
                                signUp.code = code;
                            }
                        } catch (err) {
                            console.error('Facebook Pixel', err);
                        }
                    });
                } else {
                }
            },
            { scope: 'email' },
        );
    }

    function getPopupParams() {
        var w = 650,
            h = 550,
            left = screen.width / 2 - w / 2,
            top = screen.height / 2 - h / 2;

        return 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left;
    }

    function gup(url, name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');

        var regexS = '[\\?&]' + name + '=([^&#]*)',
            regex = new RegExp(regexS),
            results = regex.exec(url);

        return results === null ? '' : results[1];
    }

    /**
     * @private fetchHTTP
     * @argument options
     * @type object:
     *      url, data, method
     */
    function fetchHTTP(options) {
        return new Promise(function (resolve, reject) {
            var xhr = new XMLHttpRequest();

            xhr.open(options.method.toLowerCase(), options.url, true);

            if (options.dataType === 'json') {
                xhr.setRequestHeader('Content-type', 'application/json; charset=utf-8');
            }

            xhr.onload = function () {
                const location = window.location.href;

                // if (location.includes('webinar.html')) {
                //     return;
                // }

                if (xhr.status > 300) return reject(xhr.response);

                if (xhr.readyState == 4 && xhr.response) {
                    var response = JSON.parse(xhr.response);

                    if (response && response.status === 'error') {
                        reject(response);
                    }

                    resolve(response);
                }
            };
            xhr.onerror = function (err) {
                xhr.abort();
            };
            xhr.send(options.data ? JSON.stringify(options.data) : null);
        });
    }

    /**
     * @private getMessageLocal
     *
     * @argument KEY: string
     */

    function getMessageLocal(key, lang) {
        if (lang === 'ru' && MESSAGES_RU && MESSAGES_RU[key]) {
            return MESSAGES_RU[key];
        } else if (lang === 'en' && MESSAGES_EN && MESSAGES_EN[key]) {
            return MESSAGES_EN[key];
        } else if (lang === 'ua' && MESSAGES_UA && MESSAGES_UA[key]) {
            return MESSAGES_UA[key];
        } else if (lang === 'pl' && MESSAGES_PL && MESSAGES_PL[key]) {
            return MESSAGES_PL[key];
        }
        return '';
    }

    /**
     * @private onSuperMask
     */
    var listenerInput, listenerKeypress, listenerChange, listenerPaste;

    function onSuperMask() {
        function _getMaskFields() {
            return Array.from(document.getElementsByClassName('maskField'));
        }

        function _getMasks(maskFields) {
            return maskFields.map((input) => input.getAttribute('mask'));
        }

        function _shouldThisInvalidDigitBeHere(digit, mask) {
            return digit == mask;
        }

        let maskFields = _getMaskFields();
        let masks = _getMasks(maskFields);
        _setupMaskEvents(maskFields, masks);

        // Rebuilding a mask if you change the mask attr dynamically
        function rebuildMasks() {
            let errorForRus = document.querySelector('#errorForRus'),
                removeError = document.querySelector('#telephone-error'),
                btnSubmit = document.querySelector('[type="submit"]');
            maskFields = _getMaskFields();
            masks = _getMasks(maskFields);
            _setupMaskEvents(maskFields, masks);

            // logic for russians numbers phone
            if (masks.join('') === 'disabled') {
                maskFields[0].disabled = true;
                errorForRus.style.display = 'block';
                errorForRus.style.color = '#312E37';
                btnSubmit.disabled = true;
                errorForRus.style.marginBottom = '18px';
                removeError.style.display = 'none';
            } else {
                maskFields[0].disabled = false;
                if (errorForRus) errorForRus.style.display = 'none';
                removeError.style.display = 'block';
            }
        }

        // Ёпта, моё дополнение к этой библиотеки, должно заработать!

        // Constructor function
        function _setupMaskEvents(maskFields, masks) {
            maskFields.forEach((field, index) => {
                if (listenerInput) {
                    field.removeEventListener('input', listenerInput, false);
                }
                listenerInput = function (event) {
                    switch (event.inputType) {
                        case 'deleteContentForward':
                            _validateBackspaceButton(field, masks[index], 'delete');
                            break;

                        case 'deleteContentBackward':
                            _validateBackspaceButton(field, masks[index]);
                            break;

                        case 'insertFromDrop':
                            _fullyValidateChange(field, masks[index], event);
                            break;
                    }
                };
                field.addEventListener('input', listenerInput, false);

                if (listenerKeypress) {
                    field.removeEventListener('keypress', listenerKeypress, false);
                }
                listenerKeypress = function (event) {
                    _validateMask(field, masks[index], event);
                };
                field.addEventListener('keypress', listenerKeypress, false);

                if (listenerChange) {
                    field.removeEventListener('change', listenerChange, false);
                }
                listenerChange = function (event) {
                    _fullyValidateChange(field, masks[index], event);
                };
                field.addEventListener('change', listenerChange, false);

                if (listenerPaste) {
                    field.removeEventListener('paste', listenerPaste, false);
                }
                listenerPaste = function (event) {
                    if (!field.hasAttribute('blockpasting')) {
                        field.value = (event.clipboardData || window.clipboardData).getData('text');
                        _fullyValidateChange(field, masks[index], event);
                    }
                    event.preventDefault();
                };
                field.addEventListener('paste', listenerPaste, false);
            });
        }

        // Function to validate mask on type, it'll also add symbols
        function _validateMask(field, mask, event) {
            let lastDigitPosition = field.value.length;
            let splitMask = mask.split('');

            try {
                if (!_validateDigit(splitMask, lastDigitPosition, event.key, field)) event.preventDefault();
            } catch (e) {
                event.preventDefault();
            }
        }

        // Capsule function to validate field value after focusout
        function _fullyValidateChange(field, mask, event) {
            _addSymbols(field, mask);
            fullyValidateMask(field, mask, event);
        }

        // Function to validate every digit
        function fullyValidateMask(field, mask, event) {
            let validatedMask = '';
            let finalSymbols = _getFinalMaskSymbols(mask);

            try {
                field.value.split('').forEach((digit, index) => {
                    if (_validateDigit(mask.split(''), index, digit, field, true))
                        return (validatedMask = validatedMask.concat(digit));

                    if (_shouldThisInvalidDigitBeHere(digit, mask[index]))
                        return (validatedMask = validatedMask.concat(digit));

                    throw 'invalid input';
                });
            } catch (e) {
                return (field.value = validatedMask + finalSymbols.join(''));
            }
        }

        // Function used to catch any symbols a mask might have at the end (such as AAA---)
        function _getFinalMaskSymbols(mask) {
            let finalSymbols = [];
            let reverseSplitMask = mask.split('').reverse();
            try {
                reverseSplitMask.forEach((digit) => {
                    if (digit.isSymbol()) finalSymbols.push(digit);
                    else throw 'Reached a non-symbolic value';
                });
            } catch (e) {}
            return finalSymbols;
        }

        // Will add every symbol the mask should have
        function _addSymbols(field, mask) {
            const textErrorForTel = document.querySelector('#telephone-error'),
                backgroundError = document.querySelector('#user-tel');
            if (field.value.length == 0) return field.value;
            let splitMask = mask.split('');
            let splitValue = field.value.split('');

            let phoneLength = backgroundError.getAttribute('Mask');
            if (phoneLength === '99999999999999999') {
                phoneLength = '9999';
            }

            if (hasPage('request')) {
                value = [
                    {
                        name: 'firstName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'lastName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'phone',
                        rules: `required|phone_number|min_length[${phoneLength.length}]`,
                    },
                    {
                        name: 'email',
                        rules: 'required|valid_email',
                    },
                    {
                        name: 'companyName',
                        rules: 'required',
                    },
                    {
                        name: 'requirements',
                        rules: 'min_length[0]',
                    },
                ];
                validator = new FormValidator('form-request', value, onValidator);
            } else if (hasPage('signup')) {
                value = [
                    {
                        name: 'firstName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'lastName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'country',
                        rules: 'required',
                    },
                    {
                        name: 'prefixPhone',
                        rules: 'required',
                    },
                    {
                        name: 'phone',
                        rules: `required|phone_number|min_length[${phoneLength.length}]`,
                    },
                    {
                        name: 'email',
                        rules: 'required|valid_email',
                    },
                    {
                        name: 'companyName',
                        rules: 'required',
                    },
                    {
                        name: 'userPassword',
                        rules: 'required|valid_pass',
                    },
                    {
                        name: 'userRepassword',
                        rules: 'required|matches[userPassword]',
                    },
                    {
                        name: 'promocode',
                        rules: 'min_length[1]',
                    },
                    {
                        name: 'agreement',
                        rules: 'required',
                    },
                ];
                validator = new FormValidator('form-register', value, onValidator);
            } else if (hasPage('make-request')) {
                value = [
                    {
                        name: 'firstName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'lastName',
                        rules: 'required|alpha|min_length[3]|max_length[50]',
                    },
                    {
                        name: 'phone',
                        rules: `required|phone_number|min_length[${phoneLength.length}]`,
                    },
                    {
                        name: 'email',
                        rules: 'required|valid_email',
                    },
                    {
                        name: 'companyName',
                        rules: 'required',
                    },
                    {
                        name: 'requirements',
                        rules: 'min_length[0]',
                    },
                ];
                validator = new FormValidator('form-make-request', value, onValidator);
            }

            if (field.value.length < phoneLength.length) {
                textErrorForTel.style.opacity = 1;
                backgroundError.style.backgroundColor = 'rgb(231, 90, 58, 0.2)';
                backgroundError.style.border = '1px solid #e75a3a';

                if (is90DayPage) {
                    backgroundError.style.backgroundColor = '#faded8';
                }
            } else {
                textErrorForTel.style.opacity = 0;
                backgroundError.style.backgroundColor = '#eff2f7';
                backgroundError.style.border = '1px solid #d6e0e7';

                if (is90DayPage) {
                    backgroundError.style.backgroundColor = '#efefef80';
                    backgroundError.style.border = 'none';
                }
            }

            try {
                splitMask.forEach((digit, index) => {
                    if (index > splitValue.length) throw 'mask is bigger than input value';
                    if (digit.isSymbol() && splitValue[index] != digit)
                        splitValue = _insertSymbol(splitValue.join(''), index, digit).split('');
                });
            } catch (e) {}
            field.value = splitValue.join('');
        }

        // Function used to insert symbol at specific position
        function _insertSymbol(fieldValue, index, symbol) {
            let splitValue = fieldValue.split('');
            let firstHalf = fieldValue.substr(0, index);
            let secondHalf = splitValue.splice(index).join('');
            return firstHalf + symbol + secondHalf;
        }

        //Function to make backspace unable to delete a symbol at the middle of the field
        function _validateBackspaceButton(field, mask, deletionType = 'backspace') {
            let caretPosition = _getCaretPos(field);
            let deletedCharacter = mask[caretPosition];

            if (deletedCharacter.isSymbol() && field.value.split('')[caretPosition]) {
                field.value = _insertSymbol(field.value, caretPosition, deletedCharacter);

                switch (deletionType) {
                    case 'backspace':
                        _setCaretPosition(field, caretPosition);
                        break;

                    case 'delete':
                        _setCaretPosition(field, caretPosition + 1);
                        break;
                }
            }
        }

        // Will validate a specific digit of the mask
        function _validateDigit(splitMask, lastDigitPosition, key, field, isFullValidation = false) {
            let digitMask = splitMask[lastDigitPosition];

            if (key == ' ') return false;

            if (digitMask.isSymbol()) {
                if (!isFullValidation) {
                    field.value = _concatSymbols(field, splitMask, lastDigitPosition);
                    return _validateDigit(splitMask, field.value.length, key, field);
                }
                return _validateDigit(splitMask, lastDigitPosition + 1, key, field, true);
            }

            if (+digitMask || digitMask == '0') return _numericValidation(digitMask, key, field);

            if (digitMask.isUpperCaseLetter() && key.isUpperCaseLetter()) return true;

            if (digitMask.isLowerCaseLetter() && key.isLowerCaseLetter()) return true;

            if (digitMask == 'Á' && key.isLetter()) return true;

            if (digitMask == 'Ã') return true;
        }

        // Function to concat symbols at the end of mask
        function _concatSymbols(field, splitMask, lastDigitPosition) {
            let symbols = '';
            for (let i = lastDigitPosition; i < splitMask.length; i++) {
                if (splitMask[i].isSymbol()) symbols = symbols.concat(splitMask[i]);
                else break;
            }
            return field.value.concat(symbols);
        }

        // Will set the carret position
        function _setCaretPosition(field, caretPos) {
            if (field.createTextRange) {
                let range = field.createTextRange();
                range.move('character', caretPos);
                range.select();
            } else if (field.selectionStart) {
                field.focus();
                field.setSelectionRange(caretPos, caretPos);
            } else field.focus();
        }

        // Will get the carret position
        function _getCaretPos(field) {
            let range, bookmark, caret_pos;
            if (document.selection && document.selection.createRange) {
                range = document.selection.createRange();
                bookmark = range.getBookmark();
                caret_pos = bookmark.charCodeAt(2) - 2;
            } else if (field.setSelectionRange) caret_pos = field.selectionStart;

            return caret_pos;
        }

        // Will validate a number in a specific position
        function _numericValidation(digitMask, key, field) {
            if (field.hasAttribute('inverted-numbers'))
                if (+key >= digitMask) return true;
                else return false;
            else if (+key <= digitMask) return true;
            else return false;
        }

        return rebuildMasks;
    }

    /**
     * Lazy Load for
     */

    function onLazyLoad() {
        let lazyBackgrounds = [].slice.call(document.querySelectorAll('.lazy-background'));

        if ('IntersectionObserver' in window) {
            let lazyBackgroundObserver = new IntersectionObserver(function (entries, observer) {
                entries.forEach(function (entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        lazyBackgroundObserver.unobserve(entry.target);
                    }
                });
            });

            lazyBackgrounds.forEach(function (lazyBackground) {
                lazyBackgroundObserver.observe(lazyBackground);
            });
        } else {
            // Поддержим у кого хреновые компутеры ;)
            let lazyImages = [].slice.call(document.querySelectorAll('.lazy-background'));
            let active = false;

            const lazyLoad = function () {
                if (active === false) {
                    active = true;

                    setTimeout(function () {
                        lazyImages.forEach(function (lazyImage) {
                            if (
                                lazyImage.getBoundingClientRect().top <= window.innerHeight &&
                                lazyImage.getBoundingClientRect().bottom >= 0
                            ) {
                                lazyImage.classList.add('visible');
                                lazyImage.classList.remove('lazy-background');

                                lazyImages = lazyImages.filter(function (image) {
                                    return image !== lazyImage;
                                });

                                if (lazyImages.length === 0) {
                                    document.removeEventListener('scroll', lazyLoad);
                                    window.removeEventListener('resize', lazyLoad);
                                    window.removeEventListener('orientationchange', lazyLoad);
                                }
                            }
                        });

                        active = false;
                    }, 200);
                }
            };

            document.addEventListener('scroll', lazyLoad);
            window.addEventListener('resize', lazyLoad);
            window.addEventListener('orientationchange', lazyLoad);
        }
    }

    // onLazyLoad();

    /**
     * @private getUserBy(key)
     * argument key:
     * @type string
     * use async FetchHTTP or localStorage
     * example: { "country":"Ukraine", "countryCode":"UA", "city":"Kyiv", "ip":"***********" }
     */

    function getUserBy(key) {
        return new Promise(function (resolve) {
            fetchHTTP({
                url: DOMAIN + 'public/getUserLocation',
                method: 'get',
                dataType: 'json',
                data: null,
            })
                .then(function (res) {
                    resolve(res[key]);
                })
                .catch(function (error) {
                    resolve('Ukraine');
                });
        });
    }

    // (function saveUtms() {
    //     let match = document.cookie.match('(?:^|;)\\s*_ga=([^;]*)');
    //     let raw = match ? decodeURIComponent(match[1]) : null;
    //     if (raw) {
    //         match = raw.match(/(\d+\.\d+)$/);
    //     }
    //     let gacid = match ? match[1] : null;
    //
    //     fetchHTTP({
    //         url: DOMAIN + '/public/saveUtms',
    //         method: 'post',
    //         dataType: 'json',
    //         data: {
    //             ga: gacid,
    //             utms: localStorage.getItem('UTMS'),
    //         },
    //     });
    // })();
});

/**
 * Extend String prototype
 */

String.prototype.isSymbol = function () {
    return !!this.match(/(?![Ã])(?![Á])[_\W]/);
};

String.prototype.isLowerCaseLetter = function () {
    return !!this.match(/[a-z]/);
};

String.prototype.isUpperCaseLetter = function () {
    return !!this.match(/[A-Z]/);
};

String.prototype.isLetter = function () {
    return !!this.match(/[a-zA-Z]/);
};
