// *** <PERSON><PERSON>ts ***
if (require_assets.indexOf('swiper') > -1)
    // Swiper slider
    script(src="https://unpkg.com/swiper@5.4.5/js/swiper.min.js")

if (require_assets.indexOf('scroll') > -1)
    script(src="https://cdnjs.cloudflare.com/ajax/libs/gsap/2.1.3/plugins/ScrollToPlugin.min.js")

script(src="https://cdnjs.cloudflare.com/ajax/libs/gsap/2.1.3/TimelineMax.min.js")
script(src="https://cdnjs.cloudflare.com/ajax/libs/gsap/2.1.3/plugins/CSSPlugin.min.js")
script(src="https://cdnjs.cloudflare.com/ajax/libs/gsap/2.1.3/TweenMax.min.js")

script(src='https://cdn.jsdelivr.net/npm/jquery@3.6.1/dist/jquery.slim.min.js')
script(src='https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js')
script(src='https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js')


//Hubspot tracking code
script.
    var script = document.createElement('script');
    script.src = "//js.hs-scripts.com/3833222.js";
    script.id = "hs-script-loader";
    script.setAttribute('async', "");
    script.setAttribute('defer', "");
    document.head.appendChild(script);

if (require_assets.indexOf('choices') > -1)
    // Choices
    script(src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js")

if (language_code != "ru")
    if (require_assets.indexOf('tabs') > -1)
        // Tabs
        script(src="modules/van11y-accessible-tab-panel-aria/van11y-accessible-tab-panel-aria.min.js")


if (language_code == "ru")
    if (require_assets.indexOf('tabs') > -1)
        // Tabs
        script(src="../modules/van11y-accessible-tab-panel-aria/van11y-accessible-tab-panel-aria.min.js")


// Primary javascript file
// en version

script(src="/external/js/" + "cs.validator.js?v=918")
script(src="/external/js/" + "cs.forms.js?v=918")

if(page_name === "Login" || page_name === "Войти" || page_name === "Увійти")
    - var src = isDevMode ? "js/" : "/external/js/"
    script(src=src + "cs.signIn.js?v=918")
if (language_code != "ru")
    - var src = isDevMode ? "js/" : "/external/js/"

    script(src=src + "api.key.const.js?v=918")
    script(src=src + "cs.js?v=918")
    //script(src=src + "cookie.policy.js?v=918")
    script(src=src + "cs.terms.js")


    if (page_type == 'homepage')
        script(src=src + "cs.home90day.js?v=918")

    if (page_type == 'forms')
        script(src=src + "cs.validator.js?v=918")
        script(src=src + "cs.forms.js?v=918")


    if (page_type == 'inner')
        script(src=src + "cs.inner.js?v=918")

    if (page_specific == 'contact')
        script(src=src + "cs.contact.js?v=918")

    if (page_specific == 'price')
        script(src=src + "cs.price.js?v=918")

// lang version
if (language_code == "ru")
    - var src = isDevMode ? "../js/" : "/external/js/"

    script(src=src + "api.key.const.js?v=918")
    script(src=src + "cs.js?v=918")
    //script(src=src + "cookie.policy.js?v=918")
    script(src=src + "cs.terms.js")

    if (page_type == 'homepage')
        script(src=src + "cs.home90day.js?v=918")

    if (page_type == 'forms')
        script(src=src + "cs.validator.js?v=918")
        script(src=src + "cs.forms.js?v=918")

    if (page_type == 'inner')
        script(src=src + "cs.inner.js?v=918")

    if (page_specific == 'contact')
        script(src=src + "cs.contact.js?v=918")

    if (page_specific == 'price')
        script(src=src + "cs.price.js?v=918")
//utm tags
script.
    function hasUrlParameterWithoutQ() {
        var sPageURL = window.location.search.substring(1);
        var sURLVariables = sPageURL.split('&');
        for (var i = 0; i < sURLVariables.length; i++) {
            var sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] !== "q") {
                return true;
            }
        }
        return false;
    }

    if (hasUrlParameterWithoutQ()) {
        var sPageURL = window.location.search.substring(1);
        if (sPageURL && sPageURL !== '') {
            localStorage.setItem("UTMS", sPageURL);
        }
    }
// utm tags end
