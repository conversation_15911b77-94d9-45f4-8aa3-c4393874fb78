fieldset(class=input_value[5] || '', style={ 'position': 'relative' }).form__group.email-input
    if (input_value[2])
        label(for=input_value[0]).form__label= input_value[2]
            if (input_value[4])
    input(name=input_value[0], id="meeting-email", type=input_value[1], placeholder=input_value[3]).form__input
    span#form__mail-indicator(style={
        'position': 'absolute',
        'left': '82px',
        'top': '8px'
    }).form__indicator.email-name *
    span#error-email-pl Wprowadź swój email
    if (error)
        .form__error #{error}
