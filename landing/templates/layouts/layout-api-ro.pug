- var isDevMode = false;
- var src_images = isDevMode ? "/images" : "/external/images"
- var language_code = "ru"

doctype html
html(lang="ru")

    head
        block page
        include ../includes/common/head-common.html
        include ../includes/head-api-ru.pug

    body
        noscript
            iframe(src="https://www.googletagmanager.com/ns.html?id=GTM-K9SHJT6", height="0", width="0", style="display:none;visibility:hidden")
        .is-ie-modal(id="ie-modal")
            .is-ie-modal-close(id='close-ie-modal')
            if(language_code === "ru")
                .is-ie-modal-title Internet Explorer не подходит для работы в CleverStaff, перейдите в Chrome или Firefox
            if(language_code !== "ru")
                .is-ie-modal-title Internet Explorer is not suitable for CleverStaff, go to Chrome or Firefox
            .is-ie-modal-icons
                .is-ie-modal-icons-chrome.is-ie-lazyLoading.chrome-logo
                .is-ie-modal-icons-chrome.is-ie-lazyLoading.firefox-logo
        if (page_type != 'inner')
            .preloader
                .preloader__base
                .preloader__brand
                    img(src=src_images + "/logo.svg", alt="CleverStaff")

        svg(width="0", height="0").hidden
            symbol(id="icon-user", viewBox="0 0 16 16")
                title user
                path(d="M4.87927 11.1499C3.52982 11.6197 2.29299 12.3958 1.25387 13.4441C0.778436 13.9237 0.359361 14.4451 0 15H1.8631C3.34082 13.2446 5.54369 12.1289 8.00001 12.1289C10.4563 12.1289 12.6592 13.2446 14.1369 15H16C15.6406 14.4451 15.2216 13.9237 14.7461 13.4441C13.707 12.3958 12.4702 11.6197 11.1207 11.1499C12.566 10.1457 13.5156 8.46494 13.5156 6.56445C13.5156 3.49622 11.0413 1 8.00001 1C4.95873 1 2.48443 3.49622 2.48443 6.56445C2.48443 8.46494 3.43397 10.1457 4.87927 11.1499ZM12.0249 6.56445C12.0249 8.80347 10.2193 10.625 8.00001 10.625C5.7807 10.625 3.97513 8.80347 3.97513 6.56445C3.97513 4.32544 5.7807 2.50391 8.00001 2.50391C10.2193 2.50391 12.0249 4.32544 12.0249 6.56445Z")

            symbol(id="icon-phone", viewBox="0 0 24 24")
                title phone
                path(d="M9 1h7a4 4 0 014 4v14a4 4 0 01-4 4H9a4 4 0 01-4-4V5a4 4 0 014-4zm0 2a2 2 0 00-2 2v14a2 2 0 002 2h7a2 2 0 002-2V5a2 2 0 00-2-2H9z")
                circle(cx="12.5" cy="17.5" r="1.5")

        .wrapper
            include ../elements/lang/menu_box.pug

            if (page_type != 'inner')
                include ../components/lang/header.pug
            else
                include ../components/lang/header-inner.pug




            block main

            include ../components/lang/footer.pug

        include ../includes/foot.pug
