import { useTranslations } from 'next-intl'

import { ChangeEmailForm } from '@/components/features/user/account/ChangeEmailForm'
import { ChangePasswordForm } from '@/components/features/user/account/ChangePasswordForm'
import { DeactivateCard } from '@/components/features/user/account/DeactivateCard'
import { WrapperTotp } from '@/components/features/user/account/totp/WrapperTotp'
import { Heading } from '@/components/ui/elements/Heading'

export default function ProfileSettings() {
	const t = useTranslations('dashboard.settings')

	return (
		<div className='mt-5 space-y-6'>
			<Heading
				title={t('account.header.heading')}
				description={t('account.header.description')}
			/>
			<ChangeEmailForm />
			<ChangePasswordForm />
			<Heading
				title={t('account.header.securityHeading')}
				description={t('account.header.securityDescription')}
			/>
			<WrapperTotp />
			<Heading
				title={t('account.header.deactivationHeading')}
				description={t('account.header.deactivationDescription')}
			/>
			<DeactivateCard />
		</div>
	)
}
