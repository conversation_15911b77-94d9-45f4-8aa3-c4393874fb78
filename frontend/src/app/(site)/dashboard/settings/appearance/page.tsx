import { useTranslations } from 'next-intl'

import { ChangeColorForm } from '@/components/features/user/appearance/ChangeColorForm'
import { ChangeLanguageForm } from '@/components/features/user/appearance/ChangeLanguageForm'
import { ChangeThemeForm } from '@/components/features/user/appearance/ChangeThemeForm'
import { Heading } from '@/components/ui/elements/Heading'

export default function ProfileSettings() {
	const t = useTranslations('dashboard.settings')

	return (
		<div className='mt-5 space-y-6'>
			<Heading
				title={t('appearance.header.heading')}
				description={t('appearance.header.description')}
			/>
			<ChangeThemeForm />
			<ChangeLanguageForm />
			<ChangeColorForm />
		</div>
	)
}
