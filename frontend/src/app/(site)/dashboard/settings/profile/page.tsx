import { useTranslations } from 'next-intl'

import { ChangeAvatarForm } from '@/components/features/user/profile/ChangeAvatarForm'
import { ChangeInfoForm } from '@/components/features/user/profile/ChangeInfoForm'
import { SocialLinksForm } from '@/components/features/user/profile/social-links-form/SocialLinksForm'
import { Heading } from '@/components/ui/elements/Heading'

export default function ProfileSettings() {
	const t = useTranslations('dashboard.settings')

	return (
		<div className='mt-5 space-y-6'>
			<Heading
				title={t('profile.header.heading')}
				description={t('profile.header.description')}
			/>
			<ChangeAvatarForm />
			<ChangeInfoForm />
			<SocialLinksForm />
		</div>
	)
}
