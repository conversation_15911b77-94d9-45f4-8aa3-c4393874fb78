{"layout": {"header": {"logo": {"platform": "Платформа для стримов"}, "headerMenu": {"login": "Войти", "register": "Регистрация", "profileMenu": {"notifications": {"heading": "Уведомления", "loading": "Загрузка...", "empty": "У вас нет уведомлений"}, "successMessage": "Вы успешно вышли из системы", "errorMessage": "Ошибка при выходе", "channel": "<PERSON><PERSON><PERSON> канал", "dashboard": "Панель управления", "logout": "Выйти"}}, "search": {"placeholder": "Поиск"}}, "sidebar": {"header": {"expand": "Развернуть", "collapse": "Свернуть", "navigation": "Навигация"}, "dashboardNav": {"settings": "Настройки", "streamSettings": "Настройки стрима", "keys": "Ключи для стрима", "chatSettings": "Настройки чата", "followers": "Подписчики", "sponsors": "Спонсоры", "premium": "Премиум планы", "transactions": "Транзакции"}, "userNav": {"home": "Главная", "categories": "Категории", "streams": "Трансляции", "recommended": "Рекомендации"}, "recommended": {"heading": "Рекомендации"}}}, "home": {"streamsHeading": "Каналы, которые могут вам понравиться", "categoriesHeading": "Категории, которые могут вам понравиться"}, "categories": {"heading": "Категории", "overview": {"heading": "Стримы к этой категории"}}, "streams": {"heading": "Трансляции", "searchHeading": "Поиск по запросу"}, "stream": {"video": {"offline": "не в сети", "loading": "Подключение...", "player": {"volume": "Громкость", "fullscreen": {"open": "Полноэкранный режим", "exit": "Выйти из полноэкранного режима"}}}, "info": {"viewers": "зрите<PERSON>ей", "offline": "Не в сети"}, "actions": {"follow": {"confirmUnfollowHeading": "Перестать отслеживать", "confirmUnfollowMessage": "Вы больше не будете получать уведомления от этого канала и он пропадет из списка отслеживаемых", "unfollowButton": "Перестать отслеживать", "followButton": "Отслеживать", "successFollowMessage": "Вы успешно подписались", "errorFollowMessage": "Ошибка при подписке", "successUnfollowMessage": "Вы успешно описались", "errorUnfollowMessage": "Ошибка при описке"}, "support": {"alreadySponsor": "Вы уже спонсор", "supportAuthor": "Поддержать автора", "perMonth": "в месяц", "choose": "Выбрать", "errorMessage": "Ошибка при создании платежа"}, "share": {"heading": "Поделиться с помощью"}}, "aboutChannel": {"heading": "Информация про", "followersCount": "фолловеров", "noDescription": "Описание не указано"}, "sponsors": {"heading": "Спонсоры канала"}, "chat": {"heading": "Чат", "unavailable": "Чат не доступен", "unavailableMessage": "Чат недоступен, пока на канале нет трансляции. Пожалуйста, загляните позже!", "loading": "Подключение...", "info": {"authRequired": "Требуется авторизация", "chatDisabled": "Чат отключен", "premiumFollowersOnly": "Только для спонсоров", "followersOnly": "Только для подписчиков"}, "sendMessage": {"placeholder": "Отправить сообщение", "emojiPlaceholder": "Поиск", "errorMessage": "Ошибка при отправке сообщения"}}, "settings": {"heading": "Параметры трансляции", "thumbnail": {"updateButton": "Обновить изображение", "confirmModal": {"heading": "Удаление превью для стрима", "message": "Вы уверены, что хотите удалить изображение для стрима? Это действие нельзя будет отменить."}, "info": "Поддерживаемые форматы: JPG, JPEG, PNG или GIF. Макс. размер: 10 МБ.", "successUpdateMessage": "Изображение стрима обновлено удалено", "errorUpdateMessage": "Ошибка при обновлении изображения стрима", "successRemoveMessage": "Изображение стрима обновлено удалено", "errorRemoveMessage": "Ошибка при удалении изображения стрима"}, "info": {"titleLabel": "Название", "titlePlaceholder": "Стрим по ГТА 5", "titleDescription": "Введите название вашего стрима. Оно должно быть коротким и запоминающимся.", "categoryLabel": "Категория", "categoryPlaceholder": "Выберете категорию", "categoryDescription": "Выберите категорию, к которой относится ваш контент. Это поможет зрителям легче находить ваши трансляции.", "submitButton": "Сохранить изменения", "successMessage": "Настройки стрима успешно обновлены", "errorMessage": "Ошибка при обновлении настроек стрима"}}}, "success": {"heading": "Оплата прошла успешно!", "details": {"heading": "Детали спонсорства:", "price": "Цена:", "duration": "Длительность:", "channel": "Канал:"}, "congratulations": "Теперь у вас есть доступ к премиум-контенту канала!", "backToHome": "Вернуться на главную", "backToChannel": "Вернуться на канал", "support": "Если у вас возникли вопросы, свяжитесь с нашей поддержкой."}, "notFound": {"description": "Упс, что-то пошло не так.", "backToHome": "Перейти на главную"}, "auth": {"register": {"heading": "Регистрация в TeaStream", "backButtonLabel": "Есть учетная запись? Войти", "usernameLabel": "Имя пользователя", "usernameDescription": "Под этим именем вас будут знать другие пользователи.", "emailLabel": "Почта", "emailDescription": "Мы можем использовать вашу почту для отправки сообщений касательно учетной записи.", "passwordLabel": "Пароль", "passwordDescription": "Пароль должен содержать не менее 8 символов.", "submitButton": "Зарегистрироваться", "successAlertTitle": "Проверьте свою почту", "successAlertDescription": "На ваш адрес электронной почты было отправлено письмо для подтверждения. Если вы не видите письмо, проверьте папку «Спам»", "errorMessage": "Ошибка при создании аккаунта"}, "verify": {"heading": "Верификация аккаунта", "successMessage": "Аккаунт верифицирован", "errorMessage": "Ошибка при верификации"}, "login": {"heading": "Войти в TeaStream", "backButtonLabel": "У вас нет учетной записи? Зарегистрируйтесь!", "loginLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loginDescription": "Ваше имя или почта, которые вы вводили при регистрации.", "passwordLabel": "Пароль", "passwordDescription": "Пароль, который вы вводили при регистрации.", "pinLabel": "6-з<PERSON><PERSON><PERSON><PERSON><PERSON> код", "pinDescription": "Введите код из вашего приложения для аутентификации.", "forgotPassword": "Забыли пароль?", "submitButton": "Войти", "successMessage": "Вы успешно вошли в систему", "errorMessage": "Ошибка при входе в систему"}, "resetPassword": {"heading": "Сброс пароля", "backButtonLabel": "Есть учётная запись? Войти", "emailLabel": "Почта", "emailDescription": "Ваша почта, которую вы вводили при регистрации", "submitButton": "Сбросить пароль", "successAlertTitle": "Ссылка отправлена", "successAlertDescription": "Мы отправили ссылку для сброса пароля на вашу почту. Если у вас включены уведомления в Telegram, ссылка также была отправлена туда", "errorMessage": "Ошибка при сбросе пароля"}, "newPassword": {"heading": "Новый пароль", "backButtonLabel": "Есть учётная запись? Войти", "passwordLabel": "Новый пароль", "passwordDescription": "Пароль, который вы вводили при регистрации", "passwordRepeatLabel": "Повторите пароль", "passwordRepeatDescription": "Повторите свой новый пароль для подтверждения", "submitButton": "Продолжить", "successMessage": "Пароль успешно изменён", "errorMessage": "Ошибка при установлении нового пароля"}, "deactivate": {"heading": "Деактивация аккаунта", "backButtonLabel": "Перейти в панель управления", "emailLabel": "Почта", "emailDescription": "Ваша почта, которую вы вводили при регистрации", "passwordLabel": "Пароль", "passwordDescription": "Пароль, который вы вводили при регистрации", "pinLabel": "6-з<PERSON><PERSON><PERSON><PERSON><PERSON> код", "pinDescription": "Вам на почту был выслан код. Если вы подключали уведомления в Telegram, мы отправили код и туда", "submitButton": "Деактивировать", "successMessage": "Вы успешно деактивировали аккаунт", "errorMessage": "Ошибка при деактивации"}}, "dashboard": {"settings": {"header": {"heading": "Настройки", "description": "Здесь вы можете управлять вашими настройками", "profile": "Профиль", "account": "Аккаунт", "appearance": "Вн<PERSON><PERSON>ний вид", "notifications": "Уведомления", "sessions": "Сессии"}, "profile": {"header": {"heading": "Профиль", "description": "Настройте ваш профиль, обновите аватар, измените информацию о себе и добавьте ссылки на социальные сети, чтобы сделать вашу страницу более привлекательной для других пользователей."}, "avatar": {"heading": "Изображение профиля", "updateButton": "Обновить изображение профиля", "confirmModal": {"heading": "Удаление аватара", "message": "Вы уверены, что хотите удалить изображение профиля? Это действие нельзя будет отменить."}, "info": "Поддерживаемые форматы: JPG, JPEG, PNG, WEBP или GIF. Макс. размер: 10 МБ.", "successUpdateMessage": "Изображение профиля успешно обновлено", "errorUpdateMessage": "Ошибка при обновлении изображения профиля", "successRemoveMessage": "Изображение профиля успешно удалено", "errorRemoveMessage": "Ошибка при удалении изображения профиля"}, "info": {"heading": "Настройки профиля", "usernameLabel": "Имя пользователя", "usernamePlaceholder": "johndoe", "usernameDescription": "Под этим именем вас будут знать другие пользователи.", "displayNameLabel": "Отображаемое имя", "displayNamePlaceholder": "<PERSON>", "displayNameDescription": "Проставьте прописные буквы в имени пользователя по своему желанию.", "bioLabel": "О себе", "bioPlaceholder": "Я люблю программировать", "bioDescription": "Информация о себе должна содержать не более 300 символов.", "submitButton": "Сохранить изменения", "successMessage": "Профиль успешно обновлён", "errorMessage": "Ошибка при обновлении профиля"}, "socialLinks": {"createForm": {"heading": "Ссылки на соцсети", "titleLabel": "Название ссылки", "titlePlaceholder": "Youtube", "titleDescription": "Текст ссылки", "urlLabel": "URL ссылки", "urlPlaceholder": "https://github.com/TeaCoder52", "urlDescription": "Куда ведет эта ссылка? Введите полный URL-адрес, например https://github.com/TeaCoder52", "submitButton": "Добавить", "successMessage": "Ссылка на соц. сеть успешно добавлена", "errorMessage": "Ошибка при добавлении ссылки на соц. сеть"}, "editForm": {"cancelButton": "Отмена", "submitButton": "Сохранить", "successUpdateMessage": "Ссылка на соц. сеть успешно изменена", "errorUpdateMessage": "Ошибка при обновлении соц. сети", "successRemoveMessage": "Ссылка на соц. сеть успешно удалена", "errorRemoveMessage": "Ошибка при удалении соц. сети"}, "successReorderMessage": "Порядок ссылок на соц. сети изменен", "errorReorderMessage": "Ошибка при изменении порядка соц. сетей"}}, "account": {"header": {"heading": "Аккаунт", "description": "Управляйте настройками вашего аккаунта, включая изменение доступа, безопасность и возможность деактивации.", "securityHeading": "Безопасность", "securityDescription": "Настройте двухфакторную аутентификацию для улучшенной защиты ваших данных.", "deactivationHeading": "Деактивация", "deactivationDescription": "Если вы хотите временно или постоянно отключить свой аккаунт, используйте эту опцию. Обратите внимание на последствия."}, "email": {"heading": "Адрес электронной почты", "emailLabel": "Почта", "emailDescription": "Введите ваш новый адрес электронной почты.", "submitButton": "Сохранить", "successMessage": "Почта успешно обновлена", "errorMessage": "Ошибка при обновлении почты"}, "password": {"heading": "Пароль от аккаунта", "oldPasswordLabel": "Старый пароль", "oldPasswordDescription": "Введите свой старый пароль, чтобы подтвердить вашу личность перед изменением пароля. Это необходимо для обеспечения безопасности вашей учетной записи.", "newPasswordLabel": "Новый пароль", "newPasswordDescription": "Ваш новый пароль должен содержать не менее 8 символов. Рекомендуется использовать также специальные символы для повышения безопасности.", "submitButton": "Сохранить", "successMessage": "Пароль успешно обновлён", "errorMessage": "Ошибка при обновлении пароля"}, "twoFactor": {"heading": "Аутентификация с помощью TOTP", "description": "Увеличьте безопасность вашего аккаунта, активировав аутентификацию с помощью TOTP. Этот дополнительный уровень защиты требует ввода уникального кода, что делает ваш аккаунт менее уязвимым к несанкционированному доступу.", "enable": {"trigger": "Включить", "heading": "Включение TOTP", "qrInstructions": "Сканируйте QR-код для добавления TOTP", "secretCodeLabel": "Секретный код: ", "pinLabel": "PIN-код", "pinDescription": "Пожалуйста, введите 6-значный код, предоставленный вашим приложением для аутентификации.", "submitButton": "Подтвердить", "successMessage": "Аутентификация с помощью TOTP включена", "errorMessage": "Ошибка при верификации кода"}, "disable": {"trigger": "Выключить", "heading": "Вы действительно хотите отключить TOTP?", "message": "При отключении аутентификации с помощью TOTP ваш аккаунт станет менее защищенным. Вы уверены, что хотите продолжить?", "successMessage": "Аутентификация через TOTP отключена", "errorMessage": "Ошибка при отключении"}}, "deactivation": {"heading": "Деактивация аккаунта", "description": "После деактивации аккаунта вы не сможете в него войти. Через 7 дней аккаунт будет полностью удалён. До этого времени вы можете обратиться в поддержку для восстановления доступа и разблокировки аккаунта.", "confirmModal": {"heading": "Подтверждение деактивации", "message": "Вы уверены, что хотите деактивировать аккаунт? Восстановить доступ можно только в течение 7 дней."}, "button": "Деактивировать"}}, "appearance": {"header": {"heading": "Вн<PERSON><PERSON>ний вид", "description": "Настройте внешний вид интерфейса сайта по своему вкусу. Выберите тему и язык, которые сделают ваше взаимодействие более комфортным."}, "theme": {"heading": "Смена темы", "description": "Вы можете выбрать темную или светлую тему для интерфейса сайта", "successMessage": "Тема сайта успешно изменена"}, "language": {"heading": "Язык сайта", "description": "Выберите язык, который будет использован на сайте", "selectPlaceholder": "Выберите язык", "successMessage": "Язык сайта успешно изменен"}, "color": {"heading": "Цвет акцента", "description": "Выберите цвет акцента для интерфейса"}}, "notifications": {"header": {"heading": "Уведомления", "description": "Настройте, как вы хотите получать уведомления от TeaStream. Вы будете получать уведомления о начале трансляций на каналах, на которые вы подписаны, а также о новых подписчиках."}, "siteNotifications": {"heading": "Уведомления на сайте", "description": "Если вы выключите уведомления на сайте, то не будете получать уведомления о новых подписчиках на ваш канал и уведомления о новых трансляциях."}, "telegramNotifications": {"heading": "Уведомления в Telegram", "description": "Если вы включите уведомления через Telegram, ваш аккаунт TeaStream свяжется с вашим Telegram-аккаунтом, и вы будете получать сообщения о новых подписках, новых стримах, сбросе пароля и коды двухфакторной аутентификации."}, "successMessage": "Настройки уведомлений успешно обновлены", "errorMessage": "Ошибка при обновлении настроек уведомлений"}, "sessions": {"header": {"heading": "Сессии", "description": "Сессии — это устройства, которые вы используете или которые использовали для входа в вашу учетную запись TeaStream. Здесь показаны активные сессии в данный момент."}, "info": {"current": "Текущая сессия", "active": "Активные сессии", "notFound": "Активных сессий не найдено"}, "sessionItem": {"deleteButton": "Удалить", "detailsButton": "Подробнее", "confirmModal": {"heading": "Удаление сессии", "message": "Вы уверены, что хотите удалить эту сессию? Это действие приведёт к необратимому удалению сессии на этом устройстве и завершит все активные действия, связанные с ним."}, "successMessage": "Сессия удалена", "errorMessage": "Ошибка при удалении сессии"}, "sessionModal": {"heading": "Информация о сессии", "device": "Устройство:", "location": "Местоположение:", "ipAddress": "IP-адрес:", "createdAt": "Дата создания:"}}}, "keys": {"header": {"heading": "Ключи для стрима", "description": "Сгенерируйте URL и ключ для проведения трансляции."}, "url": {"heading": "URL"}, "key": {"heading": "<PERSON><PERSON><PERSON><PERSON>"}, "instructionModal": {"trigger": "Инструкция", "heading": "🛠️ Инструкция по настройке OBS для прямых трансляций 🛠️", "description": "Чтобы начать делиться своим контентом с аудиторией, следуйте подробной инструкции по подключению OBS Studio. Это всего несколько шагов, и вы готовы выходить в эфир!", "step1Title": "🔑 Шаг 1: Скачивание OBS Studio и генерация Server URL и Stream Key", "step1Description": "Чтобы начать трансляцию, вам необходимо получить два важных элемента: Server URL и Stream Key (ключ трансляции). Следуйте этим шагам:", "downloadObs": "Скачайте OBS Studio", "downloadObsDescription": "Прежде чем настраивать трансляцию, скачайте и установите программу", "obsLinkText": "OBS Studio", "copyKeys": "Скопируйте Server URL и Stream Key", "copyKeysDescription": "После генерации на экране появятся два поля: Server URL и Stream Key. Нажмите на иконки копирования рядом с каждым из полей, чтобы сохранить данные в буфер обмена.", "step2Title": "🎛️ Шаг 2: Нас<PERSON>р<PERSON>йка OBS Studio", "step2Description": "Теперь, когда у вас есть необходимые данные, давайте настроим OBS для работы с вашей трансляцией:", "openObs": "Откройте OBS Studio", "openObsDescription": "Если у вас еще не установлен OBS, скачайте его с официального сайта и установите. После запуска откроется главная страница OBS.", "openStreamSettings": "Откройте настройки трансляции", "openStreamSettingsDescription": "В верхнем меню нажмите «Файл» → «Настройки». Перейдите во вкладку «Трансляция».", "enterDetails": "Введите ваши данные", "enterDetailsDescription": "В поле «Сервис» выберите опцию «Пользовательский...». Вставьте скопированный ранее Server URL в поле «URL». В поле «Ключ трансляции» вставьте ваш Stream Key.", "saveSettings": "Сохраните настройки", "saveSettingsDescription": "Нажмите «Применить», а затем «ОК», чтобы сохранить изменения.", "step3Title": "🚀 Шаг 3: Начало трансляции", "step3Description": "После успешной настройки OBS вы готовы начать прямую трансляцию:", "startStream": "Запустите трансляцию", "startStreamDescription": "Вернитесь на главный экран OBS и нажмите кнопку «Начать трансляцию».", "monitorStream": "Следите за своей трансляцией", "monitorStreamDescription": "Ваша трансляция начнется в режиме реального времени, и вы сможете наблюдать за ней как на платформе, так и в OBS.", "congrats": "✨ Поздравляем! Теперь вы успешно настроили OBS для трансляций на нашей платформе. Ваша аудитория ждет новых ярких моментов и контента! 🎉", "close": "Закрыть"}, "createModal": {"trigger": "Сгенерировать ключи", "heading": "Сгенерировать ключи", "ingressTypeLabel": "Тип подключения", "ingressTypePlaceholder": "Выберите тип подключения", "ingressTypeDescription": "Выберите RTMP для классического вещания или WHIP для WebRTC подключений.", "submitButton": "Сгенерировать", "successMessage": "Входной поток создан", "errorMessage": "Ошибка при создании входного потока"}}, "chat": {"header": {"heading": "Настройки чата", "description": "Здесь вы можете управлять настройками вашего чата."}, "isChatEnabled": {"heading": "Включить чат", "description": "Если вы включите чат, зрители смогут писать сообщения в чате во время вашей трансляции."}, "isChatFollowersOnly": {"heading": "Чат только для подписчиков", "description": "Если вы включите эту настройку, только ваши подписчики смогут писать сообщения в чате."}, "isChatPremiumFollowersOnly": {"heading": "Чат только для премиум подписчиков", "description": "Если вы включите эту настройку, только премиум подписчики смогут писать сообщения в чате."}, "successMessage": "Настройки чата успешно обновлены", "errorMessage": "Ошибка при обновлении настроек чата "}, "followers": {"header": {"heading": "Подписчики", "description": "Здесь отображается список всех ваших подписчиков."}, "columns": {"date": "Дата подписи", "user": "Пользователь", "actions": "Действия", "viewChannel": "Перейти на канал"}}, "sponsors": {"header": {"heading": "Спонсоры", "description": "Здесь отображается список всех ваших спонсоров."}, "columns": {"date": "Дата окончания", "user": "Пользователь", "plan": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Действия", "viewChannel": "Перейти на канал"}}, "plans": {"header": {"heading": "Премиум планы", "description": "Здесь отображается список премиум планов для вашего канала."}, "alert": {"heading": "Требуется верификация канала", "description": "Для создания премиум планов ваш канал должен быть верифицирован. Для этого вам нужно всего 10 подписчиков, чтобы получить галочку и разблокировать доступ к этой функции."}, "createForm": {"trigger": "Создать план", "heading": "Создать план", "titleLabel": "Название", "titlePlaceholder": "Премиум подписка на канал", "titleDescription": "Введите название вашего плана. Это название будет отображаться вашим подписчикам.", "descriptionLabel": "Описание", "descriptionPlaceholder": "Опишите, что включает в себя ваш план", "descriptionDescription": "Опишите, что включает в себя ваш план. Это поможет вашим подписчикам понять преимущества подписки.", "priceLabel": "Цена", "priceDescription": "Укажите цену вашего плана в валюте. Это сумма, которую пользователи будут платить за подписку.", "submitButton": "Создать", "successMessage": "Премиум план создан", "errorMessage": "Ошибка при создании плана"}, "columns": {"date": "Дата создания", "title": "Название", "price": "Цена", "actions": "Действия", "remove": "Удалить", "successMessage": "План успешно удалён", "removeMessage": "Ошибка при удалении плана"}}, "transactions": {"header": {"heading": "Транзакции", "description": "Здесь отображаются транзакции, связанные с оформлением спонсорства на каналы."}, "columns": {"date": "Дата создания", "status": "Статус", "success": "Успешно", "pending": "В ожидании", "failed": "Ошибка", "expired": "Истек", "amount": "Сумма"}}}, "components": {"liveBadge": {"text": "В ЭФИРЕ"}, "confirmModal": {"cancel": "Отмена", "continue": "Продолжить"}, "copyButton": {"successMessage": "Скопировано"}, "dataTable": {"notFound": "Нечего не найдено"}, "emptyState": {"heading": "Нечего не найдено", "text": "Мы обшарили все уголки нашего сайта, но к сожалению по вашему запросу ничего не нашлось. Может попробуете что-то другое?"}}, "utils": {"formatDate": {"months": {"january": "января", "february": "февраля", "march": "марта", "april": "апреля", "may": "мая", "june": "июня", "july": "июля", "august": "августа", "september": "сентября", "october": "октября", "november": "ноября", "december": "декабря"}}}}