import {BaseAPIResponse} from '../../shared/models/requests/response';
import {CandidatesListTabInfo, TabInfo} from '../../shared/models/candidatesListTabInfo';
import Timer = NodeJS.Timer;

export class CandidatesListTabInfoService {
  clearIntervalsForCandidatesList(toClearInterval: boolean = true) {
    return new Promise((resolve, reject) => {
      this.getInfoForCurrentTab().then((infoForCurrentTab: TabInfo) => {
        const currentTabTimer: Timer = infoForCurrentTab && infoForCurrentTab.timer;

        if (currentTabTimer) {
          if (toClearInterval) {
            clearInterval(currentTabTimer);
          }

          chrome.runtime.sendMessage({CandidatesListTabInfo: true, deleteValue: true, timer: currentTabTimer});
        }

        resolve();
      }, () => {
        reject();
      });
    });
  }

  getInfoForCurrentTab() {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({CandidatesListTabInfo: true, getValue: true},
        (response: BaseAPIResponse) => {
          if (response) {
            const candidatesListTabInfo: CandidatesListTabInfo = response.value;
            const tabId: number = response.tabId;
            const infoForCurrentTab: TabInfo = candidatesListTabInfo[tabId];

            if (infoForCurrentTab) {
              resolve(infoForCurrentTab);
            } else {
              reject();
            }
          } else {
            reject();
          }
        });
    });
  }

  getIntervalForCurrentTab() {
    return new Promise((resolve, reject) => {
      this.getInfoForCurrentTab().then((infoForCurrentTab: TabInfo) => {
        const currentTabTimer: Timer = infoForCurrentTab && infoForCurrentTab.timer;

        if (currentTabTimer) {
          resolve(currentTabTimer);
        } else {
          reject();
        }
      }, () => {
        reject();
      });
    });
  }

  setCandidatesListTimers(appendSearchExtensionInterval: Timer) {
    chrome.runtime.sendMessage({CandidatesListTabInfo: true, setValue: true, timer: appendSearchExtensionInterval});
  }
}
