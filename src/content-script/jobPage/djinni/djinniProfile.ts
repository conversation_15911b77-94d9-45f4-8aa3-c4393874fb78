import {ExtensionPage, BaseExtensionPage} from '../../../shared/models/extensionPage';
import Requests from '../../../shared/requests/requests';
import {GetCandidatesFromExtStringIdsData} from '../../../shared/models/requests/GetCandidatesFromExtStringIdData';
import {candidateIdForList} from '../restSites/methods/getCandidateIdForList';
import {ifThereIsNotInit} from '../restSites/methods/ifThereIsNotInit';
import {UpdateExtensionNotification} from '../../services/updateExtensionService';
import {BaseAPIResponse, SingleAPI} from '../../../shared/models/requests/response';
import {CandidatesFromExtStringIds} from '../../../shared/models/requests/candidatesFromExtStringIds';
import {CandidatesListTabInfoService} from '../../services/candidatesListTabInfoService';
import Timer = NodeJS.Timer;
import {CustomElements} from '../../../shared/models/enums/customElements';
import {CandidateService} from '../../services/candidateService';
import {CandidateItemFromDjinniProfileSearch} from './CandidateItemFromDjinniProfile';

export class DjinniProfile extends BaseExtensionPage implements ExtensionPage {
  private domain: string;
  private updateExtensionButton: HTMLElement;
  private htmlElement: HTMLElement;

  private candidatesListTabInfoService: CandidatesListTabInfoService;


  constructor(args) {
    super(args);

    this.candidatesListTabInfoService = new CandidatesListTabInfoService();

  }

  runExtension() {
    this.searchExtensionAppender();
  }

  private getCandidatesLinks(candidatesSearchList: NodeListOf<HTMLElement> | HTMLElement []) {
    const candidatesLinks: string[] = [];

    if (candidatesSearchList.length) {
      Array.from(candidatesSearchList).forEach((candidateItem: HTMLElement) => {
        if (candidateItem) {
          const candidateItemLink = candidateItem.children[0];
          const candidateId: string = candidateIdForList(this.siteCode, this.pageName, candidateItemLink, this.candidateURLMatcher);
          if (candidateId) {
            candidatesLinks.push(candidateId);
          }
        }
      });
    }
    return candidatesLinks;
  }

  private searchExtensionAppender(onScrollChangeInterval: Timer = null) {
    const candidatesSearchList: NodeListOf<HTMLElement> = document.querySelectorAll(this.saveButtonContainerSelector);
    this.htmlElement = document.querySelector('html');

    if (candidatesSearchList && candidatesSearchList.length > 0) {
      this.candidateURLSeparator = this.candidateURLSeparator ? this.candidateURLSeparator : '/';
      const currentHref = window.location.href.split('/');
      const candidateLink = currentHref[currentHref.length - 2];
      const candidateLinks: string[] = this.getCandidatesLinks(candidatesSearchList);
      candidateLinks.push(candidateLink);

      if (candidateLink && candidateLink !== 'inbox') {
        const getCandidatesFromExtStringIdsData: GetCandidatesFromExtStringIdsData = this.getCandidatesFromExtStringIdsData(candidateLinks);
        this.updateExtensionButton = UpdateExtensionNotification.appendUpdateExtensionButton(this.containerSelectorBeforeSignInOnList);

        this.sendGetCandidatesFromExtStringIds(getCandidatesFromExtStringIdsData)
          .then((respCandidatesIds: SingleAPI<CandidatesFromExtStringIds>) => {
            candidatesSearchList.forEach((candidateItem: HTMLElement) => {
              this.initSaveButton(candidateItem, respCandidatesIds);
            });

          }, (response: BaseAPIResponse) => {
            this.ifUserNotAuthorized(response);
          });
      }
    } else {
      this.ifThereIsNotInit(onScrollChangeInterval);
    }
  }

  private ifThereIsNotInit(onScrollChangeInterval: Timer) {
    ifThereIsNotInit(this.pageName, this.saveButtonContainerSelector).then(() => {
      this.searchExtensionAppender();
    }, () => {
      if (onScrollChangeInterval) {
        this.candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
        }, () => {
        });
      }
    });
  }

  private sendGetCandidatesFromExtStringIds(getCandidatesFromExtStringIdsData: GetCandidatesFromExtStringIdsData) {
    return new Promise((resolve, reject) => {
      Requests.sendRequest(
        'getCandidatesFromExtStringIds',
        {data: JSON.stringify(getCandidatesFromExtStringIdsData)}
      )
        .then((respCandidatesIds: SingleAPI<CandidatesFromExtStringIds>) => {
          resolve(respCandidatesIds);
        }, (response: BaseAPIResponse) => {
          reject(response);
        });
    });
  }

  private initSaveButton(candidateItem: HTMLElement, respCandidatesIds: SingleAPI<CandidatesFromExtStringIds>) {
    const elementForAppendButton: HTMLElement = this.linkElementSelector ? candidateItem : candidateItem.parentElement;
    const dataForCandidateItemFromList = {
      candidateItem,
      respCandidatesIds,
      elementForAppendButton,
      ...this
    };

    const candidateItemFromList = new CandidateItemFromDjinniProfileSearch(dataForCandidateItemFromList);

    candidateItemFromList.init();
  }

  private getCandidatesFromExtStringIdsData(candidateLinks: string[]): GetCandidatesFromExtStringIdsData {
    return {
      status: this.siteCode,
      id: candidateLinks.join(',')
    };
  }

  private ifUserNotAuthorized(response: BaseAPIResponse) {
    if (response && response.statusNumber === 403) {
      this.checkAuth(this.runExtension);
      return;
    }

    this.appendSignInButton();
  }

  private appendSignInButton() {
    if (this.containerSelectorBeforeSignInOnList) {
      chrome.runtime.sendMessage({isDomainHandle: true, getCurrentDomain: true}, (response: BaseAPIResponse) => {
        if (response && response.value) {
          this.candidateService = new CandidateService();
          this.candidateService.domain = response.value;

          if (this.containerSelectorBeforeSignInOnList) {
            const containerBeforeSignIn: HTMLElement = document.querySelector(this.containerSelectorBeforeSignInOnList);

            if (!containerBeforeSignIn.parentElement.querySelector(CustomElements.saveButton)) {
              this.candidateService.createSaveButton(this.pageName, '');
              this.candidateService.setSignInAttrsToBtn();

              containerBeforeSignIn.parentElement.insertBefore(this.candidateService.saveButton, containerBeforeSignIn);
              this.candidateService.onClickButton(this.updateExtensionButton);
            }
          }
        }
      });
    }
  }

  private checkAuth(initExtension) {
    Requests.sendRequest('authping').then(() => {
      initExtension();
    }, () => {
      this.appendSignInButton();
    });
  }
}
