import {getCookie} from 'src/content-script/shared/helpers/get-cookie';

interface QueryId {
  id: string;
  kind: string;
  name: string;
  typeName: string;
}

const queryNames = [
  'top-card-supplementary-query', // Main profile info
  'profile-contact-info-finder', // Profile contacts
  'profile-components', // Profile skills, experiences
];

export class LinkedinVoyager {
  private baseUrl = 'https://www.linkedin.com/voyager/api/';
  private queryIds: Record<string, QueryId>;
  private headers = new Headers({
    accept: 'application/vnd.linkedin.normalized+json+2.1',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty'
  });

  private async getQueryIds(targetNames: string[]): Promise<Record<string, QueryId>> {
    const existingQueryIds = localStorage.getItem('voyagerQueryIds');

    if (existingQueryIds) {
      return JSON.parse(existingQueryIds);
    }

    const vendorUrl = document.querySelector('script[data-fastboot-src="/assets/vendor.js"]').getAttribute('src');
    const vendorDocument = await fetch(vendorUrl).then(res => res.text());

    const objectRegex = /\{\s*kind\s*:\s*"query"\s*,\s*id\s*:\s*"[^"]*"\s*,\s*typeName\s*:\s*"[^"]*"\s*,\s*name\s*:\s*"[^"]*"\s*\}/g;
    const matches = vendorDocument.match(objectRegex);

    const results = {};
    targetNames.forEach(name => results[name] = null); // default null set

    if (!matches) {
      return results;
    }

    const nameSet = new Set(targetNames);

    for (const match of matches) {
      const jsonLike = match.replace(/(\w+)\s*:/g, '"$1":');

      try {
        const obj = JSON.parse(jsonLike);
        if (nameSet.has(obj.name)) {
          results[obj.name] = obj;

          // Optional: early stop if all names are found
          if (Object.values(results).every(val => val !== null)) {
            break;
          }
        }
      } catch (e) {
        continue;
      }
    }

    localStorage.setItem('voyagerQueryIds', JSON.stringify(results));

    return results;
  }

  private fetchProfileInfo(profileId: string) {
    return fetch(
      `${this.baseUrl}graphql?includeWebMetadata=true&variables=(vanityName:${profileId})&queryId=${this.queryIds['top-card-supplementary-query'].id}`,
      {
        method: 'GET',
        headers: this.headers,
      });
  }

  private fetchContactsInfo(profileId: string) {
    return fetch(
      `${this.baseUrl}graphql?includeWebMetadata=true&variables=(memberIdentity:${profileId})&queryId=${this.queryIds['profile-contact-info-finder'].id}`,
      {
        method: 'GET',
        headers: this.headers,
      });
  }

  private fetchProfileComponents(type: 'skills' | 'experience') {
    return fetch(
      `${this.baseUrl}graphql?variables=(profileUrn:${encodeURIComponent(this.getProfileUrnFromDocument())},` +
      `sectionType:${type},locale:en_US)&queryId=${this.queryIds['profile-components'].id}`,
      {
        method: 'GET',
        headers: this.headers,
      });
  }

  private getProfileUrnFromDocument(): string {
    const linkElement = document.querySelector('#navigation-overlay-section-see-more-company-recommendations');
    return new URL(linkElement.getAttribute('href')).searchParams.get('profileUrn');
  }

  private initHeaders() {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(['voyagerRequestHeaders'], (result) => {
        if (!result.voyagerRequestHeadersQQ) {
          reject('voyagerRequestHeaders not found in local storage');
        }

        this.headers = new Headers(result.voyagerRequestHeaders);
        resolve(result.voyagerRequestHeaders);
      });
    });
  }


  async getAllInfo(profileId: string) {
    await this.initHeaders();
    this.headers.set('csrf-token', getCookie('JSESSIONID'));
    this.queryIds = await this.getQueryIds(queryNames);


    return await Promise.all([
      this.fetchProfileInfo(profileId),
      this.fetchContactsInfo(profileId),
      this.fetchProfileComponents('skills'),
      this.fetchProfileComponents('experience'),
    ])
      .then(responses => Promise.all(responses.map(r => r.json())))
      .then(([profileInfo, contactsInfo, skillsInfo, experiencesInfo]) => ({
        profileInfo,
        contactsInfo,
        skillsInfo,
        experiencesInfo
      }));
  }
}
