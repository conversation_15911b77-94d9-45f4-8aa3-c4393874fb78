import {CandidateResume} from '../restSites/candidateResume/candidateResume';
import {ExtensionPage} from '../../../shared/models/extensionPage';
import {LinkedinService} from './methods/linkedinService';
import {intervalBeforeGetHTML, testVersionMessage} from '../../shared/methods';
import {pageSiteCodes} from '../../../shared/models/enums/pageSiteCodes';
import {BaseAPIResponse} from '../../../shared/models/requests/response';
import {currentCloudDomain} from '../../../shared/data/domains';

export class LinkedinResume extends CandidateResume implements ExtensionPage {
  private linkedinService: LinkedinService;
  private readonly avatarImgSelector = 'img.presence-entity__image.pv-top-card__photo';
  private readonly avatarImgSelectorNew = 'button.pv-top-card-profile-picture__container';
  private readonly avatarOwnProfileSelector = 'button.profile-photo-edit__edit-btn';
  private readonly linkedinModalSelector = '.artdeco-modal';

  constructor(args) {
    super(args);
    this.linkedinService = new LinkedinService();
  }

  private static closeContactsModal() {
    const closeContactsModal: HTMLElement = document.querySelector('.artdeco-modal__dismiss');
    const closeContactsModalNew: HTMLElement = document.querySelector('.artdeco-modal__dismiss.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--2.artdeco-button--tertiary.ember-view');

    if (closeContactsModal) {
      closeContactsModal.click();
    } else if (closeContactsModalNew) {
      closeContactsModalNew.click();
    }
  }

  private static clickShowMore(elements: NodeListOf<HTMLElement>) {
    elements.forEach((elem: HTMLElement) => {
      if (elem.getAttribute('aria-expanded') === 'false') {
        elem.click();
      }
    });
  }

  private static openContacts() {
    const contactsBtn: HTMLElement = document.querySelector('button[data-control-name="contact_see_more"]');
    const contacts: HTMLElement = document.querySelector('a[data-control-name="contact_see_more"]');
    const contactsBtnNew: HTMLElement = document.querySelector('a[href*=\'/detail/contact-info/\']');
    const contactsBtnNewV2: HTMLElement = document.querySelector('a[href*=\'/overlay/contact-info/\']');

    if (contactsBtn) {
      contactsBtn.click();
    } else if (contacts) {
      contacts.click();
    } else if (contactsBtnNew) {
      contactsBtnNew.click();
    } else if (contactsBtnNewV2) {
      contactsBtnNewV2.click();
    }
  }

  runExtension() {
    this.htmlParts = '';
    this.init().then(() => {}, () => {
    });
  }

  protected getCandidatesFromExtStringIdsWrapper() {
    return new Promise((resolve, reject) => {
      this.getCandidatesFromExtStringIds('profile-in-ats__icon icon-with-animation')
        .then(() => {
          this.initEventAppendModalToPage();
          this.isCurrentDomainMainForLinkedin();
          this.clickUpdateContacts();
          resolve();
        }, () => {
          reject();
        });
    });
  }

  protected appendSaveButtonWrapper() {
    return new Promise((resolve, reject) => {
      this.appendSignInButton().then(() => {
        this.isCurrentDomainMainForLinkedin();
        resolve();
      }, () => {
        reject();
      });
    });
  }

  protected saveButtonSuccessClick() {



    if (!document.querySelector('.pv-top-card__non-self-photo-wrapper')) {
      chrome.runtime.sendMessage({reloadTab: true});
      return;
    }

    if (!this.candidateService.cleverStaffId) {
      this.candidateService.changeSavingStatus(true);
      this.candidateService.togglePageClickable(false);
      this.candidateService.setLoader(true);

      this.getWorkExperienceInformation().then(() => {
        this.getSingleCompanyPositions().then(() => {
          this.getEducationInformation().then(() => {
            this.getSkillsInformation().then(() => {
              this.getProjectsExperienceInformation().then(() => {
                this.getCoursesInformation().then(() => {
                  this.getLanguagesExperienceInformation().then(() => {
                    this.openAllCandidateInfo().then((profileHTML: string) => {
                      this.saveButtonClick(profileHTML);
                    }, () => {
                      this.linkedinService.scrollToAnimated(this.candidateService.htmlElement, 0, 500).then(() => {
                        this.candidateService.togglePageClickable(true);

                        alert(chrome.i18n.getMessage('candidateWasNotSaved'));
                      });
                    });
                  });
                });
              });
            });
          });
        });
      });
    }
  }

  protected saveButtonClick(profileHTML: string) {
    if (!document.querySelector('.pv-top-card__non-self-photo-wrapper')) {
      chrome.runtime.sendMessage({reloadTab: true});
      return;
    }

    this.candidateService.candidateData = this.getCandidateDataObject(profileHTML);
    this.candidateService.setLoader(false);
    LinkedinResume.closeContactsModal();
    this.candidateService.getDuplicates(this.siteCode, false, false);
  }

  private clickUpdateContacts() {
    this.candidateService.eventFromSaveBtn('clickUpdateContacts', (updateContactsEvent: CustomEvent) => {
      updateContactsEvent.stopPropagation();

      if (!document.querySelector('.pv-top-card__non-self-photo-wrapper')) {
        chrome.runtime.sendMessage({reloadTab: true});
        return;
      }

      this.candidateService.setLoader(true);

      this.getWorkExperienceInformation().then(() => {
        this.getSingleCompanyPositions().then(() => {
          this.getEducationInformation().then(() => {
            this.getSkillsInformation().then(() => {
              this.getProjectsExperienceInformation().then(() => {
                this.getCoursesInformation().then(() => {
                  this.getLanguagesExperienceInformation().then(() => {
                    this.openAllCandidateInfo().then((profileHTML: string) => {
                      this.updateContacts(profileHTML);
                    }, () => {
                      this.linkedinService.scrollToAnimated(this.candidateService.htmlElement, 0, 500).then(() => {
                        this.candidateService.dispatchEventToSaveBtn('contactsUpdated', {bubbles: true});
                        this.candidateService.togglePageClickable(true);

                        alert(chrome.i18n.getMessage('candidateWasNotSaved'));
                      });
                    });
                  });
                });
              });
            });
          });
        });
      });
    });
  }

  private updateContacts(profileHTML: string = '') {
    this.candidateService.candidateData = this.getCandidateDataObject(profileHTML);
    this.candidateService.candidateData.candidateId = this.candidateService.cleverStaffId;

    LinkedinResume.closeContactsModal();
    this.candidateService.setLoader(false);

    this.linkedinService.scrollToAnimated(this.candidateService.htmlElement, 0, 500)
      .then(() => {
        this.saveCandidate();
      }, () => {
        this.saveCandidate();
      });
  }

  private contactsUpdatedEvent() {
    this.candidateService.togglePageClickable(true);
    this.candidateService.dispatchEventToSaveBtn('contactsUpdated', {bubbles: true});
  }

  private saveCandidate() {
    this.candidateService.saveCandidate(this.candidateService.candidateData)
      .then(() => {
        this.contactsUpdatedEvent();
      }, () => {
        this.contactsUpdatedEvent();
      });
  }

  private getProfileHTMLLinkedin(avatarPreviewWithContactsHtml: string = '') {
    return new Promise((resolve, reject) => {
      const profile: Element = document.querySelector('.neptune-grid') || document.querySelector('.scaffold-layout__content.scaffold-layout__content--main-aside');
      const activityEl: Element = profile && profile.querySelector('.pv-recent-activity-section-v2');
      let activityHTML: string;

      if (activityEl) {
        activityHTML = activityEl.innerHTML;
        activityEl.innerHTML = '';
      }

      const profileHtml: string = profile ? profile.innerHTML : '';
      const htmlParts: string = this.htmlParts || '';

      testVersionMessage(avatarPreviewWithContactsHtml, 'avatarPreviewWithContactsHtml');
      const encodedHTML: string = encodeURI(profileHtml + avatarPreviewWithContactsHtml + htmlParts);

      if (activityEl) {
        activityEl.innerHTML = activityHTML;
      }

      resolve(encodedHTML);
    });
  }

  private openAvatar(): boolean {
    const avatar: HTMLElement = document.querySelector(this.avatarImgSelectorNew);
    const avatarOld: HTMLElement = document.querySelector(this.avatarImgSelector);
    const avatarOwnProfile: HTMLElement = document.querySelector(this.avatarOwnProfileSelector);

    if (!avatar || !avatar.firstElementChild.getAttribute('src').includes('http')) {
      return false;
    }

    if (avatar) {
      avatar.click();
    } else if (avatarOwnProfile) {
      avatarOwnProfile.click();
    } else if (avatarOld) {
      avatarOld.click();
    }

    return true;
  }

  public getSkillsInformation() {
    return new Promise((resolve, reject) => {
      const skillsNewButton: HTMLElement = document.querySelector('[id*=\'skills\'] .artdeco-button--fluid');

      const divSkills: HTMLElement = document.querySelector('#skills');

      let skillsNewButtonV2: any = null;
      if (divSkills) {
        // 2 buttons for skills - artdeco-button--3
        skillsNewButtonV2 = divSkills.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (skillsNewButton || skillsNewButtonV2) {
        if (skillsNewButton) {
          skillsNewButton.click();
        } else {
          skillsNewButtonV2.click();
        }

        setTimeout(() => {
          this.linkedinService.scrollToAnimated(document.querySelector('html'), 7000, 2200)
            .then(() => {
              setTimeout(() => {
                const backToProfileAsSkillsPage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
                this.candidateService.skillsProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
                backToProfileAsSkillsPage.click();
                setTimeout(() => {
                  resolve();
                }, 500);
              }, 1000);
            });
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getEducationInformation() {
    return new Promise((resolve, reject) => {
      const educationNewButton: HTMLElement = document.querySelector('[id*=\'EDUCATION\'] .artdeco-button--3');
      const divEducation: HTMLElement = document.querySelector('#education');
      let educationNewButtonV2: any = null;

      if (divEducation) {
        // educationNewButtonV2 = divEducation.closest('.artdeco-card').querySelector('.artdeco-button--3');
        educationNewButtonV2 = divEducation.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (educationNewButton || educationNewButtonV2) {
        if (educationNewButton) {
          educationNewButton.click();
        } else {
          educationNewButtonV2.click();
        }

        setTimeout(() => {
          const backToProfileAsEducationPage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.educationProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsEducationPage.click();
          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getWorkExperienceInformation() {

    return new Promise((resolve, reject) => {
      const workExperienceNewButton: HTMLElement = document.querySelector('[id*=\'EXPERIENCE\'] .artdeco-button--2');

      const divWorkExperience: HTMLElement = document.querySelector('#experience');
      let workExperienceNewButtonV2: any = null;
      if (divWorkExperience) {
        workExperienceNewButtonV2 = divWorkExperience.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (workExperienceNewButton || workExperienceNewButtonV2) {
        if (workExperienceNewButton) {
          workExperienceNewButton.click();

        } else {
          workExperienceNewButtonV2.click();
        }

        setTimeout(() => {
          const backToProfileAsWorkPage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.workExperienceProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsWorkPage.click();

          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getSingleCompanyPositions() {
    return new Promise(resolve => {
      const workExperienceMainButton = document.querySelector('a#navigation-index-see-all-experiences');
      const singleCompanyPositions: HTMLElement = document.querySelector('#navigation-index-see-all-positions-aggregated');

      if (singleCompanyPositions && !workExperienceMainButton) {
        singleCompanyPositions.click();

        setTimeout(() => {
          const backToProfileAsWorkPage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.workExperienceProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsWorkPage.click();

          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getLanguagesExperienceInformation() {
    return new Promise((resolve, reject) => {
      const languagesNewButton: HTMLElement = document.querySelector('[id*=\'LANGUAGES\'] .artdeco-button--3');

      const divLanguages: HTMLElement = document.querySelector('#languages');
      let languagesNewButtonV2: any = null;
      if (divLanguages) {
        languagesNewButtonV2 = divLanguages.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (languagesNewButton || languagesNewButtonV2) {
        if (languagesNewButton) {
          languagesNewButton.click();
        } else {
          languagesNewButtonV2.click();
        }

        setTimeout(() => {
          const backToProfileAsLanguagePage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.languagesProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsLanguagePage.click();
          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getCoursesInformation() {
    return new Promise((resolve, reject) => {
      const coursesNewButton: HTMLElement = document.querySelector('[id*=\'COURSES\'] .artdeco-button--3');

      const divCourses: HTMLElement = document.querySelector('#courses');
      let coursesNewButtonV2: any = null;
      if (divCourses) {
        coursesNewButtonV2 = divCourses.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (coursesNewButton || coursesNewButtonV2) {
        if (coursesNewButton) {
          coursesNewButton.click();
        } else {
          coursesNewButtonV2.click();
        }

        setTimeout(() => {
          const backToProfileAsCoursePage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.coursesProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsCoursePage.click();
          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  public getProjectsExperienceInformation() {
    return new Promise((resolve, reject) => {
      const projectsNewButton: HTMLElement = document.querySelector('[id*=\'PROJECTS\'] .artdeco-button--3');

      const divProjects: HTMLElement = document.querySelector('#projects');
      let projectsNewButtonV2: any = null;
      if (divProjects) {
        projectsNewButtonV2 = divProjects.closest('.artdeco-card').querySelector('.artdeco-button--fluid');
      }

      if (projectsNewButton || projectsNewButtonV2) {
        if (projectsNewButton) {
          projectsNewButton.click();
        } else {
          projectsNewButtonV2.click();
        }

        setTimeout(() => {
          const backToProfileAsProjectsPage: HTMLElement = document.querySelector('.artdeco-button.artdeco-button--circle.artdeco-button--muted.artdeco-button--3.artdeco-button--tertiary.ember-view');
          this.candidateService.projectsProfileLinkedin = document.querySelector('.scaffold-layout__content main').innerHTML;
          backToProfileAsProjectsPage.click();
          setTimeout(() => {
            resolve();
          }, 500);
        }, 2500);
      } else {
        resolve();
      }
    });
  }

  private openSkillsSection() {
    const delay = 1000;

    return this.linkedinService.delay(() => {
      const skillsButton: HTMLElement = document.querySelector('.pv-skills-section__additional-skills');

      if (skillsButton && skillsButton.getAttribute('aria-expanded') === 'false') {
        skillsButton.click();
      }

    }, delay);
  }

  private openAccomplishmentsSections() {
    const notExpandedBtnSelector = 'button[aria-expanded="false"]';

    return this.linkedinService.delay(() => {
      const acmplBlock: NodeListOf<HTMLElement> = document.querySelectorAll('.pv-accomplishments-block');

      if (acmplBlock && acmplBlock.length) {
        acmplBlock.forEach((elem: HTMLElement) => {
          if (elem.querySelector(notExpandedBtnSelector)) {
            const showMoreButton: HTMLElement = elem.querySelector(notExpandedBtnSelector);

            showMoreButton.click();
            this.htmlParts += elem.innerHTML;
          }
        });
      }
    });
  }

  private openGeneralInfoSection() {
    return this.linkedinService.delay(() => {
      const generalInfo: HTMLElement = document.querySelector(
        `button[aria-controls="top-card-summary-treasury"][aria-expanded="false"]`
      );
      if (generalInfo) {
        generalInfo.click();
      }
    });
  }

  private openShowMoreSections() {
    return this.linkedinService.delay(() => {
      const showMore: NodeListOf<HTMLElement> = document.querySelectorAll('.lt-line-clamp__line--last .lt-line-clamp__more');

      if (showMore && showMore.length) {
        showMore.forEach(item => {
          if (item.getAttribute('aria-expanded') === 'false') {
            item.click();
          }
        });
      }
    });
  }

  private openAnotherSections() {
    const delayBeforeResolve = 1000;
    const delayBeforeShowMoreButton3 = 300;

    return this.linkedinService.delay(() => {
      const anotherButton2: NodeListOf<HTMLElement> = document.querySelectorAll(
        'button.pv-profile-section__see-more-inline.pv-profile-section__text-truncate-toggle'
      );
      const showMoreButton3: NodeListOf<HTMLElement> = document.querySelectorAll(
        '.pv-entity__paging .pv-profile-section__position-group-pager ' +
        'button.pv-profile-section__see-more-inline.pv-profile-section__text-truncate-toggle'
      );
      if (anotherButton2 && anotherButton2.length) {
        LinkedinResume.clickShowMore(anotherButton2);
        setTimeout(() => {
          if (showMoreButton3 && showMoreButton3.length) {
            LinkedinResume.clickShowMore(showMoreButton3);
          }
        }, delayBeforeShowMoreButton3);
      }
    }, delayBeforeResolve);
  }

  private openAllCandidateInfo() {
    this.candidateService.htmlElement = document.querySelector('html');

    return new Promise((resolve, reject) => {
      this.linkedinService.scrollToAnimated(this.candidateService.htmlElement, 5000, 1000)
        .then(() => {
          this.candidateService.togglePageClickable(false);
          this.linkedinService.delay(this.openSkillsSection.bind(this))
            .then(this.openAccomplishmentsSections.bind(this))
            .then(this.openGeneralInfoSection.bind(this))
            .then(this.openShowMoreSections.bind(this))
            .then(this.openAnotherSections.bind(this))
            .then(this.getContactsInfo.bind(this))
            .then(this.getAvatarPreviewHtml.bind(this))
            .then(this.getProfileHTMLLinkedin.bind(this))
            .then((profileHTML) => resolve(profileHTML), () => reject());
        });
    });
  }

  private getAvatarPreviewHtml(contactsModalHtml) {
    return new Promise((resolve, reject) => {

      const openAvatarResult =  this.openAvatar();

      if (!openAvatarResult) {
        return resolve(contactsModalHtml);
      }

      const avatarPreviewModalSelector = '.artdeco-modal.artdeco-modal--layer-default.pv-member-photo-modal';
      const avatarPreviewOpenToWorkModalSelector = '.artdeco-modal.artdeco-modal--layer-default.pv-nonself-member-photo-modal';
      const avatarPreviewModalNewDesignSelector = '.artdeco-modal.artdeco-modal--layer-default.pv-member-photo-modal--fitted';
      const avatarOwnProfileModal = '.artdeco-modal.artdeco-modal--layer-default.photo-frame-page.image-selector-modal';
      const avatarInModal = '.pv-member-photo-modal__content-image-container';

      intervalBeforeGetHTML(this.linkedinModalSelector).then(() => {
        const avatarPreviewModal: HTMLElement = document.querySelector(avatarInModal)
          || document.querySelector(avatarPreviewModalSelector)
          || document.querySelector(avatarPreviewOpenToWorkModalSelector)
          || document.querySelector(avatarPreviewModalNewDesignSelector)
          || document.querySelector(avatarOwnProfileModal);

        if (avatarPreviewModal) {
          const avatarPreviewHtml = avatarPreviewModal.outerHTML;
          resolve(avatarPreviewHtml + contactsModalHtml);
        } else {
          resolve(contactsModalHtml);
        }
      }).catch(() => {
        resolve(contactsModalHtml);
      });
    });
  }

  private closeLinkedinModal() {
    const closeModalSelector = '.artdeco-modal__dismiss.artdeco-button.artdeco-button--circle';
    const closeModalBtn: HTMLElement = document.querySelector(closeModalSelector);

    if (closeModalBtn) {
      closeModalBtn.click();
    }
  }

  private getContactsInfo() {
    window.scrollTo(0, 0);

    return new Promise((resolve, reject) => {
      LinkedinResume.openContacts();

      intervalBeforeGetHTML(this.linkedinModalSelector).then(() => {
        const contactsModal: HTMLElement = document.querySelector(this.linkedinModalSelector);
        const contactsModalHtml = contactsModal.outerHTML || '';

        this.closeLinkedinModal();

        resolve(contactsModalHtml);
      });
    });
  }

  private isCurrentDomainMainForLinkedin() {
    if (this.siteCode.includes(pageSiteCodes.linkedinNew)) {
      setTimeout(() => {
        if (!document.querySelector('.pv-top-card__non-self-photo-wrapper')) {
          let translateText = 'Перезагрузить страницу';
          const saveBtn: any = document.querySelector('.LinkedinResume');
          const updateBtn: any = document.querySelector('.update-contacts');
          if (saveBtn.className.includes('sign-in-to-ats')) { return; }

          if (this.candidateService.locale.includes('ru')) {
            translateText = 'Перезагрузить страницу';
          } else if (this.candidateService.locale.includes('en')) {
            translateText = 'Reload the page';
          } else if (this.candidateService.locale.includes('uk')) {
            translateText = 'Перезавантажити сторінку';
          } else if (this.candidateService.locale.includes('pl')) {
            translateText = 'Przeładuj stronę';
          }

          if (updateBtn) {
            updateBtn.innerText = translateText;
            return;
          }

          saveBtn.innerText = translateText;
        }
      }, 500);

      chrome.runtime.sendMessage({isDomainHandle: true, getCurrentDomain: true},
        (response: BaseAPIResponse) => {
          if (response && response.value) {
            const domain = response.value;
            const isCurrentDomainMain: boolean = domain && domain.origin && domain.origin.includes(currentCloudDomain.origin);

            if (isCurrentDomainMain) {
            }
          }
        });
    }
  }
}

