/// <reference types="@types/chrome"/>
/// <reference types="node"/>

import {LinkedinSearch} from './jobPage/linkedin/linkedinSearch';
import {LinkedinContacts} from './jobPage/linkedin/linkedinContacts';
import {LinkedinResume} from './jobPage/linkedin/linkedinResume';
import {ExtensionPage} from '../shared/models/extensionPage';
import {CandidatesList} from './jobPage/restSites/candidatesList/candidatesList';
import {CandidateResume} from './jobPage/restSites/candidateResume/candidateResume';
import {ExtensionConfig} from './extensionConfig';
import {SuperJobResume} from './jobPage/superJob/superJobResume';
import {pageTypes} from '../shared/models/enums/pageTypes';
import {LinkedinSearchExtensionPage} from '../shared/models/linkedinSearchExtensionPage';
import {CandidatesListTabInfoService} from './services/candidatesListTabInfoService';
import {pageNames} from '../shared/models/enums/pageNames';
import {RabotaUaResumeNew} from './jobPage/rabotaUa/rabotaUaResumeNew';
import {AmazingHiringSearch} from './jobPage/amazingHiring/AmazingHiringSearch';
import {AmazingHiringResume} from './jobPage/amazingHiring/AmazingHiringResume';
import {HHResume} from './jobPage/hh/hhResume';
import {CustomElements} from '../shared/models/enums/customElements';
import {DouSearch} from './jobPage/dou/douSearch';
import {DjinniInBox} from './jobPage/djinni/djinniInBox';
import {DjinniProfile} from './jobPage/djinni/djinniProfile';
import {PracujInBox} from './jobPage/pracuj/pracujInBox';
import {OlxPlBox} from './jobPage/olxPl/OlxPlBox';
import {DelucruArchive} from './jobPage/delucruMd/delucruArchive';
import {DelucruArchiveCandidate} from './jobPage/delucruMd/delucruArchiveCandidate';
import {DelucruResumes} from './jobPage/delucruMd/delucruResumes';
import {DelucruResumeCandidate} from './jobPage/delucruMd/delucruResumeCandidate';
import {RabotaMdCabinet} from './jobPage/rabotaMd/rabotaMdCabinet';
import {RabotaMdResumesCandidate} from './jobPage/rabotaMd/rabotaMdResumesCandidate';
import {RabotaMdResumes} from './jobPage/rabotaMd/rabotaMdResumes';
import {DouUserProfile} from './jobPage/dou/douUserProfile';
import {LinkedinResumeAPI} from "./jobPage/linkedin/linkedinResumeAPI";

class ContentScript {
  private extensionPageList: ExtensionPage[] = ExtensionConfig.getExtensionPageList();

  private static isExtensionScriptsLoaded(scriptsContainer: HTMLElement): boolean {
    return !!scriptsContainer.querySelector(`script[src="${chrome.runtime.getURL('bundle.js')}"]`);
  }

  public init() {
    const scriptsContainer: HTMLElement = document.head || document.documentElement;

    if (!ContentScript.isExtensionScriptsLoaded(scriptsContainer)) {
      this.loadExtensionScripts(scriptsContainer);
      this.handleMessageEventsFromBackground();
    } else {
      this.runExtension();
    }
  }

  private loadExtensionScripts(scriptsContainer: HTMLElement) {
    this.runExtension();
  }

  public destroy() {
    this.extensionPageList = null;
    this.extensionPageList = null;
  }

  private runExtension() {
    const extensionPageArguments: ExtensionPage | LinkedinSearchExtensionPage = this.getExtensionPageArguments();

    if (extensionPageArguments) {
      const extensionPage: ExtensionPage | LinkedinSearchExtensionPage = this.getExtensionPage(extensionPageArguments);

      if (extensionPage && !extensionPageArguments.pageType.includes(pageTypes.linkedinSearch) &&
        !extensionPageArguments.pageName.includes(pageNames.RabotaUaSearchNew)) {
        extensionPage.runExtension();
      }
    }
  }

  private getExtensionPageArguments(): ExtensionPage | LinkedinSearchExtensionPage {
    return this.extensionPageList.filter(item => {
      return item.urlPatternList.some(url => {
        if (item.urlMatcher) {
          return location.href.includes(url) && location.href.match(item.urlMatcher);
        }
        return location.href.includes(url);
      });
    })[0];
  }

  private getExtensionPage(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    // TODO need to refactor (temporary solution) - logic to remove save button after closing
    //  new candidate modal on Rabota.ua (modal like popup on right)
    if (!pageType.pageType.toLowerCase().includes('resume') &&
      document.querySelector('[page-name]') &&
      document.querySelector('[page-name]')
        .getAttribute('page-name')
        .toLowerCase()
        .includes('resume')) {
      document.querySelector('[page-name]').remove();
    }

    // ----------------------
    switch (pageType.pageType) {
      case pageTypes.linkedinResume:
        return new LinkedinResumeAPI(pageType);
      case pageTypes.linkedinSearch:
        this.getCurrentExtensionPageForLinkedinSearch(pageType);
        return;
      case pageTypes.linkedinContacts:
        this.getCurrentExtensionPageForLinkedinContacts(pageType);
        return;
      case pageTypes.superJobResume:
        return new SuperJobResume(pageType);
      case pageTypes.rabotaUaResumeNew:
        return new RabotaUaResumeNew(pageType);
      case pageTypes.HHResume:
        return new HHResume(pageType);
      case pageTypes.candidateResume:
        return new CandidateResume(pageType);
      case  pageTypes.candidatesList:
        if (pageType.pageName.includes(pageNames.RabotaUaSearchNew) || pageType.pageName.includes(pageNames.RabotaUaNotepadNew)) {
          this.getCurrentExtensionPageForRabotaUaSearchNew(pageType);
          return;
        } else {
          return new CandidatesList(pageType);
        }
      case pageTypes.AmazingHiringResume:
        return new AmazingHiringResume(pageType);
      case pageTypes.AmazingHiringSearch:
        return new AmazingHiringSearch(pageType);
      case pageTypes.DouSearch:
        this.getCurrentExtensionPageForDouSearch(pageType);
        return;
      case pageTypes.DouUserProfile:
        this.getCurrentExtensionPageForDouUserProfile(pageType);
        return;
      case pageTypes.djinniInBox:
        this.getCurrentExtensionPageForDjinniInBox(pageType);
        return;
      case pageTypes.PracujInBox:
        this.getCurrentExtensionPageForPracujInBox(pageType);
        return;
      case pageTypes.djinniProfile:
        this.getCurrentExtensionPageForDjinniProfile(pageType);
        return;
      case pageTypes.OlxPlMessage:
        this.getCurrentExtensionPageForOlxPlProfile(pageType);
        return;
      case pageTypes.DelucruArchive:
        this.getCurrentExtensionPageForDelucruArchive(pageType);
        return;
      case pageTypes.DelucruArchiveCandidate:
        this.getCurrentExtensionPageForDelucruArchiveCandidate(pageType);
        return;
      case pageTypes.DelucruResumes:
        this.getCurrentExtensionPageForDelucruResumes(pageType);
        return;
      case pageTypes.DelucruResumesCandidate:
        this.getCurrentExtensionPageForDelucruResumesCandidate(pageType);
        return;
      case pageTypes.RabotaMdCabinet:
        this.getCurrentExtensionPageForRabotaMdCabinet(pageType);
        return;
      case pageTypes.RabotaMdResumes:
        this.getCurrentExtensionPageForForRabotaMdResumes(pageType);
        return;
      case pageTypes.RabotaMdResumesCandidate:
        this.getCurrentExtensionPageForRabotaMdResumesCandidate(pageType);
        return;
    }
  }

  private getCurrentExtensionPageForLinkedinSearch(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new LinkedinSearch(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {});
  }

  private getCurrentExtensionPageForLinkedinContacts(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;
      extensionPage = new LinkedinContacts(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {});
  }

  private getCurrentExtensionPageForRabotaUaSearchNew(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList(false).then(() => {
      let extensionPage;

      extensionPage = new CandidatesList(pageType);
      extensionPage.runExtension();
    }, () => {});
  }

  private getCurrentExtensionPageForDouSearch(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DouSearch(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDouUserProfile(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DouUserProfile(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDjinniInBox(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DjinniInBox(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForPracujInBox(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new PracujInBox(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDjinniProfile(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DjinniProfile(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDelucruArchive(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DelucruArchive(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDelucruArchiveCandidate(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DelucruArchiveCandidate(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDelucruResumes(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DelucruResumes(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForDelucruResumesCandidate(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new DelucruResumeCandidate(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForRabotaMdCabinet(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new RabotaMdCabinet(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForForRabotaMdResumes(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new RabotaMdResumes(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForRabotaMdResumesCandidate(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new RabotaMdResumesCandidate(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private getCurrentExtensionPageForOlxPlProfile(pageType: ExtensionPage | LinkedinSearchExtensionPage) {
    const candidatesListTabInfoService: CandidatesListTabInfoService = new CandidatesListTabInfoService();

    candidatesListTabInfoService.clearIntervalsForCandidatesList().then(() => {
      let extensionPage;

      extensionPage = new OlxPlBox(pageType as LinkedinSearchExtensionPage);
      extensionPage.runExtension();
    }, () => {
    });
  }

  private handleMessageEventsFromBackground() { // remove save button from page id page is not candidate resume
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {

      if (!sender.tab) { // check if this is message from background
        if (request.needToRemoveBtn) {
          if (location.href.includes('linkedin') && !location.href.includes('/in/')) {
            document.querySelector(CustomElements.saveButton) && document.querySelector(CustomElements.saveButton).remove();
          }
        }
      }
    });
  }
}

const contentScript = new ContentScript();

setTimeout(() => {
  contentScript.init();
}, 600);
