import { Controller, Get, UseGuards } from '@nestjs/common';

import { SessionInfo } from '../auth/decorators/session-info.decorator';
import { AuthGuard, ISessionInfo } from '../auth/guards/auth.guard';
import { UserPublicDto } from './dto/user-public.dto';
import { UserService } from './user.service';

@UseGuards(AuthGuard)
@Controller('user')
export class UserController {
    constructor(private readonly userService: UserService) {}

    @Get('me')
    getMe(@SessionInfo() sessionInfo: ISessionInfo): Promise<UserPublicDto> {
        return this.userService.getMe(sessionInfo);
    }
}
