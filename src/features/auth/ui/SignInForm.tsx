'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { Visibility, VisibilityOff } from '@mui/icons-material'
import { Button, IconButton, InputAdornment, TextField } from '@mui/material'
import { useMutation } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { SignInFormDataType, signInSchema } from '@/features/auth/model/sign-in.schema'

const signInQuery = async (data: SignInFormDataType) => {
    const response = await fetch('http://localhost:3001/api/auth/sign-in', {
        body: JSON.stringify(data),
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include',
    })

    if (!response.ok) {
        throw new Error('Something went wrong!')
    }
}

export const SignInForm = () => {
    const { push } = useRouter()
    const [showPassword, setShowPassword] = useState(false)

    const { control, handleSubmit } = useForm({
        resolver: zodResolver(signInSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    })

    const { mutate: signInMutation, isPending } = useMutation({
        mutationFn: signInQuery,
        onSuccess: () => {
            push('/my-profile')
        },
    })

    return (
        <form className="flex flex-col gap-8" onSubmit={handleSubmit((data) => signInMutation(data))}>
            <Controller
                name="email"
                control={control}
                render={({ field, fieldState: { error } }) => (
                    <TextField 
                        label="Email" 
                        error={!!error} 
                        helperText={error?.message} 
                        {...field} 
                    />
                )}
            />
            <Controller
                name="password"
                control={control}
                render={({ field, fieldState: { error } }) => (
                    <TextField
                        label="Password"
                        error={!!error}
                        helperText={error?.message}
                        type={showPassword ? 'text' : 'password'}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                                            {showPassword ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            },
                        }}
                        {...field}
                    />
                )}
            />

            <Button variant="contained" type="submit" loading={isPending}>
                Sign In
            </Button>
        </form>
    )
}
