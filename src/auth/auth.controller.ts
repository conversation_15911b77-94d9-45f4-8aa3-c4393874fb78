import {
    Body,
    Controller,
    HttpCode,
    HttpStatus,
    Post,
    Req,
    Res,
    UnauthorizedException,
    UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Request, Response } from 'express';

import { CreateUserDto } from '../user/dto/create-user.dto';
import { AuthService } from './auth.service';
import { CookieService } from './cookie.service';
import { SignUpResponseDto } from './dto/sign-up-response.dto';
import { AuthGuard } from './guards/auth.guard';

@Controller('auth')
export class AuthController {
    constructor(
        private readonly authService: AuthService,
        private cookieService: CookieService,
    ) {}

    @Post('sign-up')
    @ApiOkResponse({ type: SignUpResponseDto })
    async signUp(@Body() createUserDto: CreateUserDto, @Res({ passthrough: true }) res: Response) {
        const { accessToken, refreshToken } = await this.authService.signUp(createUserDto);
        this.cookieService.setTokens(res, accessToken, refreshToken);

        return { message: 'Successfully signed up' };
    }

    @Post('sign-in')
    @HttpCode(HttpStatus.OK)
    async signIn(@Body() createUserDto: CreateUserDto, @Res({ passthrough: true }) res: Response) {
        const { accessToken, refreshToken } = await this.authService.signIn(createUserDto);
        this.cookieService.setTokens(res, accessToken, refreshToken);

        return { message: 'Successfully signed in' };
    }

    @Post('refresh')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: 'Refresh access token using refresh token' })
    async refreshTokens(@Req() req: Request, @Res({ passthrough: true }) res: Response) {
        const currentRefreshToken = req.cookies?.[CookieService.REFRESH_TOKEN_KEY] as string | undefined;

        if (!currentRefreshToken) {
            throw new UnauthorizedException('Refresh token not found');
        }

        const { accessToken, refreshToken } = await this.authService.refreshTokens(currentRefreshToken);
        this.cookieService.setTokens(res, accessToken, refreshToken);

        return { message: 'Tokens refreshed successfully' };
    }

    @UseGuards(AuthGuard)
    @HttpCode(HttpStatus.OK)
    @Post('sign-out')
    signOut(@Res({ passthrough: true }) res: Response) {
        this.cookieService.removeTokens(res);

        return { message: 'Successfully signed out' };
    }
}
