import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>Than, MoreThan, Repository } from 'typeorm';

import { RefreshToken } from './entities/refresh-token.entity';
import { RefreshTokenDto } from './dto/refresh-token.dto';

@Injectable()
export class RefreshTokenService {
    constructor(
        @InjectRepository(RefreshToken)
        private readonly refreshTokenRepository: Repository<RefreshToken>,
    ) {}

    async storeToken(refreshTokenDto: RefreshTokenDto): Promise<RefreshToken> {
        const token = this.refreshTokenRepository.create(refreshTokenDto);
        return this.refreshTokenRepository.save(token);
    }

    async findValidTokenForUser(userId: string, hashedToken: string): Promise<RefreshToken | null> {
        return this.refreshTokenRepository.findOne({
            where: {
                userId,
                hashedToken,
                isRevoked: false,
                expiresAt: More<PERSON>han(new Date()),
            },
        });
    }

    async findTokenByHashedValue(hashedToken: string): Promise<RefreshToken | null> {
        return this.refreshTokenRepository.findOne({
            where: {
                hashedToken,
                isRevoked: false,
                expiresAt: MoreThan(new Date()),
            },
        });
    }

    async revokeToken(tokenId: string): Promise<void> {
        await this.refreshTokenRepository.update(tokenId, { isRevoked: true });
    }

    async revokeAllTokensForUser(userId: string): Promise<void> {
        await this.refreshTokenRepository.update({ userId, isRevoked: false }, { isRevoked: true });
    }

    // Optional: for a scheduled task
    async deleteExpiredTokens(): Promise<void> {
        await this.refreshTokenRepository.delete({ expiresAt: LessThan(new Date()) });
    }
}
