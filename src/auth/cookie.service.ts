import { Injectable } from '@nestjs/common';
import { Response } from 'express';
import ms from 'ms';

import { accessTokenTime, refreshTokenTime } from './config/token-exp-time';

@Injectable()
export class CookieService {
    static readonly ACCESS_TOKEN_KEY = 'access_token';
    static readonly REFRESH_TOKEN_KEY = 'refresh_token';

    setAccessToken(res: Response, token: string) {
        res.cookie(CookieService.ACCESS_TOKEN_KEY, token, {
            httpOnly: true,
            secure: true,
            sameSite: 'lax',
            maxAge: ms(accessTokenTime),
            expires: new Date(Date.now() + ms(accessTokenTime)),
        });
    }

    setRefreshToken(res: Response, token: string) {
        res.cookie(CookieService.REFRESH_TOKEN_KEY, token, {
            httpOnly: true,
            secure: true,
            sameSite: 'lax',
            maxAge: ms(refreshTokenTime),
            expires: new Date(Date.now() + ms(refreshTokenTime)),
        });
    }

    setTokens(res: Response, accessToken: string, refreshToken: string) {
        console.log(ms(accessTokenTime));
        console.log(ms(refreshTokenTime));

        this.setAccessToken(res, accessToken);
        this.setRefreshToken(res, refreshToken);
    }

    removeAccessToken(res: Response) {
        res.clearCookie(CookieService.ACCESS_TOKEN_KEY);
    }

    removeRefreshToken(res: Response) {
        res.clearCookie(CookieService.REFRESH_TOKEN_KEY);
    }

    removeTokens(res: Response) {
        this.removeAccessToken(res);
        this.removeRefreshToken(res);
    }
}
