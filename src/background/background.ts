/// <reference types="chrome"/>
/// <reference types="node"/>

import {allURLs, AmazingHiringURLs} from '../shared/data/extensionPagesURLs';
import {UrlMatches} from '../shared/models/requests/urlMatches';
import {
  MessageRequest,
  MessageForDomain,
  MessageForCandidateHandler,
  MessageForLocalStorage
} from '../shared/models/requests/messageRequest';
import {RequestBackgroundService} from './requestBackgroundService';
import MessageSender = chrome.runtime.MessageSender;
import {CandidatesListTabInfo} from '../shared/models/candidatesListTabInfo';
import {DomainInfo} from '../shared/models/domainInfo';
import {environment} from '../environments/environment';
import {BaseAPIResponse} from '../shared/models/requests/response';
import {LinkedinStorageKeys} from "../content-script/jobPage/linkedin/linkedin.type";

class Background {
  candidatesLisTabInfo: CandidatesListTabInfo = {};

  private urlsForMatches: UrlMatches[];
  private requestService: RequestBackgroundService;
  private injectedScripts = {};

  constructor() {
    this.requestService = new RequestBackgroundService();
  }

  private static setToLocalStorage(localStorageKey: string, localStorageToSet: string) {
    chrome.storage.local.set({ [localStorageKey]: localStorageToSet }, () => {});
  }

  private static getFromLocalStorage(localStorageKey: any, sendResponse) {
    chrome.storage.local.get([localStorageKey], (result) => {
      sendResponse({value: result.localStorageKey});
    });
  }

  private static localStorageHandle(request: MessageForLocalStorage, sendResponse) {
    if (request.localStorageToSet) {
      Background.setToLocalStorage(request.localStorageKey, request.localStorageToSet);
    } else {
      Background.getFromLocalStorage(request.localStorageKey, sendResponse);
    }
  }

  private static runContentScript(tabId: number, url: string)  {
    if (Background.checkForRun(url)) {

      setTimeout(() => {
        // @ts-ignore // need to update chrome-types
        chrome.scripting.executeScript({
          files: ['content-script.js', 'polyfills.js', 'bundle.js', 'main.js'],
          target: {tabId}
        });
      }, 1000);
    }
  }

  private static checkForRun(url: string) {
    return !url.includes(AmazingHiringURLs[0]) || !environment.production;
  }

  initBackground() {
    this.getUrlsForMatches();
    this.chromeEvents();
    this.getAndSetDomain();
  }

  private chromeEvents() {
    this.executeScriptsWithoutPageReload();
    this.executeScriptsAfterPageReload();
    // this.initRequestListener();

    this.handleMessageEvents();
    this.initTabOnUpdatedListener();
  }

  private executeScriptsWithoutPageReload() {
    chrome.webNavigation.onHistoryStateUpdated.addListener((details: any) => {
      const domain: DomainInfo = this.requestService.getCurrentDomain();

      chrome.storage.local.set({ currentCandidate: details.documentLifecycle });

      if (details.url) {
        Background.runContentScript(details.tabId, details.url);
      }
    }, {url: this.urlsForMatches});
  }

  private executeScriptsAfterPageReload() {
    // listener for olxPl and prcuj
    chrome.webNavigation.onCompleted.addListener((details) => {
      const domain: DomainInfo = this.requestService.getCurrentDomain();

      if (details.url.includes('olx.pl') || details.url.includes('pracuj.pl')) {
        if (domain) {
          Background.runContentScript(details.tabId, details.url);
        }
      }
    }, {url: this.urlsForMatches});

    // listener for rabotamd on 'back' browser history button click
    chrome.webNavigation.onCommitted.addListener((details) => {
      const domain: DomainInfo = this.requestService.getCurrentDomain();

      if (details.transitionQualifiers[0] === 'forward_back' && (details.url.includes('rabota.md') || details.url.includes('delucru.md'))) {
        if (domain) {
          Background.runContentScript(details.tabId, details.url);
        }
      }
    }, {url: this.urlsForMatches});

    // execute content-script.js after reloading page:
    chrome.webNavigation.onDOMContentLoaded.addListener((details) => {
      const domain: DomainInfo = this.requestService.getCurrentDomain();

      if (domain) {
        Background.runContentScript(details.tabId, details.url);
      }
    }, {url: this.urlsForMatches});
  }

  private handleMessageEvents() {
    chrome.runtime.onMessage.addListener((request: MessageRequest, sender: MessageSender, sendResponse) => {
      if (request.requestUrl && request.method) {
        return this.requestService.sendRequest(request, sender, sendResponse);
      } else if ((request as MessageForDomain).isDomainHandle) {
        return this.domainHandle(request, sendResponse);
      } else if ((request as MessageForLocalStorage).localStorageKey) {
        Background.localStorageHandle(request, sendResponse);
      } else if ((request as MessageForCandidateHandler).CandidatesListTabInfo) {
        this.handleCandidatesListTabInfo(request, sender, sendResponse);
      }

      if (request.reloadTab) {
        chrome.tabs.reload(sender.tab.id);
      }
    });

    return true;
  }

  private domainHandle(request: MessageForDomain, sendResponse) {
    if (request.getDomainsList) {
      return this.requestService.getDomain(sendResponse);
    } else if (request.saveCurrentDomain) {
      this.requestService.setCurrentDomain(request.saveCurrentDomain);
      sendResponse({value: this.requestService.getCurrentDomain()});
    } else if (request.getCurrentDomain) {
      sendResponse({value: this.requestService.getCurrentDomain()});
    }
  }

  private handleCandidatesListTabInfo(request: MessageForCandidateHandler, sender: MessageSender, sendResponse) {
    const tabId: number = sender.tab && sender.tab.id;

    this.checkExistingCandidateListTabInfo(tabId);

    if (tabId) {
      if (request.getValue) {
        sendResponse({value: this.candidatesLisTabInfo, tabId});
      } else if (request.setValue) {
        this.setValueForTabInfo(request, tabId);
      } else if (request.deleteValue)  {
        this. setValueForTabInfo(request, tabId, true);
      }
    }
  }

  private setValueForTabInfo(request: MessageForCandidateHandler, tabId: number, forDelete: boolean = false) {
    if (request.timer) {
      this.candidatesLisTabInfo[tabId].timer = forDelete ? null : request.timer;
    }
  }

  private checkExistingCandidateListTabInfo(tabId: number, url: string = '') {
    if (!this.candidatesLisTabInfo) {
      this.candidatesLisTabInfo = {};
    }

    if (!this.candidatesLisTabInfo.hasOwnProperty(tabId)) {
      this.candidatesLisTabInfo[tabId] = {};

      if (url) {
        this.candidatesLisTabInfo[tabId].url = url;
      }
    }
  }

  private getUrlsForMatches() {
    this.urlsForMatches = [];

    allURLs.forEach((url: string) => {
      this.urlsForMatches.push({urlMatches: url});
    });

    // for local files:
    // this.urlsForMatches.push({urlPrefix: 'file:///*'});
  }

  private getAndSetDomain() {
    this.requestService.getDomain((response: BaseAPIResponse) => {
      if (response) {
        const activeDomain = response.value ? response.value.filter(domainItem => domainItem.authorized) : [];
        if (activeDomain.length === 1) {
          this.requestService.setCurrentDomain(activeDomain[0]);
        }
      }
    });
  }

  private initTabOnUpdatedListener() {
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, sendResponse) => {
      const url = changeInfo.url;

      if (url && url.includes('olx.pl')) {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          chrome.storage.local.get(['olxToken'], (result) => {
            chrome.tabs.sendMessage(tabs[0].id, {olxToken: result.olxToken});
          });
        });
      }

      if (url && url.includes('linkedin') && !url.includes('/in/')) {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          chrome.tabs.sendMessage(tabs[0].id, {needToRemoveBtn: true});
        });
      }
    });
  }
}

const background = new Background();
background.initBackground();

chrome.storage.local.get(['currentDomain'], (result) => {
  if (result && result.currentDomain) {
    chrome.runtime.setUninstallURL(`${result.currentDomain}i/extension-removed`);
  } else {
    chrome.runtime.setUninstallURL('https://cleverstaff.net/i/extension-removed');
  }
});


chrome.alarms.create('keepAlive', { periodInMinutes: 1 });
chrome.alarms.onAlarm.addListener((alarm) => {
  console.log('Wake up, Neo...');
});

// Intercept voyager requests to get appropriate fresh headers
chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    const headers = details.requestHeaders.reduce((acc, header) => {
      acc[header.name] = header.value;
      return acc;
    }, {});

    chrome.storage.local.set({ [LinkedinStorageKeys.voyagerRequestHeaders]: headers });
  },
  { urls: [
    'https://www.linkedin.com/voyager/api/voyagerIdentityDashProfilePhotoFrames',
    'https://www.linkedin.com/voyager/api/relationships/connectionsSummary'
    ] },
  ['requestHeaders'] // This is important to intercept request headers!
);
