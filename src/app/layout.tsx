import './globals.css'

import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import type { Metadata } from 'next'
import { Geist, Geist_Mono } from 'next/font/google'

import { ThemeProvider } from '@/shared/theme-provider/ThemeProvider'
import { ThemeToggle } from '@/shared/theme-toggle/ThemeToggle'

const geistSans = Geist({
    variable: '--font-geist-sans',
    subsets: ['latin'],
})

const geistMono = Geist_Mono({
    variable: '--font-geist-mono',
    subsets: ['latin'],
})

export const metadata: Metadata = {
    title: 'Create Next App',
    description: 'Generated by create next app',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang="en">
            <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
                <AppRouterCacheProvider options={{ enableCssLayer: true }}>
                    <ThemeProvider>
                        {children}
                        <ThemeToggle />
                    </ThemeProvider>
                </AppRouterCacheProvider>
            </body>
        </html>
    )
}
