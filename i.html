<!DOCTYPE html>
<!--[if lt IE 7]>
<html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>
<html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>
<html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!-->
<html class="no-js" ng-style="$root.activePublicController === 'SignupController' ? { height: '100%' } : null" lang="en" bindonce ng-app="RecruitingAppStart">
    <!--<![endif]-->
    <head>
        <title ng-bind="title">CleverStaff</title>
        <base href="/i" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,300,700" rel="preload" as="font" type="text/css" />
        <link href="https://fonts.googleapis.com/css?family=Roboto:400,700,300" rel="preload" as="font" type="text/css" />
        <link rel="stylesheet" href="dist/css/lib-styles.min.css?v=916" />
        <link rel="stylesheet" href="dist/css/start-styles.min.css?v=916" />
        <link rel="stylesheet" href="dist/css/react-styles.css?v=916" />
        <link type="text/css" rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/flag-icon-css/0.8.2/css/flag-icon.min.css" />
        <LINK REL="SHORTCUT ICON" HREF="images/sprite/favicon.png?v=916" />
        <script type="text/javascript" src="dist/js/startLib.js?v=916"></script>
        <meta property="og:title" content="New vacancies opened!" />
        <meta property="og:description" content="Check job descriptions here and apply now!" />
        <meta property="og:image" content="https://dev.cleverstaff.net/images/sprite/vacancy-new.jpg" />
        <meta property="og:image:width" content="600" />
        <meta property="og:image:height" content="333" />
        <meta property="og:site_name" content="CleverStaff" />
        <meta name="robots" content="{{ ngMeta['robots'] }}" />
    </head>
    <body
        ng-style="
            $root.activePublicController === 'RestorePasswordController' || $root.activePublicController === 'SignupController'
                ? { 'background-color': '#ffffff' }
                : null
        "
        ng-controller="mainController"
    >
        <!--[if lt IE 10]>
            <p class="browsehappy">
                You are using an
                <strong>outdated</strong>
                browser. Please
                <a href="http://browsehappy.com/">upgrade your browser</a>
                to improve your experience.
            </p>
        <![endif]-->
        <!-- builddist:section githubRibbon -->
        <!-- /builddist -->

        <!-- Fixed navbar -->
        <div ng-show="$root.loading" ng-switch="$root.orgId">
            <div class="loader-container" ng-switch-when="830e6f8ba79a42469df7649c81bc6a1b">
                <div class="loader-outer">
                    <div class="loader"></div>
                </div>
            </div>

            <div class="loader-container" ng-switch-default>
                <ng-include src="'partials/main-loader.html'"></ng-include>
                <!--        <ng-include src="'partials/new-year-loader.html'"></ng-include>-->
            </div>
        </div>

        <div
            ng-show="
                $root.activePublicController != 'PublicCompanyController' &&
                $root.activePublicController != 'PublicVacancyController' &&
                $root.activePublicController != 'PublicCandidateController' &&
                $root.activePublicController != 'PublicTestController' &&
                $root.activePublicController != 'pdConsentPublicDeny' &&
                $root.activePublicController != 'pdConsentPublicAllow' &&
                $root.activePublicController != 'SignupController' &&
                $root.activePublicController !== 'RestorePasswordController'
            "
            class="navbar navbar-static-top navbar-public"
            ng-class="$root.activePublicController === 'SignupController' ? 'transparent-block' : null"
            ng-cloak
        >
            <div class="container-fluid">
                <div class="navbar-header-public-vacancy">
                    <a href="../">
                        <img
                            ng-show="$root.activePublicController !== 'SignupController'"
                            src="images/sprite/CS_logo_white_transparent.png"
                            width="50px"
                            alt="CS-logo"
                        />
                    </a>
                </div>
            </div>
        </div>

        <div class="controller-view">
            <div ui-view></div>
        </div>

        <script src="lib/jquery.mask.min.js"></script>

        <!-- build:js(.) scripts/vendor.js -->
        <!-- bower:js -->
        <!-- endbower -->
        <!-- endbuild -->

        <!-- build:js(.) scripts/plugins.js -->
        <script type="text/javascript" src="js/ApiKey.js?v=916"></script>
        <script type="text/javascript" src="dist/js/startApp.js?v=916"></script>
        <script id="versionScript" type="text/javascript" src="dist/js/mainApp.js?v=916"></script>
        <script id="versionScript" type="text/javascript" src="dist/js/react-bundle.js?v=916"></script>
        <!-- endbuild -->
    </body>
</html>
